from typing import List, <PERSON><PERSON>


def normalize_list(list_num: List[float]) -> List[float]:
    """
    Normalize each number so that the sum of them is 1.

    This function takes a list of numbers and returns a new list where each number
    is divided by the total sum of the list. If the total is zero, it returns a list
    of zeros.

    Args:
        list_num (List[float]): A list of numbers to be normalized.

    Returns:
        List[float]: A list of normalized numbers.

    Example 1:
    ```python
    result = normalize_list(list_num=[1, 2, 3, 4])
    # returns [0.1, 0.2, 0.3, 0.4]
    ```

    Example 2:
    ```python
    result = normalize_list(list_num=[0, 0, 0, 0])
    # returns [0, 0, 0, 0]
    ```

    """
    total = sum(list_num)
    if total == 0:
        return [0] * len(list_num)

    normalized = [x / total for x in list_num]
    return normalized


def calculate_total_ratio_of_index_range_list(
    index_range_list: List[Tuple[int, int]], length: int
) -> float:
    """
    Calculate the total ratio of a index_range_list in a given length.

    This function calculates the total ratio of a list of index ranges within a given length.
    It sums up the lengths of all index ranges and divides it by the total length.

    Args:
        index_range_list (List[tuple[int, int]]): A list of tuples, where each tuple contains the start and end index of a range.
        length (int): The total length to calculate the ratio against.

    Returns:
        float: The total ratio of the index ranges within the given length.

    Example:
    ```python
    result = calculate_total_ratio_of_index_range_list(index_range_list=[(0, 5), (10, 15)], length=20)
    # returns 0.5
    ```
    """
    total_length = 0
    for start, end in index_range_list:
        total_length += end - start

    return total_length / length if length > 0 else 0


def calculate_matched_len_ratio(matched_len: int, whole_text_len: int) -> float:
    """Calculates the ratio of matched length to the whole text length.

    Args:
        matched_len (int): The length of the matched text.
        whole_text_len (int): The length of the whole text.

    Returns:
        float: The ratio of matched length to the whole text length.

    Example 1:
    ```python
    result = calculate_matched_len_ratio(matched_len=5, whole_text_len=10)
    # returns 0.5
    ```

    Example 2:
    ```python
    result = calculate_matched_len_ratio(matched_len=0, whole_text_len=10)
    # returns 0.0
    ```
    """
    return matched_len / whole_text_len if whole_text_len > 0 else 0


from tomsze_utils.string_utils import (
    get_number_of_words,
    get_number_of_words_by_index_range_list,
)


from typing import List, Tuple


def calculate_matched_word_ratio_using_index_range_list(
    index_range_list: List[Tuple[int, int]], text: str
) -> float:
    """Calculates the ratio of matched words to the total number of words in the text, using an index range list.

    Args:
        index_range_list (List[Tuple[int, int]]): A list of tuples, where each tuple contains the start and end index of a range.
        text (str): The text to analyze.

    Returns:
        float: The ratio of matched words to the total number of words in the text.

    Examples:
        ```python
        result = calculate_matched_word_ratio_using_index_range_list(index_range_list=[(0, 5)], text="hello world")
        # returns 0.5
        ```

        ```python
        result = calculate_matched_word_ratio_using_index_range_list(index_range_list=[(0, 5), (6, 11)], text="hello world abc")
        # returns 0.6666666666666666
        ```
    """
    num_word_matched = get_number_of_words_by_index_range_list(text, index_range_list)
    num_word_in_text = get_number_of_words(text)
    return num_word_matched / num_word_in_text if num_word_in_text > 0 else 0
