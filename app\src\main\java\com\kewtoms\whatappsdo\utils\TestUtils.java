package com.kewtoms.whatappsdo.utils;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_obtain_new_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_sign_in_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_sign_in_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_validate_access_token_link;

import android.content.Intent;

import androidx.annotation.NonNull;

import com.kewtoms.whatappsdo.MainActivity;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.generated.api_constant_keys.ApiResponseKeys;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ErrorMessages;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ResponseKeys;
import com.tomsze.JsonUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

import okhttp3.mockwebserver.MockWebServer;

public class TestUtils {

  /**
   * Creates a mock response JSONObject with structured success
   * status, access token, and refresh token.
   *
   * @param modifyOriginal (boolean): Whether to modify the original
   * JSON or create a new one.
   * @param success (boolean): The success flag to be included in the
   * response.
   * @param accessToken (String): The access token string to be
   * included in the response.
   * @param refreshToken (String): The refresh token string to be
   * included in the response.
   * @return mockResponseJson (JSONObject): A constructed mock
   * response with nested structure and provided fields.
   * @example <pre>{@code
   * JSONObject example1 = TestUtils.createMockResponseJson(true, true, "abc123", "xyz789");
   * }</pre>
   * <pre>{@code
   * JSONObject example2 = TestUtils.createMockResponseJson(false, false, null, null);
   * }</pre>
   * <p>
   * Required Imports: import org.json.JSONObject; import
   * com.kewtoms.whatappsdo.utils.TestUtils;
   */
  @NonNull
  public static JSONObject createMockSignInSuccessResponseJson(
    JSONObject mockResponseJson,
    boolean modifyOriginal,
    boolean success,
    String accessToken,
    String refreshToken) {

    try {
      String responseKey = ApiResponseKeys.API_RESPONSE;
      String apiFunResultKey =
        Constants.response_json_apiFunResult_key;
      String dbResultKey = ResponseKeys.RESULT;

      // Create the success data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.IS_SUCCESS,
        success,
        modifyOriginal
      );


      // Create the access token in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.ACCESS_TOKEN,
        accessToken,
        modifyOriginal
      );

      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.REFRESH_TOKEN,
        refreshToken,
        modifyOriginal
      );

      // Add message field for consistency with API response structure
      String message = success ? "Sign in successful" : "Sign in failed";
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.MESSAGE,
        message,
        modifyOriginal
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }

  /**
   * Creates a mock sign-in failure response JSON object for testing error scenarios.
   * This method creates a response with isSuccess=false and includes an error message.
   *
   * @param errorMessage The error message to include in the response
   * @return JSONObject representing a failed sign-in response
   */
  @NonNull
  public static JSONObject createMockSignInFailureResponseJson(String errorMessage) {
    JSONObject mockResponseJson = new JSONObject();

    try {
      String responseKey = ApiResponseKeys.API_RESPONSE;
      String apiFunResultKey = Constants.response_json_apiFunResult_key;
      String dbResultKey = ResponseKeys.RESULT;

      // Create the failure data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(responseKey, apiFunResultKey, dbResultKey),
        ResponseKeys.IS_SUCCESS,
        false,
        true
      );

      // Add error message
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(responseKey, apiFunResultKey, dbResultKey),
        ResponseKeys.MESSAGE,
        errorMessage != null ? errorMessage : "Sign in failed",
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    return mockResponseJson;
  }

  @NonNull
  public static JSONObject createMockGetIsServerOnlineResponseJson(
    boolean isOnline) {
    JSONObject mockResponseJson = new JSONObject();
    try {
      String responseKey = ApiResponseKeys.API_RESPONSE;
      String apiFunResultKey =
        Constants.response_json_apiFunResult_key;


      // Create the success data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey
        ),
        "is_online",
        isOnline,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }


  @NonNull
  public static JSONObject createMockValidateAccessTokenFailResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        ErrorMessages.TOKEN_EXPIRED,
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        false,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }

  @NonNull
  public static JSONObject createMockValidateAccessTokenSuccessResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        "",
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        true,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }


  @NonNull
  public static JSONObject createMockObtainNewAccessTokenFailResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        ErrorMessages.TOKEN_EXPIRED,
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        false,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }


  @NonNull
  public static JSONObject createMockObtainNewAccessTokenSuccessResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        "",
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        true,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }

  // TODO
  public static Object createMockSearchUserAppHasReturnResponseJson() {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        List.of(),
        // empty list
        "packageNames",
        "xxx",
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        List.of(),
        // empty list
        "scores",
        456,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }

  @NonNull
  public static Intent createIntentWithSilentSignInMockData(MockWebServer mockWebServer) {
    Intent intent = new Intent(
      getApplicationContext(),
      MainActivity.class
    );

    // Pass mock url and mock encrypt/decrypt
    intent.putExtra(
      Constants.mockSignInUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_link).toString()
    );
    intent.putExtra(
      Constants.mockGetIsServerOnlineUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_get_is_server_online_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockValidateAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_validate_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockObtainNewAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_obtain_new_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockSignInAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockIsEncryptDecryptKey,
      true
    );

    // Set mode to production (which will enable silent login)
    intent.putExtra(
      Constants.mockDeploymentModeKey,
      Configuration.DeploymentMode.LOCAL.toString()
    );
    intent.putExtra(
      Constants.mockEnvModeKey,
      Configuration.EnvironmentMode.PROD.toString()
    );
    return intent;
  }


}
