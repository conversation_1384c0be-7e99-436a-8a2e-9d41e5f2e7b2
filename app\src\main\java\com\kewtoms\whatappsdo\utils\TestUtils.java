package com.kewtoms.whatappsdo.utils;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_obtain_new_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_sign_in_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_sign_in_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_validate_access_token_link;

import android.content.Intent;

import androidx.annotation.NonNull;

import com.kewtoms.whatappsdo.MainActivity;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.generated.api_constant_keys.ApiResponseKeys;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ErrorMessages;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ResponseKeys;
import com.tomsze.JsonUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

import okhttp3.mockwebserver.MockWebServer;

public class TestUtils {

  /**
   * Creates a mock response JSONObject with structured success
   * status, access token, and refresh token.
   *
   * @param modifyOriginal (boolean): Whether to modify the original
   * JSON or create a new one.
   * @param success (boolean): The success flag to be included in the
   * response.
   * @param accessToken (String): The access token string to be
   * included in the response.
   * @param refreshToken (String): The refresh token string to be
   * included in the response.
   * @return mockResponseJson (JSONObject): A constructed mock
   * response with nested structure and provided fields.
   * @example <pre>{@code
   * JSONObject example1 = TestUtils.createMockResponseJson(true, true, "abc123", "xyz789");
   * }</pre>
   * <pre>{@code
   * JSONObject example2 = TestUtils.createMockResponseJson(false, false, null, null);
   * }</pre>
   * <p>
   * Required Imports: import org.json.JSONObject; import
   * com.kewtoms.whatappsdo.utils.TestUtils;
   */
  @NonNull
  public static JSONObject createMockSignInSuccessResponseJson(
    JSONObject mockResponseJson,
    boolean modifyOriginal,
    boolean success,
    String accessToken,
    String refreshToken) {

    try {
      String responseKey = ApiResponseKeys.API_RESPONSE;
      String apiFunResultKey =
        Constants.response_json_apiFunResult_key;
      String dbResultKey = ResponseKeys.RESULT;

      // Create the success data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.IS_SUCCESS,
        success,
        modifyOriginal
      );


      // Create the access token in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.ACCESS_TOKEN,
        accessToken,
        modifyOriginal
      );

      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.REFRESH_TOKEN,
        refreshToken,
        modifyOriginal
      );

      // Add message field for consistency with API response structure
      String message = success ? "Sign in successful" : "Sign in failed";
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey,
          dbResultKey
        ),
        ResponseKeys.MESSAGE,
        message,
        modifyOriginal
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }

  /**
   * Creates a mock sign-in failure response JSON object for testing error scenarios.
   * This method creates a response with isSuccess=false and includes an error message.
   *
   * @param errorMessage The error message to include in the response
   * @return JSONObject representing a failed sign-in response
   */
  @NonNull
  public static JSONObject createMockSignInFailureResponseJson(String errorMessage) {
    JSONObject mockResponseJson = new JSONObject();

    try {
      String responseKey = ApiResponseKeys.API_RESPONSE;
      String apiFunResultKey = Constants.response_json_apiFunResult_key;
      String dbResultKey = ResponseKeys.RESULT;

      // Create the failure data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(responseKey, apiFunResultKey, dbResultKey),
        ResponseKeys.IS_SUCCESS,
        false,
        true
      );

      // Add error message
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(responseKey, apiFunResultKey, dbResultKey),
        ResponseKeys.MESSAGE,
        errorMessage != null ? errorMessage : "Sign in failed",
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    return mockResponseJson;
  }

  @NonNull
  public static JSONObject createMockGetIsServerOnlineResponseJson(
    boolean isOnline) {
    JSONObject mockResponseJson = new JSONObject();
    try {
      String responseKey = ApiResponseKeys.API_RESPONSE;
      String apiFunResultKey =
        Constants.response_json_apiFunResult_key;


      // Create the success data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          responseKey,
          apiFunResultKey
        ),
        "is_online",
        isOnline,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }


  @NonNull
  public static JSONObject createMockValidateAccessTokenFailResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        ErrorMessages.TOKEN_EXPIRED,
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        false,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }

  @NonNull
  public static JSONObject createMockValidateAccessTokenSuccessResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        "",
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        true,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }


  @NonNull
  public static JSONObject createMockObtainNewAccessTokenFailResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        ErrorMessages.TOKEN_EXPIRED,
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        false,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }


  @NonNull
  public static JSONObject createMockObtainNewAccessTokenSuccessResponseJson(
  ) {
    JSONObject mockResponseJson = new JSONObject();
    try {

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.MESSAGE,
        "",
        true
      );

      // Create the data in response
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(
          ApiResponseKeys.API_RESPONSE,
          Constants.response_json_apiFunResult_key,
          ResponseKeys.RESULT
        ),
        ResponseKeys.IS_SUCCESS,
        true,
        true
      );

    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    return mockResponseJson;
  }

  /**
   * Creates a comprehensive mock JSON response that strictly adheres to the server's API response format.
   * This function generates responses with the complete nested structure expected by the client.
   *
   * The response structure follows this pattern:
   * {
   *   "ApiResponse": {
   *     "ApiFunResult": {
   *       "code": 200,
   *       "is_success": true,
   *       "message": "Operation successful.",
   *       "data": { ... actual payload ... },
   *       "db_result": { ... database operation result ... }
   *     }
   *   }
   * }
   *
   * @param responseType The type of response to generate (e.g., "search_user_app", "dashboard_info", "sign_in")
   * @param isSuccess Whether this should be a success or error response
   * @param customData Optional custom data to include in the response payload
   * @param customMessage Optional custom message (defaults to standard success/error messages)
   * @param httpCode Optional HTTP status code (defaults to 200 for success, 400 for error)
   * @return JSONObject representing a complete mock server response
   * @example <pre>{@code
   * // Example 1: Search user app success response
   * JSONObject searchResponse = TestUtils.createMockApiResponse(
   *   "search_user_app", true, null, null, null
   * );
   * }</pre>
   * <pre>{@code
   * // Example 2: Dashboard info with custom data
   * JSONObject customData = new JSONObject();
   * customData.put("monthly_revenue", 1500.0);
   * customData.put("num_paid_users", 25);
   * JSONObject dashboardResponse = TestUtils.createMockApiResponse(
   *   "dashboard_info", true, customData, "Dashboard loaded successfully", 200
   * );
   * }</pre>
   * <pre>{@code
   * // Example 3: Error response
   * JSONObject errorResponse = TestUtils.createMockApiResponse(
   *   "sign_in", false, null, "Invalid credentials", 401
   * );
   * }</pre>
   */
  @NonNull
  public static JSONObject createMockApiResponse(
    @NonNull String responseType,
    boolean isSuccess,
    JSONObject customData,
    String customMessage,
    Integer httpCode
  ) {
    JSONObject mockResponseJson = new JSONObject();

    try {
      // Set default values
      int code = httpCode != null ? httpCode : (isSuccess ? 200 : 400);
      String message = customMessage != null ? customMessage :
        (isSuccess ? "Operation successful." : "Operation failed.");

      // Create the main response structure
      String responseKey = ApiResponseKeys.API_RESPONSE;
      String apiFunResultKey = Constants.response_json_apiFunResult_key;

      // Add standard ApiResponse fields
      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(responseKey, apiFunResultKey),
        "code",
        code,
        true
      );

      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(responseKey, apiFunResultKey),
        "is_success",
        isSuccess,
        true
      );

      JsonUtils.constructNestedJson(
        mockResponseJson,
        Arrays.asList(responseKey, apiFunResultKey),
        "message",
        message,
        true
      );

      // Add response-type specific data
      JSONObject dataPayload = generateMockDataForResponseType(responseType, isSuccess, customData);
      if (dataPayload != null) {
        JsonUtils.constructNestedJson(
          mockResponseJson,
          Arrays.asList(responseKey, apiFunResultKey),
          "data",
          dataPayload,
          true
        );
      }

      // Add database result for responses that include it
      if (shouldIncludeDbResult(responseType)) {
        JSONObject dbResult = generateMockDbResult(responseType, isSuccess);
        JsonUtils.constructNestedJson(
          mockResponseJson,
          Arrays.asList(responseKey, apiFunResultKey),
          ResponseKeys.RESULT,
          dbResult,
          true
        );
      }

    } catch (JSONException e) {
      throw new RuntimeException("Failed to create mock API response", e);
    }

    return mockResponseJson;
  }

  /**
   * Generates mock data payload based on the response type.
   * This ensures each response type has realistic and properly structured data.
   */
  @NonNull
  private static JSONObject generateMockDataForResponseType(
    @NonNull String responseType,
    boolean isSuccess,
    JSONObject customData
  ) throws JSONException {

    if (customData != null) {
      return customData;
    }

    JSONObject data = new JSONObject();

    switch (responseType.toLowerCase()) {
      case "search_user_app":
        if (isSuccess) {
          data.put("packageNames", Arrays.asList(
            "com.whatsapp", "com.facebook.katana", "com.instagram.android"
          ));
          data.put("scores", Arrays.asList(0.95, 0.87, 0.82));
        }
        break;

      case "dashboard_info":
        if (isSuccess) {
          data.put("monthly_revenue", 2500.0);
          data.put("num_paid_users", 42);
          data.put("num_free_users", 158);
          data.put("num_logged_in_users", 23);
          data.put("system_ram", "8.2 GB");
          data.put("server_version", "1.2.3");
        }
        break;

      case "is_server_online":
        data.put("is_online", isSuccess);
        break;

      case "request_datetime":
        if (isSuccess) {
          data.put("year", "2025");
          data.put("month", "07");
          data.put("day", "05");
        }
        break;

      case "request_fake_resource":
        if (isSuccess) {
          data.put("result", "Get data success");
        }
        break;

      default:
        // Generic success data
        if (isSuccess) {
          data.put("status", "OK");
          data.put("timestamp", System.currentTimeMillis());
        }
        break;
    }

    return data;
  }

  /**
   * Determines if a response type should include a database result section.
   */
  private static boolean shouldIncludeDbResult(@NonNull String responseType) {
    return Arrays.asList(
      "sign_in", "sign_up", "validate_access_token",
      "obtain_new_access_token", "sign_in_access_token"
    ).contains(responseType.toLowerCase());
  }

  /**
   * Generates mock database result data for authentication-related responses.
   */
  @NonNull
  private static JSONObject generateMockDbResult(
    @NonNull String responseType,
    boolean isSuccess
  ) throws JSONException {

    JSONObject dbResult = new JSONObject();

    dbResult.put(ResponseKeys.IS_SUCCESS, isSuccess);

    if (isSuccess) {
      switch (responseType.toLowerCase()) {
        case "sign_in":
        case "sign_in_access_token":
          dbResult.put(ResponseKeys.ACCESS_TOKEN, "mock_access_token_" + System.currentTimeMillis());
          dbResult.put(ResponseKeys.REFRESH_TOKEN, "mock_refresh_token_" + System.currentTimeMillis());
          dbResult.put(ResponseKeys.MESSAGE, "Sign in successful");
          break;

        case "obtain_new_access_token":
          dbResult.put(ResponseKeys.ACCESS_TOKEN, "new_mock_access_token_" + System.currentTimeMillis());
          dbResult.put(ResponseKeys.REFRESH_TOKEN, "new_mock_refresh_token_" + System.currentTimeMillis());
          dbResult.put(ResponseKeys.MESSAGE, "Token refreshed successfully");
          break;

        case "validate_access_token":
          dbResult.put(ResponseKeys.MESSAGE, "Token is valid");
          break;

        default:
          dbResult.put(ResponseKeys.MESSAGE, "Operation completed successfully");
          break;
      }
    } else {
      // Error cases
      String errorMessage = "Operation failed";
      switch (responseType.toLowerCase()) {
        case "sign_in":
          errorMessage = "Invalid credentials";
          break;
        case "validate_access_token":
        case "obtain_new_access_token":
          errorMessage = ErrorMessages.TOKEN_EXPIRED;
          break;
      }
      dbResult.put(ResponseKeys.MESSAGE, errorMessage);
    }

    return dbResult;
  }

  // Convenience methods for common response types

  /**
   * Creates a mock search user app response with realistic app data.
   *
   * @param isSuccess Whether the search was successful
   * @param packageNames Optional list of package names (uses defaults if null)
   * @param scores Optional list of scores (uses defaults if null)
   * @return JSONObject representing a search response
   */
  @NonNull
  public static JSONObject createMockSearchUserAppResponse(
    boolean isSuccess,
    List<String> packageNames,
    List<Double> scores
  ) {
    JSONObject customData = null;
    if (isSuccess && (packageNames != null || scores != null)) {
      try {
        customData = new JSONObject();
        customData.put("packageNames", packageNames != null ? packageNames :
          Arrays.asList("com.whatsapp", "com.facebook.katana", "com.instagram.android"));
        customData.put("scores", scores != null ? scores :
          Arrays.asList(0.95, 0.87, 0.82));
      } catch (JSONException e) {
        throw new RuntimeException("Failed to create custom search data", e);
      }
    }
    return createMockApiResponse("search_user_app", isSuccess, customData, null, null);
  }

  /**
   * Creates a mock dashboard info response with system metrics.
   *
   * @param monthlyRevenue Monthly revenue amount
   * @param numPaidUsers Number of paid users
   * @param numFreeUsers Number of free users
   * @param numLoggedInUsers Number of currently logged in users
   * @param systemRam System RAM usage string
   * @param serverVersion Server version string
   * @return JSONObject representing a dashboard response
   */
  @NonNull
  public static JSONObject createMockDashboardInfoResponse(
    double monthlyRevenue,
    int numPaidUsers,
    int numFreeUsers,
    int numLoggedInUsers,
    String systemRam,
    String serverVersion
  ) {
    try {
      JSONObject customData = new JSONObject();
      customData.put("monthly_revenue", monthlyRevenue);
      customData.put("num_paid_users", numPaidUsers);
      customData.put("num_free_users", numFreeUsers);
      customData.put("num_logged_in_users", numLoggedInUsers);
      customData.put("system_ram", systemRam);
      customData.put("server_version", serverVersion);

      return createMockApiResponse("dashboard_info", true, customData,
        "Dashboard data retrieved successfully", 200);
    } catch (JSONException e) {
      throw new RuntimeException("Failed to create dashboard response", e);
    }
  }

  /**
   * Creates a mock error response with specific error details.
   *
   * @param responseType The type of operation that failed
   * @param errorMessage The error message to include
   * @param httpCode The HTTP error code (defaults to 400 if null)
   * @return JSONObject representing an error response
   */
  @NonNull
  public static JSONObject createMockErrorResponse(
    @NonNull String responseType,
    @NonNull String errorMessage,
    Integer httpCode
  ) {
    return createMockApiResponse(responseType, false, null, errorMessage,
      httpCode != null ? httpCode : 400);
  }

  @NonNull
  public static Intent createIntentWithSilentSignInMockData(MockWebServer mockWebServer) {
    Intent intent = new Intent(
      getApplicationContext(),
      MainActivity.class
    );

    // Pass mock url and mock encrypt/decrypt
    intent.putExtra(
      Constants.mockSignInUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_link).toString()
    );
    intent.putExtra(
      Constants.mockGetIsServerOnlineUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_get_is_server_online_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockValidateAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_validate_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockObtainNewAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_obtain_new_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockSignInAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockIsEncryptDecryptKey,
      true
    );

    // Set mode to production (which will enable silent login)
    intent.putExtra(
      Constants.mockDeploymentModeKey,
      Configuration.DeploymentMode.LOCAL.toString()
    );
    intent.putExtra(
      Constants.mockEnvModeKey,
      Configuration.EnvironmentMode.PROD.toString()
    );
    return intent;
  }

  /**
   * Example usage and testing method for the mock API response functions.
   * This method demonstrates various ways to use the createMockApiResponse function.
   *
   * @return JSONObject array containing example responses for testing
   */
  @NonNull
  public static JSONObject[] createExampleMockResponses() {
    try {
      // Example 1: Basic search response
      JSONObject searchResponse = createMockApiResponse(
        "search_user_app", true, null, null, null
      );

      // Example 2: Custom dashboard data
      JSONObject customDashboardData = new JSONObject();
      customDashboardData.put("monthly_revenue", 3500.0);
      customDashboardData.put("num_paid_users", 75);
      JSONObject dashboardResponse = createMockApiResponse(
        "dashboard_info", true, customDashboardData, "Custom dashboard loaded", 200
      );

      // Example 3: Error response
      JSONObject errorResponse = createMockErrorResponse(
        "sign_in", "Invalid email or password", 401
      );

      // Example 4: Using convenience method
      JSONObject searchConvenienceResponse = createMockSearchUserAppResponse(
        true,
        Arrays.asList("com.example.app1", "com.example.app2"),
        Arrays.asList(0.98, 0.85)
      );

      return new JSONObject[]{
        searchResponse,
        dashboardResponse,
        errorResponse,
        searchConvenienceResponse
      };

    } catch (JSONException e) {
      throw new RuntimeException("Failed to create example responses", e);
    }
  }


}
