<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    app:cardBackgroundColor="@android:color/transparent"
    card_view:cardElevation="0dp"
    android:id="@+id/card_view">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/id_app_icon"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:contentDescription="xx"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="10dp"
            android:layout_gravity="center"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/id_app_package_name"
            android:textSize="12sp"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end" />
    </LinearLayout>

</androidx.cardview.widget.CardView>