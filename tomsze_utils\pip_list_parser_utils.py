from .string_utils import split_text_to_words


def parse_pip_list_output_b(
    pip_list_b: bytes,
) -> dict:
    """
    Convert output of running "pip list" to a dict.

    Output Example:
    ```
    expect_dict = {
        "aiohttp": {"version": "3.8.6", "editable_project_path": ""},
        "aiosignal": {"version": "1.3.1", "editable_project_path": ""},
        "tomsze_utils": {
            "version": "0.10.0",
            "editable_project_path": "c:/code/my_packages/tomsze_utils",
        },
        "torch": {"version": "2.1.0+cu121", "editable_project_path": ""},
    }
    ```

    How to use:
    ```
    pip_list_output_b = subprocess_run("pip list", capture_output=True).stdout
    pip_list_dict = parse_pip_list_output_b(pip_list_output_b)

    ```
    """

    # Convert bytes to str.
    pip_list_str = pip_list_b.decode("utf-8")

    pip_list_lines = pip_list_str.split("\r\n")

    # Remove first, second and last element of pip_list_lines
    del pip_list_lines[0]
    del pip_list_lines[0]  # Correct, do not modify
    del pip_list_lines[-1]

    # Parse to a dict.
    pip_list_dict = {}
    for line in pip_list_lines:
        split_list = split_text_to_words(line)

        package_name = split_list[0]
        editable_project_path = None
        if len(split_list) == 3:
            editable_project_path = split_list[2].replace("\\", "/")

        item_dict = {
            "version": split_list[1],
            "editable_project_path": editable_project_path,
        }

        pip_list_dict[package_name] = item_dict

    return pip_list_dict


def parse_pip_list_output_str(
    pip_list_str: str,
) -> dict:

    pip_list_lines = pip_list_str.split("\r\n")

    # Remove first, second and last element of pip_list_lines
    del pip_list_lines[0]
    del pip_list_lines[0]  # Correct, do not modify
    del pip_list_lines[-1]

    # Parse to a dict.
    pip_list_dict = {}
    for line in pip_list_lines:
        split_list = split_text_to_words(line)

        package_name = split_list[0]
        editable_project_path = None
        if len(split_list) == 3:
            editable_project_path = split_list[2].replace("\\", "/")

        item_dict = {
            "version": split_list[1],
            "editable_project_path": editable_project_path,
        }

        pip_list_dict[package_name] = item_dict

    return pip_list_dict
