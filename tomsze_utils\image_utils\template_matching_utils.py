import sys
from typing import List, <PERSON>ple
import cv2
import numpy as np
import logging


def draw_match_result(
    image: np.ndar<PERSON>,
    template: np.ndarray,
    corner_loc: List[int],
    center_loc: Tuple[int, int],
    ouput_path: str,
) -> None:
    """
    Write an overlayed image to file.
    Input image will not be touched.

    loc is [x, y].
    Use min_loc for loc when using cv2.TM_CCOEFF_NORMED.

    Args:
        image (np.ndarray): The input image on which the overlay will be drawn.
        template (np.ndarray): The template image used for matching.
        corner_loc (List[int]): The top-left corner location of the matched template.
        center_loc (Tuple[int, int]): The center location of the matched template.
        ouput_path (str): The file path where the overlayed image will be saved.

    Examples:
    ```python
    draw_match_result(image=my_image, template=my_template, corner_loc=[100, 150], center_loc=(150, 200), ouput_path="output.jpg")
    ```

    ```python
    draw_match_result(image=image_array, template=template_array, corner_loc=[50, 75], center_loc=(75, 100), ouput_path="result.png")
    ```
    """

    image_overlay = image.copy()
    (templ_height, templ_width) = template.shape[:2]
    x = corner_loc[0]
    y = corner_loc[1]

    cv2.rectangle(
        image_overlay,
        (x - 1, y - 1),
        (x + templ_width, y + templ_height),
        (255, 0, 0),
        thickness=1,
    )

    cv2.circle(image_overlay, center_loc, radius=1, color=(255, 0, 0), thickness=1)

    cv2.imwrite(ouput_path, image_overlay)


def use_template_match(
    image: np.ndarray,
    template: np.ndarray,
) -> Tuple[List[int], Tuple[int, int]]:
    """
    Run opencv matchTemplate function using method cv2.TM_CCOEFF_NORMED.
    Return the best matched corner location and the center location.

    Args:
        image (np.ndarray): The input image in which to find the template.
        template (np.ndarray): The template image to match against the input image.

    Returns:
        Tuple[List[int], Tuple[int, int]]: The corner location of the best match and the center location of the matched template.

    Examples:
    ```python
    corner, center = use_template_match(image=my_image, template=my_template)
    ```

    ```python
    corner, center = use_template_match(image=image_array, template=template_array)
    ```
    """
    corr_map = cv2.matchTemplate(
        image=image,
        templ=template,
        method=cv2.TM_CCOEFF_NORMED,
    )

    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(corr_map)

    corner_loc = max_loc
    (templ_height, templ_width) = template.shape[:2]
    center_loc = (max_loc[0] + templ_width // 2, max_loc[1] + templ_height // 2)
    return corner_loc, center_loc


def use_sift_feature_flann_match(
    image: np.ndarray,
    template: np.ndarray,
) -> None:
    """
    FLANN based Matcher from https://datahacker.rs/feature-matching-methods-comparison-in-opencv/

    This function computes keypoints and descriptors using SIFT, matches them using FLANN,
    and draws the matches on the image.

    Args:
        image (np.ndarray): The input image in which to find the template.
        template (np.ndarray): The template image to match against the input image.

    Examples:
    ```python
    use_sift_feature_flann_match(image=my_image, template=my_template)
    ```

    ```python
    use_sift_feature_flann_match(image=image_array, template=template_array)
    ```
    """

    # Compute keypoints and descriptors usign sift.
    sift = cv2.xfeatures2d.SIFT_create()
    keypoints_template, descriptors_template = sift.detectAndCompute(template, None)
    keypoints_image, descriptors_image = sift.detectAndCompute(image, None)

    # Match descriptor.
    FLAN_INDEX_KDTREE = 0
    index_params = dict(algorithm=FLAN_INDEX_KDTREE, trees=5)
    search_params = dict(checks=50)
    flann = cv2.FlannBasedMatcher(index_params, search_params)
    matches = flann.knnMatch(
        descriptors_template, descriptors_image, k=2
    )  # if k=3 we would have the third-best match and so on.

    # David Lowe’s ratio test. There needs to be enough difference between the first best match and the second-best matches
    matchesMask = [[0, 0] for i in range(len(matches))]
    for i, (m1, m2) in enumerate(matches):
        if m1.distance < 0.5 * m2.distance:
            matchesMask[i] = [1, 0]

    # Draw match to image file.
    draw_params = dict(
        matchColor=(0, 0, 255),
        singlePointColor=(0, 255, 0),
        matchesMask=matchesMask,
        flags=0,
    )
    image_drawn_matches = cv2.drawMatchesKnn(
        template,
        keypoints_template,
        image,
        keypoints_image,
        matches,
        None,
        **draw_params
    )

    ouput_path = "./use_feature_match.png"
    cv2.imwrite(ouput_path, image_drawn_matches)
