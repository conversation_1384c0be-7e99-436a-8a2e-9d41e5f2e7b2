"""App plugin"""

from dataclasses import dataclass
import json
import os
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginDictToJsonWriter:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        dict_to_write = parse_data_and_store(
            logger,
            "dict_to_write",
            data_obj,
            step_config,
            config,
            type="dict",
            default={},
        )

        output_folder_path = parse_data_and_store(
            logger,
            "output_folder_path",
            data_obj,
            step_config,
            config,
            type="str",
        )

        json_file_name = parse_data_and_store(
            logger,
            "json_file_name",
            data_obj,
            step_config,
            config,
            type="str",
        )

        path_buffer_to = parse_data_and_store(
            logger,
            "path_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
        )

        assert isinstance(dict_to_write, dict)

        output_json_path = os.path.join(output_folder_path, json_file_name)
        if not output_json_path.endswith(".json"):
            output_json_path += ".json"

        os.makedirs(output_folder_path, exist_ok=True)

        with open(output_json_path, "w") as outfile:
            json.dump(dict_to_write, outfile, indent=4)

        data_obj.dict_var[f"{current_step}.{path_buffer_to}"] = output_json_path

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
