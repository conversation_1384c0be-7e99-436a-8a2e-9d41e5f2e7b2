# Python Interpreter
Use the local env in tomsze_utils

# Base structure overview in plantuml (puml)
open plantuml_code_current.puml in vscode, then run the following commands: ctrl p puml preview

# To run tests
bash ./tests/test.sh

# To benchmark some very basic functions
# in ./benchmark/basic/benchmark_basic_functions.py
python -m benchmark.basic.benchmark_basic_functions
# To benchmark api functions
bash ./benchmark/api/api.sh

# To start server locally
docker-compose up --build --force-recreate -d

# To create and update requirement.txt and local wheels
python create_update_requirements.py
# To deploy server code and start container
python deploy_and_start_container.py
# To deploy server code and database and start container
python deploy_with_db_and_start_container.py
# To start container
python start_container.py
# To backup database from server to local machine
python backup_db_from_server.py

# To profile server
bash ./benchmark/end_to_end/end_to_end.sh
NOTE: line profiler seems have bug in HITS!

# Where is the database located
./server/api/pickle_db/android/db_app

# Where to see the database main structure
my_business_search_phone_app_pickle_database_main_structure.drawio

# Tools
## To see db_apps structure
run_see_db_apps_structure.py
## To scrape play store
run_scrape_play_store.py
## To lemmatize words from nltk
run_lemmatize_nltk_words.py
## To check scraping ant api key status
get_scraping_ant_status.py
## To scrap powertheasaurus to a pickle using threads
get_synonyms.py's run_get_all_synonyms_with_scraping_ant_with_threads()
## (Regularly run) To create/update a db synonyms by scraping powertheasaurus using nltk words on the base of a db nltk synonyms
run_update_db_synonyms_with_source_powertheasaurus_using_nltk_words.py
## (Regularly run) To backup database to another folder
backup_database.py
## (Regularly run) To fill in new apps (from a txt) to db_apps
fill_new_apps.py
## (Regularly run) To fill in new apps (from a db_to_add) to db_apps
fill_new_apps_with_db_to_add.py
## Fix database each loaded_part is a list problem
fix_database_loaded_part_is_list.py
## Rebuild db_apps (when there is a need to add new item)
rebuild_db_apps.py
## To find alternative to playtore for app description
find_alternative_to_playstore.py
## To download spacy model
python -m spacy download en_core_web_trf
## To trial run api endpoints
python -m server.tools.trial_run_api_endpoints
## To create user data db
python -m server.tools.create_insert_user_data_db
## To create rate limit db
python -m server.tools.create_insert_rate_limit_data_db
## To create block ip db
python -m server.tools.create_insert_block_ip_data_db
## To scan server logs for frequent ip
python -m server.tools.scan_server_log_for_frequent_ip
## To backup secrets
python -m server.tools.backup_server_secrets

## To shutdown server
python shutdown.py