{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "remove_file",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "remove_file",
            "type": "PluginFilesRemover",
            "use": true,
            "file_path_list": [
                './tests/test_file1.txt',
                './tests/test_file2.txt',
            ],
        }
    ]
   

}
