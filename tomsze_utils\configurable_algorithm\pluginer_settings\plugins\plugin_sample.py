"""App plugin"""

import os
from dataclasses import dataclass
from typing import List, Dict
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.dict_utils import extract_data_from_dict_contains
import logging


@dataclass
class PluginSample:
    type: str
    plugin_can_change_files: bool

    def is_wanted_file_change(self, dict_app_data):
        pass

    def do_something(self, dict_app_data: Dict, plugin_unique_key: str) -> None:
        pass


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
