@startuml fuzzy_search_utils_docs
skinparam ranksep 0
allowmixing

' ---------reference from flutter -------------------------

class DatabaseNotifier {
    extractCompleteWordFromPartWord()
    filterSearchResults()
    getListListHighlightedWords()
    calculateOrderScores()
    sortSearchResults()
}

note left of DatabaseNotifier::extractCompleteWordFromPartWord() 
     /// Extract a complete word from a List of word [listWords]
  /// using a part of a word [searchPartWord].
  ///
  /// Example:
  /// listWords = [
  /// 'today',
  ///  'tue',
  ///  'tu',
  /// ]
  /// and searchPartWord = 'tu'
  /// , it returns 'tue'
end note

note left of DatabaseNotifier::getListListHighlightedWords()
   used in calculateOrderScores()
end note

note left of DatabaseNotifier::calculateOrderScores()
   used in sortSearchResults()
end note

note right of DatabaseNotifier
{{
    :var (listFoundTitles, listFoundContents) = await ref
        .read(databaseSNProvider.notifier)
        .searchIsarDatabase(searchString, searchScope);
    :Map<String, List<int>> mapFoundTitleHightlightIndex = {};
    :(listFoundTitles, listFoundContents, mapFoundTitleHightlightIndex) =
        ref.read(databaseSNProvider.notifier).filterSearchResults(
              listFoundTitles,
              listFoundContents,
              searchString,
              (searchScope == SearchScope.titleAndContent) ? true : false,
              false,
            );
    :ref
        .read(funcListBoxSNProvider.notifier)
        .setmapFoundTitleHightlightIndex(mapFoundTitleHightlightIndex);
    :listFoundTitles =
        await ref.read(databaseSNProvider.notifier).sortSearchResults(
              listFoundTitles,
              mapFoundTitleHightlightIndex,
              funcSearchFieldSNProvider,
            );
}}
end note
' ----------------------------------

class FuzzySearcher {
    matched_ratio_list = []
    
    __init__()
    fuzzy_search(search_text, text_list)
}


note left of FuzzySearcher::fuzzy_search 
    What search result should rank higher?
    1. The ratio of matched characters
    
    
end note

note right of FuzzySearcher::fuzzy_search 
{{
    :Get the number of matched characters and \n the ratio for each text in list;
}}
end note


@enduml


