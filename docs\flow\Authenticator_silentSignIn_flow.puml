@startuml Authenticator_silentSignIn_flow

legend top
  |Class     | Authenticator  |
  |Method    | silentSignIn  |
end legend

start

if (self hasAccountStored?) is (no) then
    :No account stored -> return;
    stop
else
    :Get account info from SecurePrefsManager;
    if (accessToken or refreshToken missing?) is (yes) then
        :callback.onPostError;
        stop
    else
        :self validateAccessToken;
        if (Token Valid?) is (yes) then
            :self signInUsingAccessToken();
            :callback.onPostSuccess();
        else
            :self obtainNewAccessTokenAndRefreshToken();
            :self signInUsingAccessToken(newToken);
            :callback.onPostSuccess();
        endif
    endif
endif

end
@enduml