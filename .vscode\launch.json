{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/"
            }
        },
        {
            "name": "Debug fastapi",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,

            "module": "uvicorn",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/"
            },
            "args": [
                "server.api.fastapi_app.main:app",
                "--reload",
                "--host",
                "**************",
                "--port",
                "8000",
                "--workers",
                "4"
            ]
        }

    ]
}