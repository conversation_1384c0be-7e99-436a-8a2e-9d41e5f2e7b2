import sys
import os
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit

# Create db object using PickleDatabaseSplit (will also load pickle file)
script_directory_path = os.path.dirname(
    os.path.abspath(sys.argv[0]),
)

db = PickleDatabaseSplit(
    db_fpath=script_directory_path,
    db_name="apps",
    part_data_count=1,  # <<<<< for importing to mongodb
)

# Split
db.re_split()

# Save to mongodb
db.to_mongodb(
    mongo_db_name="test_database",
    mongo_coll_name="coll1",
)
