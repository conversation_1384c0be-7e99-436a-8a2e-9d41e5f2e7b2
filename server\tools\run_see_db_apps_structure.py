import datetime
import shutil
import sys
import os
from const_path import proj_ser_tools_path, proj_ser_api_db_apps_path
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
    create_db_info,
)
from server.api.end_to_end.api_constant_keys import DBAppsColEnum
from server.api.end_to_end.utils import ppd_emb_kw_kwpp_update_using_keys

from server.api.utils.descriptions.get_description import scrape_play_store
from sentence_transformers import SentenceTransformer

"""
To fill in new apps (from a txt) to db_apps
"""
max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20


def see_db_apps_structure(
    db_apps_folder_path,
    model_id: str = "multi-qa-MiniLM-L6-cos-v1",
):
    embedder = SentenceTransformer(model_id)

    # Load the db_apps from folder
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )

    print(db_apps.loaded_parts[0])


def run_see_db_apps_structure():
    db_apps_folder_path = proj_ser_api_db_apps_path

    see_db_apps_structure(
        db_apps_folder_path=db_apps_folder_path,
    )


def main():
    # Run function
    run_see_db_apps_structure()


if __name__ == "__main__":
    sys.exit(main())
