<resources>
    <string name="app_name">WhatAppsDo</string>

    <!--navigation_drawer-->
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="nav_header_title">User xxx</string>
    <string name="nav_header_is_user_registered">User Not Registered</string>
    <string name="nav_header_desc">Navigation header</string>
    <string name="nav_header_used_search_times">Used search %d times (%d)</string>
    <string name="nav_header_app_version">%s (%s)</string>
    <string name="action_settings">Settings</string>

    <string name="menu_home_name">Home</string>
    <string name="menu_gallery_name">Gallery</string>
    <string name="menu_sign_up_name">Sign-up</string>
    <string name="menu_login_name">Login</string>

    <!--Search field-->
    <string name="search_field_hint">Input search text</string>

    <!--Buttons-->
    <string name="search_button_name">Search</string>
    <string name="local_search_button_name">Local Search</string>
    <string name="sign_up_button_text">SIGN UP</string>
    <string name="login_button_small_text">LOGIN</string>
    <string name="sign_up_large_button_text">Sign up</string>
    <string name="login_button_large_text">Login</string>
    <string name="iconCacheExt">png</string>
    <string name="get_verification_code_button_text">GET</string>


    <!--EditText-->
    <string name="email_address_editText_text">Email Address</string>
    <string name="password_editText_text">Password</string>
    <string name="confirm_password_editText_text">Confirm Password</string>
    <string name="verification_code_editText_text">Verification Code</string>


    <!--TextView-->
    <string name="sign_up_hint_default_text">Default hint</string>
    <string name="login_hint_default_text">Default hint</string>
    <string name="user_registered">User Registered</string>
    <string name="user_not_registered">User Not Registered</string>

    <!--AlertDialog-->
    <string name="long_time_not_login_title">Re-login</string>
    <string name="long_time_not_login_text">It has been too long without using the app. Please login again.</string>
    <string name="ok_text">OK</string>


</resources>