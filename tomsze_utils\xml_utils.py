from typing import Any, List, Union
import xmltodict
from tomsze_utils.dict_utils import set_nested_dict_value_by_key_list
import re


def parse_xml_string_to_dict(xml_string: str) -> dict:
    """
    Parses an XML string and converts it to a dictionary.

    Args:
        xml_string (str): The XML string to be parsed.

    Returns:
        dict: A dictionary representation of the XML.

    Examples:
        ```python
        xml_string = "<root><item>Value1</item><item>Value2</item></root>"
        result = parse_xml_string_to_dict(xml_string)
        # result will be {'root': {'item': ['Value1', 'Value2']}}
        ```

        ```python
        xml_string = "<root><item>Value1</item></root>"
        result = parse_xml_string_to_dict(xml_string)
        # result will be {'root': {'item': 'Value1'}}
        ```

    Note:
        The function uses xmltodict to perform the conversion.
    """
    return xmltodict.parse(xml_string)


def parse_xml_file_to_dict(file_path: str) -> dict:
    """
    Parses an XML file and converts it to a dictionary using parse_xml_string_to_dict.

    Args:
        file_path (str): The path to the XML file to be parsed.

    Returns:
        dict: A dictionary representation of the XML file.

    Examples:
        ```python
        file_path = "example.xml"
        result = parse_xml_file_to_dict(file_path)
        # result will be a dictionary representation of the XML content.
        ```
    """
    with open(file_path, "r") as file:
        xml_string = file.read()
    return parse_xml_string_to_dict(xml_string)


def modify_xml_file_by_key_list(
    file_path: str,
    key_list: List[tuple],
    new_value: Any,
    pretty: bool = True,
    full_document: bool = False,
) -> None:
    """
    Modifies an XML file based on a list of key-value pairs.

    Args:
        file_path (str): The path to the XML file to be modified.
        modifications (List[tuple]): A list of tuples where each tuple contains a key and its new value.
        new_value (Any): The new value to assign to the specified keys.
        pretty (bool): Whether to format the output XML string for readability.
        full_document (bool): Whether to include the XML declaration in the output.

    Examples:
        ```python
        modifications = [('item', 'NewValue')]
        modify_xml_file_by_key_list(file_path='example.xml', modifications=modifications, new_value='NewValue')
        ```

        ```python
        modifications = [('root', 'NewRootValue')]
        modify_xml_file_by_key_list(file_path='example.xml', modifications=modifications, new_value='NewRootValue')
        ```

    Note:
        This function assumes that the keys in the modifications list exist in the XML structure.
    """
    with open(file_path, "r") as file:
        xml_string = file.read()

    xml_dict = parse_xml_string_to_dict(xml_string)

    xml_dict = set_nested_dict_value_by_key_list(
        base_dict=xml_dict,
        keys=key_list,
        value=new_value,
        keys_must_exist=True,
    )

    new_xml_string = xmltodict.unparse(
        xml_dict,
        pretty=pretty,
        full_document=full_document,
    )

    with open(file_path, "w") as file:
        file.write(new_xml_string)


def find_text_tag_indices(xml_content, target_text):
    """
    Searches xml_content for all <text>...</text> tags whose inner content matches target_text.
    Returns a list of tuples (start_index, end_index) representing the locations of the matching tags.
    If not found, returns an empty list.
    """
    pattern = re.compile(r"<text\b[^>]*>(.*?)</text>", re.DOTALL)
    matches = []
    for match in pattern.finditer(xml_content):
        inner_text = match.group(1).strip()
        if inner_text == target_text:
            matches.append((match.start(), match.end()))
    return matches


def find_previous_tag_indices(xml_text: str, index_tuples: List[tuple]) -> List[int]:
    """
    For each (start, end) tuple in index_tuples, finds the XML tag that occurs immediately before the given start index.

    Parameters:
        xml_text (str): The full XML text.
        index_tuples (List[tuple]): List of (start, end) index tuples.

    Returns:
        List[int]: List of starting indices of the tag (i.e. the index of '<')
                    immediately preceding each start index. If no such tag is found, -1 is returned for that tuple.
    """
    tag_pattern = re.compile(
        r"(<!--.*?-->|<\?.*?\?>|</?[a-zA-Z0-9:_-]+(?:\s+[^>]*?)?/?>)", re.DOTALL
    )

    # Precompute all tag start indices
    tag_starts = [match.start() for match in tag_pattern.finditer(xml_text)]

    result = []
    for start, _ in index_tuples:
        prev_tag_index = -1
        for tag_start in tag_starts:
            if tag_start < start:
                prev_tag_index = tag_start
            else:
                break
        result.append(prev_tag_index)
    return result


# def find_next_tag_text(xml_text: str, index_tuples: List[tuple]) -> List[int]:
#     """
#     For each (start, end) tuple in index_tuples, finds the XML text tag's text that occurs
#     immediately after the given index_tuples.

#     Parameters:
#         xml_text (str): The full XML text.
#         index_tuples (List[tuple]): List of (start, end) index tuples.

#     Returns:
#         str: The text of the text tag
#                 immediately after each end index. If no such tag is found, -1 is returned for that tuple.
#     """


def find_next_tag_text(
    xml_text: str, index_tuples: List[tuple]
) -> List[Union[str, int]]:
    """
    For each (start, end) tuple in index_tuples, finds the XML tag's text that occurs
    immediately after the given end index.

    Parameters:
        xml_text (str): The full XML text.
        index_tuples (List[tuple]): List of (start, end) index tuples.

    Returns:
        List[Union[str, int]]: For each tuple,
            - the inner text of the first well‐formed tag found AFTER tuple[1],
            - or -1 if no such tag can be located.
    """
    results: List[Union[str, int]] = []

    for start_idx, end_idx in index_tuples:
        pos = end_idx
        found = False

        while True:
            # 1) find the next '<'
            open_start = xml_text.find("<", pos)
            if open_start == -1:
                break

            # 2) find its closing '>'
            open_end = xml_text.find(">", open_start + 1)
            if open_end == -1:
                break

            # 3) skip closing tags, comments, processing instructions, CDATA, etc.
            buff = xml_text[open_start + 1 : open_end].strip()
            if (
                buff.startswith("/")  # </tag>
                or buff.startswith("?")  # <?xml ... ?>
                or buff.startswith("!")
            ):  # <!-- or <!DOCTYPE or <![CDATA[
                pos = open_end + 1
                continue

            # 4) extract tag name (stop at whitespace or '/')
            tag_name = buff.split()[0].rstrip("/")
            close_tag = f"</{tag_name}>"

            # 5) find the matching closing tag
            close_index = xml_text.find(close_tag, open_end + 1)
            if close_index == -1:
                # no closing: skip and keep scanning
                pos = open_end + 1
                continue

            # 6) grab inner text
            inner = xml_text[open_end + 1 : close_index]
            results.append(inner)
            found = True
            break

        if not found:
            results.append(-1)

    return results
