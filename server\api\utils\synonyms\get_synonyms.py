import collections
import http.client
import os
import pickle
import re
import threading
import time
from bs4 import BeautifulSoup
from tqdm import tqdm
import concurrent.futures
from multiprocessing.pool import ThreadPool
from tomsze_utils.variable_dump_read_utils import (
    dump_variable_to_pickle,
    load_pickle_file,
)
from tomsze_utils.scraping_utils import calculate_suitable_api_key
from tomsze_utils.list_utils import duplicate_list_limited
from api_keys import dict_account_apikey


def timeit(method):
    def timed(*args, **kw):
        ts = time.time()
        result = method(*args, **kw)
        te = time.time()
        if "log_time" in kw:
            name = kw.get("log_name", method.__name__.upper())
            kw["log_time"][name] = int((te - ts) * 1000)
        else:
            print("%r  %2.2f ms" % (method.__name__, (te - ts) * 1000))
        return result

    return timed


def is_ascii(s):
    """
    Check if the string contains only ASCII characters in the range -128 to 127
    """
    return all(ord(char) < 128 for char in s)


def get_one_synonyms_with_scraping_ant(
    conn,
    api_key,
    word_underscore,
):
    list_synonym = []

    css_selector = "div#primary-area div a"

    # Connect to ScrapingAnt for proxy service.
    # conn = http.client.HTTPSConnection("api.scrapingant.com")

    # Request to www.powerthesaurus.org.
    url = f"/v2/general?url=https%3A%2F%2Fwww.powerthesaurus.org%2F{word_underscore}%2Fsynonyms&x-api-key={api_key}&browser=false"
    print(f'Requesting synonyms for the word "{word_underscore}" using url "{url}"./n')
    conn.request(
        "GET",
        #  f"/v2/general?url=https%3A%2F%2Fwww.powerthesaurus.org%2F{word_underscore}%2Fsynonyms&x-api-key=41d38965051147718c4fcc505b1379b2&browser=false")    # <EMAIL>
        #  f"/v2/general?url=https%3A%2F%2Fwww.powerthesaurus.org%2F{word_underscore}%2Fsynonyms&x-api-key=********************************&browser=false")    # <EMAIL>
        #  f"/v2/general?url=https%3A%2F%2Fwww.powerthesaurus.org%2F{word_underscore}%2Fsynonyms&x-api-key={api_key}&browser=false"
        url,
    )

    res = conn.getresponse()  # Might give Remote end closed connection without response
    data = res.read()
    dom = data.decode("utf-8")
    status_code = res.status

    if status_code != 200:
        print(f"status code {res.status}")
        print(f"{dom}")

    # Extract synonyms from dom.
    if status_code == 200:
        soup = BeautifulSoup(dom, "html.parser")
        list_match = soup.select(css_selector)
        for match in list_match:
            list_synonym.append(match.text)

    return status_code, list_synonym


def get_all_synonyms_with_scraping_ant(
    conn,
    api_key,
    list_word_underscore,
    synonym_pickle_path,
    no_synonym_pickle_path,
    dict_synonyms,
    dict_no_synonyms,
):
    status_code = 200

    for word_underscore in list_word_underscore:
        # Skip if [word_underscore] is already in [dict_synonyms].
        if word_underscore in dict_synonyms:
            print(
                f'Word(underscore) "{word_underscore}" already in dict_synonyms, skip.'
            )
            continue

        # Skip if [word_underscore] is already in [dict_no_synonyms].
        if word_underscore in dict_no_synonyms:
            print(
                f'Word(underscore) "{word_underscore}" already in dict_no_synonyms, skip.'
            )
            continue

        # Skip if [word_underscore] is not ascii.
        if not is_ascii(word_underscore):
            print(f'Word(underscore) "{word_underscore}" is not ascii, skip.')
            continue

        # Get synonyms.
        _status_code, list_synonym = get_one_synonyms_with_scraping_ant(
            conn, api_key, word_underscore
        )

        # Skip saving to pickle if [_status_code] is not 200.
        if _status_code != 200:
            status_code = _status_code
            continue

        # Save to pickle.
        if len(list_synonym) > 0:
            print(
                f'Fecthed {len(list_synonym)} result. Save the keyword "{word_underscore}" to synonym dict pickle file.'
            )
            dict_synonyms[word_underscore] = list_synonym
            dump_variable_to_pickle(
                pickle_path=synonym_pickle_path, variable=dict_synonyms
            )
        else:
            print(
                f'Fecthed 0 result. Save the keyword "{word_underscore}" to NO synonym dict pickle file.'
            )
            dict_no_synonyms[word_underscore] = 1
            dump_variable_to_pickle(
                pickle_path=no_synonym_pickle_path, variable=dict_no_synonyms
            )

    conn.close()

    return status_code


def test_get_one_synonyms_with_scraping_ant():
    conn = http.client.HTTPSConnection("api.scrapingant.com")
    api_key = "********************************"
    word_underscore = "hi"

    status_code, list_synonym = get_one_synonyms_with_scraping_ant(
        conn,
        api_key,
        word_underscore,
    )
    assert status_code == 200
    assert len(list_synonym) > 0

    print("test_get_one_synonyms_with_scraping_ant done")


def test_get_all_synonyms_with_scraping_ant():
    conn = http.client.HTTPSConnection("api.scrapingant.com")
    api_key = "********************************"
    list_word_underscore = [
        "hi",
        "bye",
        "QSXEDC",
        "galavisión",  # not ascii, will not add to dict_no_synonym
    ]
    synonym_pickle_path = r"./test_get_all_synonyms_with_scraping_ant_synonyms.pickle"
    no_synonym_pickle_path = (
        r"./test_get_all_synonyms_with_scraping_ant_no_synonyms.pickle"
    )
    dict_synonyms = {}
    dict_no_synonyms = {}

    # Remove the pickle file first.
    if os.path.exists(synonym_pickle_path):
        os.remove(synonym_pickle_path)
    if os.path.exists(no_synonym_pickle_path):
        os.remove(no_synonym_pickle_path)

    status_code = get_all_synonyms_with_scraping_ant(
        conn,
        api_key,
        list_word_underscore,
        synonym_pickle_path,
        no_synonym_pickle_path,
        dict_synonyms,
        dict_no_synonyms,
    )

    conn.close()

    assert status_code == 200
    assert os.path.exists(synonym_pickle_path)
    assert os.path.exists(no_synonym_pickle_path)

    dict_synonyms.clear()
    dict_no_synonyms.clear()
    dict_synonyms = load_pickle_file(synonym_pickle_path, {})
    dict_no_synonyms = load_pickle_file(no_synonym_pickle_path, {})

    assert len(dict_synonyms) == 2
    assert len(dict_no_synonyms) == 1

    print("test_get_all_synonyms_with_scraping_ant done")


def run_get_all_synonyms_with_scraping_ant():
    conn = http.client.HTTPSConnection("api.scrapingant.com")
    api_key = "********************************"
    synonym_site = "powerthesaurus"

    synonym_pickle_path = (
        f"./api/pythonanywhere/my_site/dataset/android/synonyms_{synonym_site}.pickle"
    )
    no_synonym_pickle_path = f"./api/pythonanywhere/my_site/dataset/android/no_synonyms_{synonym_site}.pickle"
    keyword_pickle_path = (
        r"./api/pythonanywhere/my_site/dataset/android/004_keywords.pickle"
    )
    dict_synonyms = {}
    dict_no_synonyms = {}
    dict_id_keywords = {}

    # Read the pickle file first.
    dict_synonyms = load_pickle_file(synonym_pickle_path, {})
    dict_no_synonyms = load_pickle_file(no_synonym_pickle_path, {})
    dict_id_keywords = load_pickle_file(keyword_pickle_path, {})

    status_code = 200
    for app_id in tqdm(dict_id_keywords.keys()):
        # Get list of keywords from [dict_id_keywords].
        list_word_underscore = dict_id_keywords[app_id]

        _status_code = get_all_synonyms_with_scraping_ant(
            conn,
            api_key,
            list_word_underscore,
            synonym_pickle_path,
            no_synonym_pickle_path,
            dict_synonyms,
            dict_no_synonyms,
        )

        if _status_code != 200:
            status_code = _status_code
            print(f"status_code {status_code}")
            break

    # Close the connection explicitly
    conn.close()

    return status_code


def clean_dict_no_synonym(conn, api_key, no_synonym_pickle_path):
    # Read the pickle file.
    dict_no_synonym = load_pickle_file(no_synonym_pickle_path, {})

    status_code = 200
    list_word_underscore = list(dict_no_synonym.keys())
    for word_underscore in tqdm(list_word_underscore):
        remove = False

        # Check if [word_underscore] is ascii, remove.
        if not is_ascii(word_underscore):
            print(f'"{word_underscore}" is not ascii, remove it from dict_no_synonym.')
            remove = True

        # Request synonyms.
        if not remove:
            _status_code, list_synonym = get_one_synonyms_with_scraping_ant(
                conn,
                api_key,
                word_underscore,
            )

        if _status_code != 200:
            status_code = _status_code
            continue

        # Check requested result. If not empty, remove it from [dict_no_synonym].
        if len(list_synonym) > 0:
            print(
                f'Requested {len(list_synonym)} for "{word_underscore}", remove it from dict_no_synonym.'
            )
            remove = True

        # Remove [word_underscore] from [dict_no_synonym].
        if remove:
            del dict_no_synonym[word_underscore]
            dump_variable_to_pickle(
                pickle_path=no_synonym_pickle_path, variable=dict_no_synonym
            )

    return status_code


def test_clean_dict_no_synonym():
    conn = http.client.HTTPSConnection("api.scrapingant.com")
    api_key = "********************************"

    # Create test pickle file.
    no_synonym_pickle_path = f"./utils/synonyms/test_clean_dict_no_synonym.pickle"
    dict_no_synonym = {
        "hi": 1,
        "QWDZDCF": 1,
        "street": 1,
        "galavisión": 1,
    }
    dump_variable_to_pickle(
        pickle_path=no_synonym_pickle_path, variable=dict_no_synonym
    )

    # Clean the pickle file.
    clean_dict_no_synonym(conn, api_key, no_synonym_pickle_path)
    conn.close()

    # Read the updated pickle file.
    dict_no_synonym_updated = load_pickle_file(no_synonym_pickle_path, {})

    assert "hi" not in dict_no_synonym_updated
    assert "street" not in dict_no_synonym_updated
    assert "galavisión" not in dict_no_synonym_updated

    print("test_clean_dict_no_synonym done")


def run_clean_dict_no_synonym():
    conn = http.client.HTTPSConnection("api.scrapingant.com")
    api_key = "********************************"
    synonym_site = "powerthesaurus"

    no_synonym_pickle_path = f"./api/pythonanywhere/my_site/dataset/android/no_synonyms_{synonym_site}.pickle"

    # Clean the pickle file.
    status_code = clean_dict_no_synonym(conn, api_key, no_synonym_pickle_path)
    conn.close()

    assert status_code == 200


def my_thread_funciton(number):
    time.sleep(10)
    print(f"my_thread_funciton {number}")


def my_thread_funciton_v2(number1, number2):
    time.sleep(2)
    print(f"my_thread_funciton {number1} {number2}")


def thread_dump_pickle(number):
    time.sleep(10)
    print(f"my_thread_funciton {number}")


def threads_dump_pickle():
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        executor.submit(my_thread_funciton, 1)
        executor.submit(my_thread_funciton, 2)
        executor.submit(my_thread_funciton, 3)
        executor.submit(my_thread_funciton, 4)


# at the end of with block, ThreadPoolExecutor will do a join() on each of the threads in the pool.

dump_lock = threading.Lock()


def thread_func_simple_dump_pickle(pickle_path, num):
    print(f"thread {num}, {pickle_path} processing/n")

    with dump_lock:  # Use lock to Prevent race condition and pickle.load error
        dict_s = load_pickle_file(pickle_path, {})
        if num not in dict_s:
            dict_s[num] = ""

        dump_variable_to_pickle(pickle_path=pickle_path, variable=dict_s)

    print(f"thread {num}, {pickle_path} done/n")


def threads_simple_dump_pickle_v2(
    pickle_path,
    list_data,
    threads=2,
):
    list_pickle_path = duplicate_list_limited([pickle_path], len(list_data))
    list_args = list(zip(list_pickle_path, list_data))
    ThreadPool(threads).starmap(thread_func_simple_dump_pickle, list_args)

    print("threads_dump_pickle_v2 done")


def test_threads_dump_pickle():
    threads_dump_pickle()


def test_threads_simple_dump_pickle_v2():
    pickle_path = r"./utils/synonyms/test_threads_dump_pickle_v2.pickle"
    list_data = [1, 2, 3, 4, 5]
    threads_simple_dump_pickle_v2(
        pickle_path,
        list_data,
    )


def thread_get_one_synonyms_with_scraping_ant(
    api_key_lock,
    conn,
    api_key,
    word_underscore,
):
    with api_key_lock:
        status_code, list_synonym = get_one_synonyms_with_scraping_ant(
            conn,
            api_key,
            word_underscore,
        )

    print(
        f'done thread "{word_underscore}" api_key {api_key} fetched {len(list_synonym)} synonyms'
    )

    return status_code, word_underscore, list_synonym


# def thread_get_all_synonyms_with_scraping_ant(
#     api_key_lock,
#     # dump_lock,
#     conn,
#     api_key,
#     word_underscore,
#     # synonym_pickle_path,
#     # no_synonym_pickle_path,
#     # dict_synonyms,
#     # dict_no_synonyms
# ):
#     status_code = 200


#     # Get synonyms.
#     with api_key_lock:
#         _status_code, list_synonym = get_one_synonyms_with_scraping_ant(
#             conn,
#             api_key,
#             word_underscore
#         )

#     if _status_code != 200:
#         status_code = _status_code

#     return status_code, list_synonym


def test_thread_request_synonym():
    list_keyword = [
        "hi",
        "good",
        "shopping",
        "bye",
    ]
    list_api_key = [
        "********************************",
        "f74d656b70784fa5bfab393eac2138b1",
    ]

    threads = len(list_api_key)  # Num of threads must follow num of api_key.

    # Initiate different conn for each api key to avoid Request-sent error.
    list_conn = [
        http.client.HTTPSConnection("api.scrapingant.com")
        for i in range(len(list_api_key))
    ]

    # Initiate different lock for each api key to avoid race condition on using api_key.
    list_lock = [threading.Lock() for i in range(len(list_api_key))]

    # Start threads.
    list_api_key_duplicated = duplicate_list_limited(list_api_key, len(list_keyword))
    list_conn_duplicated = duplicate_list_limited(list_conn, len(list_keyword))
    list_lock_duplicated = duplicate_list_limited(list_lock, len(list_keyword))

    list_args = list(
        zip(
            list_lock_duplicated,
            list_conn_duplicated,
            list_api_key_duplicated,
            list_keyword,
        )
    )

    list_thread_result = ThreadPool(threads).starmap(
        thread_get_one_synonyms_with_scraping_ant,
        list_args,
    )

    # Check.
    assert len(list_thread_result) == len(list_keyword)
    for thread_result in list_thread_result:
        list_synonym = thread_result[1]
        assert len(list_synonym) > 0

    print("test_thread_request_synonym done")


def test_get_all_synonyms_with_scraping_ant_with_threads():
    list_word_underscore = [
        "hi",
        "bye",
        "bye",  # Repeated, will not add to dict_no_synonym.
        "QSXEDC",
        "galavisión",  # Not ascii, will not add to dict_no_synonym.
        "SDFVSD",  # Already in dict_no_synonym, will be filtered.
        "like",  # Already in dict_synonym, will be filtered.
    ]
    list_api_key = [
        "********************************",
        "f74d656b70784fa5bfab393eac2138b1",
    ]
    threads = len(list_api_key)  # Num of threads must follow num of api_key.
    min_dump_counter = len(list_api_key)  # After n requests, dump results to pickle.

    # Initiate different conn for each api key to avoid Request-sent error.
    list_conn = [
        http.client.HTTPSConnection("api.scrapingant.com")
        for i in range(len(list_api_key))
    ]

    # Initiate different lock for each api key to avoid race condition on using api_key.
    # list_dump_lock = [threading.Lock()] * len(list_api_key)
    list_api_key_lock = [threading.Lock() for i in range(len(list_api_key))]

    script_dir_path = os.path.dirname(os.path.abspath(__file__))
    synonym_pickle_path = os.path.join(
        script_dir_path,
        r"./test_get_all_synonyms_with_scraping_ant_with_threads_synonyms.pickle",
    )
    no_synonym_pickle_path = os.path.join(
        script_dir_path,
        r"./test_get_all_synonyms_with_scraping_ant_with_threads_no_synonyms.pickle",
    )

    # Remove the pickle file first.
    if os.path.exists(synonym_pickle_path):
        os.remove(synonym_pickle_path)

    if os.path.exists(no_synonym_pickle_path):
        os.remove(no_synonym_pickle_path)

    # Create test pickles.
    dict_synonym_test = {
        "drink": ["beverage", "drinkable"],
        "like": ["enjoy", "love", "adore"],
    }
    dict_no_synonym_test = {
        "ORKVSC": 1,
        "SDFVSD": 1,
        "TYHRFE": 1,
    }
    dump_variable_to_pickle(pickle_path=synonym_pickle_path, variable=dict_synonym_test)
    dump_variable_to_pickle(
        pickle_path=no_synonym_pickle_path, variable=dict_no_synonym_test
    )

    # -------Start main functions----------------------.
    # Filter keywords.
    list_word_underscore_filtered = filter_keywords(
        list_word_underscore,
        synonym_pickle_path,
        no_synonym_pickle_path,
    )

    # Create thread arguments.
    list_api_key_duplicated = duplicate_list_limited(
        list_api_key, len(list_word_underscore)
    )
    list_conn_duplicated = duplicate_list_limited(list_conn, len(list_word_underscore))
    # list_dump_lock_duplicated = duplicate_list_limited(list_dump_lock, len(list_word_underscore))
    list_api_key_lock_duplicated = duplicate_list_limited(
        list_api_key_lock, len(list_word_underscore)
    )
    # list_synonym_pickle_path = duplicate_list_limited([synonym_pickle_path], len(list_word_underscore))
    # list_no_synonym_pickle_path = duplicate_list_limited([no_synonym_pickle_path], len(list_word_underscore))

    list_args = list(
        zip(
            list_api_key_lock_duplicated,
            # list_dump_lock_duplicated,
            list_conn_duplicated,
            list_api_key_duplicated,
            list_word_underscore_filtered,
            # list_synonym_pickle_path,
            # list_no_synonym_pickle_path,
        )
    )

    # Split [list_args] into sub-list by [min_dump_counter] so we dont need to wait
    # for all requests to be sent then dump data.
    # n = len(list_api_key)
    list_list_args_split = [
        list_args[i : i + min_dump_counter]
        for i in range(0, len(list_args), min_dump_counter)
    ]

    # t0 = time.time()
    # list_thread_result = ThreadPool(threads).starmap(
    #     thread_get_one_synonyms_with_scraping_ant,
    #     list_args
    # )
    # t1 = time.time()
    # print(f'{(t1-t0)*1000} ms')

    t0 = time.time()
    for list_args_split in list_list_args_split:
        # Start threads.
        list_thread_result = ThreadPool(threads).starmap(
            thread_get_one_synonyms_with_scraping_ant, list_args_split
        )

        # Dump to pickle (after above threads finished).
        dict_synonym = load_pickle_file(synonym_pickle_path, {})
        dict_no_synonym = load_pickle_file(no_synonym_pickle_path, {})
        for thread_result in list_thread_result:
            word_underscore = thread_result[1]
            list_synonym = thread_result[2]

            # No-synonym dump.
            if len(list_synonym) == 0:
                dict_no_synonym[word_underscore] = 1
                dump_variable_to_pickle(
                    pickle_path=no_synonym_pickle_path, variable=dict_no_synonym
                )
                continue

            # Synonym dump.
            dict_synonym[word_underscore] = list_synonym
            dump_variable_to_pickle(
                pickle_path=synonym_pickle_path, variable=dict_synonym
            )

    t1 = time.time()
    print(f"{(t1-t0)*1000} ms")

    for conn in list_conn:
        conn.close()
    # ------------------------------------------------

    # Check.
    assert os.path.exists(synonym_pickle_path)
    assert os.path.exists(no_synonym_pickle_path)

    dict_synonym.clear()
    dict_no_synonym.clear()
    dict_synonym = load_pickle_file(synonym_pickle_path, {})
    dict_no_synonym = load_pickle_file(no_synonym_pickle_path, {})
    dict_synonym_correct = {
        "drink": ["beverage", "drinkable"],
        "like": ["enjoy", "love", "adore"],
        "hi": ["dummy"],
        "bye": ["dummy"],
    }
    dict_no_synonym_correct = {
        "ORKVSC": 1,
        "SDFVSD": 1,
        "TYHRFE": 1,
        "QSXEDC": 1,
    }

    assert len(dict_synonym) == len(dict_synonym_correct)
    assert dict_no_synonym == dict_no_synonym_correct

    # assert status_code == 200

    print("test_get_all_synonyms_with_scraping_ant_with_threads done")


def run_get_all_synonyms_with_scraping_ant_with_threads():
    synonym_site = "powerthesaurus"

    # ./server/api/tmp/my_site/dataset/android/synonyms_{synonym_site}.pickle
    # ./server/api/tmp/my_site/dataset/android/no_synonyms_{synonym_site}.pickle
    # ./server/api/tmp/my_site/dataset/android/004_keywords.pickle

    synonym_pickle_path = (
        f"./server/api/tmp/my_site/dataset/android/synonyms_{synonym_site}.pickle"
    )
    no_synonym_pickle_path = (
        f"./server/api/tmp/my_site/dataset/android/no_synonyms_{synonym_site}.pickle"
    )
    keyword_pickle_path = (
        r"./server/api/tmp/my_site/dataset/android/004_keywords.pickle"
    )

    # dict_synonyms = {}
    # dict_no_synonyms = {}
    dict_id_keywords = {}

    list_api_key = list(dict_account_apikey.values())

    # Find suitable api keys.
    total_request_needed = 1000
    list_api_key, total_credit, is_credit_enough = calculate_suitable_api_key(
        list_api_key,
        total_request_needed,
    )

    threads = len(list_api_key)  # Num of threads must follow num of api_key.
    min_dump_counter = len(list_api_key)  # After n requests, dump results to pickle.

    # Initiate different conn for each api key to avoid Request-sent error.
    list_conn = [
        http.client.HTTPSConnection("api.scrapingant.com")
        for i in range(len(list_api_key))
    ]

    # Initiate different lock for each api key to avoid race condition on using api_key.
    # list_dump_lock = [threading.Lock()] * len(list_api_key)
    list_api_key_lock = [threading.Lock() for i in range(len(list_api_key))]

    # Read the pickle file first.
    # dict_synonyms = load_pickle_file(synonym_pickle_path, {})
    # dict_no_synonyms = load_pickle_file(no_synonym_pickle_path, {})
    dict_id_keywords = load_pickle_file(keyword_pickle_path, {})

    list_word_underscore = [y for x in dict_id_keywords.values() for y in x]

    # Filter keywords.
    list_word_underscore_filtered = filter_keywords(
        list_word_underscore,
        synonym_pickle_path,
        no_synonym_pickle_path,
    )

    # Create thread arguments.
    list_api_key_duplicated = duplicate_list_limited(
        list_api_key, len(list_word_underscore)
    )
    list_conn_duplicated = duplicate_list_limited(list_conn, len(list_word_underscore))
    list_api_key_lock_duplicated = duplicate_list_limited(
        list_api_key_lock, len(list_word_underscore)
    )

    list_args = list(
        zip(
            list_api_key_lock_duplicated,
            list_conn_duplicated,
            list_api_key_duplicated,
            list_word_underscore_filtered,
        )
    )

    # Split [list_args] into sub-list by [min_dump_counter] so we dont need to wait
    # for all requests to be sent then dump data.
    list_list_args_split = [
        list_args[i : i + min_dump_counter]
        for i in range(0, len(list_args), min_dump_counter)
    ]

    t0 = time.time()
    for list_args_split in tqdm(list_list_args_split):
        # Start threads.
        list_thread_result = ThreadPool(threads).starmap(
            thread_get_one_synonyms_with_scraping_ant, list_args_split
        )

        # Dump to pickle (after above threads finished).
        dict_synonym = load_pickle_file(synonym_pickle_path, {})
        dict_no_synonym = load_pickle_file(no_synonym_pickle_path, {})
        for thread_result in list_thread_result:
            word_underscore = thread_result[1]
            list_synonym = thread_result[2]

            # No-synonym dump.
            if len(list_synonym) == 0:
                dict_no_synonym[word_underscore] = 1
                dump_variable_to_pickle(
                    pickle_path=no_synonym_pickle_path, variable=dict_no_synonym
                )
                continue

            # Synonym dump.
            dict_synonym[word_underscore] = list_synonym
            dump_variable_to_pickle(
                pickle_path=synonym_pickle_path, variable=dict_synonym
            )

    t1 = time.time()
    print(f"{(t1-t0)*1000} ms")

    for conn in list_conn:
        conn.close()

    print("run_get_all_synonyms_with_scraping_ant_with_threads done")


def filter_keywords(
    list_keywords,
    synonym_pickle_path,
    no_synonym_pickle_path,
    result_unqiue=True,
):
    """
    Given:
    list_keyword = [
        'hello',    # In [dict_synonym], should be filtered.
        'hi',
        'hi',       # Repeated, should be filtered.
        'QQFEX',    # In [dict_no_synonym], should be filtered.
        'ODFJE',    # In [dict_no_synonym], should be filtered.
        'eat',
        'galavisión',    # Not ascii, should be filtered
    ]

    and in pickle file (synonym_pickle_path)
    dict_synonym = {
        'hello',
        'bye',
    }

    and in pickle file (no_synonym_pickle_path)
    dict_no_synonym = {
        'QQFEX',
        'ODFJE',
    }

    Return:
    list_keyword_correct = [
        'hi',
        'eat',
    ]

    """
    list_keywords_filtered = list_keywords.copy()

    dict_synonyms = load_pickle_file(synonym_pickle_path, {})
    dict_no_synonyms = load_pickle_file(no_synonym_pickle_path, {})

    for keyword in list_keywords:

        # Remove if [word_underscore] is already in [dict_synonyms].
        if keyword in dict_synonyms:
            print(f'Word(underscore) "{keyword}" already in dict_synonyms, skip.')
            list_keywords_filtered.remove(keyword)
            continue

        # Remove if [word_underscore] is already in [dict_no_synonyms].
        if keyword in dict_no_synonyms:
            print(f'Word(underscore) "{keyword}" already in dict_no_synonyms, skip.')
            list_keywords_filtered.remove(keyword)
            continue

        # Remove if [word_underscore] is not ascii.
        if not is_ascii(keyword):
            print(f'Word(underscore) "{keyword}" is not ascii, skip.')
            list_keywords_filtered.remove(keyword)
            continue

    if result_unqiue:
        list_keywords_filtered = list(set(list_keywords_filtered))

    return list_keywords_filtered


def test_filter_keywords():
    list_keyword = [
        "hello",  # In [dict_synonym], should be filtered.
        "hi",
        "hi",  # Repeated, should be filtered.
        "QQFEX",  # In [dict_no_synonym], should be filtered.
        "ODFJE",  # In [dict_no_synonym], should be filtered.
        "eat",
        "galavisión",  # Not ascii, should be filtered
    ]
    synonym_pickle_path = f"./utils/synonyms/test_filter_keywords_synonym.pickle"
    no_synonym_pickle_path = f"./utils/synonyms/test_filter_keywords_no_synonym.pickle"

    # Create test pickle.
    if os.path.exists(synonym_pickle_path):
        os.remove(synonym_pickle_path)

    if os.path.exists(no_synonym_pickle_path):
        os.remove(no_synonym_pickle_path)

    dict_synonym = {
        "hello",
        "bye",
    }

    dict_no_synonym = {
        "QQFEX",
        "ODFJE",
    }
    dump_variable_to_pickle(pickle_path=synonym_pickle_path, variable=dict_synonym)
    dump_variable_to_pickle(
        pickle_path=no_synonym_pickle_path, variable=dict_no_synonym
    )

    # Filter.
    list_keyword_filtered = filter_keywords(
        list_keyword,
        synonym_pickle_path,
        no_synonym_pickle_path,
    )

    # Check.
    list_keyword_correct = [
        "hi",
        "eat",
    ]
    assert collections.Counter(list_keyword_filtered) == collections.Counter(
        list_keyword_correct
    )

    print("test_filter_keywords done")


def main():
    # Test.
    # test_get_one_synonyms_with_scraping_ant()
    # test_get_all_synonyms_with_scraping_ant()
    # test_filter_keywords()
    # test_get_all_synonyms_with_scraping_ant_with_threads()
    # test_clean_dict_no_synonym()
    # test_threads_dump_pickle()
    # test_threads_simple_dump_pickle_v2()
    # test_duplicate_list_limited()
    # test_thread_request_synonym()

    # Run.
    # run_get_all_synonyms_with_scraping_ant()
    # run_clean_dict_no_synonym()
    run_get_all_synonyms_with_scraping_ant_with_threads()


if __name__ == "__main__":
    main()
