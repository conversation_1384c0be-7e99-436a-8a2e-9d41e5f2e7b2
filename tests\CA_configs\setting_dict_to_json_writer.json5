{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "write_dict_to_json",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "write_dict_to_json",
            "type": "PluginDictToJsonWriter",
            "use": true,
            "dict_to_write": {
                "key1": "value1",
                "key2": "value2",
                "key3": "value3"
            },
            "output_folder_path": "./tests/json_output",
            "json_file_name": "output_file",
            "path_buffer_to": "output_path"
        }
    ]
   

}
