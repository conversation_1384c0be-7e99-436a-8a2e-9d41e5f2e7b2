import pytest
from tomsze_utils.version_utils import bump_version_str


def test_bump_version_str_patch():
    original_version = "1.0.0"
    commit_type = "patch"
    expected_version = "1.0.1"
    assert bump_version_str(original_version, commit_type) == expected_version


def test_bump_version_str_minor():
    original_version = "1.0.0"
    commit_type = "minor"
    expected_version = "1.1.0"
    assert bump_version_str(original_version, commit_type) == expected_version


def test_bump_version_str_major():
    original_version = "1.0.0"
    commit_type = "major"
    expected_version = "2.0.0"
    assert bump_version_str(original_version, commit_type) == expected_version


def test_bump_version_str_none():
    original_version = "1.0.0"
    commit_type = "none"
    expected_version = "1.0.0"
    assert bump_version_str(original_version, commit_type) == expected_version
