package com.kewtoms.whatappsdo.auth;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;
import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.RootMatchers.isDialog;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withContentDescription;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withText;

import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_obtain_new_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_sign_in_access_token_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_sign_in_link;
import static com.kewtoms.whatappsdo.data.Constants.mock_post_req_url_validate_access_token_link;
import static com.kewtoms.whatappsdo.data.TestUtilsConstants.CODE_RUN_FOR_TEST_FOLDER_NAME;
import static com.kewtoms.whatappsdo.utils.TestUtils.createIntentWithSilentSignInMockData;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.kewtoms.whatappsdo.MainActivity;
import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;

import org.json.JSONObject;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;

import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.RecordedRequest;

import com.kewtoms.whatappsdo.utils.CodeRunManager;
import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
import com.kewtoms.whatappsdo.utils.TestUtils;

@RunWith(AndroidJUnit4.class)
public class SignInTest {
  public static final String TAG = "APP:SignInTest";

  //  // Rule to launch the MainActivity
  //  @Rule
  //  public ActivityScenarioRule<MainActivity> activityScenarioRule =
  //    new ActivityScenarioRule<>(MainActivity.class);


  @BeforeClass
  public static void disableAnimations() {
    // Disable all animation scales to ensure test stability
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global window_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global transition_animation_scale 0");
    InstrumentationRegistry.getInstrumentation()
      .getUiAutomation()
      .executeShellCommand("settings put global animator_duration_scale 0");
  }

  @Test
  public void test_sign_in_success_mock()
    throws
    IOException {

    // Run CodeRunManager for test
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");


    // 1. Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Create the mock response with realistic tokens
    String mockAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_access_token";
    String mockRefreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_refresh_token";

    JSONObject mockResponseJson =
      TestUtils.createMockSignInSuccessResponseJson(
        new JSONObject(),
        true,
        true,
        mockAccessToken,
        mockRefreshToken
      );

    // set to current structure of response
    mockWebServer.enqueue(new MockResponse().setBody(mockResponseJson.toString()));

    mockWebServer.start(); // Use dynamic port allocation instead of fixed port 8000

    //  ---- Launch activity ----
    Intent intent = new Intent(
      getApplicationContext(),
      MainActivity.class
    );

    // Pass mock url and encrypt/decrypt
    intent.putExtra(
      Constants.mockGetIsServerOnlineUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_get_is_server_online_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockSignInUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_link).toString()
    );
    intent.putExtra(
      Constants.mockIsEncryptDecryptKey,
      true
    );

    intent.putExtra(
      Constants.mockDeploymentModeKey,
      Configuration.DeploymentMode.LOCAL.toString()
    );
    intent.putExtra(
      Constants.mockEnvModeKey,
      Configuration.EnvironmentMode.TEST.toString()
    );
    ActivityScenario.launch(intent);
    // --------------------------

    // Open navigation drawer
    Context context = getApplicationContext();
    onView(withContentDescription(context.getString(R.string.navigation_drawer_open))).perform(click());


    // Click on "Sign-in" item on drawer menu
    onView(withId(R.id.nav_login)).perform(click());

    // 4. Trigger a network action (e.g., click a sign-up button)
    onView(withId(R.id.button_login_large)).perform(click());

    // 5. Verify the app shows a success message (navigated to home screen)
    onView(withId(R.id.search_field)).check(matches(isDisplayed()));

    // 6. Verify that sign-in data was properly saved
    long loginTime = SecurePrefsManager.getLoginTime(context);
    assert loginTime > 0;

    // Verify access token was saved
    String savedAccessToken = SecurePrefsManager.getAccessToken(context);
    assert savedAccessToken != null;
    assert savedAccessToken.equals(mockAccessToken);

    // Verify refresh token was saved
    String savedRefreshToken = SecurePrefsManager.getRefreshToken(context);
    assert savedRefreshToken != null;
    assert savedRefreshToken.equals(mockRefreshToken);

    // Verify login status was set
    boolean hasSuccessLoggedIn = SecurePrefsManager.getHasSuccessLoggedIn(context);
    assert hasSuccessLoggedIn;

    // 7. Clean up
    mockWebServer.shutdown();

  }

  @Test
  public void test_sign_in_failure_mock()
    throws
    IOException {

    // Run CodeRunManager for test
    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // 1. Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Create the mock failure response
    JSONObject mockResponseJson =
      TestUtils.createMockSignInFailureResponseJson("Invalid email or password");

    // set to current structure of response
    mockWebServer.enqueue(new MockResponse().setBody(mockResponseJson.toString()));

    mockWebServer.start(); // Use dynamic port allocation instead of fixed port 8000

    //  ---- Launch activity ----
    Intent intent = new Intent(
      getApplicationContext(),
      MainActivity.class
    );

    // Pass mock url and encrypt/decrypt
    intent.putExtra(
      Constants.mockGetIsServerOnlineUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_get_is_server_online_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockSignInUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_link).toString()
    );
    intent.putExtra(
      Constants.mockIsEncryptDecryptKey,
      true
    );

    intent.putExtra(
      Constants.mockDeploymentModeKey,
      Configuration.DeploymentMode.LOCAL.toString()
    );
    intent.putExtra(
      Constants.mockEnvModeKey,
      Configuration.EnvironmentMode.TEST.toString()
    );
    ActivityScenario.launch(intent);
    // --------------------------

    // Open navigation drawer
    Context context = getApplicationContext();
    onView(withContentDescription(context.getString(R.string.navigation_drawer_open))).perform(click());

    // Click on "Sign-in" item on drawer menu
    onView(withId(R.id.nav_login)).perform(click());

    // 4. Trigger a network action (e.g., click a sign-in button)
    onView(withId(R.id.button_login_large)).perform(click());

    // 5. Verify the app shows an error message (should stay on login screen)
    onView(withId(R.id.button_login_large)).check(matches(isDisplayed()));

    // 6. Verify that no sign-in data was saved
    String savedAccessToken = SecurePrefsManager.getAccessToken(context);
    assert savedAccessToken == null || savedAccessToken.isEmpty();

    // 7. Clean up
    mockWebServer.shutdown();

  }

  @Test
  public void test_silent_sign_in_success_mock()
    throws
    IOException {

    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Create the mock get is server online response
    JSONObject mockSGetIsServerOnlineResponseJson =
      TestUtils.createMockGetIsServerOnlineResponseJson(true);

    // Set to current structure of response
    mockWebServer.enqueue(new MockResponse().setBody(mockSGetIsServerOnlineResponseJson.toString()));

    // Create the mock sign in response with realistic tokens
    String mockAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_access_token_silent";
    String mockRefreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_refresh_token_silent";

    JSONObject mockSignInResponseJson =
      TestUtils.createMockSignInSuccessResponseJson(
        new JSONObject(),
        true,
        true,
        mockAccessToken,
        mockRefreshToken
      );


    // Set to current structure of response
    mockWebServer.enqueue(new MockResponse().setBody(mockSignInResponseJson.toString()));

    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent = new Intent(
      getApplicationContext(),
      MainActivity.class
    );

    // Pass mock url and mock encrypt/decrypt
    intent.putExtra(
      Constants.mockSignInUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_link).toString()
    );
    intent.putExtra(
      Constants.mockGetIsServerOnlineUrlKey,
      mockWebServer.url(Constants.mock_post_req_url_get_is_server_online_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockValidateAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_validate_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockSignInAccessTokenUrlKey,
      mockWebServer.url(mock_post_req_url_sign_in_access_token_link)
        .toString()
    );
    intent.putExtra(
      Constants.mockIsEncryptDecryptKey,
      true
    );

    // Set mode to production (which will enable silent login)
    intent.putExtra(
      Constants.mockDeploymentModeKey,
      Configuration.DeploymentMode.LOCAL.toString()
    );
    intent.putExtra(
      Constants.mockEnvModeKey,
      Configuration.EnvironmentMode.PROD.toString()
    );
    ActivityScenario.launch(intent);
    // ---- end launch activity ----

    // 5. Verify ...
    long loginTime = SecurePrefsManager.getLoginTime(context);
    assert loginTime > 0;

    // 6. Clean up
    mockWebServer.shutdown();

  }


  @Test
  public void test_silent_sign_in_refresh_token_expired()
    throws
    IOException {

    String methodName =
      Thread.currentThread().getStackTrace()[2].getMethodName();

    CodeRunManager.initialize(getApplicationContext()); // Initialize once
    CodeRunManager.getInstance()
      .loadConfig(CODE_RUN_FOR_TEST_FOLDER_NAME + "/" + methodName + ".properties");

    // Save sign in data for silent sign in
    Context context = getApplicationContext();

    SecurePrefsManager.saveSignInData(
      context,
      "123abc",
      "123123",
      "<EMAIL>",
      true
    );

    // Start the fake server
    MockWebServer mockWebServer = new MockWebServer();

    // Set up a Dispatcher to handle different endpoints
    Dispatcher dispatcher = new Dispatcher() {
      @NonNull
      @Override
      public MockResponse dispatch(RecordedRequest request) {
        String path = request.getPath();
        if (mock_post_req_url_validate_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockValidateAccessTokenFailResponseJson()
            .toString());
        } else if (mock_post_req_url_obtain_new_access_token_link.equals(path)) {
          return new MockResponse().setBody(TestUtils.createMockObtainNewAccessTokenFailResponseJson()
            .toString());
        }
        // Default response for any other requests
        return new MockResponse().setResponseCode(404);
      }
    };

    // Set the dispatcher on the server
    mockWebServer.setDispatcher(dispatcher);


    mockWebServer.start(8000);

    //  ---- Place mock data to intent and launch activity ----
    Intent intent =
      createIntentWithSilentSignInMockData(mockWebServer);
    ActivityScenario<MainActivity> scenario =
      ActivityScenario.launch(intent);
    // ---- end launch activity ----

    // 5. Verify app goes to LoginFragment
    // Now simulate clicking the "OK" button on the dialog
    onView(withText("OK")).inRoot(isDialog()).perform(click());

    // Check that the fragment in R.id.fragment_container is the one you expect.
    onView(withId(R.id.button_login_large)).check(matches(isDisplayed()));

    // 6. Clean up
    mockWebServer.shutdown();

  }


}