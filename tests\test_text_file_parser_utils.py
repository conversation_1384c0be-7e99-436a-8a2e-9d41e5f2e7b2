import os

from tomsze_utils.security_utils import detect_frequent_ips_in_folder
from tomsze_utils.text_file_parser_utils import (
    extract_data_from_lines_in_file,
    parse_line_using_delimiter_to_dict,
    parse_line_with_equal_to_dict,
)


def test_parse_line_with_equal_to_dict():
    fake_text_file_path = r"./tests/fake_text_file_to_test/fake_to_parse.txt"
    assert os.path.exists(fake_text_file_path)

    data_dict = parse_line_with_equal_to_dict(
        full_file_path=fake_text_file_path,
        remove_double_quote=False,
        remove_comma=False,
    )

    expect = {
        "var1": '"1",',
        "var2": '"2",',
        "var3": '"3",',
        "var4": "'4',",
        "var5": "5,",
        "var6": "6,",
        "var7": "7,",
        "var8": ",",
        "var9": "9;",
    }

    assert expect == data_dict


import pytest
import tempfile
import os
from tomsze_utils.text_file_parser_utils import parse_lines_with_delimiter_to_tuples


def test_parse_lines_with_delimiter_to_tuples():
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary file
    temp_file_path = os.path.join(temp_dir, "temp_file.txt")

    # Write content to the temporary file
    with open(temp_file_path, "w") as f:
        f.write("John,25,Developer\n")
        f.write("Jane,30,Manager\n")
        f.write("Bob,35,Engineer\n")

    # Test the function
    result = parse_lines_with_delimiter_to_tuples(
        file_path=temp_file_path,
        delimiter=",",
        index_list=[0, 2],
    )

    # Expected result
    expected_result = [("John", "Developer"), ("Jane", "Manager"), ("Bob", "Engineer")]

    # Assertions
    assert result == expected_result

    # Clean up
    os.remove(temp_file_path)
    os.rmdir(temp_dir)


def test_extract_data_from_lines():
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary file
    temp_file_path = os.path.join(temp_dir, "temp_file.txt")

    # Write content to the temporary file
    with open(temp_file_path, "w") as f:
        f.write("time: 2022-01-01 00:00:00, name: Tom, age: 5\n")
        f.write("time: 2022-01-01 00:00:01, name: Jane, age: 6\n")
        f.write("time: 2022-01-01 00:00:01, name: Tim: 1, age: 7,\n")
        f.write("time: 2022-01-01 00:00:02, name: Bob,\n")

    # Test the function
    result = extract_data_from_lines_in_file(
        file_path=temp_file_path,
        data_list=["time", "name", "age"],
    )

    # Expected result
    expected_result = [
        ("2022-01-01 00:00:00", "Tom", "5"),
        ("2022-01-01 00:00:01", "Jane", "6"),
        ("2022-01-01 00:00:01", "Tim: 1", "7"),
        ("2022-01-01 00:00:02", "Bob", None),
    ]

    # Assertions
    assert result == expected_result

    # Clean up
    os.remove(temp_file_path)
    os.rmdir(temp_dir)


def test_extract_data_from_lines_empty_data_list():
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary file
    temp_file_path = os.path.join(temp_dir, "temp_file.txt")

    # Write content to the temporary file
    with open(temp_file_path, "w") as f:
        f.write("time: 2022-01-01 00:00:00, name: Tom, age: 5\n")
        f.write("time: 2022-01-01 00:00:01, name: Jane, age: 6\n")
        f.write("time: 2022-01-01 00:00:02, name: Bob\n")

    # Test the function
    result = extract_data_from_lines_in_file(
        file_path=temp_file_path,
        data_list=[],
    )

    # Expected result
    expected_result = []

    # Assertions
    assert result == expected_result

    # Clean up
    os.remove(temp_file_path)
    os.rmdir(temp_dir)


def test_detect_frequent_ips_in_folder():
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create temporary log files
    temp_log_file_path1 = os.path.join(temp_dir, "temp_log1.log")
    temp_log_file_path2 = os.path.join(temp_dir, "temp_log2.log")
    temp_log_file_path3 = os.path.join(
        temp_dir, "temp_log3.log1"
    )  # Updated log file name

    # Write content to the temporary log files
    with open(temp_log_file_path1, "w") as f:
        f.write("time: 2022-01-01 00:00:00, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:01, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:02, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:03, Request ip: ***********\n")

    with open(temp_log_file_path2, "w") as f:
        f.write("time: 2022-01-01 00:00:00, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:01, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:02, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:03, Request ip: ***********\n")

    with open(temp_log_file_path3, "w") as f:  # Write to the new log file
        f.write("time: 2022-01-01 00:00:00, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:01, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:02, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:03, Request ip: ***********\n")

    # Test the function
    result = detect_frequent_ips_in_folder(
        folder_path=temp_dir,
        short_time_period=3,
        appear_times=3,
    )

    # Expected result
    expected_result = {
        "temp_log1.log": ["***********"],
        "temp_log2.log": ["***********"],
        "temp_log3.log1": ["***********"],  # Include expected result for new log file
    }

    # Assertions
    assert result == expected_result

    # Clean up
    os.remove(temp_log_file_path1)
    os.remove(temp_log_file_path2)
    os.remove(temp_log_file_path3)  # Clean up new log file
    os.rmdir(temp_dir)


def test_detect_frequent_ips_in_folder_no_frequent_ips():
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary log file
    temp_log_file_path1 = os.path.join(temp_dir, "temp_log1.log")
    temp_log_file_path2 = os.path.join(temp_dir, "temp_log2.log")

    # Write content to the temporary log files
    with open(temp_log_file_path1, "w") as f:
        f.write("time: 2022-01-01 00:00:00, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:01, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:02, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:03, Request ip: ***********\n")

    with open(temp_log_file_path2, "w") as f:
        f.write("time: 2022-01-01 00:00:00, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:01, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:02, Request ip: ***********\n")
        f.write("time: 2022-01-01 00:00:03, Request ip: ***********\n")

    # Test the function
    result = detect_frequent_ips_in_folder(
        folder_path=temp_dir,
        short_time_period=3,
        appear_times=4,
    )

    # Expected result
    expected_result = {}

    # Assertions
    assert result == expected_result

    # Clean up
    os.remove(temp_log_file_path1)
    os.remove(temp_log_file_path2)
    os.rmdir(temp_dir)


def test_parse_line_using_delimiter_to_dict():
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create a test file
        test_file_path = os.path.join(tmpdir, "test_file.txt")
        with open(test_file_path, "w") as f:
            f.write("key1:value1\n")
            f.write("key2: value2\n")
            f.write("key3:   value3\n")

        # Call the function to parse the file
        result = parse_line_using_delimiter_to_dict(test_file_path, ":")

        # Expected result
        expected_result = {
            "key1": "value1",
            "key2": "value2",
            "key3": "value3",
        }

        # Assertions
        assert result == expected_result


def test_parse_line_using_delimiter_to_dict_no_delimiter():
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create a test file
        test_file_path = os.path.join(tmpdir, "test_file.txt")
        with open(test_file_path, "w") as f:
            f.write("key1value1\n")
            f.write("key2value2\n")

        # Call the function to parse the file
        result = parse_line_using_delimiter_to_dict(test_file_path, ":")

        # Expected result
        expected_result = {}

        # Assertions
        assert result == expected_result


if __name__ == "__main__":

    test_parse_line_with_equal_to_dict()
