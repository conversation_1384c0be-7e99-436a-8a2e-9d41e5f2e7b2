import sys
import os
import spacy
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.database_utils.run_pickle_database_split import remove_pickles
from tqdm import tqdm
from server.api.pickle_db.android.early_preprocess.create_db_synonyms_with_source.create_db_synonyms_with_source_utils import (
    create_db_synonyms_with_nltk_source,
)
import jsonlines
from server.api.utils.keywords.get_keywords import lemmatization


def run_create_db_synonyms_with_source_nltk_using_en_thesaurus():
    """
    Create db_synonyms (10000 words for each part)
    Create nlp_model for lemmatization

    Load the jsonl and read each line.
    Extract each word and synonyms into a dict.

    For each word in the dict
        Lemmatize the word.
        Update the database.

    Dump to file.
    """

    # Create a db synonyms (in current dir)
    script_directory_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )
    remove_pickles(script_directory_path)

    db_synonyms = PickleDatabaseSplit(
        db_fpath=script_directory_path,
        db_name="db_syns",
        load_by_thread=False,
    )
    nlp_model = spacy.load("en_core_web_sm", disable=["parser", "ner"])
    jsonl_path = os.path.join(script_directory_path, "en_thesaurus.jsonl")

    # Load the jsonl and read each line.
    dict_data = {}
    with jsonlines.open(jsonl_path) as reader:
        for obj in reader:
            # Extract each word and synonyms.
            word = obj["word"]
            synonyms = obj["synonyms"]

            if not synonyms:
                continue

            if not word in dict_data:
                dict_data[word] = []

            dict_data[word] += synonyms.copy()

    # For each word in the dict
    for word in tqdm(dict_data.keys()):

        # Lemmatize the word.
        word_lemm = lemmatization(
            nlp_model=nlp_model,
            text=word,
        )

        # Update the database.
        db_synonyms.update_data_v2(
            key=word_lemm,
            col="nltk",
            data=list(set(dict_data[word])),  # use list(set()) for a unqiue list
            do_merge_if_exist=True,
        )

    # Dump to file.
    db_synonyms.dump_dirty_parts_to_pickles_thread()

    # Dump to a json (for manual checking)
    db_synonyms.to_json()


def main():
    run_create_db_synonyms_with_source_nltk_using_en_thesaurus()


if __name__ == "__main__":
    sys.exit(main())
