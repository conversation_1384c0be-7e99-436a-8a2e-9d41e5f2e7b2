from tomsze_utils.logger_utils import SimpleLogger

logger = SimpleLogger(
    get_existing_instance=True,
)

from server.api.end_to_end import data_class
from server.api.end_to_end.api_constant_keys import (
    AccessTokeDataConstant,
    ApiFunResponseKeys,
    ClientColEnum,
    DBAppsColEnum,
    DBEmailVericiationColEnum,
    DBToAddColEnum,
    DBUserDataColEnum,
)

import os
from typing import Callable, Dict, List
from googletrans import Translator
from line_profiler import profile
from server.api.end_to_end.utils import ppd_emb_kw_kwpp_update


logger.info("Done importing Translator")
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.database_utils.user_database import UserDatabase
from tomsze_utils.time_code_utils import timeit
from tomsze_utils.scoring_utils import normalize_list
from tomsze_utils.list_utils import search_list_text
from tomsze_utils.auth_utils import (
    validate_token,
)
from tomsze_utils.operating_system_utils import get_system_usage
from tomsze_utils.datetime_utils import (
    get_current_system_datetime,
    extract_datetime_components,
)
from business.business_plans import plans_list

logger.info("Done importing PickleDatabaseSplit")
from server.api.end_to_end.data_class import (
    AccessTokeData,
    CodeVerifyData,
    FakeRequestData,
    IsUserSignedUpData,
    ObtainNewAccessTokenByRefreshTokenData,
    RemoveUserData,
    SendVerificationEmailData,
    SignInAccessTokenData,
    SignInData,
    SignupData,
    ValidateUserAccessTokenData,
)

logger.info("Done importing end_to_end")
from const_path import (
    proj_ser_api_db_apps_path,
    proj_ser_api_db_apps_small_path,
    proj_ser_api_db_synonyms_path,
    proj_ser_api_db_synonyms_small_path,
    proj_ser_api_db_synonyms_need_powerthesaurus_path,
    proj_ser_api_db_to_add_path,
    proj_ser_api_db_user_path,
    proj_ser_api_db_synonyms_need_powerthesaurus_small_path,
)

logger.info("Done importing get_description")

from server.api.utils.keywords.get_keywords import lemmatization

logger.info("Done importing lemmatization")
from server.api.utils.generate_vectors.utils import (
    convert_to_embedding,
)

logger.info("Done importing convert_to_embedding")

from server.api.utils.generate_vectors.utils import extract_device_app_synonyms_v2

logger.info(
    "Done importing extract_device_app_synonyms_v2, extract_keyword_using_yake, normalize_list"
)

from server.api.utils.generate_vectors.utils import (
    similarity_search,
    split_text,
)

logger.info(
    "Done importing post_process_app_description, similarity_search, split_text"
)

from server.api.utils.translation.translate import check_is_eng, translate_to_eng


import spacy
from mode import is_production

logger.info("Done importing spacy")

## Initializing objects
# Database load.
db_apps = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_apps_path,
    db_name="db_app",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)
db_synonyms = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_synonyms_path,
    db_name="db_synonyms",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)

db_synonyms_need_powerthesaurus = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_synonyms_need_powerthesaurus_path,
    db_name="db_synonyms_need_powerthesaurus",
    load_by_thread=False,
)

db_to_add = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_to_add_path,
    db_name="db_to_add",
    load_by_thread=False,
)


db_user = UserDatabase(
    name="db_user",
    db_fpath=proj_ser_api_db_user_path,
)

translator = Translator()

model_id = "multi-qa-MiniLM-L6-cos-v1"

max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20

lemmatization_model = spacy.load("en_core_web_sm", disable=["parser", "ner"])
logger.info("Done initializing objects")


@timeit
# @profile
def update_server_db_apps_and_db_to_add(
    client_apps_to_update: Dict,
    db_apps: PickleDatabaseSplit = db_apps,
    db_to_add: PickleDatabaseSplit = db_to_add,
    func_with_args_after_update: Callable = None,
    lock_lock_file: bool = True,
):
    """
    To be ran in endpoint 'usa'

    Update server apps
    """

    # Lock the database from other access.
    if lock_lock_file:
        db_apps.acquire_lock()

    # For each app id
    for ind, app_id in enumerate(client_apps_to_update[ClientColEnum.APPIDS]):
        scrape_result = client_apps_to_update[ClientColEnum.SCRAPERESULTS][ind]

        # Skip and update db_to_add if this app scrape result is not success
        if scrape_result["exception"] != "":
            db_to_add.update_data_with_keys(
                keys=[app_id, "not_found_playstore"], data="true"
            )
            continue

        # Skip and remove id from db_to_add if this app is NOT new to db_apps (already added to db_apps)
        if db_apps.is_key_in(app_id):
            db_to_add.remove_key(app_id)
            continue

        # Update app_id, description, description embedding
        from sentence_transformers import SentenceTransformer

        embedder = SentenceTransformer(model_id)
        ppd_emb_kw_kwpp_update(
            app_id=app_id,
            db_apps=db_apps,
            scrape_result=scrape_result,
            embedder=embedder,
            max_ngram_size=max_ngram_size,
            deduplication_threshold=deduplication_threshold,
            max_num_keywords=max_num_keywords,
        )

        # Remove the key if it is in db_to_add.
        if db_to_add.is_key_in(app_id):
            db_to_add.remove_key(app_id)

    # Dump changes of db_to_add to disk.
    # TODO add test
    if db_to_add.dirty_inds:
        db_to_add.dump_dirty_parts_to_pickles()

    if db_apps.dirty_inds:
        db_apps.dump_dirty_parts_to_pickles()

    if lock_lock_file:
        db_apps.release_lock()

    # Run something after the main code.
    if func_with_args_after_update:
        func_with_args_after_update(
            db_apps=db_apps,
            db_to_add=db_to_add,
        )

    result = {
        ApiFunResponseKeys.RESULT: f"Server received {len(client_apps_to_update[ClientColEnum.APPIDS])} apps"
    }
    return result


@timeit
@profile
def search_user_app(
    client_search_data: Dict,
    debug_mode: bool,
    db_apps: PickleDatabaseSplit = db_apps,
    db_synonyms: PickleDatabaseSplit = db_synonyms,
    db_synonyms_need_powerthesaurus: PickleDatabaseSplit = db_synonyms_need_powerthesaurus,
):
    """
    To be ran in enpoint '__search_user_app'

    Search user apps from server apps.

    Only update db_synonyms_need_powerthesaurus.
    """
    logger.info(f"------ Client data ---------")
    list_device_apps = client_search_data[ClientColEnum.APPIDS]
    client_search = client_search_data[ClientColEnum.QUERY]
    logger.info(f"search text: '{client_search}'")
    logger.info(f"client apps: {list_device_apps}")
    logger.info(f"----------------------------")

    # Return if not allowed search.
    if not client_search_data[ClientColEnum.ALLOWSEARCH]:
        return {ClientColEnum.ALLOWSEARCH: False}

    # Check if client search is english, else translate to eng
    try:
        success, is_eng = check_is_eng(translator, client_search)
    except Exception as e:
        logger.error(str(e))

    if not success:
        logger.error(f"Error checking if client search is english: {client_search}")

    if not is_eng:
        success, client_search = translate_to_eng(translator, client_search)

    assert success == True  # TODO log error
    # --------------------------------------------------------

    """
    ====================================================================================
    Semantic search (similarity search).
    
    Lemmatization on client search text.
    Similarity search.
    Word search.
    Word search on synonyms.
    """
    # Lemmatization on client search text.
    query_text = lemmatization(nlp_model=lemmatization_model, text=client_search)
    query = [query_text]

    from sentence_transformers import SentenceTransformer

    embedder = SentenceTransformer(model_id)
    query_embedding = convert_to_embedding(
        embedder,
        query,
        device="cpu",
        batch_size=128,
    )

    list_device_embedding_tensor = []
    list_device_description = []
    dict_device_id = {}
    for device_app_id in list_device_apps:
        if db_apps.is_key_in(device_app_id):
            list_device_embedding_tensor.append(
                db_apps.query_key_col(
                    key=device_app_id,
                    col=DBAppsColEnum.EMBD,
                )
            )
            list_device_description.append(
                db_apps.query_key_col(
                    key=device_app_id,
                    col=DBAppsColEnum.DESC,
                )
            )

            dict_device_id[device_app_id] = ""

    if not list_device_description:
        return {ApiFunResponseKeys.ERROR: "Not even one user app is found in database."}

    # Similarity search.
    hits = similarity_search(
        query_embeddings=query_embedding,
        list_text_embedding=list_device_embedding_tensor,
        top_k=30,
    )

    # Word search.
    list_query_words = split_text(query_text)
    list_num_matches = search_list_text(
        list_device_description,
        list_query_words,
    )
    list_normalized_word_score = normalize_list(list_num_matches)
    list_normalized_word_score_reorder = []
    hits = hits[0]  # Get the hits for the first query (Use this for app is correct)
    for hit in hits:  # Reorder by similarity search result
        list_normalized_word_score_reorder.append(
            list_normalized_word_score[hit["corpus_id"]]
        )

    # Word search on synonyms.
    list_device_synonyms = extract_device_app_synonyms_v2(
        list_device_apps=list_device_apps,
        db_apps=db_apps,
        db_synonyms=db_synonyms,
        lemmatization_model=lemmatization_model,
    )
    list_num_matches_syn = search_list_text(
        list_device_synonyms,
        list_query_words,
    )
    list_normalized_word_score_syn = normalize_list(list_num_matches_syn)
    list_normalized_word_score_reorder_syn = []
    for hit in hits:  # Reorder by similarity search result
        list_normalized_word_score_reorder_syn.append(
            list_normalized_word_score_syn[hit["corpus_id"]]
        )

    """
    Mark keywords of device id not that db_synonyms
    
    For each device
        For each keyword in keyword
            Lemmatized the keyword
            Query the keyword
            If keyword not exist, add to db_synonyms_need_powertheasaurus
            
    (Dump db_synonyms_need_powertheasaurus to file after send response to client)
    """
    # For each device
    for device_app_id in list_device_apps:
        if db_apps.is_key_in(device_app_id):
            # Query app id keywords.
            list_device_keywords = db_apps.query_key_col(
                key=device_app_id,
                col=DBAppsColEnum.KEYW,
            )

            # For each keyword.
            for keyword in list_device_keywords:
                # Lemmatize keyword.
                keyw_lemmatized = lemmatization(
                    nlp_model=lemmatization_model,
                    text=keyword,
                )
                dict_synonyms = {}
                if db_synonyms.is_key_in(keyw_lemmatized):

                    # Query the keyword
                    dict_synonyms = db_synonyms.query_key(
                        key=keyw_lemmatized,
                        default=[],
                    )

                # If keyword not exist, add to db_synonyms_need_powertheasaurus
                if not dict_synonyms:
                    db_synonyms_need_powerthesaurus.update_data(
                        datas={keyw_lemmatized: 1}
                    )
                # If keyword not exist in pt, add to db_synonyms_need_powertheasaurus
                elif not "pt" in dict_synonyms:
                    db_synonyms_need_powerthesaurus.update_data(
                        datas={keyw_lemmatized: 1}
                    )

    # Dump (remove later)
    if db_synonyms_need_powerthesaurus.dirty_inds:
        db_synonyms_need_powerthesaurus.dump_dirty_parts_to_pickles_thread()
    """
    ====================================================================================
    Scoring and ranking.
    """
    list_id_from_dict = list(dict_device_id)
    list_appid_score_sim_only = []
    list_appid_score_word_search = []
    list_appid_score_syn_search = []
    list_appScore = []
    list_id_hits = []

    # Sementic search score only.
    for hit in hits:
        list_appid_score_sim_only.append(
            [list_id_from_dict[hit["corpus_id"]], hit["score"]]
        )
        list_id_hits.append(list_id_from_dict[hit["corpus_id"]])
        list_appScore.append(hit["score"])

    list_normalized_similarity_score = normalize_list(list_appScore)

    # Word search score addon.
    list_final_app_score = [
        x + y
        for x, y in zip(
            list_normalized_word_score_reorder,
            list_normalized_similarity_score,
        )
    ]
    # sort by score.
    list_appid_score_word_search = [
        [id, score] for score, id in sorted(zip(list_final_app_score, list_id_hits))
    ]
    list_appid_score_word_search.reverse()

    # Syn search score addon.
    list_final_app_score = [
        x + y + z
        for x, y, z in zip(
            list_normalized_word_score_reorder_syn,
            list_normalized_word_score_reorder,
            list_normalized_similarity_score,
        )
    ]
    # sort by score.
    list_appid_score_syn_search = [
        [id, score] for score, id in sorted(zip(list_final_app_score, list_id_hits))
    ]
    list_appid_score_syn_search.reverse()

    list_appid_syn_search_sorted = [
        id for _, id in sorted(zip(list_final_app_score, list_id_hits))
    ]
    list_appid_syn_search_sorted.reverse()

    list_score_syn_search_sorted = [
        score for score, _ in sorted(zip(list_final_app_score, list_id_hits))
    ]
    list_score_syn_search_sorted.reverse()

    response = {}
    if debug_mode:
        response = ApiResponse.success_response(
            data={
                ApiFunResponseKeys.DEBUG_SIM: list_appid_score_sim_only,
                ApiFunResponseKeys.DEBUG_SIM_WORD: list_appid_score_word_search,
                ApiFunResponseKeys.DEBUG_SIM_WORD_SYN: list_appid_score_syn_search,
            }
        )
    else:
        response = ApiResponse.success_response(
            data={
                "packageNames": list_appid_syn_search_sorted,
                "scores": list_score_syn_search_sorted,
            }
        )

    return response


def get_is_server_online() -> Dict[str, Dict[str, str]]:
    """
    Checks if the server is online.

    Args:
        None

    Returns:
        Dict[str, Dict[str, str]]: A dictionary containing the server online status.

    Examples:
        # Example 1: Basic usage
        result = get_is_server_online()
        print(result)
        # Output: {'Result': {'is_online': True}}

        # Example 2: Using the function in a conditional
        if get_is_server_online()["Result"]["is_online"]:
            print("Server is online")
        else:
            print("Server is offline")
    """
    logger.info(f"Getting is server online")
    response = ApiResponse.success_response(
        data={
            "is_online": True,
        }
    )
    return response


def get_datetime() -> Dict[str, Dict[str, str]]:
    """
    Retrieves the current system month and day.

    Returns:
        Dict[str, Dict[str, str]]: A dictionary containing the current month and day.

    Args:
        None

    Examples:
        ```python
        # Example 1: Getting the current month and day
        get_datetime()
        # Expected Output (example): {'ApiFunResult': {'month': '12', 'day': '24'}}
        ```

        ```python
        # Example 2: Demonstrating the function call
        get_datetime()
        # Expected Output (example): {'ApiFunResult': {'month': '01', 'day': '01'}}
        ```
    """
    logger.info(f"Getting datetime")
    current_datetime = get_current_system_datetime()  # example 2025-04-05 11:50:54
    datetime_component = extract_datetime_components(current_datetime)
    response = ApiResponse.success_response(
        data={
            "year": datetime_component.year,
            "month": datetime_component.month,
            "day": datetime_component.day,
        }
    )
    return response


def check_client_apps_need_scrape_and_update_db_to_add(
    client_apps: Dict,
    db_apps: PickleDatabaseSplit = db_apps,
    db_to_add: PickleDatabaseSplit = db_to_add,
    dump: bool = True,
):
    """
    Checks which app from client_apps that needs scrape.

    Will add (stage) the app to db_to_add if the app is not in db_apps.

    Returns:
        A dict that include a list of apps that need scrape.
    """
    logger.info(f"check_client_apps_need_scrape_and_update_db_to_add run")
    logger.info(f"------ Client data ---------")
    list_device_apps = client_apps[ClientColEnum.APPIDS]
    logger.info(f"client apps: {list_device_apps}")
    logger.info(f"----------------------------")

    need_scrape_app_list = []
    for device_app_id in list_device_apps:
        # Skip if this app id is not in db_apps.
        if db_apps.is_key_in(device_app_id):
            continue

        # Skip if this app id in db_to_add has empty not_found_playstore.
        not_found_playstore = db_to_add.query_key_col(
            device_app_id,
            "not_found_playstore",
        )
        if (
            not_found_playstore != "" and not_found_playstore != None
        ):  # if not_found_playstore None (when id not in db_to_add), no need to skip
            continue

        # Update the list by adding id to notice user to scrape.
        need_scrape_app_list.append(device_app_id)

        # Update db_add.
        db_to_add.update_data_with_keys(
            keys=[device_app_id, DBToAddColEnum.CHECK_DATE_TIME],
            data="",
        )
        db_to_add.update_data_with_keys(
            keys=[device_app_id, DBToAddColEnum.NOT_FOUND_PLAYSTORE],
            data="",
        )

    if dump:
        db_to_add.dump_all_parts_to_pickles()

    logger.info(f"check_client_apps_need_scrape_and_update_db_to_add done")
    response = ApiResponse.success_response(
        data={
            "packageNames": need_scrape_app_list,
        }
    )
    return response


def sign_up(
    sign_up_data: SignupData,
    db_user: UserDatabase = db_user,
) -> Dict[str, str]:
    """
    Signs up a new user by adding their email and password to the database.

    This function checks if the user is already signed up and, if not, adds their information to the database.

    Args:
        sign_up_data (SignupData): The data containing the user's email address, password, and verification code.
        db_user (UserDatabase, optional): The user database instance. Defaults to db_user.

    Returns:
        Dict[str, str]: A dictionary containing the result of the sign-up process.
    """
    response = db_user.sign_up(
        sign_up_data.email_address,
        sign_up_data.password,
        sign_up_data.verification_code,
    )
    if not response[user_database.ResponseKeys.RESULT][
        user_database.ResponseKeys.IS_SUCCESS
    ]:
        return {ApiFunResponseKeys.RESULT: response}

    return {ApiFunResponseKeys.RESULT: "success"}


from tomsze_utils.database_utils import user_database
from tomsze_utils.api_response_utils import ApiResponse


def send_verification_email(
    send_verification_email_data: SendVerificationEmailData,
    db_user: UserDatabase = db_user,
) -> Dict[str, str]:
    """
    Send a verification email to the user.

    This function sends a verification email to the specified email address using the provided password.

    Args:
        send_verification_email_data (SendVerificationData): The data containing the email address and password.
        db_user (UserDatabase, optional): The user database instance. Defaults to db_user.

    Returns:
        Dict[str, str]: A dictionary containing the result of the email sending operation.
    """
    response = db_user.send_verification_email(
        send_verification_email_data.email_address,
    )

    return response


def sign_in(
    sign_in_data: SignInData,
    db_user: UserDatabase = db_user,
) -> Dict[str, str]:
    """
    Sign in a user by verifying their email and whether they have signed up.

    Args:
        sign_in_data (SignInData): The sign-in data containing the user's email and password.
        user_db (UserDatabase, optional): The user database instance. Defaults to user_db.

    Returns:
        Dict[str, str]: A dictionary containing the result of the sign-in attempt.
    """
    response = db_user.sign_in(sign_in_data.email_address, sign_in_data.password)

    return response


def sign_in_access_token(
    sign_in_data: SignInAccessTokenData,
    db_user: UserDatabase = db_user,
) -> Dict[str, str]:
    """Sign in a user using an access token.

    ```python
    # Example 1: Successful sign-in
    result = sign_in_access_token(sign_in_data=SignInAccessTokenData(access_token="valid_token"))
    assert result == {'ApiFunResult': {'Result': {'success': True, 'message': 'Sign-in successful'}}}
    ```

    ```python
    # Example 2: Invalid access token
    result = sign_in_access_token(sign_in_data=SignInAccessTokenData(access_token="invalid_token"))
    assert result == {'ApiFunResult': {'Result': {'success': False, 'message': 'Invalid access token'}}}
    ```
    Args:
        sign_in_data: The sign-in data containing the access token.
        db_user: The user database instance. Defaults to db_user.

    Returns:
        A dictionary containing the result of the sign-in attempt.
    """
    response = db_user.sign_in_with_access_token(sign_in_data.access_token)

    return response


def remove_user(
    remove_user_data: RemoveUserData,
    db_user: UserDatabase = db_user,
) -> Dict[str, str]:
    """
    Remove a user from the database.

    Args:
        remove_user_data (RemoveUserData): The data containing the user's email address.
        user_db (UserDatabase, optional): The user database instance. Defaults to user_db.

    Returns:
        Dict[str, str]: A dictionary containing the result of the operation.
    """
    response = db_user.delete_user_account(remove_user_data.email_address)
    return response


def is_user_signed_up(
    is_user_signed_up_data: IsUserSignedUpData,
    db_user: UserDatabase = db_user,
) -> Dict[str, str]:
    """
    Check if a user is signed up.

    Args:
        is_user_signed_up_data (IsUserSignedUpData): The data containing the user's email address.
        user_db (UserDatabase, optional): The user database instance. Defaults to user_db.

    Returns:
        Dict[str, str]: A dictionary containing the result of the check.
    """
    response = db_user.is_user_signed_up(is_user_signed_up_data.email_address)
    return response


def get_dashboard_info(
    db_user: UserDatabase = db_user,
) -> Dict[str, str]:
    """
    Retrieves dashboard information including monthly revenue, number of paid users,
    number of free users, number of logged-in users, system RAM and server version.

    Will be called in fastapi main script.

    Returns:
        Dict[str, str]: A dictionary containing the dashboard information.
    """
    from tomsze_utils.text_file_parser_utils import parse_line_with_equal_to_dict

    num_paid_users = db_user.get_num_paid_users()
    num_free_users = db_user.get_num_free_users()
    monthly_revenue = get_monthly_revenue(num_paid_users)
    num_logged_in_users = db_user.get_num_logged_in_users()
    system_usage = get_system_usage()[-1]

    file_path = "./setup.py"
    full_path = os.path.realpath(file_path)
    dict_data = parse_line_with_equal_to_dict(full_file_path=full_path)
    server_version = dict_data["version"]

    response = ApiResponse.success_response(
        data={
            "monthly_revenue": monthly_revenue,
            "num_paid_users": num_paid_users,
            "num_free_users": num_free_users,
            "num_logged_in_users": num_logged_in_users,
            "system_ram": system_usage,
            "server_version": server_version,
        }
    )
    return response


def get_monthly_revenue(num_paid_users: int) -> float:
    """
    Calculates the total monthly revenue based on the number of paid users
    and the pricing information from the plans_list.

    Args:
        num_paid_users (int): The number of paid users.

    Returns:
        float: The total monthly revenue.
    """
    total_revenue = 0.0
    for plan in plans_list:
        if plan["name"] == "Free":
            # Assuming free plan has no revenue
            pass
        if plan["name"] == "Paided":
            price_str = plan["price"].split(" ")[0]
            try:
                price = float(price_str)
                total_revenue = price * num_paid_users  # Corrected calculation
            except ValueError:
                logger.info(f"Could not convert price to float: {price_str}")
                total_revenue = 0.0
            break  # Assuming only one paid plan
    return total_revenue


def validate_user_access_token(
    validate_user_access_token_data: ValidateUserAccessTokenData,
    db_user: UserDatabase = db_user,
) -> Dict[str, Dict[str, str]]:
    """
    Validates a user access token.

    Args:
        validate_user_access_token_data (ValidateUserAccessTokenData): An object containing the access token to validate.
        user_db (UserDatabase, optional): The user database instance. Defaults to user_db.

    Returns:
        Dict[str, Dict[str, str]]: A dictionary containing the validation result.

    Examples:
        ```python
        # Example 1: Successful validation
        validate_user_access_token(validate_user_access_token_data=ValidateUserAccessTokenData(access_token="valid_token"))
        # Expected Output: {ApiFunResponseKeys.Result: {"validate_result": "success"}}
        ```

        ```python
        # Example 2: Validation error
        validate_user_access_token(validate_user_access_token_data=ValidateUserAccessTokenData(access_token="invalid_token"))
        # Expected Output: {ApiFunResponseKeys.Result: {"validate_result": "error: Invalid token"}}
        ```
    """
    access_token = validate_user_access_token_data.access_token
    try:
        response = db_user.validate_access_token(token=access_token)
    except Exception as e:
        response = ApiResponse.error_response(error=str(e))

    return response


def obtain_new_access_token_by_refresh_token(
    obtain_new_access_token_by_refresh_tokendata: ObtainNewAccessTokenByRefreshTokenData,
    db_user: UserDatabase = db_user,
) -> Dict[str, Dict[str, str]]:
    """
    Obtains a new access token using a refresh token.

    Args:
        obtain_new_access_token_by_refresh_tokendata (ObtainNewAccessTokenByRefreshTokenData):
            An object containing the refresh token and email.
        db_user (UserDatabase, optional): The user database instance. Defaults to db_user.

    Returns:
        Dict[str, Dict[str, str]]: A dictionary containing the result of the operation.

    Examples:
        ```python
        # Example 1: Successful token retrieval
        obtain_new_access_token_by_refresh_token(
            obtain_new_access_token_by_refresh_tokendata=ObtainNewAccessTokenByRefreshTokenData(refresh_token="valid_refresh_token", email="<EMAIL>")
        )
        # Expected Output: {ApiFunResponseKeys.Result: {"access_token": "new_access_token", "refresh_token": "new_refresh_token"}}
        ```

        ```python
        # Example 2: Invalid refresh token
        obtain_new_access_token_by_refresh_token(
            obtain_new_access_token_by_refresh_tokendata=ObtainNewAccessTokenByRefreshTokenData(refresh_token="invalid_refresh_token", email="<EMAIL>")
        )
        # Expected Output: {ApiFunResponseKeys.Result: {"validate_result": "error: Invalid refresh token"}}
        ```
    """
    refresh_token = obtain_new_access_token_by_refresh_tokendata.refresh_token
    email = obtain_new_access_token_by_refresh_tokendata.email
    try:
        response = db_user.create_tokens_by_refresh_token(
            email=email,
            refresh_token=refresh_token,
        )
    except Exception as e:
        response = ApiResponse.error_response(error=str(e))

    return response


def obtain_verification_code_and_send_email(
    obtain_verification_code_data: data_class.ObtainVerificationCodeData,
    db_user: UserDatabase = db_user,
) -> Dict[str, Dict[str, str]]:
    """
    Retrieves a verification code and send verification code to the user's email.

    Args:
        get_verification_code_data (data_class.GetVerificationCodeData):
            An object containing the email.
        db_user (UserDatabase, optional): The user database instance. Defaults to db_user.

    Returns:
        Dict[str, Dict[str, str]]: A dictionary containing the result, which includes the verification code.
    """
    email = obtain_verification_code_data.email

    response = db_user.obtain_verification_code_and_send_email(
        email=email,
    )
    return response


# ==============utils=================
from typing import Dict, List, Tuple, Any


def validate_data_dict_item_empty(
    data_dict: Dict[str, Any], key_list: List[str]
) -> Tuple[bool, Dict[str, Dict[str, str]]]:
    """
    Validates if the specified keys in the data dictionary have non-empty values after stripping whitespace.

    ```python
    # Example 1: All keys have non-empty values
    data = {"name": "John", "email": "<EMAIL>"}
    keys = ["name", "email"]
    proceed, response = validate_data_dict_item_empty(data_dict=data, key_list=keys)
    # Expected Output: (True, {})
    ```

    ```python
    # Example 2: One key has an empty value
    data = {"name": " ", "email": "<EMAIL>"}
    keys = ["name", "email"]
    proceed, response = validate_data_dict_item_empty(data_dict=data, key_list=keys)
    # Expected Output: (False, {'Response': {'Error': 'name should not be empty'}})
    ```

    Args:
        data_dict (Dict[str, Any]): The dictionary to validate.
        key_list (List[str]): A list of keys to check for non-empty values.

    Returns:
        Tuple[bool, Dict[str, Dict[str, str]]]: A tuple containing:
            - A boolean indicating whether all specified keys have non-empty values (True) or not (False).
            - A dictionary containing an error message if any key has an empty value, otherwise an empty dictionary.
    """
    proceed = True
    response = {}

    for key in key_list:
        if data_dict[key].strip() == "":
            response = ApiResponse.error_response(error=f"{key} should not be empty")
            proceed = False
            break

    return proceed, response


# ==============dummy=================
def request_fake_resource(
    fake_request_data: FakeRequestData,
):
    result_str = validate_token(
        fake_request_data.access_token,
    )
    if result_str != "Valid token":
        return ApiResponse.error_response(error="Invalid token")

    return ApiResponse.success_response(data={"result": "Get data success"})


def dummy():
    logger.info("This is a dummy function from logger")


def main():
    # test_search_user_app()
    # test_search_user_app_on_missing_nltk_keyword_syn_that_need_scraping_powerthesaurus()
    pass


if __name__ == "__main__":
    main()
