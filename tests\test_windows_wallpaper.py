import os
import shutil
from tomsze_utils.windows_wallpaper_utils import Wallpaper


def test_windows_wallpaper():
    # Remove temp folder first.
    temp_folder_path = r"./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)
    os.mkdir(temp_folder_path)

    pil_image = Wallpaper.get(returnImgObj=True)
    save_path = r"./tests/temp/wallpaper.png"
    pil_image.save(save_path)

    assert os.path.exists(save_path) == True


if __name__ == "__main__":
    test_windows_wallpaper()
