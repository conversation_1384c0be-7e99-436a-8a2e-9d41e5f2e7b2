import sys
import os
from tomsze_utils.variable_dump_read_utils import (
    load_pickle_file,
    dump_variable_to_pickle,
)
from enums import DBColEnum


def main():
    script_directory_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )

    # --------------------
    embd_pickle_path = os.path.join(
        script_directory_path,
        "004_embeddings_all-MiniLM-L6-v2.pickle",
    )
    dict_embd = load_pickle_file(
        pickle_path=embd_pickle_path,
        default={},
    )

    print(f"len embedding: {len(dict_embd)}")
    # --------------------
    desc_pickle_path = os.path.join(
        script_directory_path,
        "004_descriptions_all-MiniLM-L6-v2.pickle",
    )
    dict_desc = load_pickle_file(
        pickle_path=desc_pickle_path,
        default={},
    )

    print(f"len description: {len(dict_desc)}")
    # --------------------
    ind_pickle_path = os.path.join(
        script_directory_path,
        "004_dict_ids_all-MiniLM-L6-v2.pickle",
    )
    dict_ind = load_pickle_file(
        pickle_path=ind_pickle_path,
        default={},
    )
    print(f"len index: {len(dict_ind)}")
    # --------------------
    keyw_pickle_path = os.path.join(
        script_directory_path,
        "004_keywords.pickle",
    )
    dict_keyw = load_pickle_file(
        pickle_path=keyw_pickle_path,
        default={},
    )
    print(f"len dict_keyword: {len(dict_keyw)}")
    # --------------------

    merged_pickle_path = os.path.join(
        script_directory_path,
        r"merged.pickle",
    )
    dict_merge = {}
    for app_id in dict_ind:
        item = {
            DBColEnum.IND: dict_ind[app_id],
            DBColEnum.DESC: dict_desc[app_id],
            DBColEnum.EMBD: dict_embd[app_id],
            DBColEnum.KEYW: dict_keyw[app_id],
        }
        dict_merge[app_id] = item

    dump_variable_to_pickle(
        dict_merge,
        merged_pickle_path,
    )


if __name__ == "__main__":
    sys.exit(main())
