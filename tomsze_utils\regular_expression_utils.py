from dataclasses import dataclass
import json
import re
import sys


def extract_docstring_descryption(docstring: str) -> str:
    """
    Cleans a docstring by removing sections starting from 'Args:', 'Returns:', or 'Examples:'.

    Args:
        docstring (str): The docstring to be cleaned.

    Returns:
        str: The cleaned docstring without the specified sections.

    Examples:
        ```python
        cleaned = extract_docstring_descryption('''
        This is a sample function.

        Args:
            arg1 (int): The first argument.
            arg2 (str): The second argument.
        ''')
        # cleaned will be "This is a sample function."
        ```

        ```python
        cleaned = extract_docstring_descryption('''
        This function does something important.

        Returns:
            bool: The return value.
        ''')
        # cleaned will be "This function does something important."
        ```

    Note:
        The function uses regular expressions to perform the cleaning.
    """
    return re.sub(
        r"(?s)(.*?)(?:Args:|Returns:|Examples:|Parameters:|Note:).*", r"\1", docstring
    ).strip()


def extract_first_tool_call(text: str, is_include_tag: bool = False) -> str:
    """Extracts the first <tool_call> tag and its content from the given text.

    Args:
        text (str): The input text containing <tool_call> tags.
        is_include_tag (bool): If True, includes the <tool_call> tags in the returned string.

    Returns:
        str: The content of the first <tool_call> tag, or an empty string if none is found.

    Examples:
        ```python
        result = extract_first_tool_call('''sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}
        </tool_call>
        fgdh
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}
        </tool_call>''')
        # result will be '{"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}'

        result_with_tag = extract_first_tool_call('''sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}
        </tool_call>
        fgdh''', is_include_tag=True)
        # result_with_tag will be '<tool_call>{"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}</tool_call>'
        ```
    """
    match = re.search(r"<tool_call>(.*?)</tool_call>", text, re.DOTALL)
    if match:
        return (
            f"<tool_call>{match.group(1).strip()}</tool_call>"
            if is_include_tag
            else match.group(1).strip()
        )
    return ""


def extract_first_tool_call_to_dict(text: str) -> dict:
    """Extracts the first <tool_call> tag and its content from the given text and returns it as a dictionary.

    Args:
        text (str): The input text containing <tool_call> tags.

    Returns:
        dict: A dictionary representation of the content of the first <tool_call> tag, or an empty dictionary if none is found.

    Examples:
        ```python
        result = extract_first_tool_call_to_dict('''sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}
        </tool_call>
        fgdh
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}
        </tool_call>''')
        # result will be {'name': 'calculator', 'arguments': {'a': 2, 'b': 2, 'operator': '+'}}
        ```
    """
    tool_call_content = extract_first_tool_call(text)
    if tool_call_content:
        import json

        return json.loads(tool_call_content)
    return {}


@dataclass
class ToolCallArgs:
    name: str
    arguments: dict


def extract_first_tool_call_to_ToolCallArgs(text: str) -> ToolCallArgs:
    """Extracts the first <tool_call> tag and its content from the given text and returns it as a ToolCallArgs object.

    Args:
        text (str): The input text containing <tool_call> tags.

    Returns:
        ToolCallArgs: An object containing the name and arguments extracted from the first <tool_call> tag,
        or None if no <tool_call> is found.

    Examples:
        ```python
        result = extract_first_tool_call_to_ToolCallArgs('''sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}
        </tool_call>''')
        # result will be ToolCallArgs(name='calculator', arguments={'a': 2, 'b': 2, 'operator': '+'})
        ```
    """
    tool_call_content = extract_first_tool_call(text)
    if tool_call_content:
        import json

        data = json.loads(tool_call_content)
        return ToolCallArgs(name=data["name"], arguments=data["arguments"])
    return None


def extract_tool_name_from_string(
    text: str,
    tool_prefix: str = "Tool to be used:",  # New argument for the tool prefix
    start_enclosing_string: str = "```",
    end_enclosing_string: str = "```",
) -> str:
    """Extracts the tool name from a given string.

    Args:
        text (str): The input string containing the tool name.
        tool_prefix (str): The string that precedes the tool name. Defaults to 'Tool to be used:'.
        start_enclosing_string (str): The string that starts the enclosing for the tool name. Defaults to '```'.
        end_enclosing_string (str): The string that ends the enclosing for the tool name. Defaults to '```'.

    Returns:
        str: The extracted tool name, or an empty string if not found.

    Examples:
        ```python
        tool_name = extract_tool_name_from_string("Tool to be used: ```calculator```")
        # tool_name will be 'calculator'

        tool_name = extract_tool_name_from_string("Tool to be used: {{calculator}}", "{{", "}}")
        # tool_name will be 'calculator'
        ```
    """
    import re

    pattern = rf"{re.escape(tool_prefix)} {re.escape(start_enclosing_string)}(.*?){re.escape(end_enclosing_string)}"
    match = re.search(pattern, text)
    return match.group(1) if match else ""


@dataclass
class ToolCallArgs:
    name: str
    arguments: dict


def extract_tool_call_args_from_completion_message_content(
    message_content_str: str,
) -> ToolCallArgs:
    """Extracts tool call arguments from a completion message.

    Args:
        message_content_str (str): The string containing the tool call data.

    Returns:
        toolCallArgs: An object containing the tool name and its arguments, or None if no arguments are found.

    Examples:
        ```python
        result = extract_tool_call_args_from_completion_message_content(
            message_content_str='tool_call (write_text_to_file) with arguments {"file_path": "example.txt", "text": "Hello, World!"} abc'
        )
        # result will be toolCallArgs(name="write_text_to_file", arguments={"file_path": "example.txt", "text": "Hello, World!"})

        result = extract_tool_call_args_from_completion_message_content(
            message_content_str='tool_call (calculate_sum) with arguments {"a": 5, "b": 10}'
        )
        # result will be toolCallArgs(name="calculate_sum", arguments={"a": 5, "b": 10})
        ```
    """
    tool_name = extract_tool_name_from_string(
        message_content_str,
        tool_prefix="tool_call",
        start_enclosing_string="(",
        end_enclosing_string=")",
    )

    try:
        arguments = json.loads(message_content_str)
    except Exception as e:
        # Check if the arguments JSON exists in the message content
        if '{"' in message_content_str:
            arguments_start = message_content_str.index('{"')
            arguments_end = (
                message_content_str.rindex("}") + 1
            )  # Changed to use rindex for the last }
            arguments = json.loads(message_content_str[arguments_start:arguments_end])
        else:
            return None  # Return None if no arguments are found

    return ToolCallArgs(name=tool_name, arguments=arguments)


def main():
    docstring = """
    Extracts function names and their corresponding docstrings from all Python scripts
    in a specified directory.

    Args:
        script_dir (str): The path to the directory containing Python scripts.
        ignore_class_functions (bool): A flag indicating whether to ignore functions
        defined within classes. Defaults to True.

    Returns:
        List[FunctionMeta]: A list of FunctionMeta objects containing function names
        and their docstrings.

    Examples:
        ```python
        # Example 1: Extracting function names while ignoring class functions
        function_meta_list = extract_function_names_from_script_in_dir("path/to/scripts", ignore_class_functions=True)

        # Example 2: Extracting function names including class functions
        function_meta_list = extract_function_names_from_script_in_dir("path/to/scripts", ignore_class_functions=False)
        ```
"""
    cleaned_docstring = extract_docstring_descryption(docstring)
    print(cleaned_docstring)


if __name__ == "__main__":
    sys.exit(main())
