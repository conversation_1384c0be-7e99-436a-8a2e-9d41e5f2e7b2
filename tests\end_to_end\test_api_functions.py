import datetime
import tempfile
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from tomsze_utils.logger_utils import SimpleLogger

from server.api.end_to_end.api_constant_keys import (
    ApiFunResponseKeys,
    ClientColEnum,
    DBAppsColEnum,
    DBEmailVericiationColEnum,
    DBToAddColEnum,
    DBUserDataColEnum,
)

logger = SimpleLogger(
    logs_folder_path="./tests/test_api_function_log",
    log_base_name="test_api_function",
    new_file_for_each="month",
    get_existing_instance=False,
)

import os
import shutil
from typing import List
import pytest
from tomsze_utils.env_parser_utils import parse_env
from tomsze_utils.auth_utils import hash_password, create_access_token
from server.api.utils.generate_vectors.utils import scrap_play_store

from server.api.end_to_end.api_functions import (
    confirm_verify_code,
    request_fake_resource,
    search_user_app,
    sign_in,
    sign_up,
    update_server_db_apps_and_db_to_add,
    check_client_apps_need_scrape_and_update_db_to_add,
    get_dashboard_info,
)
from server.api.end_to_end.data_class import (
    ClientApps,
    ClientSearchData,
    CodeVerifyData,
    FakeRequestData,
    ClientAppsToUpdate,
    SignInData,
    SignupData,
)
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from const_path import (
    proj_ser_api_db_apps_small_path,
    proj_ser_api_db_synonyms_small_path,
    proj_ser_api_db_synonyms_need_powerthesaurus_small_path,
    proj_ser_api_db_to_add_small_path,
    proj_tests_end_to_end_path,
)


@pytest.fixture(scope="function")
def tmp_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


def setup_db_apps(
    test_app_id,
    is_test_id_in_db_apps,
    db_fpath,
):
    db_apps = None
    apps_list = ["com.whatsapp", "com.mcdonalds.app"]
    if is_test_id_in_db_apps:
        apps_list.append(test_app_id)

    db_apps = fill_db_apps(
        db_fpath=db_fpath,
        apps_list=apps_list,
    )

    return db_apps


def setup_db_to_add(
    test_app_id,
    is_test_id_in_db_to_add,
    db_fpath,
):
    db_to_add = None
    apps_to_add_list = ["com.google.android.apps.maps"]
    if is_test_id_in_db_to_add:
        apps_to_add_list.append(test_app_id)

    db_to_add = fill_db_to_add(
        db_fpath=db_fpath,
        apps_list=apps_to_add_list,
    )

    return db_to_add


def fill_db_to_add(
    db_fpath: str,
    apps_list: List,
    not_found_playstore_list_str: List = [],
):
    db_to_add = PickleDatabaseSplit(
        db_fpath=db_fpath,
        db_name="db_to_add",
        load_by_thread=False,  # not use thread to load this moment for faster develop
        part_data_count=2,
    )

    ind = 0
    for app in apps_list:
        if not_found_playstore_list_str and len(not_found_playstore_list_str) == len(
            apps_list
        ):
            not_found_playstore_str = not_found_playstore_list_str[ind]
        else:
            not_found_playstore_str = ""

        db_to_add.update_data_with_keys(
            keys=[app, DBToAddColEnum.CHECK_DATE_TIME],
            data="test datetime",
        )
        db_to_add.update_data_with_keys(
            keys=[app, DBToAddColEnum.NOT_FOUND_PLAYSTORE],
            data=not_found_playstore_str,
        )
        # Note: No need to add embedding to test..

        ind += 1

    return db_to_add


def fill_db_apps(
    db_fpath: str,
    apps_list: List,
):
    db_apps = PickleDatabaseSplit(
        db_fpath=db_fpath,
        db_name="db_app",
        load_by_thread=False,  # not use thread to load this moment for faster develop
        part_data_count=2,
    )
    for app in apps_list:
        db_apps.update_data_with_keys(
            keys=[app, DBAppsColEnum.DESC],
            data="test desc",
        )
        db_apps.update_data_with_keys(
            keys=[app, DBAppsColEnum.KEYW],
            data=["test", "keyw"],
        )
        # Note: No need to add embedding to test..

    return db_apps


def assert_db_apps(
    test_app_id,
    db_apps: PickleDatabaseSplit,
    is_final_test_key_in_db_apps,
    final_num_keys,
):
    if is_final_test_key_in_db_apps:
        assert db_apps.is_key_in(test_app_id) == True
        assert (
            db_apps.query_key_col(key=test_app_id, col=DBAppsColEnum.DESC, default="")
            != ""
        )
        assert (
            db_apps.query_key_col(key=test_app_id, col=DBAppsColEnum.KEYW, default=[])
            != []
        )
    else:
        assert db_apps.is_key_in(test_app_id) == False
        assert (
            db_apps.query_key_col(key=test_app_id, col=DBAppsColEnum.DESC, default="")
            == ""
        )
        assert (
            db_apps.query_key_col(key=test_app_id, col=DBAppsColEnum.KEYW, default=[])
            == []
        )

    assert db_apps.get_db_num_keys() == final_num_keys


def assert_db_to_add(
    test_app_id,
    db_to_add: PickleDatabaseSplit,
    scrape_success,
    is_final_test_key_in_db_to_add,
):
    if is_final_test_key_in_db_to_add:
        assert db_to_add.is_key_in(test_app_id) == True
    else:
        assert db_to_add.is_key_in(test_app_id) == False

    if not scrape_success:
        assert db_to_add.query_key_col(test_app_id, "not_found_playstore") == "true"


@pytest.mark.parametrize(
    """
    test_app_id, 
    scrape_success, 
    is_test_id_in_db_apps, 
    is_test_id_in_db_to_add, 
    is_final_test_id_in_db_apps, 
    is_final_test_id_in_db_to_add, 
    final_num_keys
    """,
    [
        # scrape success cases
        ("com.rovio.BadPiggies", True, True, True, True, False, 3),
        ("com.rovio.BadPiggies", True, True, False, True, False, 3),
        ("com.rovio.BadPiggies", True, False, True, True, False, 3),
        ("com.rovio.BadPiggies", True, False, False, True, False, 3),
        # scrape fail cases
        ("com.rovio.BadPiggies", False, True, True, True, True, 3),
        ("com.rovio.BadPiggies", False, True, False, True, True, 3),
        ("com.rovio.BadPiggies", False, False, True, False, True, 2),
        ("com.rovio.BadPiggies", False, False, False, False, True, 2),
    ],
)
def test_update_server_db_apps_and_db_to_add(
    test_app_id,
    scrape_success,
    is_test_id_in_db_apps,
    is_test_id_in_db_to_add,
    is_final_test_id_in_db_apps,
    is_final_test_id_in_db_to_add,
    final_num_keys,
):
    # Create temp database folder.
    db_apps_folder_path = os.path.join(proj_tests_end_to_end_path, "db_apps")
    db_to_add_folder_path = os.path.join(proj_tests_end_to_end_path, "db_to_add")
    if os.path.exists(db_apps_folder_path):
        shutil.rmtree(db_apps_folder_path)
    if os.path.exists(db_to_add_folder_path):
        shutil.rmtree(db_to_add_folder_path)
    os.mkdir(db_apps_folder_path)
    os.mkdir(db_to_add_folder_path)

    # Test on a valid package name.
    client_apps_to_update = ClientAppsToUpdate()
    client_apps_to_update.appIds.append(test_app_id)

    _, result = scrap_play_store(
        device_app_id=test_app_id,
        lang="en",
    )

    if scrape_success:
        result["exception"] = ""  # simulate scrape result from phone/user.
    else:
        result["exception"] = "sim fake scrape exception"

    client_apps_to_update.scrape_results.append(result)
    client_apps_to_update = client_apps_to_update.dict()

    db_apps = setup_db_apps(
        test_app_id=test_app_id,
        is_test_id_in_db_apps=is_test_id_in_db_apps,
        db_fpath=db_apps_folder_path,
    )

    db_to_add = setup_db_to_add(
        test_app_id=test_app_id,
        is_test_id_in_db_to_add=is_test_id_in_db_to_add,
        db_fpath=db_to_add_folder_path,
    )

    result = update_server_db_apps_and_db_to_add(  # <<< main function to test
        client_apps_to_update,
        db_apps=db_apps,
        db_to_add=db_to_add,
    )

    assert_db_apps(
        test_app_id=test_app_id,
        db_apps=db_apps,
        is_final_test_key_in_db_apps=is_final_test_id_in_db_apps,
        final_num_keys=final_num_keys,
    )

    assert_db_to_add(
        test_app_id=test_app_id,
        db_to_add=db_to_add,
        scrape_success=scrape_success,
        is_final_test_key_in_db_to_add=is_final_test_id_in_db_to_add,
    )

    db_apps.close_lock_file()
    db_to_add.close_lock_file()
    if os.path.exists(db_apps_folder_path):
        shutil.rmtree(db_apps_folder_path)
    if os.path.exists(db_to_add_folder_path):
        shutil.rmtree(db_to_add_folder_path)


def test_search_user_app():
    # in db: com.whatsapp,  com.mcdonalds.app,
    debug_mode = True
    client_search_data = ClientSearchData()
    client_search_data.allowSearch = True
    client_search_data.appIds.append("com.whatsapp")  # already exists in database
    client_search_data.appIds.append("com.miniclip.footballstrike")
    client_search_data.query = "chats"
    client_search_data = client_search_data.dict()

    db_apps = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_apps_small_path,
        db_name="db_app",
        load_by_thread=False,  # not use thread to load this moment for faster develop
    )
    db_synonyms = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_synonyms_small_path,
        db_name="db_synonyms",
        load_by_thread=False,  # not use thread to load this moment for faster develop
    )

    db_synonyms_need_powerthesaurus = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_synonyms_need_powerthesaurus_small_path,
        db_name="db_synonyms_need_powerthesaurus",
        load_by_thread=False,
    )

    result = search_user_app(
        client_search_data=client_search_data,
        debug_mode=debug_mode,
        db_apps=db_apps,
        db_synonyms=db_synonyms,
        db_synonyms_need_powerthesaurus=db_synonyms_need_powerthesaurus,
    )
    expect_first_app_id = "com.whatsapp"
    expect_score = 0.33
    assert result[ApiFunResponseKeys.DEBUG_SIM][0][0] == expect_first_app_id
    assert result[ApiFunResponseKeys.DEBUG_SIM][0][1] > expect_score

    db_apps.close_lock_file()
    db_apps.close_lock_file()
    db_synonyms_need_powerthesaurus.close_lock_file()


def test_search_user_app_on_missing_nltk_keyword_syn_that_need_scraping_powerthesaurus():

    # in db: com.whatsapp,  com.mcdonalds.app,
    debug_mode = True
    client_search_data = ClientSearchData()
    client_search_data.allowSearch = True
    client_search_data.appIds.append("com.whatsapp")  # already exists in database
    client_search_data.appIds.append("com.miniclip.footballstrike")
    client_search_data.query = "chats"
    client_search_data = client_search_data.dict()

    db_apps = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_apps_small_path,
        db_name="db_app",
        load_by_thread=False,
    )
    # This db_synonyms only has 'chat'
    db_synonyms = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_synonyms_small_path,
        db_name="db_synonyms",
        load_by_thread=False,
    )

    db_synonyms_need_powerthesaurus = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_synonyms_need_powerthesaurus_small_path,
        db_name="db_synonyms_need_powerthesaurus",
        load_by_thread=False,
    )

    result = search_user_app(
        client_search_data=client_search_data,
        debug_mode=debug_mode,
        db_apps=db_apps,
        db_synonyms=db_synonyms,
        db_synonyms_need_powerthesaurus=db_synonyms_need_powerthesaurus,
    )

    assert (
        len(db_synonyms_need_powerthesaurus.loaded_parts[0]) > 0
    )  # Since db_synonyms does not have other keywords.
    assert not db_synonyms_need_powerthesaurus.query_key(key="chat")
    assert not db_synonyms_need_powerthesaurus.query_key(key="chats")


# # @pytest.mark.xfail(reason='not yet implemented')
# def test_receive_user_installed_apps_where_has_new_app():
#     # Has new app
#     client_apps = ClientApps()
#     client_apps.appIds.append("com.miniclip.eightballpool")
#     client_apps.appIds.append("com.miniclip.footballstrike")
#     client_apps = client_apps.dict()

#     server_apps_ids = ServerApps()
#     server_apps_ids.appIds["com.supercell.clashroyale"] = 1
#     server_apps_ids_pickle_path = os.path.join(  # TODO: refactor
#         proj_serv_test_end_to_end_path,
#         r"server_app_ids_test_save.pickle",
#     )
#     server_apps_ids = server_apps_ids.appIds

#     expect_new_apps = {
#         "com.miniclip.eightballpool": 1,
#         "com.miniclip.footballstrike": 1,
#     }
#     expect_is_new = True
#     response = receive_user_installed_apps(
#         client_apps,
#         server_apps_ids,
#         server_apps_ids_pickle_path,
#     )
#     assert response.new_apps == expect_new_apps
#     assert response.is_new == expect_is_new
#     assert os.path.exists(server_apps_ids_pickle_path) == True

#     # Remove test save pickle file.
#     if os.path.exists(server_apps_ids_pickle_path):
#         os.remove(server_apps_ids_pickle_path)


# def test_receive_user_installed_apps_where_has_no_new_app():
#     # No new app
#     client_apps = ClientApps()
#     client_apps.appIds.append("com.gameloft.anmp.sniper.champions")
#     client_apps = client_apps.dict()

#     server_apps_ids = ServerApps()
#     server_apps_ids.appIds["com.gameloft.anmp.sniper.champions"] = 1
#     server_apps_ids_pickle_path = os.path.join(  # TODO: refactor
#         proj_serv_test_end_to_end_path,
#         r"server_app_ids_test_save.pickle",
#     )
#     server_apps_ids = server_apps_ids.appIds

#     expect_new_apps = {}
#     expect_is_new = False
#     response = receive_user_installed_apps(
#         client_apps,
#         server_apps_ids,
#         server_apps_ids_pickle_path,
#     )

#     assert response.new_apps == expect_new_apps
#     assert response.is_new == expect_is_new
#     assert os.path.exists(server_apps_ids_pickle_path) == False


def test_search_user_app_not_allowed():
    debug_mode = True
    client_search_data = ClientSearchData()
    client_search_data.allowSearch = False
    client_search_data.query = "chats"
    client_search_data = client_search_data.dict()

    db_apps = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_apps_small_path,
        db_name="db_app",
        load_by_thread=False,  # not use thread to load this moment for faster develop
    )
    db_synonyms = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_synonyms_small_path,
        db_name="db_synonyms",
        load_by_thread=False,  # not use thread to load this moment for faster develop
    )

    db_synonyms_need_powerthesaurus = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_synonyms_need_powerthesaurus_small_path,
        db_name="db_synonyms_need_powerthesaurus",
        load_by_thread=False,
    )

    result = search_user_app(
        client_search_data=client_search_data,
        debug_mode=debug_mode,
        db_apps=db_apps,
        db_synonyms=db_synonyms,
        db_synonyms_need_powerthesaurus=db_synonyms_need_powerthesaurus,
    )
    expect = {ClientColEnum.ALLOWSEARCH: False}
    assert result == expect


@pytest.mark.parametrize(
    """
    client_app_ids_list,
    db_apps_list,
    db_to_add_list,
    not_found_playstore_list_str,
    expect_need_scrape_app_list,
    expect_keys_in_db_to_add,
    """,
    [
        # B in db_apps, no apps need to scrape
        (["B"], ["B", "C"], [], [], [], []),
        # "D" not in db_apps,
        (["D"], ["B", "C"], [], [], ["D"], ["D"]),
        # "D" not in db_apps, "A" is in db_to_add
        (["D"], ["B", "C"], ["A"], [""], ["D"], ["A", "D"]),
        # "A" not in db_apps
        (["A", "B"], ["B", "C"], [], [], ["A"], ["A"]),
        # "A" not in db_apps, "A" is in db_to_add
        (["A", "B"], ["B", "C"], ["A"], [""], ["A"], ["A"]),
        # "A" not in db_apps, "A" is in db_to_add, but not_found_play is "true"
        (["A", "B"], ["B", "C"], ["A"], ["true"], [], ["A"]),
        # "A" and "D" not in db_apps, "A" and "D" are in db_to_add, but "A" 's not_found_play is "true"
        (["A", "B", "D"], ["B", "C"], ["A", "D"], ["true", ""], ["D"], ["A", "D"]),
    ],
)
def test_check_client_apps_need_scrape_and_update_db_to_add(
    client_app_ids_list,
    db_apps_list,
    db_to_add_list,
    not_found_playstore_list_str,
    expect_need_scrape_app_list,
    expect_keys_in_db_to_add,
):
    clientApps = ClientApps()
    clientApps.allowSearch = True
    clientApps.appIds = client_app_ids_list
    clientApps.downloadSource = []

    db_apps = fill_db_apps(db_fpath=None, apps_list=db_apps_list)
    db_to_add = fill_db_to_add(
        db_fpath=None,
        apps_list=db_to_add_list,
        not_found_playstore_list_str=not_found_playstore_list_str,
    )

    response = check_client_apps_need_scrape_and_update_db_to_add(
        client_apps=clientApps.dict(),
        db_apps=db_apps,
        db_to_add=db_to_add,
        dump=False,
    )

    result_need_scrape_app_list = response[ApiFunResponseKeys.RESULT]["packageNames"]
    assert result_need_scrape_app_list == expect_need_scrape_app_list

    result_keys_in_db_to_add = db_to_add.get_db_keys()
    assert expect_keys_in_db_to_add == result_keys_in_db_to_add


def test_sign_up_normal(tmp_path):
    env_dict = parse_env("./.env_email")
    email_address = env_dict["RECEIVER_EMAIL"]
    hased_password = hash_password("123")
    sign_up_data = SignupData(
        email_address=email_address,
        password=hased_password,
    )
    db_email_verification = PickleDatabaseSplit(
        db_name="db_email_verification",
        db_fpath=tmp_path,
    )

    db_user = PickleDatabaseSplit(
        db_name="db_user",
        db_fpath=tmp_path,
    )

    response = sign_up(
        sign_up_data=sign_up_data,
        db_email_verification=db_email_verification,
        db_user=db_user,
    )

    assert (
        db_email_verification.query_by_keys_list(
            keys_list=[
                email_address,
                DBEmailVericiationColEnum.VERIFICATION_CODE,
            ]
        )[0]
        is not None
    )

    assert (
        db_user.query_by_keys_list(
            keys_list=[
                email_address,
                DBUserDataColEnum.IS_VERIFIED,
            ]
        )[0]
        == False
    )
    assert response == {"result": "success"}


class TestConfirmVerifyCode:

    def test_confirm_verify_code_success(self, tmp_path):
        email_address = "<EMAIL>"
        verification_code = "123456"
        db_email_verification = PickleDatabaseSplit(
            db_name="db_email_verification",
            db_fpath=tmp_path,
        )
        db_user = PickleDatabaseSplit(
            db_name="db_user",
            db_fpath=tmp_path,
        )

        # Set up the test data
        db_email_verification.update_data_with_keys(
            keys=[email_address, DBEmailVericiationColEnum.VERIFICATION_CODE],
            data=verification_code,
        )
        db_user.update_data_with_keys(
            keys=[email_address, DBUserDataColEnum.IS_VERIFIED],
            data=False,
        )

        code_verify_data = CodeVerifyData(
            email_address=email_address,
            verification_code=verification_code,
            hashed_password="hashed_password_example",
        )

        response = confirm_verify_code(
            code_verify_data=code_verify_data,
            db_email_verification=db_email_verification,
            db_user=db_user,
        )

        assert response == {"result": "success"}
        assert (
            db_user.query_by_keys_list(
                keys_list=[email_address, DBUserDataColEnum.IS_VERIFIED]
            )[0]
            is True
        )

    def test_confirm_verify_code_wrong_code(self, tmp_path):
        email_address = "<EMAIL>"
        verification_code = "111111"
        db_email_verification = PickleDatabaseSplit(
            db_name="db_email_verification",
            db_fpath=tmp_path,
        )
        db_user = PickleDatabaseSplit(
            db_name="db_user",
            db_fpath=tmp_path,
        )

        db_email_verification.update_data_with_keys(
            keys=[
                email_address,
                DBEmailVericiationColEnum.VERIFICATION_CODE,
            ],
            data="123456",
        )
        db_user.update_data_with_keys(
            keys=[email_address, DBUserDataColEnum.IS_VERIFIED],
            data=False,
        )

        code_verify_data = CodeVerifyData(
            email_address=email_address,
            verification_code=verification_code,
            hashed_password="hashed_password_example",
        )

        response = confirm_verify_code(
            code_verify_data=code_verify_data,
            db_email_verification=db_email_verification,
            db_user=db_user,
        )

        assert response == {
            "result": f"User email {email_address} verification code does not match, click sign up to try again."
        }

        assert (
            db_user.query_by_keys_list(
                keys_list=[email_address, DBUserDataColEnum.IS_VERIFIED],
                default=None,
            )[0]
            is False,
        )


class TestSignIn:
    def test_sign_in_success(self, tmp_path):
        email_address = "<EMAIL>"
        password = "password_example"

        sign_in_data = SignInData(
            email_address=email_address,
            password=password,
        )

        db_user = PickleDatabaseSplit(
            db_name="db_user",
            db_fpath=tmp_path,
        )

        db_user.update_data_with_keys(
            keys=[email_address, DBUserDataColEnum.IS_VERIFIED],
            data=True,
        )

        db_user.update_data_with_keys(
            keys=[email_address, DBUserDataColEnum.HASHED_PASSWORD],
            data=hash_password(password),
        )

        response = sign_in(
            sign_in_data=sign_in_data,
            db_user=db_user,
        )

        # check the response contain access token
        assert "access_token" in response.get("result")

    def test_sign_in_with_user_not_signed_up(self, tmp_path):
        email_address = "<EMAIL>"
        hashed_password = "hashed_password_example"

        sign_in_data = SignInData(
            email_address=email_address,
            hashed_password=hashed_password,
        )

        db_user = PickleDatabaseSplit(
            db_name="db_user",
            db_fpath=tmp_path,
        )

        db_user.update_data_with_keys(
            keys=[email_address, DBUserDataColEnum.IS_VERIFIED],
            data=False,
        )

        response = sign_in(
            sign_in_data=sign_in_data,
            db_user=db_user,
        )

        # check the response contain access token
        assert "has not verified yet" in response.get("result")

    def test_sign_in_with_email_not_verified(self, tmp_path):
        email_address = "<EMAIL>"
        hashed_password = "hashed_password_example"

        sign_in_data = SignInData(
            email_address=email_address,
            hashed_password=hashed_password,
        )

        db_user = PickleDatabaseSplit(
            db_name="db_user",
            db_fpath=tmp_path,
        )

        response = sign_in(
            sign_in_data=sign_in_data,
            db_user=db_user,
        )

        # check the response contain access token
        assert "has not been signed up" in response.get("result")


class TestRequestResource:
    def test_request_resource_success(self, tmp_path):
        access_token = create_access_token(data={"a": "1"})
        fake_request_data = FakeRequestData(
            access_token=access_token,
            resource_item="data_x",
        )

        response = request_fake_resource(
            fake_request_data=fake_request_data,
        )

        assert response.get("result", None) == "Get data success"

    def test_request_resource_with_timeout_access_token(self, tmp_path):
        specific_time = datetime.datetime(2000, 11, 19, 15, 30)
        access_token = create_access_token(
            data={"a": "1"},
            current_datetime=specific_time,
        )
        fake_request_data = FakeRequestData(
            access_token=access_token,
            resource_item="data_x",
        )

        with pytest.raises(HTTPException) as exc_info:
            response = request_fake_resource(
                fake_request_data=fake_request_data,
            )
            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert exc_info.value.detail == "Could not validate credentials"

            assert response.get("result", None) == "Invalid token"


from tomsze_utils.database_utils import user_database
from tomsze_utils.database_utils.user_database import UserDatabase


class TestSignIn:
    def test_sign_in_success(self, temp_dir_path):
        # Arrange
        email = "<EMAIL>"
        password = "securepassword"
        db_user = UserDatabase(
            db_fpath=temp_dir_path
        )  # Use UserDatabase instead of PickleDatabaseSplit
        db_user.sign_up(email, password)  # Simulate user sign up
        sign_in_data = SignInData(email_address=email, password=password)

        # Act
        response = sign_in(sign_in_data=sign_in_data, db_user=db_user)

        # Assert
        assert "access_token" in response.get(ApiFunResponseKeys.RESULT)

    def test_sign_in_invalid_credentials(self, temp_dir_path):
        # Arrange
        email = "<EMAIL>"
        password = "wrongpassword"
        db_user = UserDatabase(
            db_fpath=temp_dir_path
        )  # Use UserDatabase instead of PickleDatabaseSplit
        db_user.sign_up(email, "securepassword")  # Simulate user sign up
        sign_in_data = SignInData(email_address=email, password=password)

        # Act
        response = sign_in(sign_in_data=sign_in_data, db_user=db_user)

        # Assert
        asf = (
            response.get(ApiFunResponseKeys.RESULT)
            .get(user_database.ResponseKeys.RESULT)
            .get(user_database.ResponseKeys.SUCCESS)
        )
        assert False == (
            response.get(ApiFunResponseKeys.RESULT)
            .get(user_database.ResponseKeys.RESULT)
            .get(user_database.ResponseKeys.SUCCESS)
        )
        assert "Incorrect password" in response.get(ApiFunResponseKeys.RESULT).get(
            user_database.ResponseKeys.RESULT
        ).get(user_database.ResponseKeys.MESSAGE)


class TestDashboardInfo:
    def test_get_dashboard_info(self, temp_dir_path):
        # Arrange
        email = "<EMAIL>"
        password = "securepassword"
        db_user = UserDatabase(
            db_fpath=temp_dir_path
        )  # Use UserDatabase instead of PickleDatabaseSplit

        response = db_user.send_verification_email(
            "<EMAIL>",
            is_really_send_email=False,
        )

        verification_code = db_user._get_user_verification_code("<EMAIL>")
        db_user.sign_up(email, password, verification_code)  # Simulate user sign up

        dashboard_info = get_dashboard_info(db_user)
        assert dashboard_info is not None


if __name__ == "__main__":
    # test_update_server_apps()
    # test_search_user_app()
    # test_search_user_app_not_allowed()
    # test_update_server_db_apps_and_db_to_add()
    # test_check_client_apps_need_scrape_and_update_db_to_add()
    test_search_user_app_on_missing_nltk_keyword_syn_that_need_scraping_powerthesaurus()
    # pass
