# Python verion
3.10.0

# Preparations.
- Buy a domain name.
- Buy a remote server (or local server) and obtain its public ip.
- Create a Cloudflare tunnel on the remote server on localhost:8000 (must match to the one in docker-compose.yml).
- Manually save your local machine's public ssh key to remote server's /root/.ssh/authorized_keys (so you and github actions can log in to the remote server)
- Set image name in docker-compose.yml

# To generate requirements.txt and copy local package wheel file to here.
pip istall pipreqs
Open create_update_requirements.py and setup whl path.
python create_update_requirements.py.

# To Build image and restart container locally.
Run command "docker-compose up --build --force-recreate"
