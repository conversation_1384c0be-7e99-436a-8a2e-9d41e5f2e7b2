"""App plugin"""

from dataclasses import dataclass
from tomsze_utils.plugins.constant.plugin_constants import (
    PLUGIN_LOOP_COUNTER_VAR_NAME,
    PLUGIN_LOOP_LIST_INDEX_VAR_NAME,
    PLUGIN_LOOP_LIST_ITEM_VAR_NAME,
)
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginLoopEnder:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        which_starter_step = parse_data_and_store(
            logger,
            "which_starter_step",
            data_obj,
            step_config,
            config,
        )

        assert which_starter_step in data_obj.list_steps

        # Set next step to the one below the starter_step and
        # decrease the loop counter.
        if (
            data_obj.dict_var.get(
                f"{which_starter_step}.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None
            )
            > 0
        ):

            data_obj.hijack_next_step_index = True
            data_obj.next_step_index = data_obj.list_steps.index(which_starter_step) + 1
            data_obj.dict_var[
                f"{which_starter_step}.{PLUGIN_LOOP_COUNTER_VAR_NAME}"
            ] -= 1

            starter_loop_list = parse_data_and_store(
                logger,
                "loop_list",
                data_obj,
                data_obj.dict_step_config[which_starter_step],
                config,
            )

            # Set loop list item for this looper pair.
            if (
                f"{which_starter_step}.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}"
                in data_obj.dict_var
            ):
                data_obj.dict_var[
                    f"{which_starter_step}.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}"
                ] += 1

                if data_obj.dict_var[
                    f"{which_starter_step}.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}"
                ] < len(
                    starter_loop_list
                ):  # Note: +1 already above so this is correct check.
                    data_obj.dict_var[
                        f"{which_starter_step}.{PLUGIN_LOOP_LIST_ITEM_VAR_NAME}"
                    ] = starter_loop_list[
                        data_obj.dict_var[
                            f"{which_starter_step}.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}"
                        ]
                    ]

        # Follow above and check again.
        if (
            data_obj.dict_var.get(
                f"{which_starter_step}.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None
            )
            == 0
        ):

            data_obj.hijack_next_step_index = False
            data_obj.next_step_index = data_obj.list_steps.index(current_step)

            # Update when used loop_list.
            if (
                f"{which_starter_step}.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}"
                in data_obj.dict_var
            ):
                data_obj.dict_var[
                    f"{which_starter_step}.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}"
                ] -= 1
                data_obj.dict_var[
                    f"{which_starter_step}.{PLUGIN_LOOP_LIST_ITEM_VAR_NAME}"
                ] = None

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
