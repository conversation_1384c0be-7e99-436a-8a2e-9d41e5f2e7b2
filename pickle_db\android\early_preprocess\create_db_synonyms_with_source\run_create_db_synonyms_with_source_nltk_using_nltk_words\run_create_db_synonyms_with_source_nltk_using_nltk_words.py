import sys
import os
import spacy
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.database_utils.run_pickle_database_split import remove_pickles
from tqdm import tqdm
from server.api.pickle_db.android.early_preprocess.create_db_synonyms_with_source.create_db_synonyms_with_source_utils import (
    create_db_synonyms_with_nltk_source,
)
import jsonlines
from server.api.utils.keywords.get_keywords import lemmatization
from nltk.corpus import words


def run_create_db_synonyms_with_source_nltk_using_nltk_words():
    """
    Create db_synonyms (10000 words for each part)
    Create nlp_model for lemmatization

    Get words from nltk (around 23 0000 words, but some have no synonyms)

    For each word in the dict
        Lemmatize the word.
        Get synonyms
        Update the database.

    Dump to file.
    """

    script_directory_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )

    # Remove pickles
    remove_pickles(script_directory_path)

    # Create a db synonyms (in current dir)
    db_synonyms = PickleDatabaseSplit(
        db_fpath=script_directory_path,
        db_name="db_syns",
        load_by_thread=False,
    )
    nlp_model = spacy.load("en_core_web_sm", disable=["parser", "ner"])

    # Get words from nltk
    word_list = words.words()

    # For each word in the dict
    #     Lemmatize the word.
    #     Get synonyms
    #     Update the database.
    create_db_synonyms_with_nltk_source(
        db_synonyms=db_synonyms,
        nlp_model=nlp_model,
        keywords=word_list,
    )

    # Dump to file.
    db_synonyms.dump_dirty_parts_to_pickles_thread()

    # Dump to a json (for manual checking)
    db_synonyms.to_json()


def main():
    run_create_db_synonyms_with_source_nltk_using_nltk_words()


if __name__ == "__main__":
    sys.exit(main())
