@startuml User Sign up
' using the teoz rendering engine
!pragma teoz true

' client side
box "client side"
    participant "user" as user
    ' client side app
    box "App" #LightBlue
        participant "Get\n button" as get_button
        participant "sign\n up\n button" as sign_up_button
        participant "code\n textfield" as code_textfield
    end box
    ' client side email
    participant "verification\n email" as verification_email
end box

' server side
box "server side"
    ' fastapi
    box "fastapi" #LightBlue
        participant "obtain\n verification\n code\n endpoint" as obtain_verification_code_endpoint
        participant "sign\n up\n endpoint" as sign_up_endpoint
    end box
    ' databse
    box "database" #LightBlue
        ' user database
        participant "user\n database" as user_db
        ' email verification database
    end box
end box


user -> get_button: Click
get_button -> obtain_verification_code_endpoint: POST /sign_up
obtain_verification_code_endpoint -> user_db: Store user with not verified
note left
	obtain_verification_code data
	    email address
end note

alt Case: user has signed up
    obtain_verification_code_endpoint -> get_button: Show error
else
    obtain_verification_code_endpoint -> user_db: Create entry with is_verifed = False
    obtain_verification_code_endpoint -> user_db: Create entry with verification code
    obtain_verification_code_endpoint -> verification_email: Send email with verification code
end

verification_email-> user: Obtain code
user -> code_textfield: Enter code
user -> sign_up_button: Click
sign_up_button -> sign_up_endpoint: POST /sign_up
note left
	sign_up data
        email address
        password
        verification code
end note

alt Case: email address not exist
    sign_up_endpoint -> sign_up_button: Show error
else Case: verification code is wrong
    sign_up_endpoint -> sign_up_button: Show error
end

sign_up_endpoint  -> sign_up_button: Response with Sign up successful
@enduml