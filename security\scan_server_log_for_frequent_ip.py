from tomsze_utils.configurable_algorithm.configurable_algorithm import CA
from tomsze_utils.security_utils import detect_frequent_ips_in_folder


def detect_frequent_ips_in_folder_wrapped(
    folder_path,
    datetime_format,
):
    scan_result = detect_frequent_ips_in_folder(
        folder_path=folder_path,
        datetime_format=datetime_format,
    )
    return scan_result


def run():

    config_folder_path = r"./CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_tasks_scan_server_log.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var

    frequent_ip_dict = dict_var[
        "run_script_function_check_the_logs_for_frequent_ips.return"
    ]

    if len(frequent_ip_dict) > 0:
        print("Found frequent ip!!!")
        print(frequent_ip_dict)
    else:
        print("No frequent ip is found.")


if __name__ == "__main__":
    run()
