package com.kewtoms.whatappsdo.ui.login;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.databinding.FragmentLoginBinding;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;
import com.kewtoms.whatappsdo.model.auth.Authenticator;

import org.json.JSONObject;

import java.util.Objects;

public class LoginFragment
  extends Fragment {

  public static final String TAG = "APP:LoginFragment";
  private LoginViewModel loginViewModel;
  private FragmentLoginBinding binding;
  private Configuration config = Configuration.getInstance();

  public View onCreateView(
    @NonNull LayoutInflater inflater,
    ViewGroup container,
    Bundle savedInstanceState) {

    String methodName = "onCreateView: ";

    Log.d(
      TAG,
      methodName + "run"
    );

    loginViewModel =
      new ViewModelProvider(requireActivity()).get(LoginViewModel.class);

    binding = FragmentLoginBinding.inflate(
      inflater,
      container,
      false
    );
    View root = binding.getRoot();

    Authenticator authenticator = new Authenticator(requireContext());

    // Register (observe) LiveData with view's methods
    loginViewModel.getEmailAddress().observe(
      getViewLifecycleOwner(),
      binding.editTextEmailAddress::setText
    );
    loginViewModel.getPassword().observe(
      getViewLifecycleOwner(),
      binding.textInputEditTextPassword::setText
    );
    loginViewModel.getStatus().observe(
      getViewLifecycleOwner(),
      binding.textViewLoginStatus::setText
    );

    // Set up Sign Up button small on click listener
    Button singupSmallButton = binding.buttonSignUp;
    singupSmallButton.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        // Navigate to SignupFragment
        NavController navController = Navigation.findNavController(v);
        navController.navigate(R.id.action_nav_login_to_nav_sign_up);
      }
    });

    // Set up Sign in (Log in) button large on click listener
    Button loginButtonLarge = binding.buttonLoginLarge;
    loginButtonLarge.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        Log.i(
          TAG,
          methodName + ": " + "Clicked login button large"
        );

        // Login (sign in) user.
        String email =
          binding.editTextEmailAddress.getText().toString();
        String password =
          Objects.requireNonNull(binding.textInputEditTextPassword.getText())
            .toString();

        try {
          authenticator.signIn(
            email,
            password,
            new PostResponseCallback() {
              @Override
              public void onPostSuccess(JSONObject response) {

                Log.i(
                  TAG,
                  methodName + ": " + "Login successful"
                );

                Log.d(
                  TAG,
                  methodName + ": " + "response: " + response
                );
                // Navigate to HomeFragment
                Log.i(
                  TAG,
                  methodName + ": " + "Navigating to home fragment"
                );
                NavController navController =
                  Navigation.findNavController(v);
                navController.navigate(R.id.action_nav_login_to_nav_home);

              }

              @Override
              public void onPostError(String errorMessage) {
                Toast toast = Toast.makeText(
                  requireActivity(),
                  errorMessage,
                  Toast.LENGTH_SHORT
                );
                toast.show();
              }
            },
            true
          );
        } catch (InterruptedException e) {
          Log.e(
            TAG,
            methodName + ": " + "InterruptedException: " + e
          );
          throw new RuntimeException(e);
        } // end of try catch sign in

      } // end of onClick
    });


    // Auto login user for DEV mode after view laid out
    loginButtonLarge.post(() -> {
      if (config.isDev()) {
        Log.i(
          TAG,
          methodName + ": " + config.getEnvironmentMode() + " mode: " + "Auto " + "logging" + " " + "in user"
        );
        loginButtonLarge.performClick();
      }
    });

    Log.d(
      TAG,
      methodName + ": " + "Done"
    );

    return root;
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    binding = null;
  }
}