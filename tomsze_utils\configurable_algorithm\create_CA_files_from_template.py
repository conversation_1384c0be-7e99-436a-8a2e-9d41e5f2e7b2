import os
from typing import <PERSON><PERSON>
from tomsze_utils.script_modify_utils import (
    add_simple_function_def,
    append_simple_function_call,
    comment_last_line,
)


def create_CA_files_from_template(
    plugin_name_undersocre: str,
    CA_test_script_path: str = r"./tests/test_configurable_algorithm.py",
    overwrite_test_script: bool = True,
) -> Tuple[str, str, str]:
    """
    Creates necessary files for a configurable algorithm based on a template.

    This function generates a test script, a plugin script, and a settings file
    for a specified plugin. It ensures that the required directories and files
    exist before proceeding with the creation.

    Args:
        plugin_name_undersocre (str): The name of the plugin in underscore format.
        CA_test_script_path (str, optional): The path to the test script to update. Defaults to "./tests/test_configurable_algorithm.py".
        overwrite_test_script (bool, optional): Whether to overwrite the test script if it exists. Defaults to True.

    Returns:
        Tuple[str, str, str]: Paths to the updated test script, created plugin script, and created settings file.

    Examples:
        ```python
        create_CA_files_from_template("script_runner")
        ```

        ```python
        create_CA_files_from_template("data_processor", overwrite_test_script=False)
        ```
    """
    test_folder_path = r"./tests"
    plugin_script_folder_path = (
        r"./tomsze_utils/configurable_algorithm/pluginer_settings/plugins"
    )

    plugin_name_capitalize = "".join(
        [x.capitalize() for x in plugin_name_undersocre.split("_")]
    )
    plugin_name_capitalize_full = f"Plugin{plugin_name_capitalize}"

    assert os.path.exists(test_folder_path)
    assert os.path.exists(CA_test_script_path)
    assert os.path.exists(plugin_script_folder_path)

    # Add test function to the CA test script
    function_body = f"""config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_{plugin_name_undersocre}.json5",
    )
    ca.run()
    
    assert ca.data_obj.dict_var.get("stepxx.xx", None) is not None
    """
    update_file, write_to_file_path = add_simple_function_def(
        script_path=CA_test_script_path,
        function_name=f"test_{plugin_name_undersocre}",
        function_body=function_body,
        overwrite=overwrite_test_script,
    )

    update_file, CA_test_save_file_path = comment_last_line(
        script_path=write_to_file_path,
        overwrite=True,  # Correct, no mistake
    )

    update_file, CA_test_save_file_path = append_simple_function_call(
        script_path=write_to_file_path,
        function_name=f"test_{plugin_name_undersocre}",
        overwrite=True,  # Correct, no mistake
    )

    # Create plugin script from template script
    plugin_template_script_file_path = os.path.join(
        plugin_script_folder_path, "template.py"
    )
    plugin_script_file_save_path = os.path.join(
        plugin_script_folder_path, f"{plugin_name_undersocre}.py"
    )
    plugin_script_file_save_path = plugin_script_file_save_path.replace("\\", "/")

    assert os.path.exists(plugin_template_script_file_path)

    data = open(plugin_template_script_file_path).read()
    data = data.replace("PluginTemplate", f"{plugin_name_capitalize_full}")

    with open(plugin_script_file_save_path, "w") as file:
        file.write(data)

    # Create setting json5 for testing.
    setting_template_file_path = r"./tests/CA_configs/setting_template.json5"
    setting_save_file_path = (
        f"./tests/CA_configs/setting_{plugin_name_undersocre}.json5"
    )
    assert os.path.exists(setting_template_file_path)

    data = open(setting_template_file_path).read()
    data = data.replace("assign_variable_1", f"xxx")
    data = data.replace("PluginVariableAssigner", f"{plugin_name_capitalize_full}")

    with open(setting_save_file_path, "w") as file:
        file.write(data)

    return (
        CA_test_save_file_path,
        plugin_script_file_save_path,
        setting_save_file_path,
    )


if __name__ == "__main__":
    plugin_name_undersocre = "script_runner"
    create_CA_files_from_template(
        plugin_name_undersocre=plugin_name_undersocre,
        overwrite_test_script=True,
    )
