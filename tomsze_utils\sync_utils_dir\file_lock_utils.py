import sys
import time
from typing import IO
import portalocker


def acquire_lock(lockfile: IO | int) -> None:
    """
    Acquires an exclusive lock on the specified lockfile.

    This function attempts to lock the given file exclusively. If the lock cannot be acquired, it will wait until it can.

    Args:
        lockfile (str): The path to the lock file to be locked.

    Examples:
        ```python
        file = open('somefile', 'r+')
        acquire_lock(lockfile=file)
        ```

    Note: If the lock cannot be acquired, a message will be printed indicating that it is waiting.
    """
    try:
        portalocker.lock(lockfile, portalocker.LockFlags.EXCLUSIVE)
        print("Locked")
        return
    except IOError:
        print("Lock not acquired, waiting...")


def release_lock(lockfile: IO) -> None:
    """
    Releases the lock on the specified lockfile.

    This function unlocks the given file, allowing other processes to acquire the lock.

    Args:
        lockfile (str): The path to the lock file to be unlocked.

    Examples:
        ```python
        file = open('somefile', 'r+')
        release_lock(lockfile=file)
        ```

    Note: If the lock cannot be released, an exception may be raised.
    """
    portalocker.unlock(lockfile)
    print("Unlocked")


def is_lock_file_locked(file_path: str) -> bool:
    """
    Checks if the specified lock file is currently locked.

    This function attempts to acquire a non-blocking lock on the given file. If the lock is already held by another process, it will return True.

    Args:
        file_path (str): The path to the lock file to check.

    Returns:
        bool: True if the file is locked, False otherwise.

    Examples:
        ```python
        is_locked = is_lock_file_locked(file_path="./my_lock_file.txt")
        ```

        ```python
        is_locked = is_lock_file_locked(file_path="./another_lock_file.txt")
        ```

    Note: If the file is locked, a message will be printed indicating that the file is locked.
    """
    file_locked = False
    try:
        with portalocker.Lock(file_path, "rb+", timeout=0.25) as fh:
            pass
    except portalocker.exceptions.AlreadyLocked:
        file_locked = True
        print(f'file "{file_path}" is locked')

    return file_locked


def main():
    file = open("./lock_file.txt", "r+")
    acquire_lock(file)

    wait_time = 60
    print(f"locking for {wait_time}s")
    time.sleep(wait_time)

    release_lock(file)
    file.close()


if __name__ == "__main__":
    sys.exit(main())
