import os

from tomsze_utils.string_utils import extract_data_from_a_string_of_lines


def parse_line_with_equal_to_dict(
    full_file_path: str,
    remove_double_quote: bool = True,
    remove_comma: bool = True,
) -> dict:
    """
    Parses a text file where each line contains an "=" character, converting it into a dictionary.

    This function reads a text file specified by `full_file_path` and processes each line that contains
    an "=" character. Each line is split into a key-value pair at the "=" character. The key and value
    are stripped of leading and trailing whitespace. The resulting key-value pairs are stored in a dictionary, which is returned.
    Lines not containing exactly one "=" are skipped.

    Args:
        full_file_path (str): The path to the text file to be parsed.
        remove_double_quote (bool, optional): Whether to remove double quotes from the value. Defaults to True.
        remove_comma (bool, optional): Whether to remove commas from the value. Defaults to True.

    Returns:
        dict: A dictionary containing the key-value pairs extracted from the file.

    Example:
        Given a text file with the following content:
        ```
        var1="1",
        var2='2',
        var3=3,
        var4=,
        ```

        The resulting dictionary will be:
        ```
        {
            "var1": '1',
            "var2": "'2'",
            "var3": '3',
            "var4": '',
        }
        ```
    """
    assert os.path.exists(full_file_path), f"File {full_file_path} does not exist."
    data_dict = {}

    # Read script.
    with open(full_file_path) as f:
        lines_list = f.readlines()

    for line in lines_list:
        if not "=" in line:
            continue

        line = line.replace("\n", "")
        split_list = line.split("=")
        if not len(split_list) == 2:
            continue

        key = split_list[0].strip()
        value = split_list[1].strip()

        if remove_double_quote:
            value = value.replace('"', "")
        if remove_comma:
            value = value.replace(",", "")

        data_dict[key] = value
    return data_dict


def parse_line_using_delimiter_to_dict(
    file_path: str,
    delimiter: str,
) -> dict:
    """
    Parses a text file into a dictionary, using a specified delimiter to separate keys and values on each line.

    The function reads the file, splits each line at the delimiter, and stores the resulting key-value pairs in a dictionary.
    Leading/trailing whitespace is removed from both keys and values.  Lines not containing the delimiter are skipped.

    Args:
        file_path (str): The path to the text file to be parsed.
        delimiter (str): The delimiter used to separate keys and values within each line.

    Returns:
        dict: A dictionary where keys are the strings found before the delimiter, and values are the strings found after the delimiter.

    Raises:
        AssertionError: If the provided file path does not exist.

    Example:
        For a text file named 'data.txt' containing:
        ```
        key1:value1
        key2:value2
        key3:value3
        ```

        Calling `parse_line_using_delimiter_to_dict('data.txt', ':')` would return:
        ```
        {
            "key1": "value1",
            "key2": "value2",
            "key3": "value3",
        }
        ```
    """
    assert os.path.exists(file_path)
    data_dict = {}

    # Read script.
    with open(file_path) as f:
        lines_list = f.readlines()

    for line in lines_list:
        if not delimiter in line:
            continue

        line = line.replace("\n", "")
        split_list = line.split(delimiter)
        if not len(split_list) == 2:
            continue

        key = split_list[0].strip()
        value = split_list[1].strip()

        data_dict[key] = value
    return data_dict


def parse_lines_with_delimiter_to_tuples(
    file_path: str,
    delimiter: str,
    index_list: list,
) -> list:
    """
    Parse each line in a txt file and return a list of tuples.

    Args:
        file_path (str): The path to the txt file.
        delimiter (str): The delimiter to split each line.
        index_list (list): The list of index to extract after splitting.

    Returns:
        list: A list of tuples.

    Examples:
        >>> file_path = "example.txt"
        >>> delimiter = ","
        >>> index_list = [0, 2]
        >>> # example.txt content:
        >>> # John,25,Developer
        >>> # Jane,30,Manager
        >>> # Bob,35,Engineer
        >>> result = parse_lines_with_delimiter_to_tuples(file_path, delimiter, index_list)
        >>> result
        [('John', 'Developer'), ('Jane', 'Manager'), ('Bob', 'Engineer')]

        >>> file_path = "example2.txt"
        >>> delimiter = ";"
        >>> index_list = [1, 3]
        >>> # example2.txt content:
        >>> # ID;Name;Age;Position
        >>> # 1;John;25;Developer
        >>> # 2;Jane;30;Manager
        >>> # 3;Bob;35;Engineer
        >>> result = parse_lines_with_delimiter_to_tuples(file_path, delimiter, index_list)
        >>> result
        [('John', 'Developer'), ('Jane', 'Manager'), ('Bob', 'Engineer')]
    """
    assert os.path.exists(file_path)
    result_list = []

    # Read script.
    with open(file_path) as f:
        lines_list = f.readlines()

    for line in lines_list:
        line = line.replace("\n", "")
        split_list = line.split(delimiter)

        # Check if the line has enough elements to extract
        if len(split_list) <= max(index_list):
            continue

        tuple_result = tuple(split_list[i] for i in index_list)
        result_list.append(tuple_result)

    return result_list


def extract_data_from_lines_in_file(file_path: str, data_list: list) -> list:
    """
    Extracts data from each line in a txt file based on a list of strings, handling missing data.

    Args:
        file_path (str): The path to the txt file.
        data_list (list): The list of strings to extract from each line.

    Returns:
        list: A list of tuples containing extracted data, with missing data represented as None.

    Examples:
        >>> file_path = "example.txt"
        >>> data_list = ["time", "name", "age"]
        >>> # example.txt content:
        >>> # time: 2022-01-01 00:00:00, name: Tom, age: 5
        >>> # time: 2022-01-01 00:00:01, name: Jane, age: 6
        >>> # time: 2022-01-01 00:00:02, name: Bob
        >>> result = extract_data_from_lines(file_path, data_list)
        >>> result
        [('2022-01-01 00:00:00', 'Tom', '5'), ('2022-01-01 00:00:01', 'Jane', '6'), ('2022-01-01 00:00:02', 'Bob', None)]

        >>> file_path = "example2.txt"
        >>> data_list = ["time", "name", "age", "location"]
        >>> # example2.txt content:
        >>> # time: 2022-01-01 00:00:00, name: Tom, age: 5
        >>> # time: 2022-01-01 00:00:01, name: Jane, age: 6
        >>> # time: 2022-01-01 00:00:02, name: Bob, age: 7, location: New York
        >>> result = extract_data_from_lines(file_path, data_list)
        >>> result
        [('2022-01-01 00:00:00', 'Tom', '5', None), ('2022-01-01 00:00:01', 'Jane', '6', None), ('2022-01-01 00:00:02', 'Bob', '7', 'New York')]
    """
    assert os.path.exists(file_path)

    # If data_list is empty, return an empty list
    if not data_list:
        return []

    # Read the file content
    with open(file_path) as f:
        file_content = f.read()

    # Use the optimized function to extract data
    return extract_data_from_a_string_of_lines(file_content, data_list)
