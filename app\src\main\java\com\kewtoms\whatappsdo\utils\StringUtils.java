package com.kewtoms.whatappsdo.utils;

public class StringUtils {

 // TODO Move this from this utils to ScrapPlayStoreUtils.

 /**
  * Create parsable string for use of ObjectMapper.
  */
 public static String createScrapeGooglePlayStoreParsableString(String matchedStr) {
  String newMatchedStr = matchedStr.replace(
   "data:",
   "\"data\":"
  );
  newMatchedStr = newMatchedStr.replace(
   "sideChannel:",
   "\"sideChannel\":"
  );
  newMatchedStr = "{" + newMatchedStr + "}";
  return newMatchedStr;
 }
}
