package com.kewtoms.whatappsdo;

import com.kewtoms.whatappsdo.interfaces.GreetingInterfaceWrap;

import org.junit.Test;

public class InterfaceWrapTest {
 @Test
 public void interfaceWrap_try() {

  GreetingInterfaceWrap greetingWrap = new GreetingInterfaceWrap() {
   @Override
   protected void greetImpl() {
    System.out.println("there");
   }
  };

  greetingWrap.greet();
  // "hi there" should be printed


 }

}