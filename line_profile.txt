Done importing
Loading pickle files
1 pickles loaded in:1.00 ms
------ Client data ---------
search text: 'eat'
client apps: ['com.miniclip.eightballpool', 'com.miniclip.footballstrike']
----------------------------
Waiting for tasks to complete...
All tasks are done!
'post_process_app_description'  0.00 ms
'convert_to_embedding'  1088.05 ms
'post_process_app_description'  0.00 ms
'convert_to_embedding'  124.01 ms
'search_user_app'  1667.27 ms
Wrote profile results to api_functions.py.lprof
Timer unit: 0.001 s

Total time: 1.66716 s
File: ./server/api/end_to_end/api_functions.py
Function: search_user_app at line 62

Line #      Hits         Time  Per Hit   % Time  Line Contents
==============================================================
    62                                           @timeit
    63                                           @profile
    64                                           def search_user_app(
    65                                               client_apps: Dict,
    66                                               client_search: str,
    67                                           ):
    68                                               """
    69                                               To be ran in enpoint 'sua'
    70                                           
    71                                               Search user apps from server apps.
    72                                               """
    73         1          0.0      0.0      0.0      print(f"------ Client data ---------")
    74         1          0.0      0.0      0.0      print(f"search text: '{client_search}'")
    75         1          0.0      0.0      0.0      print(f"client apps: {client_apps['appIds']}")
    76         1          0.0      0.0      0.0      print(f"----------------------------")
    77                                           
    78                                               # Check if client search is english, else translate to eng
    79         1        161.8    161.8      9.7      success, is_eng = check_is_eng(translator, client_search)
    80         1          0.0      0.0      0.0      assert success == True  # TODO log error
    81         1          0.0      0.0      0.0      if not is_eng:
    82                                                   client_search = translate_to_eng(translator, client_search)
    83                                           
    84                                               # --------------------------------------------------------
    85         <USER>          <GROUP>.0      0.0      0.0      """
    86                                               Check if client apps in db, 
    87                                               if not, 
    88                                               scrape from google play, 
    89                                               post process app description (desc), 
    90                                               convert app desc to embedding,
    91                                               extract keywords from app desc,
    92                                               get synonyms for each keyword,
    93                                               and store to db
    94                                               
    95                                               if in, proceed to search..
    96                                               """
    97                                               # Check if client apps in db
    98         2          0.0      0.0      0.0      is_new, new_appIds = db_apps.check_keys_in_db(
    99         1          0.0      0.0      0.0          client_apps["appIds"],
   100                                               )
   101         1          0.0      0.0      0.0      if is_new:
   102                                                   # Scrape play store
   103         1          0.0      0.0      0.0          (
   104         1          0.0      0.0      0.0              scrap_successes,
   105         1          0.0      0.0      0.0              scrap_results,
   106         1          0.0      0.0      0.0              app_ids,
   107         1        293.6    293.6     17.6          ) = scrape_play_store_thread(new_appIds)
   108         1          0.0      0.0      0.0          assert True in scrap_successes  # TODO log error
   109                                           
   110         4          0.0      0.0      0.0          for _, scrap_result, app_id in zip(
   111         1          0.0      0.0      0.0              scrap_successes,
   112         1          0.0      0.0      0.0              scrap_results,
   113         1          0.0      0.0      0.0              app_ids,
   114                                                   ):
   115                                                       # Post-process app description.
   116         2          0.1      0.0      0.0              app_desc_orig, app_desc = post_process_app_description(scrap_result)
   117                                           
   118                                                       # Convert app desc to embedding.
   119         4       1211.6    302.9     72.7              output_embedding_tensor = convert_to_embedding(
   120         2          0.0      0.0      0.0                  embedder, app_desc, device="cpu", batch_size=128
   121                                                       )
   122                                           
   123         2          0.0      0.0      0.0              g = 1

