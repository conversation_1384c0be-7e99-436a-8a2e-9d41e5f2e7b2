{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "read_env",
            "set_server_server_log_path",
            "create_a_new_server_log_dir_if_exist",
            "download_server_logs",
            "run_script_function_check_the_logs_for_frequent_ips",
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "read_env",
            "type": "PluginEnvReader",
            "use": true,
            "use_env_path": true,
            "env_path": "./.env"
        },
        {
            "step_name": "set_server_server_log_path",
            "type": "PluginVariableAssigner",
            "use": true,
            "server_server_log_path":"/root/code/{read_env.REPO}/server_log",
        },
        {
            "step_name": "create_a_new_server_log_dir_if_exist",
            "type": "PluginDirectoryCreator",
            "use": true,
            "directory_path": "D:/code/my_projects/search_phone_app_all/search_phone_app_server_tmp/downloaded_server_logs",
            "create_empty": true
        },
        {
            "step_name": "download_server_logs",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
scp -r -P {read_env.SSH_PORT} {read_env.SSH_USER}@{read_env.SSH_HOST}:{server_server_log_path} {create_a_new_server_log_dir_if_exist.directory_path}",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "run_script_function_check_the_logs_for_frequent_ips",
            "type": "PluginScriptRunner",
            "use": true,
            "script_path": './security/scan_server_log_for_frequent_ip.py',
            "function_name": 'detect_frequent_ips_in_folder_wrapped',
            'args_dict': {
                "folder_path": "{create_a_new_server_log_dir_if_exist.directory_path}/server_log",
                "datetime_format": "%m/%d/%Y %I:%M:%S %p",
            }
        }
       
    ]
   

    

}
