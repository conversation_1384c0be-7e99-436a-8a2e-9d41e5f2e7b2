"""Factory for creating an app plugin."""

from typing import Any, Callable, Dict
from ...plugins.app_plugin_base.app_plugin import AppPlugin

dict_appPlugin_creator: Dict[str, Callable[..., AppPlugin]] = {}


def register(app_plugin_type: str, func: Callable[..., AppPlugin]) -> None:
    """
    Register a new app plugin type by storing class definition to a dict
    """
    dict_appPlugin_creator[app_plugin_type] = func


def unregister(app_plugin_type: str) -> None:
    """
    Unregister an app plugin type by removing this class definition from a dict
    """
    dict_appPlugin_creator.pop(app_plugin_type, None)

def unregister_all() -> None:
    """
    Unregister all app plugin types by removing this class definition from a dict
    """
    dict_appPlugin_creator.clear()

def create_plugin_object(arguments: Dict[str, Any]) -> AppPlugin:
    """Create an app plugin of a specific type, given JSON data."""
    args_copy = arguments.copy()
    args_copy.pop("use")
    app_plugin_type = args_copy.get("type", None)
    if not app_plugin_type:
        raise ValueError(f"unknown app plugin type") from None
        
    try:
        creator = dict_appPlugin_creator[app_plugin_type]
    except KeyError:
        raise ValueError(f"unknown app plugin type {app_plugin_type!r}") from None
    return creator(**args_copy)
