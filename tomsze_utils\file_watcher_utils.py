import logging
from typing import List
from path import Path
import watchdog
import watchdog.events
from events import Events
from watchdog.observers import Observer
from tomsze_utils.timer_callback_utils import CountDownCaller


class WatchDogFileChangeEventHandler(watchdog.events.FileSystemEventHandler):
    """Logs all the events captured."""

    def __init__(
        self,
        events: Events,
        list_ignore_directory: List[str] = [],
        collected_changes_trigger_run_mil_second: int = 4500,  # Added parameter with default value
    ):
        super().__init__()

        # TODO: allow to change collected_changes_trigger_run_mil_second in project setting.
        self.collected_changes_trigger_run_mil_second = (
            collected_changes_trigger_run_mil_second
        )
        self.count_down_caller = CountDownCaller(
            seconds=self.collected_changes_trigger_run_mil_second / 1000,
            on_time_up=self.on_count_down_timer_times_up,
        )
        self.current_change_file: str = ""
        self.list_collected_changed_files = []
        self.next_change_clear_list = False

        self.events = events
        self.event_name = "default_event"
        self.reason = "default_reason"
        self.list_ignore_directory = list_ignore_directory

    def register_external_event_callback(self, event_name, func):
        # self.events.event_x += func
        self.events.__dict__[event_name] = func

    def on_moved(self, event):
        super().on_moved(event)

        what = "directory" if event.is_directory else "file"
        self.reason = f"Moved {what}: from {event.src_path} to {event.dest_path}"
        logging.info(self.reason)  # Note any \\ in reason will be shown as \

        how = "Moved"
        if self.check_ignore_directory(what, how, event):
            return

        # Invoke the registered event.
        self.collected_changes_run_event_callbacks(event.src_path)

    def on_created(self, event):
        super().on_created(event)

        what = "directory" if event.is_directory else "file"
        self.reason = f"Created {what}: {event.src_path}"
        logging.info(self.reason)

        how = "Created"
        if self.check_ignore_directory(what, how, event):
            return

        # Invoke the registered event.
        self.collected_changes_run_event_callbacks(event.src_path)

    def on_deleted(self, event):
        super().on_deleted(event)

        what = "directory" if event.is_directory else "file"
        self.reason = f"Deleted {what}: {event.src_path}"
        logging.info(self.reason)

        how = "Deleted"
        if self.check_ignore_directory(what, how, event):
            return

        # Invoke the registered event.
        self.collected_changes_run_event_callbacks(event.src_path)

    def on_modified(self, event):
        super().on_modified(event)

        what = "directory" if event.is_directory else "file"
        self.reason = f"Modified {what}: {event.src_path}"
        logging.info(self.reason)

        how = "Modified"
        if self.check_ignore_directory(what, how, event):
            return

        # Invoke the registered event.
        self.collected_changes_run_event_callbacks(event.src_path)

    def on_count_down_timer_times_up(self):
        logging.debug("on_count_down_timer_times_up")
        self.next_change_clear_list = True
        logging.info(self.list_collected_changed_files)
        self.run_event_callbacks()

    def check_ignore_directory(self, what, how, event):
        if what == "directory":
            for ignore_directory in self.list_ignore_directory:
                if ignore_directory in event.src_path:
                    logging.debug(f"{event.src_path} ignored.")
                    return True
                if how == "Moved":
                    if ignore_directory in event.dest_path:
                        logging.debug(f"{event.src_path} ignored.")
                        return True
        if what == "file":
            for ignore_directory in self.list_ignore_directory:
                if ignore_directory in str(Path(event.src_path).dirname):
                    logging.debug(f"{event.src_path} ignored.")
                    return True
                if how == "Moved":
                    if ignore_directory in str(Path(event.dest_path).dirname):
                        logging.debug(f"{event.src_path} ignored.")
                        return True
        return False

    def collected_changes_run_event_callbacks(self, file_path):
        logging.debug("collected_changes_run_event_callbacks")
        logging.debug(self.list_collected_changed_files)

        file_path = file_path.replace("\\", "/")

        is_counting = self.count_down_caller.timer.is_alive()

        if self.next_change_clear_list:
            logging.debug("list_collected_changed_files cleared.")
            self.list_collected_changed_files.clear()
            self.next_change_clear_list = False

        if file_path != "":
            logging.debug(f'"{file_path}" appended.')
            if file_path not in self.list_collected_changed_files:
                self.list_collected_changed_files.append(file_path)
            logging.debug(self.list_collected_changed_files)

        if not is_counting:
            self.next_change_clear_list = False
            self.count_down_caller.restart()
            return

        if is_counting:
            self.count_down_caller.restart()

    def run_event_callbacks(self):
        for event_name in self.events.__dict__:
            if not event_name == "__event_slot_cls__":
                self.events.__dict__[event_name](1, self.reason)

    def get_file_path_from_log_message(self, log_message):
        file_path = ""
        list_reason_split = log_message.split(": ")
        file_action = list_reason_split[0].split(", ")[-1]
        if file_action == "Created file":
            file_path = list_reason_split[1]
        if file_action == "Modified file":
            file_path = list_reason_split[1]
        if file_action == "Deleted file":
            file_path = list_reason_split[1]
        if file_action == "Modified directory":
            pass
        if file_action == "Deleted directory":
            pass

        file_path = file_path.replace("\\", "/")

        return file_path


class FileWatcher:
    def __init__(
        self,
        events,
        list_ignore_directory: List[str] = [],
        collected_changes_trigger_run_mil_second: int = 4500,
    ):
        self.events = events
        self.watchdog_event_handler = WatchDogFileChangeEventHandler(
            self.events,
            list_ignore_directory=list_ignore_directory,
            collected_changes_trigger_run_mil_second=collected_changes_trigger_run_mil_second,
        )
        self.watchdog_observer = Observer()

    def register_external_event_callback(self, event_name, callback=None):
        self.watchdog_event_handler.register_external_event_callback(
            event_name, callback
        )

    def start(
        self,
        path=None,
    ):
        logging.info(f'Watchdog for file changes started on path "{path}"')
        # self.sub_app.status_text.add_text(1, 'watchdog started')

        if not path:
            path = "."

        self.watchdog_observer = Observer()
        self.watchdog_observer.schedule(
            self.watchdog_event_handler, path, recursive=True
        )
        self.watchdog_observer.start()

    def stop(self):
        logging.info(f"Watchdog for file changes stopped")
        self.watchdog_observer.stop()

    def get_list_collected_changed_files(self):
        """
        Note: all paths separators are of  /.
        """
        return self.watchdog_event_handler.list_collected_changed_files
