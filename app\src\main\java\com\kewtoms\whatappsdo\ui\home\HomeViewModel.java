package com.kewtoms.whatappsdo.ui.home;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.kewtoms.whatappsdo.adapter.CardIconTextRecyclerViewAdapter;
import com.kewtoms.whatappsdo.data.Configuration;

/**
 * ViewModel for the HomeFragment that holds and manages UI-related
 * data. This class is responsible for providing data to the UI and
 * surviving configuration changes. It contains LiveData objects for
 * text, progress, enable state, and a RecyclerView adapter.
 */
public class HomeViewModel
  extends ViewModel {

  private final MutableLiveData<String> mText;
  private final MutableLiveData<String> mSearchText;
  private final MutableLiveData<Integer> mProgress;
  private final MutableLiveData<Boolean> mEnable;
  private final MutableLiveData<CardIconTextRecyclerViewAdapter>
    mRVAdapter;
  private Configuration config = Configuration.getInstance();

  public HomeViewModel() {
    mText = new MutableLiveData<>();
    mText.setValue("This is home fragment");

    mSearchText = new MutableLiveData<>();

    mProgress = new MutableLiveData<>();
    mProgress.setValue(0);

    mEnable = new MutableLiveData<>();
    mEnable.setValue(Boolean.FALSE);

    mRVAdapter = new MutableLiveData<>();

    if (config.isDev()) {
      mSearchText.setValue("eat");
    }
  }

  public LiveData<String> getText() {
    return mText;
  }

  public LiveData<String> getSearchText() {
    return mSearchText;
  }

  public LiveData<Integer> getProgress() {
    return mProgress;
  }

  public void setProgress(int progress) {
    mProgress.postValue(progress);
  }

  public LiveData<Boolean> getEnable() {
    return mEnable;
  }

  public void setEnable(Boolean isEnable) {
    mEnable.postValue(isEnable);
  }

  public LiveData<CardIconTextRecyclerViewAdapter> getRVAdapter() {
    return mRVAdapter;
  }

  public void setRVAdapter(CardIconTextRecyclerViewAdapter adapter) {
    mRVAdapter.postValue(adapter);
  }
}