"""App plugin"""

import os
from dataclasses import dataclass
import shutil
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginDirectoryCreator:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        directory_path = parse_data_and_store(
            logger,
            "directory_path",
            data_obj,
            step_config,
            config,
        )

        create_empty = parse_data_and_store(
            logger,
            "create_empty",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not os.path.exists(directory_path):
            os.makedirs(directory_path, exist_ok=True)
            logger.info(f"Created directory: {directory_path}")
        elif create_empty:
            # Remove the existing directory and all its contents
            shutil.rmtree(directory_path)
            # Recreate the directory to ensure it is empty
            os.makedirs(directory_path, exist_ok=True)
            logger.info(f"Cleared contents of existing directory: {directory_path}")
        else:
            logger.info(f"Directory already exists: {directory_path}")

        data_obj.dict_var[f"{current_step}.directory_path"] = directory_path
        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
