package com.kewtoms.whatappsdo.utils.Authentication;


public class RegisterResult {
 private Boolean isSuccess;
 private String resultStr;

 // Constructor
 public RegisterResult(
  Boolean isSuccess,
  String resultStr) {
  this.isSuccess = isSuccess;
  this.resultStr = resultStr;
 }

 public Boolean getIsSuccess() {
  return isSuccess;
 }

 public void setIsSuccess(Boolean isSuccess) {
  this.isSuccess = isSuccess;
 }

 public String getResultStr() {
  return resultStr;
 }

 public void setResultStr(String resultStr) {
  this.resultStr = resultStr;
 }

}