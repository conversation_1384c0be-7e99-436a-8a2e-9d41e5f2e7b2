{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "list_puml_files",
            "loop_puml_files",
                "run_command_to_convert_puml_to_svg",
            "end_loop_puml_files"
        ],
        "variables":{
            "jar_path":"./tomsze_utils/jars/plantuml_version_has_interactive_svg.jar",
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "list_puml_files",
            "type": "PluginDirectoryFilesLister",
            "use": true,
            "directory_path": "./tests/test_directory_for_interactive_svg_from_puml",
            "file_type_list": [
                '.puml',
            ],
        },
        {
            "step_name": "loop_puml_files",
            "type": "PluginLoopStarter",
            "use": true,
            "use_num_loops": false,
            "num_loops": 0,
            "use_loop_list": true,
            "loop_list": "{list_puml_files.file_list}",
        },
        {
            "step_name": "end_loop_puml_files",
            "type": "PluginLoopEnder",
            "use": true,
            "which_starter_step": "loop_puml_files",
        },
        {
            "step_name": "run_command_to_convert_puml_to_svg",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "java -jar {jar_path} -tsvg {loop_puml_files.loop_list_item}",
            "output_str_buffer_to": "output_str",
        }
    ]
   

}
