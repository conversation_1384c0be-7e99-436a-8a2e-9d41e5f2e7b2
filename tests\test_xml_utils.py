import os
import shutil
import tempfile
import pytest
from typing import List
from tomsze_utils.xml_utils import (
    find_next_tag_text,
    find_previous_tag_indices,
    find_text_tag_indices,
)
from tomsze_utils.xml_utils import (
    modify_xml_file_by_key_list,
    parse_xml_file_to_dict,
    parse_xml_string_to_dict,
)


def test_parse_xml_string_to_dict_valid():
    xml_string = "<root><item>Value1</item><item>Value2</item></root>"
    expected_output = {"root": {"item": ["Value1", "Value2"]}}
    assert parse_xml_string_to_dict(xml_string) == expected_output


def test_parse_xml_string_to_dict_empty():
    xml_string = "<root></root>"
    expected_output = {"root": None}
    assert parse_xml_string_to_dict(xml_string) == expected_output


def test_parse_xml_file_to_dict_valid():
    xml_content = "<root><item>Value1</item><item>Value2</item></root>"
    with tempfile.TemporaryDirectory() as tmpdir:
        temp_file_path = os.path.join(tmpdir, "temp.xml")
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(xml_content.encode("utf-8"))

        expected_output = {"root": {"item": ["Value1", "Value2"]}}
        assert parse_xml_file_to_dict(temp_file_path) == expected_output


def test_parse_xml_file_to_dict_empty():
    xml_content = "<root></root>"
    with tempfile.TemporaryDirectory() as tmpdir:
        temp_file_path = os.path.join(tmpdir, "temp.xml")
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(xml_content.encode("utf-8"))

        expected_output = {"root": None}
        assert parse_xml_file_to_dict(temp_file_path) == expected_output


def test_modify_xml_file_by_key_list():
    xml_content = "<root><item>Value1</item></root>"
    key_list = ["root", "item"]
    with tempfile.TemporaryDirectory() as tmpdir:
        temp_file_path = os.path.join(tmpdir, "temp.xml")
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(xml_content.encode("utf-8"))

        modify_xml_file_by_key_list(temp_file_path, key_list, "NewValue")

        expected_output = {"root": {"item": "NewValue"}}
        result_dict = parse_xml_file_to_dict(temp_file_path)

        assert result_dict == expected_output


def test_modify_xml_file_by_key_list_nonexistent_key():
    xml_content = "<root><item>Value1</item></root>"
    key_list = ["nonexistent_key"]
    with tempfile.TemporaryDirectory() as tmpdir:
        temp_file_path = os.path.join(tmpdir, "temp.xml")
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(xml_content.encode("utf-8"))

        modify_xml_file_by_key_list(temp_file_path, key_list, "NewValue")

        expected_output = {"root": {"item": "Value1"}}
        result_dict = parse_xml_file_to_dict(temp_file_path)

        assert result_dict == expected_output


import re


class TestWrapSvgElementsWithMatchingText:

    # Sample XML content for testing.
    sample_xml = r"""<?xml version="1.0" encoding="us-ascii" standalone="no"?>
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
        contentStyleType="text/css" height="131px" preserveAspectRatio="none"
        style="width:140px;height:131px;background:#FFFFFF;" version="1.1" viewBox="0 0 140 131"
        width="140px" zoomAndPan="magnify">
        <defs />
        <g>
            <rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5"
                style="stroke:#181818;stroke-width:0.5;" width="82" x="29" y="11" />
            <text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing"
                textLength="62" x="39" y="34.752">Hello world</text>
            <rect fill="#F1F1F1" height="52.7773" rx="12.5" ry="12.5"
                style="stroke:#181818;stroke-width:0.5;" width="118" x="11" y="67.3887" />
            <text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing"
                textLength="98" x="21" y="91.1406">This is defined on</text>
            <text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing"
                textLength="40" x="21" y="107.5293">several</text>
            <text fill="#000000" font-family="sans-serif" font-size="12" font-weight="bold"
                lengthAdjust="spacing" textLength="27" x="64" y="107.5293">lines</text>
            <text fill="#000000" font-family="sans-serif" font-size="12" font-weight="bold"
                lengthAdjust="spacing" textLength="27" x="64" y="107.5293">lines</text>
            <line style="stroke:#181818;stroke-width:1.0;" x1="70" x2="70" y1="47.3887" y2="67.3887" />
            <polygon fill="#181818" points="66,57.3887,70,67.3887,74,57.3887,70,61.3887"
                style="stroke:#181818;stroke-width:1.0;" />
        </g>
    </svg>"""

    def test_tag_found(self):
        """
        Test that the function correctly returns the indices for an existing <text> tag.
        In this case, we're looking for "Hello world", which is in one of the <text> tags.
        """
        target = "Hello world"
        indices = find_text_tag_indices(self.sample_xml, target)
        # Assert that we did find a match.
        assert indices is not None, "Expected tag indices, but got None."
        # Use the indices to extract the substring and verify that it contains the target text.
        extracted_tag = self.sample_xml[indices[0][0] : indices[0][1]]
        assert (
            target in extracted_tag
        ), "Extracted tag does not contain the expected target text."

    def test_two_tags_found(self):
        target = "lines"
        indices = find_text_tag_indices(self.sample_xml, target)
        # Assert that we did find a match.
        assert indices is not None, "Expected tag indices, but got None."
        # Use the indices to extract the substring and verify that it contains the target text.

        assert (
            len(indices) == 2
        ), "Expected two tag indices, but got a different number."

    def test_tag_not_found(self):
        """
        Test that the function returns None when the target text is not present in the XML content.
        """
        target = "Non-existent text"
        indices = find_text_tag_indices(self.sample_xml, target)
        # We expect None for target text that is not present.
        assert len(indices) == 0, "Expected None, but got tag indices."


class TestFindPreviousTagIndices:

    # Test 1: Basic nested XML tags.
    def test_basic_tags(self):
        xml_text = "<root><child>text</child></root>"
        # The tags in order start at:
        # "<root>"   -> index 0
        # "<child>"  -> index 6
        # "</child>" -> index 17
        # "</root>"  -> index 25
        # For each tuple, the returned index should be that of the most recent tag preceding the start index.
        index_tuples = [
            (
                14,
                0,
            ),  # 14 comes after "<child>" (index 6) but before "</child>" (index 17)
            (18, 0),  # 18 is after "</child>" starts at 17
            (26, 0),  # 26 is after "</root>" starts at 25
            (0, 0),
        ]  # 0 has no preceding tag, should return -1.
        expected = [6, 17, 25, -1]
        result = find_previous_tag_indices(xml_text, index_tuples)
        assert (
            result == expected
        ), f"test_basic_tags failed: Expected {expected}, got {result}"

    # Test 2: XML with no valid tags.
    def test_no_tags(self):
        xml_text = "This is plain text with no tags at all."
        # As there are no tags in the string, every tuple should yield -1.
        index_tuples = [(5, 5), (10, 10)]
        expected = [-1, -1]
        result = find_previous_tag_indices(xml_text, index_tuples)
        assert (
            result == expected
        ), f"test_no_tags failed: Expected {expected}, got {result}"

    # Test 3: Complex XML including an XML declaration and a comment.
    def test_complex_xml(self):
        xml_text = '<?xml version="1.0"?><!-- comment --><note>Remember</note>'
        # Let's analyze the positions:
        # "<?xml version="1.0"?>" starts at index 0.
        # "<!-- comment -->" starts immediately after at index 21.
        # "<note>" starts after the comment at index 37.
        # "</note>" starts later at index 51.
        # For example:
        #   • A tuple with start index 30 should return 21 (the comment tag).
        #   • A tuple with start index 45 should return 37 (the <note> tag).
        #   • A tuple with start index 0 should have no preceding tag so return -1.
        index_tuples = [(30, 30), (45, 45), (0, 0)]
        expected = [21, 37, -1]
        result = find_previous_tag_indices(xml_text, index_tuples)
        assert (
            result == expected
        ), f"test_complex_xml failed: Expected {expected}, got {result}"


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for any file‐based tests
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


class TestFindNextTagText:

    def test_simple_sequence_of_tags(self, temp_dir_path):
        # Write a small XML document into a temp file and read it back
        xml_content = "<root><item>One</item><item>Two</item></root>"
        xml_path = os.path.join(temp_dir_path, "test.xml")
        with open(xml_path, "w", encoding="utf-8") as f:
            f.write(xml_content)

        with open(xml_path, "r", encoding="utf-8") as f:
            xml = f.read()

        # Span just after <root> opening tag
        start = xml.find("<root>")
        end = xml.find(">", start) + 1
        spans = [(start, end)]

        # The very next tag is <item>One</item>, so we expect ["One"]
        result = find_next_tag_text(xml, spans)
        assert result == ["One"]

    def test_simple_sequence_of_tags_svg(self, temp_dir_path):
        # Write a small XML document into a temp file and read it back
        xml_content = "<svg><text>Class</text><text>Home</text></svg>"
        xml_path = os.path.join(temp_dir_path, "test.xml")
        with open(xml_path, "w", encoding="utf-8") as f:
            f.write(xml_content)

        with open(xml_path, "r", encoding="utf-8") as f:
            xml = f.read()

        # Span just after <root> opening tag
        start = xml.find("<text>")
        end = xml.find(">", start) + 1
        spans = [(start, end)]

        # The very next tag is <item>One</item>, so we expect ["One"]
        result = find_next_tag_text(xml, spans)
        assert result == ["Home"]

    def test_no_following_tags_returns_minus_one(self):
        # XML with two tags, but span is placed after the entire string
        xml = "<a>A</a><b>B</b>"
        spans = [(len(xml), len(xml))]

        # Since there's nothing after the span, we should get [-1]
        result = find_next_tag_text(xml, spans)
        assert result == [-1]
