package com.kewtoms.whatappsdo.data;

import androidx.annotation.NonNull;

public class Configuration {
  private static Configuration instance;
  private DeploymentMode deploymentMode;
  private EnvironmentMode environmentMode;
  private ServerStatus serverStatus;

  public Configuration() {
    // Default configuration
    this.deploymentMode = DeploymentMode.LOCAL;
    this.environmentMode = EnvironmentMode.TEST;
    this.serverStatus = ServerStatus.OFFLINE;
  }

  public static synchronized Configuration getInstance() {
    if (instance == null) {
      instance = new Configuration();
    }
    return instance;
  }

  // Deployment mode methods
  public DeploymentMode getDeploymentMode() {
    return deploymentMode;
  }

  public void setDeploymentMode(DeploymentMode deploymentMode) {
    if (deploymentMode == null) {
      throw new IllegalArgumentException("Deployment mode cannot be null");
    }
    this.deploymentMode = deploymentMode;
  }

  public boolean isLocal() {
    return deploymentMode == DeploymentMode.LOCAL;
  }

  public boolean isRemote() {
    return deploymentMode == DeploymentMode.REMOTE;
  }

  // Environment mode methods
  public EnvironmentMode getEnvironmentMode() {
    return environmentMode;
  }

  public ServerStatus getServerStatus() {
    return serverStatus;
  }

  public void setEnvironmentMode(EnvironmentMode environmentMode) {
    if (environmentMode == null) {
      throw new IllegalArgumentException("Environment mode cannot be null");
    }
    this.environmentMode = environmentMode;
  }


  public void setServerStatus(ServerStatus serverStatus) {
    if (serverStatus == null) {
      throw new IllegalArgumentException("serverStatus mode cannot be null");
    }
    this.serverStatus = serverStatus;
  }


  public boolean isTest() {
    return environmentMode == EnvironmentMode.TEST;
  }

  public boolean isDev() {
    return environmentMode == EnvironmentMode.DEV;
  }

  public boolean isProd() {
    return environmentMode == EnvironmentMode.PROD;
  }

  public boolean isServerOnline() {
    return serverStatus == ServerStatus.ONLINE;
  }

  public boolean isServerOffline() {
    return serverStatus == ServerStatus.OFFLINE;
  }


  @NonNull
  @Override
  public String toString() {
    return String.format(
      "Configuration [deploymentMode=%s, environmentMode=%s]",
      deploymentMode,
      environmentMode
    );
  }

  public enum DeploymentMode {
    LOCAL,
    REMOTE
  }

  public enum EnvironmentMode {
    TEST,
    DEV,
    PROD
  }

  public enum ServerStatus {
    ONLINE,
    OFFLINE
  }
}