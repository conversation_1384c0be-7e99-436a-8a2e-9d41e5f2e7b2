{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "run_command",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "run_command",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "pip list",
            "output_str_buffer_to": "output_str",
        }
    ]
   

}
