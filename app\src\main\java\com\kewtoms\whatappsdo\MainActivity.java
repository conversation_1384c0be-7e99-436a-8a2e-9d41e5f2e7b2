package com.kewtoms.whatappsdo;

import static androidx.test.core.app.ApplicationProvider.getApplicationContext;

import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.View;
import android.widget.TextView;

import com.google.android.material.navigation.NavigationView;

import androidx.annotation.NonNull;
import androidx.navigation.NavController;
import androidx.navigation.NavGraph;
import androidx.navigation.Navigation;
import androidx.navigation.ui.AppBarConfiguration;
import androidx.navigation.ui.NavigationUI;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.appcompat.app.AppCompatActivity;

import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.databinding.ActivityMainBinding;
import com.kewtoms.whatappsdo.model.UserUsage;
import com.kewtoms.whatappsdo.utils.CodeRunManager;
import com.kewtoms.whatappsdo.utils.SecurePrefsManager;

public class MainActivity
  extends AppCompatActivity {

  private static final String TAG = "APP:MainActivity";

  private AppBarConfiguration mAppBarConfiguration;
  private ActivityMainBinding binding;
  private UserUsage userUsage;

  private Configuration config = Configuration.getInstance();

  // Initialize the activity and set up the layout and navigation components.
  @Override
  protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    String methodName = "onCreate: ";

    Log.d(
      TAG,
      methodName + "run"
    );

    // Initialize some constants. Set null is correct so i can set
    // in ConstantsPrivate.java.

    // For integrated tests.
    String deploymentModeStr =
      getIntent().getStringExtra(Constants.mockDeploymentModeKey);
    String environmentModeStr =
      getIntent().getStringExtra(Constants.mockEnvModeKey);

    Configuration.DeploymentMode deploymentMode = null;
    Configuration.EnvironmentMode environmentMode = null;
    if (deploymentModeStr != null) {
      deploymentMode =
        Configuration.DeploymentMode.valueOf(deploymentModeStr);
    }

    if (environmentModeStr != null) {
      environmentMode =
        Configuration.EnvironmentMode.valueOf(environmentModeStr);
    }

    // Initialize constants
    Constants.getInstance().initializeData(
      deploymentMode,
      environmentMode
    );

    // Inflate the layout for the main activity using View Binding.
    binding = ActivityMainBinding.inflate(getLayoutInflater());
    // Set the root view of the activity.
    setContentView(binding.getRoot());

    Log.i(
      TAG,
      methodName + "done setting root view"
    );

    // Set the toolbar as the action bar.
    setSupportActionBar(binding.appBarMain.toolbar);

    // ------- Setup Navigation View and drawer -------
    DrawerLayout drawer = binding.drawerLayout;

    userUsage =
      new UserUsage(this); // user usage count to display in the navigation drawer

    drawer.addDrawerListener(new DrawerLayout.DrawerListener() {
      @Override
      public void onDrawerSlide(
        @NonNull View drawerView,
        float slideOffset) {

        // Set user email
        TextView textView_user_email =
          binding.navView.getHeaderView(0)
            .findViewById(R.id.textView_user_name);
        textView_user_email.setText(SecurePrefsManager.getAccountEmail(MainActivity.this));


        // Set is user registered
        if (SecurePrefsManager.getAccountEmail(MainActivity.this) != null) {
          TextView textView_is_user_registered =
            binding.navView.getHeaderView(0)
              .findViewById(R.id.textView_user_is_registered);
          textView_is_user_registered.setText(R.string.user_registered);
        } else {
          TextView textView_is_user_registered =
            binding.navView.getHeaderView(0)
              .findViewById(R.id.textView_user_is_registered);
          textView_is_user_registered.setText(R.string.user_not_registered);
        }


        // Update user usage count
        TextView textView_search_times =
          binding.navView.getHeaderView(0)
            .findViewById(R.id.textView_used_search_times);

        int usageCount = userUsage.getUsageCount();
        int free_usage_monthly = Constants.FREE_USAGE_COUNT_MONTHLY;
        String formattedString = String.format(
          getString(R.string.nav_header_used_search_times),
          usageCount,
          free_usage_monthly
        );
        textView_search_times.setText(formattedString);

        // Set app version
        TextView textView_app_version =
          binding.navView.getHeaderView(0)
            .findViewById(R.id.textView_app_version);

        String versionAndDebugStr =
          BuildConfig.VERSION_NAME + " (" + config.getDeploymentMode() + " " + config.getEnvironmentMode() + ")";
        textView_app_version.setText(versionAndDebugStr);

      }

      @Override
      public void onDrawerOpened(@NonNull View drawerView) {
      }

      @Override
      public void onDrawerClosed(@NonNull View drawerView) {

      }

      @Override
      public void onDrawerStateChanged(int newState) {

      }
    });

    Log.i(
      TAG,
      methodName + "Done initializing drawer"
    );

    NavigationView navigationView = binding.navView;
    // Build the configuration for the app bar and drawer.
    mAppBarConfiguration = new AppBarConfiguration.Builder(
      R.id.nav_home,
      R.id.nav_gallery,
      R.id.nav_sign_up,
      R.id.nav_login
    ).setOpenableLayout(drawer).build();
    // Get the NavController for navigating between destinations.
    NavController navController = Navigation.findNavController(
      this,
      R.id.nav_host_fragment_content_main
    );

    // --- Programmatically set startDestination based on modes ---
    // Inflate your navigation graph (replace R.navigation.mobile_navigation with your XML)
    NavGraph navGraph = navController.getNavInflater()
      .inflate(R.navigation.mobile_navigation);

    // Set the new start destination (replace R.id.desired_start_dest with your target ID)
    if (config.isDev() || config.isTest()) {
      Log.i(
        TAG,
        methodName + " mode:" + config.getEnvironmentMode() + " Overriding " + "startDestination " + "to " + "login " + "fragment"
      );
      navGraph.setStartDestination(R.id.nav_login);
    }

    if (config.isProd()) {
      Log.i(
        TAG,
        methodName + " mode:" + config.getEnvironmentMode() + ". " + "Overriding startDestination to home fragment"
      );
      navGraph.setStartDestination(R.id.nav_home);
    }

    // Apply the modified graph to the NavController
    navController.setGraph(navGraph);
    // -----------------------

    // Set up the action bar with navigation support.
    NavigationUI.setupActionBarWithNavController(
      this,
      navController,
      mAppBarConfiguration
    );
    // Set up the navigation view with the nav controller.
    NavigationUI.setupWithNavController(
      navigationView,
      navController
    );

    // Initialize CodeRunManager
    CodeRunManager.initialize(getApplicationContext());

    Log.i(
      TAG,
      methodName + ". " + "Done initializing NavigationUI and " + "navController"
    );

    Log.d(
      TAG,
      methodName + "Done"
    );


  }

  @Override
  public boolean onCreateOptionsMenu(Menu menu) {
    // Inflate the menu; this adds items to the action bar if it is present.
    getMenuInflater().inflate(
      R.menu.main,
      menu
    );
    return true;
  }

  /**
   * Handles the navigation back operation. This method is called when
   * the user presses the back button or performs a similar navigation
   * action. Its purpose is to control the application's behavior when
   * navigating back, following the user's navigation hierarchy.
   *
   * @return true if the back operation is handled; otherwise, false
   */
  @Override
  public boolean onSupportNavigateUp() {
    // Retrieves the NavController instance associated with the navigation host fragment in the current activity
    NavController navController = Navigation.findNavController(
      this,
      R.id.nav_host_fragment_content_main
    );
    // Attempts to navigate up based on the current AppBar configuration; if unable to navigate up, it calls the superclass method for default behavior
    return NavigationUI.navigateUp(
      navController,
      mAppBarConfiguration
    ) || super.onSupportNavigateUp();
  }

}