import datetime
import glob
import math
import pickle
import shutil
import sys
import os
import tempfile
import threading
import time

import portalocker
import pytest
from tomsze_utils.config_parser_utils import (
    create_config_parser_from_path,
    set_and_write_parser_to_file,
)
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.variable_dump_read_utils import dump_variable_to_pickle


def create_test_db_info(
    to_folder_path: str,
    part_data_count: int,
) -> None:
    """
    Creates and updates the database configuration with the specified part data count.

    Args:
        part_data_count (int): The number of data parts to set in the configuration.

    Examples:
        ```python
        create_test_db_info(part_data_count=10)
        ```

        ```python
        create_test_db_info(part_data_count=25)
        ```
    """

    config_path = os.path.join(
        to_folder_path,
        "db.ini",
    )
    conf_parser = create_config_parser_from_path(
        config_path=config_path,
    )

    # Set part_data_count
    set_and_write_parser_to_file(
        config_path=config_path,
        config_parser=conf_parser,
        key="part_data_count",
        data=part_data_count,
        section="sectionx",
    )


def remove_pickles_and_update_ver_file_and_lock_file(
    dir_path: str,
    db: PickleDatabaseSplit = None,
    remove_lock_file: bool = True,
) -> None:
    """
    Removes all pickle files from the specified directory, updates the version file,
    and optionally removes the lock file.

    This function walks through the given directory and deletes any files with the
    '.pickle' extension. It also removes the 'update_ver.txt' file and the lock file
    if specified.

    Args:
        dir_path (str): The path to the directory from which pickle files will be removed.
        db (PickleDatabaseSplit, optional): An instance of PickleDatabaseSplit to close the lock file. Defaults to None.
        remove_lock_file (bool, optional): Whether to remove the lock file. Defaults to True.

    Examples:
        ```python
        remove_pickles_and_update_ver_file_and_lock_file(dir_path="/path/to/directory")
        ```

        ```python
        remove_pickles_and_update_ver_file_and_lock_file(dir_path="/path/to/directory", db=my_db_instance, remove_lock_file=False)
        ```
    """
    for dirpath, _, filenames in os.walk(dir_path):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            if filetype in ["pickle"]:
                file_path = os.path.join(dirpath, filename)

                os.remove(file_path)

    update_ver_fpath = os.path.join(dir_path, "update_ver.txt")
    if os.path.exists(update_ver_fpath):
        os.remove(update_ver_fpath)

    if remove_lock_file:
        if db:
            db.close_lock_file()
        lock_file_path = os.path.join(dir_path, "lock_file")
        if os.path.exists(lock_file_path):
            os.remove(lock_file_path)


def remove_inis():
    script_dir_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )
    for dirpath, _, filenames in os.walk(script_dir_path):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            if filetype in ["ini"]:
                file_path = os.path.join(dirpath, filename)

                os.remove(file_path)


def remove_jsons():
    script_dir_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )
    for dirpath, _, filenames in os.walk(script_dir_path):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            if filetype in ["json"]:
                file_path = os.path.join(dirpath, filename)

                os.remove(file_path)


def create_test_pickles(
    to_folder_path,
    db_name,
    part_data_len,
    n_data,
):

    data_ind = 1
    n_pickles = math.ceil(n_data / part_data_len)
    count = 0
    for i in range(n_pickles):

        data = {}
        for j in range(part_data_len):
            data[f"k{data_ind}"] = f"d{data_ind}"
            data_ind += 1
            count += 1
            if count == n_data:
                break

        # Dump
        str_num = str(i).zfill(5)
        pickle_path = os.path.join(
            to_folder_path,
            f"{db_name}_{str_num}.pickle",
        )

        dump_variable_to_pickle(
            data,
            pickle_path,
        )


def create_test_pickles_old(
    db_name,
    part_data_len,
    n_pickles,
):
    script_dir_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )

    data_ind = 1
    for i in range(n_pickles):

        data = {}
        for j in range(part_data_len):
            data[f"k{data_ind}"] = f"d{data_ind}"
            data_ind += 1

        str_num = str(i).zfill(5)

        pickle_path = os.path.join(
            script_dir_path,
            f"{db_name}_{str_num}.pickle",
        )

        dump_variable_to_pickle(
            data,
            pickle_path,
        )


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


class TestCreateOrLoadPickleNoThread:

    def test_create_or_load_pickle_thread(self, temp_dir_path):
        part_data_len = 2
        db_name = "test_db"
        n_data = 40  # num pickles 20

        create_test_pickles(
            to_folder_path=temp_dir_path,
            db_name=db_name,
            part_data_len=part_data_len,
            n_data=n_data,
        )

        db = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=part_data_len,
        )
        expect = 20
        assert len(db.loaded_parts) == expect

    def test_create_or_load_pickle_thread_with_two_db(self, temp_dir_path):
        part_data_len = 2

        db_1 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=part_data_len,
        )

        db_2 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=part_data_len,
        )

        assert db_1.loaded_parts == db_2.loaded_parts


def test_create_or_load_pickle_thread_with_update_version_file_and_lock_file():
    part_data_len = 2
    db_name = "test_db"
    n_data = 40  # num pickles 20

    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    create_test_pickles(
        to_folder_path=temp_dir_path,
        db_name=db_name,
        part_data_len=part_data_len,
        n_data=n_data,
    )

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=part_data_len,
        use_sync_by_lock_file=True,
    )
    expect = 20
    assert len(db.loaded_parts) == expect

    assert os.path.exists(db.update_version_fpath) == True  # <<<
    assert os.path.exists(db.lock_file_path) == True  # <<<


def test_empty_db_fpath_when_init():
    part_data_len = 2
    db_name = "test_db"

    db = PickleDatabaseSplit(db_name=db_name)
    assert db.loaded_parts == [{}]


def test_non_exist_db_fpath_when_init():
    db_name = "test_db"

    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_name=db_name,
        db_fpath=temp_dir_path,
    )
    assert db.loaded_parts == [{}]


def test_read_data_count():
    # Create test db info in a ini
    part_data_count = 123

    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    create_test_db_info(
        to_folder_path=temp_dir_path,
        part_data_count=part_data_count,
    )

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
    )

    assert db.part_data_count == part_data_count


def test_re_split():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db_name = "test_db"
    n_data = 11  # <<<

    create_test_pickles(
        to_folder_path=temp_dir_path,
        db_name=db_name,
        part_data_len=10,  # <<<
        n_data=n_data,
    )

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,  # <<<
    )

    db.re_split()
    expect = 6
    assert len(db.loaded_parts) == expect

    db.re_split(part_data_count=3)
    expect = 4
    assert len(db.loaded_parts) == expect


def test_update_data_with_key_and_col():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data_with_key_and_col(
        key="k1",
        col="c1",
        data="dc1",
    )

    db.update_data_with_key_and_col(
        key="k1",
        col="c2",
        data="dc2",
    )
    expect = [
        {
            "k1": {
                "c1": "dc1",
                "c2": "dc2",
            },
        },
    ]
    assert db.loaded_parts == expect
    assert db.dirty_inds == [0]

    db.update_data_with_key_and_col(
        key="k2",
        col="c1",
        data="dc1",
    )
    expect = [
        {
            "k1": {
                "c1": "dc1",
                "c2": "dc2",
            },
            "k2": {
                "c1": "dc1",
            },
        },
    ]
    assert db.loaded_parts == expect
    assert db.dirty_inds == [0]

    db.update_data_with_key_and_col(
        key="k3",
        col="c1",
        data="dc1",
    )
    expect = [
        {
            "k1": {
                "c1": "dc1",
                "c2": "dc2",
            },
            "k2": {
                "c1": "dc1",
            },
        },
        {
            "k3": {
                "c1": "dc1",
            },
        },
    ]
    assert db.loaded_parts == expect
    assert db.dirty_inds == [0, 1]


def test_update_data_with_key_and_col_on_update_existing_key():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data_with_key_and_col(
        key="k1",
        col="c1",
        data="dc1",
    )

    db.update_data_with_key_and_col(
        key="k1",
        col="c1",
        data="dc1new",
    )
    expect = [
        {
            "k1": {
                "c1": "dc1new",
            },
        },
    ]
    assert db.loaded_parts == expect
    assert db.dirty_inds == [0]


def test_update_data_with_key_and_col_merge_option():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data_with_key_and_col(
        key="k1",
        col="c1",
        data=["dc1_1"],
        do_merge_if_exist=True,
    )

    db.update_data_with_key_and_col(
        key="k1",
        col="c1",
        data=["dc1_2"],
        do_merge_if_exist=True,
    )

    expect = [
        {
            "k1": {
                "c1": ["dc1_1", "dc1_2"],
            },
        },
    ]
    assert db.loaded_parts == expect


def test_update_data():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )
    expect = [
        {
            "k1": "d1",
            "k2": "d2",
        },
        {
            "k3": "d3",
            "k4": "d4",
        },
    ]
    assert db.loaded_parts == expect

    pickle_path = os.path.join(temp_dir_path, "test_db_00000.pickle")
    assert os.path.exists(pickle_path)

    # Create a new temp folder
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db.update_data(
        datas={
            "k5": "d5",
            "k6": "d6",
            "k7": "d7",
        }
    )
    expect = [
        {
            "k1": "d1",
            "k2": "d2",
        },
        {
            "k3": "d3",
            "k4": "d4",
        },
        {
            "k5": "d5",
            "k6": "d6",
        },
        {
            "k7": "d7",
        },
    ]
    assert db.loaded_parts == expect

    db.update_data(
        datas={
            "k1": "d1new",
            "k5": "d5new",
        }
    )
    expect = [
        {
            "k1": "d1new",
            "k2": "d2",
        },
        {
            "k3": "d3",
            "k4": "d4",
        },
        {
            "k5": "d5new",
            "k6": "d6",
        },
        {
            "k7": "d7",
        },
    ]
    assert db.loaded_parts == expect


class TestUpdateDataWithKeys:

    def test_update_data_with_keys_normal(self):
        temp_dir = tempfile.TemporaryDirectory()
        temp_dir_path = temp_dir.name

        db = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
        )

        # Insert a data to the empty loaded_parts.
        db.update_data_with_keys(
            keys=["k1", "k12", "k13"],
            data="k1_data",
        )
        expect = [
            {
                "k1": {"k12": {"k13": "k1_data"}},
            },
        ]
        assert db.loaded_parts == expect
        assert db.dirty_inds == [0]

        # Insert another data to the loaded_parts.
        db.update_data_with_keys(
            keys=["k2", "k22", "k23"],
            data="k2_data",
        )
        expect = [
            {
                "k1": {"k12": {"k13": "k1_data"}},
                "k2": {"k22": {"k23": "k2_data"}},
            },
        ]
        assert db.loaded_parts == expect
        assert db.dirty_inds == [0]

        # Insert another data to the loaded_parts.
        db.update_data_with_keys(
            keys=["k3", "k32", "k33"],
            data="k3_data",
        )
        expect = [
            {
                "k1": {"k12": {"k13": "k1_data"}},
                "k2": {"k22": {"k23": "k2_data"}},
            },
            {
                "k3": {"k32": {"k33": "k3_data"}},
            },
        ]
        assert db.loaded_parts == expect
        assert db.dirty_inds == [0, 1]

    def test_update_data_with_keys_with_two_db(self, temp_dir_path):
        # temp_dir = tempfile.TemporaryDirectory()
        # temp_dir_path = temp_dir.name

        db_1 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
        )

        db_2 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
        )

        # Insert a data to the empty loaded_parts.
        db_1.update_data_with_keys(
            keys=["k1"],
            data="k1_data",
        )

        db_2.update_data_with_keys(
            keys=["k1"],
            data="k1_data",
        )


def test_update_data_on_dirty_inds():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="db_test",
        part_data_count=2,
    )

    db.update_data(  # this will mark dirty_inds
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )
    assert db.dirty_inds == [0, 1]
    db.dump_dirty_parts_to_pickles_thread()  # this will clear dirty_inds
    assert db.dirty_inds == []


def test_dump_all_parts_to_pickles():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="db_test",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )

    db.dump_all_parts_to_pickles()
    expect_db_path1 = os.path.join(temp_dir_path, "db_test_00000.pickle")
    expect_db_path2 = os.path.join(temp_dir_path, "db_test_00001.pickle")

    assert os.path.exists(expect_db_path1) == True
    assert os.path.exists(expect_db_path2) == True

    # Remove test db pickle.
    os.remove(expect_db_path1)
    os.remove(expect_db_path2)


def test_dump_dirty_parts_to_pickles():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="db_test",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
            "k5": "d5",  # <<
            "k6": "d6",  # <<
        },
        dump=False,
    )

    db.update_data(
        datas={
            "k5": "d5new",
            "k6": "d6new",
        },
        dump=False,
    )

    db.dump_dirty_parts_to_pickles()
    expect_db_path0 = os.path.join(temp_dir_path, "db_test_00000.pickle")
    expect_db_path1 = os.path.join(temp_dir_path, "db_test_00001.pickle")
    expect_db_path2 = os.path.join(temp_dir_path, "db_test_00002.pickle")

    assert os.path.exists(expect_db_path0) == True
    assert os.path.exists(expect_db_path1) == True
    assert os.path.exists(expect_db_path2) == True
    remove_pickles_and_update_ver_file_and_lock_file(
        temp_dir_path,
        db=db,
    )

    db.update_data(
        datas={
            "k3": "d3new",
            "k4": "d4new",
        }
    )
    db.dump_dirty_parts_to_pickles()
    assert os.path.exists(expect_db_path1) == True


def test_dump_dirty_parts_to_pickles_thread():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="db_test",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
            "k5": "d5",
            "k6": "d6",
            "k7": "d7",
            "k8": "d8",
            "k9": "d9",
            "k10": "d10",
            "k11": "d11",
            "k12": "d12",
        },
        dump=False,
    )

    db.update_data(
        datas={
            "k1": "d1new",  # 0
            "k4": "d4new",  # 1
            "k9": "d9new",  # 4
        },
        dump=False,
    )

    db.dump_dirty_parts_to_pickles_thread()
    expect_db_path0 = os.path.join(temp_dir_path, "db_test_00000.pickle")
    expect_db_path1 = os.path.join(temp_dir_path, "db_test_00001.pickle")
    expect_db_path2 = os.path.join(temp_dir_path, "db_test_00002.pickle")
    expect_db_path3 = os.path.join(temp_dir_path, "db_test_00003.pickle")
    expect_db_path4 = os.path.join(temp_dir_path, "db_test_00004.pickle")
    expect_db_path5 = os.path.join(temp_dir_path, "db_test_00005.pickle")

    assert os.path.exists(expect_db_path0) == True
    assert os.path.exists(expect_db_path1) == True
    assert os.path.exists(expect_db_path2) == True
    assert os.path.exists(expect_db_path3) == True
    assert os.path.exists(expect_db_path4) == True
    assert os.path.exists(expect_db_path5) == True

    remove_pickles_and_update_ver_file_and_lock_file(
        temp_dir_path,
        db=db,
    )

    db.update_data(
        datas={
            "k13": "d13",
        }
    )
    db.dump_dirty_parts_to_pickles_thread()
    expect_db_path = os.path.join(temp_dir_path, "db_test_00006.pickle")

    assert os.path.exists(expect_db_path) == True


# TODO
def test_dump_all_parts_to_pickles_with_write_update_version():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="db_test",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )

    db.dump_all_parts_to_pickles()
    expect_db_path1 = os.path.join(temp_dir_path, "db_test_00000.pickle")
    expect_db_path2 = os.path.join(temp_dir_path, "db_test_00001.pickle")

    assert os.path.exists(expect_db_path1) == True
    assert os.path.exists(expect_db_path2) == True

    assert db.update_version != ""  # <<<
    assert os.path.exists(db.update_version_fpath)
    with open(db.update_version_fpath, "r") as file:  # <<<
        assert file.read() == db.update_version

    # Remove test db pickle.
    os.remove(expect_db_path1)
    os.remove(expect_db_path2)


def test_check_data_consistency():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": {"i1": "d11", "i2": "d21"},
            "k2": {"i1": "d12", "i2": "d22"},
            "k3": {"i1": "d13", "i2": "d23", "i3": "d33"},
            "k4": {"i1": "d14", "i2": "d24"},
        }
    )
    expect = {"k3": {"i1": "d13", "i2": "d23", "i3": "d33"}}

    data_inconsistency = db.check_data_consistency()
    assert data_inconsistency == expect


def test_query_as_list():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": {"i1": "d11", "i2": "d21"},
            "k2": {"i1": "d12", "i2": "d22"},
            "k3": {"i1": "d13", "i2": "d23"},
            "k4": {"i1": "d14", "i2": "d24"},
        }
    )

    expect = [
        "d21",
        "d23",
    ]

    results = db.query_as_list(["k1", "k3"], "i2")

    assert results == expect

    # Remove test db pickle.
    os.remove(os.path.join(temp_dir_path, "test_db_00000.pickle"))


def test_dump_time():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    # Create data to dump
    data = {}
    for x in range(5000000):
        str_num = str(x).zfill(10)
        data[str_num] = str_num

    time_start = time.time()

    dump_variable_to_pickle(
        variable=data,
        pickle_path=os.path.join(
            temp_dir_path,
            "test_dump.pickle",
        ),
    )
    time_dump_once = time.time()
    print("time used to dump once:" + str((time_dump_once - time_start) * 1000) + " ms")

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas=data)

    db.dump_all_parts_to_pickles()


def test_check_no_duplicate():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )
    expect = True
    no_duplicate = db.is_no_duplicate()
    assert no_duplicate == expect

    # Add a duplicate
    db.loaded_parts.append(db.loaded_parts[-1])
    expect = False
    no_duplicate = db.is_no_duplicate()
    assert no_duplicate == expect


def test_is_key_in():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )
    key = "k3"
    is_in = db.is_key_in(key=key)
    expect = True
    assert is_in == expect

    key = "k5"
    is_in = db.is_key_in(key=key)
    expect = False
    assert is_in == expect


def test_dump_json():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )

    db.dump_json()

    json_path = os.path.join(temp_dir_path, "db_apps.json")

    assert os.path.exists(json_path) == True


def test_check_keys_in_db():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
            "k4": "d4",
        }
    )
    is_new, new_keys, existing_keys = db.check_keys_in_db(["k1", "k2"])
    assert is_new == False
    assert new_keys == []

    is_new, new_keys, existing_keys = db.check_keys_in_db(["k1", "k2", "k5"])
    assert is_new == True
    assert new_keys == ["k5"]

    is_new, new_keys, existing_keys = db.check_keys_in_db(["k6"])
    assert is_new == True
    assert new_keys == ["k6"]


def test_query_key():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": {"c1": "dc11", "c2": "dc21"},
            "k2": {"c1": "dc12", "c2": "dc22"},
            "k3": {"c1": "dc13", "c2": "dc23"},
            "k4": {"c1": "dc14", "c2": "dc24"},
        }
    )

    key = "k3"
    query_result = db.query_key(
        key=key,
        default={},
    )
    expect = {"c1": "dc13", "c2": "dc23"}
    assert expect == query_result

    key = "k5"
    query_result = db.query_key(
        key=key,
        default={},
    )
    expect = {}
    assert expect == query_result


def test_query_key_col():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": {"c1": "dc11", "c2": "dc21"},
            "k2": {"c1": "dc12", "c2": "dc22"},
            "k3": {"c1": "dc13", "c2": "dc23"},
            "k4": {"c1": "dc14", "c2": "dc24"},
        }
    )

    key = "k3"
    col = "c2"

    query_result = db.query_key_col(
        key=key,
        col=col,
    )
    expect = "dc23"

    assert expect == query_result


def test_fetch_or_func():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "k1": {"c1": "dc11", "c2": "dc21"},
            "k2": {"c1": "dc12", "c2": "dc22"},
            "k3": {"c1": "dc13", "c2": "dc23"},
            "k4": {"c1": "dc14", "c2": "dc24"},
        }
    )

    key = "k2"
    col = "c2"

    a = 0

    def funA(a):
        a = 99
        return a

    fetchedResult = db.fetch_or_func(
        key=key,
        col=col,
        func_to_value=funA,
        func_kwargs={"a": a},
    )
    expect = "dc22"

    assert expect == fetchedResult.obtained_value

    key = "k9"
    col = "c2"

    a = ""

    def funA(key):
        result = f"{key} funA"
        return result

    fetchedResult = db.fetch_or_func(
        key=key,
        col=col,
        func_to_value=funA,
        func_kwargs={"key": "keytest"},
    )
    expect = "keytest funA"
    expect_dirty_inds = [0, 1, 2]

    assert expect == fetchedResult.obtained_value
    assert expect == db.query_key_col(key, col)
    assert expect_dirty_inds == db.dirty_inds


def test_get_db_num_keys():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    n_keys = db.get_db_num_keys()
    expect = 1
    assert expect == n_keys

    db.update_data(datas={"k2": "d2"})
    n_keys = db.get_db_num_keys()
    expect = 2
    assert expect == n_keys

    db.update_data(datas={"k3": "d3"})
    n_keys = db.get_db_num_keys()
    expect = 3
    assert expect == n_keys

    db.update_data(datas={"k4": "d4"})
    n_keys = db.get_db_num_keys()
    expect = 4
    assert expect == n_keys


def test_get_num_data_by_key_value():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(
        datas={
            "d1": {"key1": "value1"},
            "d2": {"key1": "value2"},
            "d3": {"key1": "value3"},
        }
    )

    num = db.get_num_data_by_key_value(key="key1", value="value1")
    expect = 1
    assert expect == num


def test_get_db_keys():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    keys_list = db.get_db_keys()
    expect = ["k1"]
    assert expect == keys_list

    db.update_data(datas={"k2": "d2"})
    keys_list = db.get_db_keys()
    expect = ["k1", "k2"]
    assert expect == keys_list

    db.update_data(datas={"k3": "d3"})
    keys_list = db.get_db_keys()
    expect = ["k1", "k2", "k3"]
    assert expect == keys_list


def test_remove_pickles():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.remove_pickles()

    extension = "*.pickle"
    file_list = glob.glob(os.path.join(temp_dir_path, extension))
    assert len(file_list) == 0


def test_get_last_data():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    last_data = db.get_last_data()
    expect = {"k1": "d1"}
    assert last_data == expect

    db.update_data(datas={"k2": "d2"})
    last_data = db.get_last_data()
    expect = {"k2": "d2"}
    assert last_data == expect

    db.update_data(datas={"k3": "d3"})

    last_data = db.get_last_data()
    expect = {"k3": "d3"}
    assert last_data == expect


def test_remove_all_empty_loaded_part():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    db.update_data(datas={"k2": "d2"})
    db.update_data(datas={"k3": "d3"})

    del db.loaded_parts[0]["k1"]
    del db.loaded_parts[0]["k2"]
    assert len(db.loaded_parts[0]) == 0

    db.remove_all_empty_loaded_part()
    expect = [{"k3": "d3"}]
    assert db.loaded_parts == expect

    remove_pickles_and_update_ver_file_and_lock_file(
        temp_dir_path,
        db=db,
    )

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    db.update_data(datas={"k2": "d2"})
    db.update_data(datas={"k3": "d3"})
    db.update_data(datas={"k4": "d4"})

    del db.loaded_parts[1]["k3"]
    del db.loaded_parts[1]["k4"]
    assert len(db.loaded_parts[1]) == 0

    db.remove_all_empty_loaded_part()
    expect = [{"k1": "d1", "k2": "d2"}]
    assert db.loaded_parts == expect


def test_reorganize_loaded_parts():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    db.update_data(datas={"k2": "d2"})
    db.update_data(datas={"k3": "d3"})

    del db.loaded_parts[0]["k2"]

    db.reorganize_loaded_parts()
    expect = [{"k1": "d1", "k3": "d3"}]
    assert db.loaded_parts == expect
    assert len(db.loaded_parts) == 1

    del db.loaded_parts[0]["k1"]

    db.reorganize_loaded_parts()
    expect = [{"k3": "d3"}]
    assert db.loaded_parts == expect
    assert len(db.loaded_parts) == 1

    remove_pickles_and_update_ver_file_and_lock_file(
        temp_dir_path,
        db=db,
    )

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    db.update_data(datas={"k2": "d2"})
    db.update_data(datas={"k3": "d3"})
    db.update_data(datas={"k4": "d4"})
    db.update_data(datas={"k5": "d5"})
    db.update_data(datas={"k6": "d6"})

    del db.loaded_parts[0]["k1"]
    del db.loaded_parts[1]["k3"]
    del db.loaded_parts[2]["k5"]

    db.reorganize_loaded_parts()
    expect = [{"k2": "d2", "k6": "d6"}, {"k4": "d4"}]
    assert db.loaded_parts == expect
    assert len(db.loaded_parts) == 2


def test_remove_key():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    db.update_data(datas={"k2": "d2"})
    db.update_data(datas={"k3": "d3"})
    assert len(db.loaded_parts) == 2

    success = db.remove_key(key="k1")

    assert success == True
    assert db.is_key_in("k1") == False
    assert len(db.loaded_parts) == 1

    success = db.remove_key(key="k4")
    assert success == False


def test_remove_all_keys_data():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    db.update_data(datas={"k2": "d2"})
    db.update_data(datas={"k3": "d3"})

    db.remove_all_keys_data()

    assert db.query_key("k1") == {}
    assert db.query_key("k2") == {}
    assert db.query_key("k3") == {}


def test_remove_all_keys():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data(datas={"k1": "d1"})
    db.update_data(datas={"k2": "d2"})
    db.update_data(datas={"k3": "d3"})

    db.remove_all_keys()

    assert db.dirty_inds == [0, 1]
    assert db.is_key_in("k1") == False
    assert db.is_key_in("k2") == False
    assert db.is_key_in("k3") == False


# TODO
def test_file_watcher():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db_tmp = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
        use_sync_by_lock_file=True,
    )

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
        use_sync_by_lock_file=True,
    )
    db.file_watcher.start(db.db_fpath)  # Start watching update version file.

    # Make a change to the db_fpath using db_tmp.
    db_tmp.update_data(datas={"k1": "d1"})
    db_tmp.dump_all_parts_to_pickles()

    with open(db.update_version_fpath, "w") as file:
        date_now_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        file.write(date_now_str)

    time.sleep(2)

    db.file_watcher.stop()
    assert db.loaded_parts == [{"k1": "d1"}]
    assert db.update_version == date_now_str


class TestSyncByLockFile:

    def test_sync_by_lock_file_basic(self, temp_dir_path):
        """
        Test the synchronization of two PickleDatabaseSplit instances using a lock file.
        This test verifies that when one instance updates a key and dumps its data to
        pickle files, the other instance can still access the updated data, ensuring
        that the locking mechanism works correctly to prevent data corruption.
        """
        db_name = "test_db"

        db_1 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name=db_name,
            part_data_count=2,
            use_sync_by_lock_file=True,
        )
        db_1.disable_file_watcher()

        db_2 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name=db_name,
            part_data_count=2,
            use_sync_by_lock_file=True,
        )
        db_2.disable_file_watcher()

        db_1.update_data_with_keys(
            keys=["k1"],
            data="k1_data",
        )

        db_2.update_data_with_keys(
            keys=["k1"],
            data="k1_data_new",
        )

        fake_dump_time = 1

        # Create an event object
        start_event = threading.Event()

        def my_thread_func():
            print("db_1 started")
            start_event.set()
            db_1.dump_all_parts_to_pickles(fake_dump_time=fake_dump_time)
            print("db_1 dumped")

        # Start thread to use db_1 to dump to pickle
        t0 = time.time()
        thread = threading.Thread(target=my_thread_func)
        thread.start()
        start_event.wait()

        # db_2 dump right after db_1 start to dump
        print("db_2 started")
        error_message = db_2.dump_all_parts_to_pickles()
        print("db_2 dumped")

        thread.join()  # Ensure thread is finished

        # Load the variable from the file
        assert error_message == ""
        pickle_path = os.path.join(temp_dir_path, "test_db_00000.pickle")
        with open(pickle_path, "rb") as f:
            loaded_variable = pickle.load(f)

        assert loaded_variable == {"k1": "k1_data_new"}

        t1 = time.time()
        time_diff = t1 - t0
        assert time_diff > fake_dump_time

    def test_sync_by_lock_file_timeout(self, temp_dir_path):
        """
        Test the synchronization of two PickleDatabaseSplit instances using a lock file.
        This test verifies that when one instance updates a key and dumps its data to
        pickle files, the other instance can still access the updated data, ensuring
        that the locking mechanism works correctly to prevent data corruption.
        """
        db_name = "test_db"

        db_1 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name=db_name,
            part_data_count=2,
            use_sync_by_lock_file=True,
        )
        db_1.disable_file_watcher()

        db_2 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name=db_name,
            part_data_count=2,
            use_sync_by_lock_file=True,
        )
        db_2.disable_file_watcher()

        db_1.update_data_with_keys(
            keys=["k1"],
            data="k1_data",
        )

        db_2.update_data_with_keys(
            keys=["k1"],
            data="k1_data_new",
        )

        fake_dump_time = 3

        # Create an event object
        start_event = threading.Event()

        def my_thread_func():
            start_event.set()
            db_1.dump_all_parts_to_pickles(fake_dump_time=fake_dump_time)

        # Start thread to use db_1 to dump to pickle
        thread = threading.Thread(target=my_thread_func)
        thread.start()
        start_event.wait()

        # db_2 dump right after db_1 start to dump
        error_message = db_2.dump_all_parts_to_pickles(
            wait_lock_release_time_in_second=1
        )

        thread.join()  # Ensure thread is finished

        pickle_path = os.path.join(temp_dir_path, "test_db_00000.pickle")
        with open(pickle_path, "rb") as f:
            loaded_variable = pickle.load(f)

        assert loaded_variable == {"k1": "k1_data"}
        assert error_message != ""

    def test_sync_db_by_update_version_file(self, temp_dir_path):
        db_name = "test_db"

        db_1 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name=db_name,
            part_data_count=2,
            use_sync_by_lock_file=True,
        )

        db_2 = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name=db_name,
            part_data_count=2,
            use_sync_by_lock_file=True,
        )

        print("db_1 started update")
        db_1.update_data_with_keys(
            keys=["k1"],
            data="k1_data",
        )

        db_1.dump_all_parts_to_pickles()  # The updaer version file will be changed here

        time.sleep(2)  # Add some sleep for db_2 to sync

        assert db_2.loaded_parts == [{"k1": "k1_data"}]


def test_query_by_keys_list():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    db.update_data_with_keys(
        keys=["k1", "k12", "k13"],
        data="k1_data",
    )

    data, index = db.query_by_keys_list(
        keys_list=["k1", "k12", "k13"],
        default=None,
    )

    expect = "k1_data"
    assert data == expect

    data, index = db.query_by_keys_list(
        keys_list=["k1", "k12", "k14"],
        default=None,
    )
    assert data != expect


def test_append_and_squeeze_list_data():
    temp_dir = tempfile.TemporaryDirectory()
    temp_dir_path = temp_dir.name

    db = PickleDatabaseSplit(
        db_fpath=temp_dir_path,
        db_name="test_db",
        part_data_count=2,
    )

    keys = ["k1", "k12", "k13"]
    db.update_data_with_keys(
        keys=keys,
        data=[1, 2, 3, 4, 5],
    )

    db.append_and_squeeze_list_data(
        keys=keys,
        data=6,
        list_fix_size=5,
    )

    expect = [2, 3, 4, 5, 6]
    data, index = db.query_by_keys_list(keys_list=keys, default=None)
    assert expect == data


from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit


class TestReloadFromPath:

    def test_reload_from_path_with_valid_path(self, temp_dir_path):
        db = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
        )
        # Simulate writing to the database
        db.set_update_version("1.0")
        db_path = os.path.join(temp_dir_path, "db.ini")
        with open(db_path, "w") as f:
            f.write("[sectionx]\npart_data_count=5\n")

        # Reload from the path
        db.reload_from_path(temp_dir_path)

        # Verify that the update version is set correctly
        assert db.update_version == "1.0"
        assert db.read_data_count() == 5  # Ensure the data count is read correctly

    def test_reload_from_path_with_valid_path_and_data(self, temp_dir_path):
        db = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
        )
        # Simulate writing to the database
        db.set_update_version("1.0")
        db_path = os.path.join(temp_dir_path, "db.ini")
        with open(db_path, "w") as f:
            f.write("[sectionx]\npart_data_count=5\n")

        keys = ["k1", "k12", "k13"]
        db.update_data_with_keys(
            keys=keys,
            data=[1, 2, 3, 4, 5],
        )
        db.dump_all_parts_to_pickles()

        db.loaded_parts = []

        # Reload from the path
        db.reload_from_path(temp_dir_path)

        # Verify has data
        assert db.loaded_parts != []

    def test_reload_from_path_with_invalid_path(self, temp_dir_path):

        fpath = os.path.join(temp_dir_path, "invalid_path2")
        db = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
            create_db_fpath_if_not_exist=True,
        )
        # Attempt to reload from an invalid path
        result = db.reload_from_path(fpath)

        # Verify that the update version remains unchanged
        assert (
            result == f"Path '{fpath}' does not exist."
        )  # Ensure the correct message is returned


class TestPickleDatabaseSplit:

    def test_to_dict_with_valid_data(self, temp_dir_path):
        db = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
        )
        # Simulate adding data to the database
        db.update_data({"key1": {"col1": "value1", "col2": "value2"}})
        db.update_data({"key2": {"col1": "value3", "col2": "value4"}})

        expected_dict = {
            "key1": {"col1": "value1", "col2": "value2"},
            "key2": {"col1": "value3", "col2": "value4"},
        }
        assert db.to_dict() == expected_dict

    def test_to_dict_with_empty_data(self, temp_dir_path):
        db = PickleDatabaseSplit(
            db_fpath=temp_dir_path,
            db_name="test_db",
            part_data_count=2,
        )
        expected_dict = {}
        assert db.to_dict() == expected_dict
