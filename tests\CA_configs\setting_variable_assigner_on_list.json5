{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "assign_variable_1",
            "assign_variable_2",
            "assign_variable_3",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "assign_variable_1",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1": 1,
        },
        {
            "step_name": "assign_variable_2",
            "type": "PluginVariableAssigner",
            "use": true,
            "var2": 2,
        },
        {
            "step_name": "assign_variable_3",
            "type": "PluginVariableAssigner",
            "use": true,
            "var3": [
                '{assign_variable_1.var1}',
                '{assign_variable_2.var2}',
            ],
        }
    ]
   

}
