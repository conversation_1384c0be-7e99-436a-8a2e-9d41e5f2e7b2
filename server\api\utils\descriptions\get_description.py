from typing import Dict, List
import google_play_scraper
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import wait


def scrape_play_store(
    device_app_id: str,
    lang: str = "en",
):
    """
    Use package google_play_scraper to scrap.

    param lang can be 'en' or 'zh'
    """
    scrap_success = False
    result = {}
    try:
        result = google_play_scraper.app(device_app_id, lang=lang)  # defaults to 'en'
        scrap_success = True
        result["source"] = "playstore"
    except google_play_scraper.exceptions.NotFoundError:
        print(f"{device_app_id} is not found")

    return scrap_success, result


def scrape_play_store_thread(
    device_app_ids: List[str],
    lang: str = "en",
    max_scrap_workers=4,
):
    """
    Scrap play store using threads.
    """
    scraped_app_ids = []
    scrape_successes = []
    results = []

    def task(device_app_id, lang):
        scrap_success, result = scrape_play_store(device_app_id, lang)
        scraped_app_ids.append(device_app_id)
        scrape_successes.append(scrap_success)
        results.append(result)

    # start the thread pool
    with ThreadPoolExecutor(max_scrap_workers) as executor:
        # submit tasks and collect futures
        futures = [
            executor.submit(
                task,
                device_app_id,
                lang,
            )
            for device_app_id in device_app_ids
        ]

        # wait for all tasks to complete
        print("Waiting for tasks to complete...")
        wait(futures)
        print("All tasks are done!")

    return scrape_successes, results, scraped_app_ids


def scrap_apkmonk(
    device_app_id,
):
    """
    Use requests to scrap app description from apkmonk.
    """
    scrap_success = False
    result = {}
    try:
        # result = google_play_scraper.app(
        #     device_app_id,
        #     lang=lang     # defaults to 'en'
        # )
        scrap_success = True
    except Exception as e:
        print(f"{device_app_id} is not found")

    return scrap_success, result


def main():
    # Test.
    pass


if __name__ == "__main__":
    main()
