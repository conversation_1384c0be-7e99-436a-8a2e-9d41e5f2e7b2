import pytest
from server.api.utils.app_id import verify_client_apps


def test_verify_client_apps():
    client_apps = {
        "appIds": [
            "com.scopely.monopolygo",
            "com.gameloft.anmp.sniper.champions",  # This is new to sever database
        ],
        "downloadSource": [
            "google_playstore",
            "google_playstore",
        ],
    }
    server_apps_ids = {
        "com.scopely.monopolygo": 1,
    }

    verify_result = verify_client_apps(client_apps, server_apps_ids)

    expect_new_apps = {"com.gameloft.anmp.sniper.champions": 1}
    expect_is_new = True

    assert expect_new_apps == verify_result.new_apps
    assert expect_is_new == verify_result.is_new
