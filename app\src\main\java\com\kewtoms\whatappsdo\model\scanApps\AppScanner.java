package com.kewtoms.whatappsdo.model.scanApps;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.util.Log;

import com.kewtoms.whatappsdo.data.ScanAppData;
import com.kewtoms.whatappsdo.interfaces.ScanCallback;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class AppScanner {
 public static final String LOG_TAG = "APP:ScanAppCallable";
 private final Activity activity;
 private final ScanCallback scanCallback;

 // Constructor with parameters
 public AppScanner(
  Activity activity,
  ScanCallback scanCallback) {
  this.activity = activity;
  this.scanCallback = scanCallback;
 }

 public Callable<ScanAppData> createCallable() {
  return () -> {
   Log.i(
    LOG_TAG,
    "run: " + Thread.currentThread().getName()
   );
   List<String> packageNames = new ArrayList<>();
   List<String> appNames = new ArrayList<>();
   List<String> imagePaths = new ArrayList<>();

   scanCallback.onScanStart();

   // Set up icon cache directory.
   File iconCacheDirFile = activity.getCacheDir();
   iconCacheDirFile = new File(
    iconCacheDirFile,
    "icons"
   );
   boolean success = iconCacheDirFile.mkdirs();
   if (success) {
    System.out.println("Directories created successfully");
   } else {
    System.out.println("Failed to create directories");
   }

   // Get package names.
   PackageManager packageManager = activity.getPackageManager();
   Intent intent = new Intent(
    Intent.ACTION_MAIN,
    null
   );
   intent.addCategory(Intent.CATEGORY_LAUNCHER);
   List<ResolveInfo> listResolveInfo =
    packageManager.queryIntentActivities(
     intent,
     0
    );

   String thisAppPackageName = activity.getPackageName();
   Iterator<ResolveInfo> resolveInfoIterator =
    listResolveInfo.iterator();
   while (resolveInfoIterator.hasNext()) {
    ResolveInfo resolveInfo = resolveInfoIterator.next();

    String packageName = resolveInfo.activityInfo.packageName;
    String appLabel =
     resolveInfo.loadLabel(packageManager).toString();
    String imagePath = new File(
     iconCacheDirFile,
     packageName + ".png"
    ).toString();

    // Not include duplicated package name.
    if (packageNames.contains(packageName)) {
     resolveInfoIterator.remove();
     continue;
    }

    // Not include this app.
    if (packageName.equals(thisAppPackageName)) {
     resolveInfoIterator.remove();
     continue;
    }

    packageNames.add(packageName);
    appNames.add(appLabel);
    imagePaths.add(imagePath);
   }

   // Get cached icons (only not existed).
   if (!imagePaths.isEmpty()) {
    int progressCount = 0;
    for (ResolveInfo resolveInfo : listResolveInfo) {   // loop listResolveInfo to load icon
     String packageName = resolveInfo.activityInfo.packageName;
     String imagePath = imagePaths.get(progressCount);

     if (new File(imagePath).exists()) {
      continue;
     }

     Drawable iconDrawable = resolveInfo.loadIcon(packageManager);

     // Convert Drawable to Bitmap.
     Log.i(
      LOG_TAG,
      "converting " + packageName + " icon drawable to bitmap"
     );
     Bitmap bitmap = Bitmap.createBitmap(
      iconDrawable.getIntrinsicWidth(),
      iconDrawable.getIntrinsicHeight(),
      Bitmap.Config.ARGB_8888
     );
     Canvas canvas = new Canvas(bitmap);
     iconDrawable.setBounds(
      0,
      0,
      canvas.getWidth(),
      canvas.getHeight()
     );
     iconDrawable.draw(canvas);

     // Write the Bitmap to the file using FileOutputStream.
     try {
      FileOutputStream outputStream = new FileOutputStream(imagePath);
      bitmap.compress(
       Bitmap.CompressFormat.PNG,
       100,
       outputStream
      );
      outputStream.flush();
      outputStream.close();
     } catch (IOException e) {
      Log.e(
       LOG_TAG,
       "run: " + e.toString()
      );
     }

     // Update progress.
     progressCount += 1;
     int progress = 100 * progressCount / listResolveInfo.size();
     scanCallback.onScanning(progress);

    }
    scanCallback.onScanDone(new ScanAppData(
     packageNames,
     appNames,
     imagePaths
    ));
   }

   return new ScanAppData(
    packageNames,
    appNames,
    imagePaths
   );
  };
 }


 public Future<ScanAppData> scan() {
  // Scan apps and cache icons.
  ExecutorService executorService = Executors.newFixedThreadPool(1);
  Callable<ScanAppData> scanAppCallable = createCallable();

  Future<ScanAppData> future =
   executorService.submit(scanAppCallable);

  executorService.shutdown();

  return future;
 }

}
