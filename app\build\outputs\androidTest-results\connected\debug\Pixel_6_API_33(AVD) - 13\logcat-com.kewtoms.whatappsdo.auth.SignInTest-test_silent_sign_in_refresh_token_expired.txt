07-05 14:04:52.406  9812  9835 I TestRunner: started: test_silent_sign_in_refresh_token_expired(com.kewtoms.whatappsdo.auth.SignInTest)
07-05 14:04:52.407  1543  1736 I AiAiEcho: Predicting[0]:
07-05 14:04:52.409  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 14:04:52.411  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:04:52.413  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:04:52.416  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:04:52.417  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:04:52.419  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:04:52.422  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:04:52.422  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 14:04:52.423  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 14:04:52.426  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 14:04:52.450   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:04:52.453   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:04:52.454   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 14:04:52.469  9812  9835 D APP:SecurePrefsManager: saveSignInData: run
07-05 14:04:52.469  9812  9835 D APP:SecurePrefsManager: saveAccessToken: run
07-05 14:04:52.469  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:52.496  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.auth.SignInTest.test_silent_sign_in_refresh_token_expired(SignInTest.java:376)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:04:52.731  9812  9835 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveAccessToken(SecurePrefsManager.java:158)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.saveSignInData(SecurePrefsManager.java:774)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.auth.SignInTest.test_silent_sign_in_refresh_token_expired(SignInTest.java:376)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 14:04:52.798  9812  9835 W AndroidKeysetManager: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 14:04:52.821  9812  9835 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 14:04:52.833  9812  9835 D APP:SecurePrefsManager: saveAccessToken: done
07-05 14:04:52.833  9812  9835 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 14:04:52.833  9812  9835 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 14:04:52.833  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:52.836  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:52.868  9812  9835 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 14:04:52.869  9812  9835 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 14:04:52.869  9812  9835 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 14:04:52.869  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:52.871  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:52.902  9812  9835 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 14:04:52.902  9812  9835 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 14:04:52.902  9812  9835 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 14:04:52.902  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:52.905  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:52.939  9812  9835 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 14:04:52.939  9812  9835 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 14:04:52.939  9812  9835 D APP:SecurePrefsManager: saveLoginTime: run
07-05 14:04:52.940  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:52.942  9812  9835 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:52.974  9812  9835 D APP:SecurePrefsManager: saveLoginTime: done
07-05 14:04:52.974  9812  9835 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 14:04:52.974  9812  9835 D APP:SecurePrefsManager: saveSignInData: done
07-05 14:04:53.161   336  9863 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10194 0}
07-05 14:04:53.162  9812  9835 D TrafficStats: tagSocket(92) with statsTag=0xffffffff, statsUid=-1
07-05 14:04:53.173   336  9865 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10194 0}
07-05 14:04:53.190  9812  9835 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 14:04:53.223   796   930 D SplashScreenView: Build android.window.SplashScreenView{4f285ba V.E...... ......ID 0,0-0,0}
07-05 14:04:53.223   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 14:04:53.223   796   930 D SplashScreenView: Branding: view: android.view.View{7ddcc6b G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 14:04:53.225   796   966 W Parcel  : Expecting binder but got null!
07-05 14:04:53.244  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 14:04:53.250  9812  9867 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 14:04:53.263  9812  9867 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 14:04:53.268  9812  9867 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 14:04:53.294   564  1119 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 14:04:53.294   564  1119 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 14:04:53.294   564  1119 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 14:04:53.294   564  1119 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 14:04:53.294   564  1119 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 14:04:53.294   564  1119 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 14:04:53.294   564  1119 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 14:04:53.294   564  1119 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 14:04:53.301  1166  1782 D EGL_emulation: app_time_stats: avg=2088.87ms min=269.66ms max=3908.09ms count=2
07-05 14:04:53.309   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:04:53.480   796   966 D EGL_emulation: app_time_stats: avg=16818.75ms min=16818.75ms max=16818.75ms count=1
07-05 14:04:53.513  9812  9812 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 14:04:53.524  9812  9812 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: PRE_ON_CREATE
07-05 14:04:53.525  9812  9812 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 14:04:53.569   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4d10
07-05 14:04:53.653  9812  9812 D APP:MainActivity: onCreate: run
07-05 14:04:53.655  9812  9812 D APP:Constants: initializeData: run
07-05 14:04:53.655  9812  9812 D APP:Constants: initializeData: done
07-05 14:04:53.853   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d2cd0
07-05 14:04:54.256  9812  9812 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10194; state: ENABLED
07-05 14:04:54.287  9812  9812 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 14:04:54.288  9812  9812 I APP:MainActivity: onCreate: done setting root view
07-05 14:04:54.308  9812  9812 I APP:MainActivity: onCreate: Done initializing drawer
07-05 14:04:54.361  9812  9812 I APP:MainActivity: onCreate:  mode:PROD. Overriding startDestination to home fragment
07-05 14:04:54.825  9812  9812 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 220.035ms (1263.43 bytecodes/s) (8240B approximate peak alloc)
07-05 14:04:54.857  9812  9812 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 14:04:54.858  9812  9812 D APP:MainActivity: onCreate: Done
07-05 14:04:54.858  9812  9812 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: CREATED
07-05 14:04:54.859  9812  9812 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:04:54.864  9812  9812 D APP:HomeFragment: onCreateView: run
07-05 14:04:54.954  9812  9812 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 14:04:54.955  9812  9812 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: disabled
07-05 14:04:54.955  9812  9812 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 14:04:54.955  9812  9812 I APP:HomeFragment: silentSignIn: run
07-05 14:04:55.261  9812  9812 D APP:Authenticator: silentSignIn: run
07-05 14:04:55.261  9812  9812 D APP:Authenticator: hasAccountStored: run
07-05 14:04:55.261  9812  9812 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:04:55.261  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.265  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.296  9812  9812 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:04:55.296  9812  9812 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:04:55.296  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.298  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.333  9812  9812 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:04:55.333  9812  9812 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:04:55.333  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.335  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.370  9812  9812 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:04:55.370  9812  9812 D APP:SecurePrefsManager: getHasSuccessLoggedIn: run
07-05 14:04:55.370  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.373  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.408  9812  9812 D APP:SecurePrefsManager: getHasSuccessLoggedIn: done
07-05 14:04:55.408  9812  9812 D APP:Authenticator: hasAccountStored: end: true
07-05 14:04:55.408  9812  9812 D APP:SecurePrefsManager: getAccountEmail: run
07-05 14:04:55.408  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.410  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.440  9812  9812 D APP:SecurePrefsManager: getAccountEmail: done
07-05 14:04:55.440  9812  9812 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:04:55.441  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.443  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.473  9812  9812 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:04:55.473  9812  9812 D APP:SecurePrefsManager: getRefreshToken: run
07-05 14:04:55.473  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.475  9812  9812 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.507  9812  9812 D APP:SecurePrefsManager: getRefreshToken: done
07-05 14:04:55.557  9812  9812 D APP:Authenticator: validateAccessToken: run
07-05 14:04:55.729  9812  9812 I APP:Authenticator: validateAccessToken: Sending request to:http://localhost:8000/__mock_validate_user_access_token
07-05 14:04:55.730  9812  9812 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:04:55.732  9812  9812 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:04:55.733  9812  9870 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:04:55.733  9812  9870 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:04:55.774  9812  9870 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:04:55.775  9812  9812 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:04:55.775  9812  9812 D APP:Authenticator: validateAccessToken: end
07-05 14:04:55.776  9812  9812 D APP:Authenticator: silentSignIn: done
07-05 14:04:55.777  9812  9812 D APP:HomeFragment: silentSignIn: done
07-05 14:04:55.777  9812  9812 D APP:HomeFragment: onCreateView: done
07-05 14:04:55.778  9812  9872 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:04:55.778  9812  9872 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:55.783  9812  9872 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:55.789  9812  9812 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: STARTED
07-05 14:04:55.790  9812  9812 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:04:55.793  9812  9812 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@170a635 in: RESUMED
07-05 14:04:55.793  9812  9812 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@170a635
07-05 14:04:55.801  9812  9812 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10194; state: DISABLED
07-05 14:04:55.808  9812  9866 W Parcel  : Expecting binder but got null!
07-05 14:04:55.848  9812  9872 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:04:55.856   336  9878 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10194 0}
07-05 14:04:55.857  9812  9872 D TrafficStats: tagSocket(101) with statsTag=0xb8332793, statsUid=-1
07-05 14:04:55.862  9812  9864 D TrafficStats: tagSocket(104) with statsTag=0xffffffff, statsUid=-1
07-05 14:04:55.881   398   545 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10194 pid=0
07-05 14:04:55.881   398   428 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10194 pid=9812
07-05 14:04:55.881   398   545 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10194 => denied (1358 us)
07-05 14:04:55.881   398   428 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10194 => denied (1185 us)
07-05 14:04:55.882   398   428 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10194 pid=9812
07-05 14:04:55.882   398   545 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10194 pid=0
07-05 14:04:55.882   398   428 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10194 => denied (530 us)
07-05 14:04:55.882   398   545 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10194 => denied (548 us)
07-05 14:04:55.883   398   428 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10194 pid=9812
07-05 14:04:55.883   398   545 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10194 pid=0
07-05 14:04:55.883   398   428 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10194 => denied (723 us)
07-05 14:04:55.883   398   545 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10194 => denied (808 us)
07-05 14:04:55.895  9812  9866 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:04:55.899  9812  9812 E RecyclerView: No adapter attached; skipping layout
07-05 14:04:55.900  9812  9866 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 14:04:55.902  9812  9866 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 14:04:55.917  9812  9866 D EGL_emulation: eglCreateContext: 0x720c5f5e0a50: maj 3 min 1 rcv 4
07-05 14:04:55.921   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d1e30
07-05 14:04:55.921   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3b70
07-05 14:04:55.931   336  9882 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10194 0}
07-05 14:04:55.960  9812  9866 D EGL_emulation: eglMakeCurrent: 0x720c5f5e0a50: ver 3 1 (tinfo 0x720e7729e080) (first time)
07-05 14:04:55.961   796   966 D EGL_emulation: app_time_stats: avg=9847.96ms min=288.94ms max=19406.98ms count=2
07-05 14:04:55.977   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 14:04:55.977  9812  9866 I Gralloc4: mapper 4.x is not supported
07-05 14:04:55.980   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 14:04:55.981   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 14:04:55.981  9812  9866 W Gralloc4: allocator 4.x is not supported
07-05 14:04:56.002  9812  9866 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 14:04:56.002  9812  9866 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:04:56.105   564   602 W ziparchive: Unable to open '/data/app/~~qHVXt21DRiBq92UQ7Owv8w==/com.kewtoms.whatappsdo-DPFwJpUNdrANf1oHNBrc2A==/base.dm': No such file or directory
07-05 14:04:56.106   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +2s904ms
07-05 14:04:56.130   564  2201 W InputManager-JNI: Input channel object '15cf943 Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 14:04:56.132  9812  9812 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:04:56.133  9812  9812 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:04:56.133  9812  9812 D APP:Authenticator: validateAccessToken: onPostSuccess:run
07-05 14:04:56.133  9812  9812 I APP:Authenticator: silentSignIn: run validateAccessToken outer callback:Post Success
07-05 14:04:56.133  9812  9812 I APP:Authenticator: silentSignIn: Validate fail. Obtaining new access token and new refresh token
07-05 14:04:56.134  9812  9812 D APP:Authenticator: obtainNewAccessTokenAndRefreshToken: run
07-05 14:04:56.134  9812  9812 I APP:Authenticator: obtainNewAccessTokenAndRefreshToken: : Sending request:http://localhost:8000/__mock_obtain_new_access_token_by_refresh_token
07-05 14:04:56.135  9812  9812 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 14:04:56.135  9812  9812 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 14:04:56.137  9812  9884 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 14:04:56.141  9812  9884 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 14:04:56.144  9812  9884 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 14:04:56.148  9812  9812 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 14:04:56.149  9812  9812 D APP:Authenticator: obtainNewAccessTokenAndRefreshToken: done
07-05 14:04:56.150  9812  9812 D APP:Authenticator: validateAccessToken: onPostSuccess:done
07-05 14:04:56.152  9812  9888 D APP:SecurePrefsManager: getAccessToken: run
07-05 14:04:56.152  9812  9888 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 14:04:56.158  9812  9888 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 14:04:56.177  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 14:04:56.178  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 14:04:56.178  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 14:04:56.178  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.kewtoms.whatappsdo fieldId=-1 fieldName=null extras=null}, false)
07-05 14:04:56.179  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 14:04:56.179  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:04:56.180  1166  1181 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef62f930 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 14:04:56.256  9812  9888 D APP:SecurePrefsManager: getAccessToken: done
07-05 14:04:56.263   336  9891 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10194 0}
07-05 14:04:56.268  9812  9812 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 14:04:56.268  9812  9812 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 14:04:56.268  9812  9812 D APP:Authenticator: obtainNewAccessTokenAndRefreshToken: : onPostSuccess run
07-05 14:04:56.268  9812  9812 I APP:Authenticator: obtainNewAccessTokenAndRefreshToken: : error message: Expired Token signature.
07-05 14:04:56.268  9812  9812 D APP:Authenticator: silentSignIn:  callsobtainNewAccessTokenAndRefreshToken: outer callback message: onPostSuccess run
07-05 14:04:56.269  9812  9812 D APP:Authenticator: silentSignIn:  callsobtainNewAccessTokenAndRefreshToken: outer callback message: responseJsonObj: {"code":401,"is_success":false,"message":"Expired Token signature.","data":{},"error":"Expired Token signature."}
07-05 14:04:56.269  9812  9812 D APP:HomeFragment: silentSignIn callsauthenticator.silentSignin: outer callback message: onPostSuccess run
07-05 14:04:56.269  9812  9812 I APP:HomeFragment: Refresh token expired.
07-05 14:04:56.277   796   966 D EGL_emulation: app_time_stats: avg=2797.16ms min=2797.16ms max=2797.16ms count=1
07-05 14:04:56.287  9812  9812 D CompatibilityChangeReporter: Compat change id reported: 171228096; UID 10194; state: ENABLED
07-05 14:04:56.298  9812  9835 W FileTestStorage: Output properties is not supported.
07-05 14:04:56.301  9812  9835 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 14:04:56.314  9812  9866 W Parcel  : Expecting binder but got null!
07-05 14:04:56.325  9812  9812 D APP:HomeFragment: silentSignIn callsauthenticator.silentSignin: outer callback message: onPostSuccess done
07-05 14:04:56.325  9812  9812 D APP:Authenticator: silentSignIn:  callsobtainNewAccessTokenAndRefreshToken: outer callback message: onPostSuccess done
07-05 14:04:56.325  9812  9812 D APP:Authenticator: obtainNewAccessTokenAndRefreshToken: onPostSuccess done
07-05 14:04:56.373  9812  9835 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 14:04:56.373  9812  9835 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 14:04:56.373  9812  9835 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 14:04:56.374  9812  9835 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 14:04:56.374  1274  3065 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 14:04:56.375  1274  3065 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 14:04:56.377  1274  3065 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 14:04:56.389  1274  3065 I FontLog : Pulling font file for id = 35, cache size = 6 [CONTEXT service_id=132 ]
07-05 14:04:56.391  9812  9866 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 14:04:56.400  1274  3015 I FontLog : Pulling font file for id = 35, cache size = 6 [CONTEXT service_id=132 ]
07-05 14:04:56.501  9812  9835 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 14:04:56.528  9812  9812 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:04:56.528  9812  9812 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 14:04:56.529  9812  9812 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 14:04:56.545  9812  9812 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 14:04:56.545  9812  9812 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:04:56.546  9812  9812 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 14:04:56.566  9812  9812 I ViewInteraction: Performing 'single click' action on view an instance of android.widget.TextView and view.getText() with or without transformation to match: is "OK"
07-05 14:04:56.574  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=893.5, y[0]=1364.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4687572, downTime=4687572, deviceId=-1, source=0x1002, displayId=0, eventId=-44471926 }
07-05 14:04:56.616  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=893.5, y[0]=1364.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4687614, downTime=4687572, deviceId=-1, source=0x1002, displayId=0, eventId=-1046211142 }
07-05 14:04:56.778  9812  9812 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.login.LoginFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 124.947ms (1576.66 bytecodes/s) (7224B approximate peak alloc)
07-05 14:04:56.780  9812  9812 W OnBackInvokedCallback: OnBackInvokedCallback is not enabled for the application.
07-05 14:04:56.780  9812  9812 W OnBackInvokedCallback: Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
07-05 14:04:56.790   564  1119 W InputManager-JNI: Input channel object 'a2bb2fe com.kewtoms.whatappsdo/com.kewtoms.whatappsdo.MainActivity (client)' was disposed without first being removed with the input manager!
07-05 14:04:56.802  9812  9812 D APP:LoginFragment: onCreateView: run
07-05 14:04:56.837   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4f50
07-05 14:04:56.837   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3030
07-05 14:04:56.852   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5130
07-05 14:04:56.853   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5370
07-05 14:04:56.853   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0870
07-05 14:04:56.854   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0870
07-05 14:04:56.931  9812  9820 I toms.whatappsdo: Compiler allocated 4160KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
07-05 14:04:56.985  1562  9438 I ContentMaintenance: Internal cache bytes used: 0; limit: 100000000; download size: 0
07-05 14:04:56.994  1562  9438 I RealtimeCacheCleanup: Beginning Realtime garbage collection.
07-05 14:04:56.995  1562  9438 I RealtimeCacheCleanup: Finished Realtime garbage collection.
07-05 14:04:57.022  9812  9812 D APP:LoginFragment: onCreateView: : Done
07-05 14:04:57.045  9812  9812 D AutofillManager: Trigger fill request at view entered
07-05 14:04:57.083  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 14:04:57.085  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 14:04:57.086  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.kewtoms.whatappsdo fieldId=-1 fieldName=null extras=null}, false)
07-05 14:04:57.086  9812  9812 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10194; state: ENABLED
07-05 14:04:57.086  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 14:04:57.088  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 14:04:57.089  9812  9812 I AssistStructure: Flattened final assist data: 3544 bytes, containing 1 windows, 23 views
07-05 14:04:57.101  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 14:04:57.106  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 14:04:57.106  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 14:04:57.132  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:1425847617
07-05 14:04:57.360  9812  9812 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)}' assertion on view view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>
07-05 14:04:57.364  9812  9835 I TestRunner: finished: test_silent_sign_in_refresh_token_expired(com.kewtoms.whatappsdo.auth.SignInTest)
