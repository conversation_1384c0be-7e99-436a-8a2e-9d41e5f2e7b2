{"general": {"init_steps": [], "steps": ["send_get_request", "send_get_request2"], "variables": {"var_online": false}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "log_to_file": true, "folder_path": "./tests/tmp_CA_logs", "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_get_requester": [{"step_name": "send_get_request", "type": "PluginSimpleGetRequester", "use": true, "url": "https://example.com/", "headers": {}, "response_buffer_to": "response", "headers_buffer_to": "headers", "content_buffer_to": "content", "text_buffer_to": "text", "status_code_buffer_to": "status_code", "exception_buffer_to": "exception"}, {"step_name": "send_get_request2", "type": "PluginSimpleGetRequester", "use": true, "url": "https://httpbin.org/get", "headers": {}, "response_buffer_to": "response", "headers_buffer_to": "headers", "content_buffer_to": "content", "text_buffer_to": "text", "status_code_buffer_to": "status_code", "if_dict_buffer_to": "response_dict", "exception_buffer_to": "exception"}]}