{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "assign_variable_true",
        ],
        "variables":{
            "jar_path":"./tomsze_utils/jars/plantuml_version_has_interactive_svg.jar",
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "assign_variable_true",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1": "eval{1+1}",
        },
    ]
   

}
