"""App plugin"""

from dataclasses import dataclass

from bs4 import BeautifulSoup
from tomsze_utils.plugins.constant.plugin_constants import (
    PLUGIN_HTML_PARSER_ELE_RESULTS_VAR_NAME,
    PLUGIN_HTML_PARSER_ELE_STR_RESULTS_VAR_NAME,
    PLUGIN_HTML_PARSER_ELE_TEXT_RESULTS_VAR_NAME,
    PLUGIN_HTML_PARSER_ELE_TEXT_RESULT_VAR_NAME,
)
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginHtmlParserB4soup:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        html_text = parse_data_and_store(
            logger,
            "html_text",
            data_obj,
            step_config,
            config,
        )

        css_selector = parse_data_and_store(
            logger,
            "css_selector",
            data_obj,
            step_config,
            config,
        )

        use_filter_by_text = parse_data_and_store(
            logger,
            "use_filter_by_text",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        filter_by_text_conditions = parse_data_and_store(
            logger,
            "filter_by_text_conditions",
            data_obj,
            step_config,
            config,
        )

        use_filter_by_attribute = parse_data_and_store(
            logger,
            "use_filter_by_attribute",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        filter_by_attribute_conditions = parse_data_and_store(
            logger,
            "filter_by_attribute_conditions",
            data_obj,
            step_config,
            config,
        )

        get_next_element = parse_data_and_store(
            logger,
            "get_next_element",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        is_get_element_text_results = parse_data_and_store(
            logger,
            "is_get_element_text_results",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        is_get_element_str_results = parse_data_and_store(
            logger,
            "is_get_element_str_results",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        is_get_element_results = parse_data_and_store(
            logger,
            "is_get_element_results",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        soup = BeautifulSoup(html_text, "html.parser")

        element_list = soup.select(css_selector)

        element_text_results = []
        element_str_results = []
        element_results = []
        if use_filter_by_text:
            for element in element_list:

                text = element.text
                for text_cond in filter_by_text_conditions:
                    if text_cond in text:
                        if get_next_element:
                            element = element.find_next_sibling()

                        if is_get_element_text_results:
                            is_split_text = parse_data_and_store(
                                logger,
                                "is_split_text",
                                data_obj,
                                step_config,
                                config,
                                type="bool",
                                default=False,
                            )

                            split_text_delimiter = parse_data_and_store(
                                logger,
                                "split_text_delimiter",
                                data_obj,
                                step_config,
                                config,
                            )

                            split_index = parse_data_and_store(
                                logger,
                                "split_index",
                                data_obj,
                                step_config,
                                config,
                            )
                            if is_split_text:
                                split_list = text.split(split_text_delimiter)
                                if split_index <= len(split_list) - 1:
                                    text = split_list[split_index].strip()

                            is_replace_text = parse_data_and_store(
                                logger,
                                "is_replace_text",
                                data_obj,
                                step_config,
                                config,
                                type="bool",
                                default=False,
                            )

                            replace_what = parse_data_and_store(
                                logger,
                                "replace_what",
                                data_obj,
                                step_config,
                                config,
                            )

                            replace_as_what = parse_data_and_store(
                                logger,
                                "replace_as_what",
                                data_obj,
                                step_config,
                                config,
                            )

                            if is_replace_text:
                                text = text.replace(
                                    replace_what, replace_as_what
                                ).strip()

                            element_text_results.append(text)
                        if is_get_element_str_results:
                            element_str_results.append(str(element))
                        if is_get_element_results:
                            element_results.append(element)
                        break

        if use_filter_by_attribute:
            for element in element_list:

                found = True
                for key in filter_by_attribute_conditions.keys():
                    value = element.get(key, None)
                    if not value:
                        found = False
                        continue
                    if not filter_by_attribute_conditions.get(key) in value:
                        found = False
                if found:
                    if get_next_element:
                        element = element.find_next_sibling()

                    if is_get_element_text_results:
                        text = element.text
                        element_text_results.append(text)
                    if is_get_element_str_results:
                        element_str_results.append(str(element))
                    if is_get_element_results:
                        element_results.append(element)

        data_obj.dict_var[
            f"{current_step}.{PLUGIN_HTML_PARSER_ELE_TEXT_RESULTS_VAR_NAME}"
        ] = element_text_results
        data_obj.dict_var[
            f"{current_step}.{PLUGIN_HTML_PARSER_ELE_TEXT_RESULT_VAR_NAME}"
        ] = element_text_results[0]
        data_obj.dict_var[
            f"{current_step}.{PLUGIN_HTML_PARSER_ELE_STR_RESULTS_VAR_NAME}"
        ] = element_str_results
        data_obj.dict_var[
            f"{current_step}.{PLUGIN_HTML_PARSER_ELE_RESULTS_VAR_NAME}"
        ] = element_results

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
