import shutil
import sys
import os
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.database_utils.run_pickle_database_split import remove_pickles
from tomsze_utils.variable_dump_read_utils import (
    dump_variable_to_pickle,
    load_pickle_file,
)
from tqdm import tqdm
from server.api.pickle_db.android.early_preprocess.create_db_synonyms_with_source.create_db_synonyms_with_source_utils import (
    update_db_synonyms_by_scraping_powertheasaurus,
)
from server.api.utils.keywords.get_keywords import lemmatization
from nltk.corpus import words
from const_path import (
    proj_ser_api_db_early_create_syn_path,
    proj_ser_api_db_early_create_syn_lemmatize_nltk_words_path,
)


def run_update_db_synonyms_with_source_powertheasaurus_using_nltk_words(
    create_new_from_db_ntlk_synonyms: bool = False,
    total_request_needed: int = 10000,
):
    """
    Create db_synonyms (10000 words for each part)
    Create nlp_model for lemmatization

    Get words from nltk (around 23 0000 words, but some have no synonyms)


        Lemmatize the word.
        Get synonyms using threads
        Update the database.
        Dump to file.
    """

    script_directory_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )

    """
    Copy db_synonyms from ntlk source
    Caution! Uncomment below will remove pt add to db.
    """
    if create_new_from_db_ntlk_synonyms:
        # Remove pickles
        remove_pickles(script_directory_path)

        # Copy pickles from source nltk to this dir.
        source_nltk_dir = os.path.join(
            proj_ser_api_db_early_create_syn_path,
            "run_create_db_synonyms_with_source_nltk_using_nltk_words",
        )
        source_nltk_dir = os.path.realpath(source_nltk_dir)
        list_file_type = [
            "pickle",
        ]

        for dirpath, dirnames, filenames in os.walk(source_nltk_dir):
            for filename in tqdm(filenames):
                filetype = filename.split(".")[-1]
                if filetype in list_file_type:
                    source_file_path = os.path.join(dirpath, filename)
                    dst_file_path = os.path.join(script_directory_path, filename)

                    shutil.copyfile(source_file_path, dst_file_path)

    """
    Get synonyms from pt
    """
    # Create a db nltk lemmatized words
    db_ntlk_lemmatize = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_early_create_syn_lemmatize_nltk_words_path,
        db_name="db_ntlk_lemmatize",
        load_by_thread=False,
    )

    # Create a db synonyms (in current dir)
    db_synonyms = PickleDatabaseSplit(
        db_fpath=script_directory_path,
        db_name="db_syns",
        load_by_thread=False,
    )

    # Get words that need to scrape (should be lemmatized and skip stopwords).
    words_need_scrape = []
    for ntlk_lemmatize_part in tqdm(db_ntlk_lemmatize.loaded_parts):
        for word_lemm in ntlk_lemmatize_part:

            if " " in word_lemm:
                word_lemm = word_lemm.replace(" ", "_")

            if not db_synonyms.is_key_in_col(key=word_lemm, col="pt"):
                words_need_scrape.append(word_lemm)

    # Create or update db.
    update_db_synonyms_by_scraping_powertheasaurus(
        db_synonyms=db_synonyms,
        lemmatized_keywords=words_need_scrape,
        total_request_needed=total_request_needed,
    )

    # TODO deal with those word without underscore (create another tool)


def main():
    run_update_db_synonyms_with_source_powertheasaurus_using_nltk_words(
        create_new_from_db_ntlk_synonyms=False,
        total_request_needed=50000,
    )


if __name__ == "__main__":
    sys.exit(main())
