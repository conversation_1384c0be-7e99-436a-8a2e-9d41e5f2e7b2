import os
import sqlite3
import time

app_description_table_cols = [
    ("AppId", "TEXT", "PRIMARY KEY", "NOT NULL"),
    ("Description", "TEXT", "", "NOT NULL"),
    ("Keywords", "TEXT", "", "NOT NULL"),
    ("DescriptionEmbedding", "TEXT", "", "NOT NULL"),
]

synoyms_table_cols = [
    ("Keyword", "TEXT", "PRIMARY KEY", "NOT NULL"),
    ("Synonyms", "TEXT", "", "NOT NULL"),
]


def create_cmd_create_table(table_cols):
    """
    A function to create sqlite table using table_cols.
    """
    col_settings = ""
    for col in table_cols:
        col_name = col[0]
        type = col[1]
        as_primary_key = col[2]
        not_null = col[3]
        col_settings += f"{col_name}  {type}  {as_primary_key}  {not_null},"
    # Remove last ,
    col_settings = col_settings[:-1]

    cmd = f"""
    CREATE TABLE Main
    (
        {col_settings}
    );
    """
    return cmd


cmd_create_app_description_table = create_cmd_create_table(
    app_description_table_cols,
)

cmd_create_synonyms_table = create_cmd_create_table(
    synoyms_table_cols,
)


class SerDatabase:
    def __init__(
        self,
        database_name,
        database_path,
    ):
        self._database_name = database_name
        self._database_path = database_path
        self._database_conn = None

    @property
    def database_path(self):
        """Get the database path of the database."""
        return self._database_path

    def create_database(self, cmd_create_table):
        if os.path.join(self._database_path):
            return

        self.database_conn = sqlite3.connect(self.database_path)
        print("Connected to database successfully")
        c = self.database_conn.cursor()

        c.execute(cmd_create_table)

        print("Table created successfully")
        self.database_conn.commit()
        self.database_conn.close()

    def update_last_use(self):
        self._database_conn = sqlite3.connect(self._database_path)
        c = self._database_conn.cursor()
        print("Connected to database successfully")

        description = "*** Last Use"
        year = time.localtime().tm_year
        month = time.localtime().tm_mon
        day = time.localtime().tm_mday
        hour = time.localtime().tm_hour
        mins = time.localtime().tm_min
        sec = time.localtime().tm_sec
        str_date_time = "{}‐{:0>2d}‐{:0>2d} {:0>2d}:{:0>2d}:{:0>2d}".format(
            year, month, day, hour, mins, sec
        )

        cmd = ""
        try:
            # try to insert
            cmd = "INSERT INTO main (Description,Command,Enter,CopyPaste,UseAsNote,Comment,OpenFolder) \
                                                            VALUES ('{}', '{}', '{}', '{}', '{}', '{}', '{}' )".format(
                description,
                str_date_time,
                str(False),
                str(False),
                str(False),
                "nothing",
                str(False),
            )

            c.execute(cmd)
        except Exception as e:
            print(e)
            try:
                print("No need to Insert, Desicription *** Last Use existed")
                print("Updating...")
                cmd = (
                    "UPDATE Main SET "
                    "Description='{}', Command='{}', "
                    "Enter='{}', CopyPaste='{}', "
                    "UseAsNote='{}', Comment='{}', OpenFolder='{}' "
                    "WHERE "
                    "Description='{}'".format(
                        description,
                        str_date_time,
                        str(False),
                        str(False),
                        str(False),
                        "nothing",
                        str(False),
                        description,
                    )
                )
            except Exception as e:
                print(e)

            c.execute(cmd)

        self._database_conn.commit()
        print("Update successfully")
        self._database_conn.close()
