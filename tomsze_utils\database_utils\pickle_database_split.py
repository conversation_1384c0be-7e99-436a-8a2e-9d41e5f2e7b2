import sys
import datetime
from concurrent.futures import as_completed, wait
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor
from dataclasses import dataclass
import json
import os
import time
from typing import Any, Dict, List, Tuple
from events import Events

import numpy as np
import portalocker
from tomsze_utils.config_parser_utils import (
    create_config_parser_from_path,
    read_from_config_parser,
    set_and_write_parser_to_file,
)
from tomsze_utils.dict_utils import (
    get_nested_dict_value_by_key_list,
    set_nested_dict_value_by_key_list,
    split_a_dict_to_list_dict,
)
from tomsze_utils.list_utils import append_and_squeeze_list
from tomsze_utils.path_utils import remove_all_files_of_a_type_in_directory
from tomsze_utils.randome_utils import generate_random_string
from tomsze_utils.sync_utils_dir.file_lock_utils import (
    acquire_lock,
    release_lock,
    is_lock_file_locked,
)
from tomsze_utils.variable_dump_read_utils import (
    dump_variable_to_pickle,
    load_pickle_file,
)
from tomsze_utils.file_watcher_utils import FileWatcher


def create_db_info(
    part_data_count: int,
    where_path: str,
) -> None:
    """
    Creates and updates the database configuration file with the specified part data count.

    This function constructs the path to the configuration file and updates the
    `part_data_count` in the specified section of the configuration.

    Args:
        part_data_count (int): The count of parts of data to be set in the configuration.
        where_path (str): The directory path where the configuration file is located.

    Examples:
    ```python
    create_db_info(part_data_count=5, where_path="/path/to/config")
    ```

    ```python
    create_db_info(part_data_count=10, where_path="/another/path")
    ```
    """
    config_path = os.path.join(
        where_path,
        "db.ini",
    )
    conf_parser = create_config_parser_from_path(
        config_path=config_path,
    )

    # Set part_data_count
    set_and_write_parser_to_file(
        config_path=config_path,
        config_parser=conf_parser,
        key="part_data_count",
        data=part_data_count,
        section="sectionx",
    )


def remove_pickles(dir_path: str) -> None:
    """
    Removes all pickle files from the specified directory and its subdirectories.

    This function walks through the given directory and deletes any files
    with the '.pickle' extension.

    Args:
        dir_path (str): The path to the directory from which pickle files will be removed.

    Examples:
    ```python
    remove_pickles(dir_path="/path/to/directory")
    ```

    ```python
    remove_pickles(dir_path="/another/directory")
    ```
    """
    for dirpath, _, filenames in os.walk(dir_path):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            if filetype in ["pickle"]:
                file_path = os.path.join(dirpath, filename)

                os.remove(file_path)


@dataclass
class FetchedResult:
    """
    ```
    use_cache: bool
    obtained_value: Any
    ```
    """

    use_cache: bool
    obtained_value: Any


"""
Technical highlight:
- Use pickle as database
- Split data into parts to dump.
- Dump only the part with updated (dirty) item.
- TODO: parallel load and dump
- 
"""


class PickleDatabaseSplit:
    """
    This database wraps a pickle file and
    provides some methods.

    The pickle file is just a dict.
    ```
    {
        'xx':
            {
                'aa': any,
                'cc': any,
            },
        'yy':
            {
                'aa': any,
                'cc': any,
            },
    }
    ```
    where aa and cc must be consistent for each data.

    """

    def __init__(
        self,
        db_name: str,
        id: str = None,
        db_fpath: str = None,
        create_db_fpath_if_not_exist: bool = True,
        dump_db_fpath: str = None,
        part_data_count: int = -1,
        load_by_thread: bool = True,
        use_sync_by_lock_file: bool = False,
    ) -> None:
        """
        Initializes the PickleDatabaseSplit instance.

        Args:
            db_name (str): The name of the database.
            db_fpath (str, optional): The file path for the database. Defaults to None.
            create_db_fpath_if_not_exist (bool, optional): Whether to create the database path if it does not exist. Defaults to True.
            dump_db_fpath (str, optional): The file path for dumping the database. Defaults to None.
            part_data_count (int, optional): The number of parts to split the data into. Defaults to -1.
            load_by_thread (bool, optional): Whether to load data using threads. Defaults to True.
                start_file_watcher (bool, optional): Whether to start the file watcher, which monitors the database file for any changes made externally.
                This ensures that the database instance remains synchronized with the file system, allowing for real-time updates and preventing data inconsistency.
                When enabled, the file watcher will continuously check for modifications to the database file and automatically update the in-memory representation of the database accordingly.
                This helps to avoid issues that may arise from concurrent modifications. Defaults to False.
            use_sync_by_lock_file (bool, optional): Whether to use a lock file for synchronization. Defaults to True.

        Examples:
            ```python
            db = PickleDatabaseSplit(db_name="my_db", db_fpath="/path/to/db")
            ```

            ```python
            db = PickleDatabaseSplit(db_name="my_db", part_data_count=5, load_by_thread=False)
            ```
        """
        if db_fpath:
            self.db_fpath = db_fpath
            if create_db_fpath_if_not_exist:
                if not os.path.exists(self.db_fpath):
                    os.makedirs(self.db_fpath, exist_ok=True)
        else:
            self.db_fpath = ""

        self.db_name = db_name
        if not id:
            self.id = generate_random_string(5)
        else:
            self.id = id

        if dump_db_fpath:
            self.dump_db_fpath = dump_db_fpath
        else:
            self.dump_db_fpath = db_fpath

        self.n_db_ind_places = 5
        self.max_dump_workers = 10
        self.max_load_workers = 1  # 1 is the best..
        self.load_by_thread = load_by_thread
        self.loaded_parts: List[Dict] = (
            self.create_or_load_pickle_thread(
                self.db_fpath,
            )
            if load_by_thread
            else self.create_or_load_pickle_no_thread(
                self.db_fpath,
            )
        )
        # Since part_data_count is designed to be optional, if not given, read from db_fpath
        self.part_data_count = (
            part_data_count if part_data_count > 0 else self.read_data_count()
        )
        assert self.part_data_count > 0

        # self.dirties: List[bool] = []  # currently no use
        self.dirty_inds: List[int] = []  # only update in update_data method.
        self.data_inconsistency = {}

        self.update_version_fpath = ""
        if self.db_fpath != "":
            self.update_version_fpath = os.path.join(self.db_fpath, "update_ver.txt")
        self.create_update_version_file_if_not_exist()

        # Sync by lock file.
        self.use_sync_by_lock_file = use_sync_by_lock_file
        self.lock_file = None
        if self.use_sync_by_lock_file:
            # Create lock file (real file).
            self.lock_file_path = ""
            if self.db_fpath != "":
                self.lock_file_path = os.path.join(self.db_fpath, "lock_file")
            self.create_lock_file()

            # Create file watcher.
            if self.db_fpath != "":
                self.events = Events()
                self.file_watcher = FileWatcher(
                    events=self.events,
                    collected_changes_trigger_run_mil_second=5,
                    list_ignore_directory=["__pycache__"],
                )
                self.is_file_watcher_disable = False
                self.file_watcher.register_external_event_callback(
                    event_name="update_version_file_change_event",
                    callback=self.file_change_callback,
                )
                if not self.is_file_watcher_disable:
                    self.file_watcher.start(self.db_fpath)

            # Set update version from text in update version file.
            self.update_version = (
                ""  # an update datetime of the db folder of a last update
            )
            if os.path.exists(self.update_version_fpath):
                with open(self.update_version_fpath, "r") as file:
                    self.update_version = file.read()

    def disable_file_watcher(self):
        self.file_watcher.stop()
        self.is_file_watcher_disable = True

    def file_change_callback(
        self,
        e,
        reason,
    ):
        list_collected_file_changes = (
            self.file_watcher.get_list_collected_changed_files()
        )

        # Check if the update version file has changed, indicating a need to reload data from pickles.
        if self.update_version_fpath.replace("\\", "/") in list_collected_file_changes:
            print(f"db id {self.id} might need to reload from pickles.")

            # Reload from pickles only if the update version file has changed.
            with open(self.update_version_fpath) as f:
                content_str = f.read()
            if self.update_version != content_str:
                self.reload_from_pickles()

    def reload_from_pickles(self):
        print(f"db id {self.id} reloading from pickles")
        self.__init__(
            db_fpath=self.db_fpath,
            db_name=self.db_name,
            id=self.id,
            dump_db_fpath=self.dump_db_fpath,
            load_by_thread=self.load_by_thread,
            part_data_count=self.part_data_count,
            use_sync_by_lock_file=self.use_sync_by_lock_file,
        )
        print(f"db id {self.id} reloaded from pickles")

    def reload_from_path(self, path):
        """
        Reloads the database from a specified path.
        """
        print(f"db id {self.id} reloading from path {path}")

        if not os.path.exists(path):
            return f"Path '{path}' does not exist."

        self.__init__(
            db_fpath=path,
            db_name=self.db_name,
            id=self.id,
            dump_db_fpath=self.dump_db_fpath,
            load_by_thread=self.load_by_thread,
            part_data_count=self.part_data_count,
            use_sync_by_lock_file=self.use_sync_by_lock_file,
        )

        return "Done"

    def set_update_version(self, update_version: str):
        """
        Sets the update version for the database.

        Args:
            update_version (str): The new update version to be set.
        """
        self.update_version = update_version

    def read_data_count(self):
        """
        Read data count from database path txt file (db_info.ini)
        """
        config_path = os.path.join(self.db_fpath, "db.ini")

        if not os.path.exists(config_path):
            # TODO log error
            print(f'config_path "{config_path}" does not exist')
            print(f"unable to read data_count, set to 10")
            return 10

        conf_parser = create_config_parser_from_path(config_path=config_path)

        # Read from ini
        part_data_count = read_from_config_parser(
            config_parser=conf_parser,
            is_string=False,
            default=-3,
            key="part_data_count",
            section="sectionx",
        )

        return part_data_count

    def create_or_load_pickle_no_thread(
        self,
        pickle_db_fpath,
    ):
        """
        Runs in __init__ method.

        Creates pickle if no pickle file exist in given directory.
        Else loads the pickle file(s) to a list.

        Returns:
        A list of pickled data.
        """
        create_pickle = True
        loaded_parts = [{}]

        # Check if given path exist, else do nothing
        if not os.path.exists(pickle_db_fpath):
            print(
                f'pickle_db_fpath "{pickle_db_fpath}" does not exist.'
            )  # TODO log error
            return loaded_parts

        # If there is no file, create one (with index 0)
        # If there is no pickle file in the folder, create one (with index 0),
        pickle_files = []
        for dirpath, _, filenames in os.walk(pickle_db_fpath):
            for filename in filenames:
                filetype = filename.split(".")[-1]
                if filetype in ["pickle"]:
                    file_path = os.path.join(dirpath, filename)
                    create_pickle = False
                    pickle_files.append(file_path)

        # Create pickle file
        if create_pickle:
            str_num = str(0).zfill(self.n_db_ind_places)
            dump_variable_to_pickle(
                variable=[{}],
                pickle_path=os.path.join(
                    pickle_db_fpath,
                    f"{self.db_name}_{str_num}.pickle",
                ),
            )

        # Load all pickle files
        print("Loading pickle files")
        if not create_pickle:
            time_start = time.time()
            del loaded_parts[-1]

            for pickle_file in pickle_files:
                loaded = load_pickle_file(
                    pickle_path=pickle_file,
                    default={},
                )
                if loaded != [{}]:
                    loaded_parts.append(loaded)

            time_end = time.time()
            print(
                f"{len(pickle_files)} pickles loaded in:"
                + "{:.2f}".format((time_end - time_start) * 1000)
                + " ms"
            )

        return loaded_parts

    def create_or_load_pickle_thread(
        self,
        pickle_db_fpath,
    ):
        """
        Runs in __init__ method.

        Creates pickle if no pickle file exist in given directory.
        Else loads the pickle file(s) to a list using thread.

        Note: loaded parts WILL NOT be have same order as the pickle file index.

        Returns:
        A list of pickled data.
        """
        create_pickle = True
        loaded_parts = [{}]

        # Check if given path exist, else do nothing
        if not os.path.exists(pickle_db_fpath):
            print(
                f'pickle_db_fpath "{pickle_db_fpath}" does not exist.'
            )  # TODO log error
            return loaded_parts

        # If there is no file, create one (with index 0)
        # If there is no pickle file in the folder, create one (with index 0),
        pickle_files = []
        for dirpath, _, filenames in os.walk(pickle_db_fpath):
            for filename in filenames:
                filetype = filename.split(".")[-1]
                if filetype in ["pickle"]:
                    file_path = os.path.join(dirpath, filename)
                    create_pickle = False
                    pickle_files.append(file_path)

        # Create pickle file
        if create_pickle:
            str_num = str(0).zfill(self.n_db_ind_places)
            dump_variable_to_pickle(
                variable=[{}],
                pickle_path=os.path.join(
                    pickle_db_fpath,
                    f"{self.db_name}_{str_num}.pickle",
                ),
            )

        # Load all pickle files using thread
        if not create_pickle:
            time_start = time.time()
            del loaded_parts[-1]

            def thread_load_pickle_file(pickle_file):
                loaded = load_pickle_file(
                    pickle_file,
                    {},
                )
                loaded_parts.append(loaded)

            with ThreadPoolExecutor(max_workers=self.max_load_workers) as executor:
                futures = []
                for pickle_file in pickle_files:

                    future = executor.submit(
                        thread_load_pickle_file,
                        pickle_file,
                    )

                    futures.append(future)

                wait(futures)

            time_end = time.time()
            print(
                f"{len(pickle_files)} pickles loaded in:"
                + "{:.2f}".format((time_end - time_start) * 1000)
                + " ms"
            )

        # In the case when another db instance load the folder with
        # an pickle file with one [{}]. The loaded_parts will
        # be [[{}]].
        if loaded_parts == [[{}]]:
            return [{}]

        return loaded_parts

    def re_split(
        self,
        part_data_count=-1,
    ):
        """
        Check if current loaded parts matches part_data_count.
        If not, re-split it

        If a part_data_count is not given,
        use constuct parameter part_data_count instead.
        """
        if part_data_count == -1:
            part_data_count = self.part_data_count

        resplit = False
        for loaded_part in self.loaded_parts:
            if len(loaded_part) != part_data_count:
                resplit = True
                break

        if not resplit:
            return

        # Split
        n_data = np.sum([len(x) for x in self.loaded_parts])
        loaded_parts_new = []
        loaded_part_new = {}
        count = 0
        for loaded_part in self.loaded_parts:
            for key, value in loaded_part.items():
                loaded_part_new[key] = value
                count += 1
                if len(loaded_part_new) == part_data_count or count == n_data:
                    loaded_parts_new.append(loaded_part_new.copy())
                    loaded_part_new.clear()

        self.loaded_parts = loaded_parts_new.copy()

    # def update_data_back(
    #     self,
    #     datas: Dict,
    # ):
    #     """
    #     Update (also insert) to the loaded parts
    #     by ** operator.
    #     """
    #     # Get last of loaded_parts and remove it from loaded_parts temporily
    #     last_part = self.loaded_parts[-1].copy()
    #     del self.loaded_parts[-1]

    #     for i, key in enumerate(datas.keys()):
    #         # If last part is full,
    #         # add to loaded_parts when it is full (only if added by new data),
    #         # create a new dict
    #         if len(last_part) == self.part_data_count:
    #             # if list(last_part.keys())[-1] in datas:
    #             self.loaded_parts.append(last_part.copy())
    #             # self.loaded_parts[last_ind] = last_loaded
    #             last_part.clear()

    #         # If last loaded is not full, add item to the dict if it is new
    #         # else update
    #         new = True
    #         for loaded_part in self.loaded_parts:
    #             if key in loaded_part:
    #                 new = False
    #                 loaded_part[key] = datas[key]
    #                 break

    #         if new:
    #             last_part[key] = datas[key]

    #     if len(last_part) > 0:
    #         self.loaded_parts.append(last_part.copy())

    def update_data(
        self,
        datas: Dict,
        dump: bool = False,
    ):
        """
        Update a dict (also insert) to the loaded parts.
        And update dirties list.

        ** Remember to dump to pickle file using related method.

        Example:
            ```python
            db.update_data(
                datas={
                    "k1": "d1",
                    "k2": "d2",
                    "k3": "d3",
                    "k4": "d4",
                }
            )
            expect = [
                {
                    "k1": "d1",
                    "k2": "d2",
                },
                {
                    "k3": "d3",
                    "k4": "d4",
                },
            ]
            assert db.loaded_parts == expect
            ```
        """
        # Get last of loaded_parts and remove it from loaded_parts temporily
        last_part = {}
        if self.loaded_parts:
            last_part = self.loaded_parts[-1].copy()
            del self.loaded_parts[-1]

        # if self.loaded_parts[0] == {}:
        #     del self.loaded_parts[0]

        # self.dirty_inds.clear()

        for i, key in enumerate(datas.keys()):
            # If last part is full,
            # add to loaded_parts when it is full
            if len(last_part) == self.part_data_count:
                # if list(last_part.keys())[-1] in datas:
                self.loaded_parts.append(last_part.copy())
                # self.loaded_parts[last_ind] = last_loaded
                last_part.clear()

            # -- If it is new, add to part, else update. --
            # Update if not new.
            new = True
            for i, loaded_part in enumerate(self.loaded_parts):
                if key in loaded_part:
                    new = False
                    loaded_part[key] = datas[key]

                    # Mark each part whether dirty.
                    if not i in self.dirty_inds:
                        self.dirty_inds.append(i)
                    break

            # Add to part if new.
            if new:
                last_part[key] = datas[key]

                # Record dirty ind for new data.
                if not len(self.loaded_parts) in self.dirty_inds:
                    self.dirty_inds.append(len(self.loaded_parts))

        if len(last_part) > 0:
            self.loaded_parts.append(last_part.copy())

        # # Set the dirty list.
        # self.dirties = [False for x in self.loaded_parts]
        # for i in self.dirty_inds:
        #     self.dirties[i] = True

        # Dump
        if dump:
            if self.dirty_inds:
                self.dump_dirty_parts_to_pickles_thread()

    def update_data_with_key_and_col(
        self,
        key: str,
        col: str,
        data: Any,
        do_merge_if_exist: bool = False,
    ):
        """
        Update a data (also insert) using key and col to the loaded parts.
        And update dirties list.

        ** Remember to dump to pickle file using related method.

        Example:
            db.update_data_with_key_and_col(
                key="k1",
                col="c1",
                data="dc1",
            )

            db.update_data_with_key_and_col(
                key="k1",
                col="c2",
                data="dc2",
            )
            expect = [
                {
                    "k1": {
                        "c1": "dc1",
                        "c2": "dc2",
                    },
                },
            ]
            assert db.loaded_parts == expect
        """
        is_empty = False
        if self.loaded_parts == [{}]:
            self.loaded_parts = []
            is_empty = True

        if self.loaded_parts == []:
            is_empty = True

        new_key = True
        new_col = True
        for i, loaded_part in enumerate(self.loaded_parts):
            if key in loaded_part:
                new_key = False

                if col in loaded_part[key]:
                    new_col = False

                # Update loaded parts.
                if do_merge_if_exist:
                    if isinstance(data, List) or isinstance(data, Dict):
                        if new_col:
                            loaded_part[key][col] = data
                        else:
                            loaded_part[key][col] += data
                    else:
                        do_merge_if_exist = False

                if not do_merge_if_exist:
                    loaded_part[key][col] = data

                # Mark each part whether dirty.
                if not i in self.dirty_inds:
                    self.dirty_inds.append(i)

                break

        is_full = False
        if self.loaded_parts:
            if len(self.loaded_parts[-1]) == self.part_data_count:
                is_full = True

        # Insert new part if full
        if new_key and new_col:
            if is_full:
                if not len(self.loaded_parts) in self.dirty_inds:
                    self.dirty_inds.append(len(self.loaded_parts))
                self.loaded_parts.append({key: {col: data}})

            # Insert to last part if not full
            if not is_full:
                if is_empty:
                    self.loaded_parts = [{key: {col: data}}]
                    if not 0 in self.dirty_inds:
                        self.dirty_inds.append(0)

                if not is_empty:
                    self.loaded_parts[-1][key] = {}
                    self.loaded_parts[-1][key][col] = data
                    if not len(self.loaded_parts) - 1 in self.dirty_inds:
                        self.dirty_inds.append(len(self.loaded_parts) - 1)

    def update_data_with_keys(
        self,
        keys: List[str],
        data: Any,
    ):
        """
        Update a data (also insert) using keys to the loaded parts.
        And update dirties list.

        ** Remember to dump to pickle file using related method.

        Example:
            db.update_data_with_keys(
                keys=["k1", "k12", "k13"],
                data="k1_data",
            )

            assert db.loaded_parts ==  [
                {
                    "k1": {"k12": {"k13": "k1_data"}},
                },
            ]
        """
        # Check if loaded_parts is empty.
        is_empty = False
        if self.loaded_parts == [{}]:
            self.loaded_parts = []
            is_empty = True

        if self.loaded_parts == []:
            is_empty = True

        # Loop through the loaded parts, if key exist, update it with new data.
        new_key = True
        for i, loaded_part in enumerate(self.loaded_parts):
            key = keys[0]
            if key in loaded_part:
                new_key = False

                # Update loaded parts.
                loaded_part = set_nested_dict_value_by_key_list(
                    base_dict=loaded_part,
                    keys=keys,
                    value=data,
                )

                # Mark each part whether dirty.
                if not i in self.dirty_inds:
                    self.dirty_inds.append(i)

                break

        # Check if the last loaded part is full.
        is_full = False
        if self.loaded_parts:
            if len(self.loaded_parts[-1]) == self.part_data_count:
                is_full = True

        # Insert the new data the last part is full.
        if new_key:
            dict_new = set_nested_dict_value_by_key_list(
                base_dict={},
                keys=keys,
                value=data,
            )
            if is_full:
                if not len(self.loaded_parts) in self.dirty_inds:
                    self.dirty_inds.append(len(self.loaded_parts))

                self.loaded_parts.append(dict_new)

            # Insert to last part if not full
            if not is_full:
                if is_empty:
                    self.loaded_parts = [dict_new]
                    if not 0 in self.dirty_inds:
                        self.dirty_inds.append(0)

                if not is_empty:
                    self.loaded_parts[-1] = set_nested_dict_value_by_key_list(
                        base_dict=self.loaded_parts[-1],
                        keys=keys,
                        value=data,
                    )

                    if not len(self.loaded_parts) - 1 in self.dirty_inds:
                        self.dirty_inds.append(len(self.loaded_parts) - 1)

    def dump_all_parts_to_pickles(
        self,
        fake_dump_time=0,
        wait_lock_release_time_in_second=10,
    ):
        """
        Dump each load parts to pickle files.

        Just a simple wrap around the dump_to_pickle
        function from tomsze_utils.
        """
        error_message = ""

        def dump_and_write_update_version():
            if fake_dump_time > 0:
                time.sleep(fake_dump_time)

            if not os.path.exists(
                self.dump_db_fpath
            ):  # might be "" due to db_fpath might be None ""
                print("dump_db_fpath is None, skip rest of function")
                return

            # If data  is empty, remove all pickle files
            if len(self.loaded_parts) == 0:
                remove_all_files_of_a_type_in_directory(self.dump_db_fpath, "pickle")

                str_num = str(0).zfill(self.n_db_ind_places)
                dump_variable_to_pickle(
                    variable=[{}],
                    pickle_path=os.path.join(
                        self.dump_db_fpath,
                        f"{self.db_name}_{str_num}.pickle",
                    ),
                )
            else:
                for i, loaded_part in enumerate(self.loaded_parts):
                    str_num = str(i).zfill(self.n_db_ind_places)
                    dump_variable_to_pickle(
                        variable=loaded_part,
                        pickle_path=os.path.join(
                            self.dump_db_fpath,
                            f"{self.db_name}_{str_num}.pickle",
                        ),
                    )
            self.write_update_version()

        # --- end of nested function

        if self.use_sync_by_lock_file:
            try:
                with portalocker.Lock(
                    filename=self.lock_file_path,
                    mode="rb+",
                    timeout=wait_lock_release_time_in_second,
                ) as fh:  # timeout in s
                    dump_and_write_update_version()
            except portalocker.exceptions.AlreadyLocked as e:
                error_message = str(e)
                print(f"An error occurred while acquiring the lock: {e}")
        else:
            dump_and_write_update_version()

        self.dirty_inds.clear()

        return error_message

    def dump_dirty_parts_to_pickles(self):
        """
        Dump 'dirty' load parts to pickle files.

        (Just a simple wrap around the dump_to_pickle
        function from tomsze_utils.)
        """
        if not os.path.exists(
            self.dump_db_fpath
        ):  # might be "" due to db_fpath might be None ""
            print("dump_db_fpath is None, skip rest of function")
            return

        for dirty_ind in self.dirty_inds:
            str_num = str(dirty_ind).zfill(self.n_db_ind_places)

            # Dump only when the part is dirty
            dump_variable_to_pickle(
                variable=self.loaded_parts[dirty_ind],
                pickle_path=os.path.join(
                    self.dump_db_fpath,
                    f"{self.db_name}_{str_num}.pickle",
                ),
            )

        self.dirty_inds.clear()

        self.write_update_version()

    def dump_dirty_parts_to_pickles_thread(self):
        """
        Dump 'dirty' load parts to pickle files using thread.

        (Just a simple wrap around the dump_to_pickle
        function from tomsze_utils.)
        """
        if not os.path.exists(
            self.dump_db_fpath
        ):  # might be "" due to db_fpath might be None ""
            print("dump_db_fpath is None, skip rest of function")
            return

        with ThreadPoolExecutor(max_workers=self.max_dump_workers) as executor:
            futures = []
            for dirty_ind in self.dirty_inds:
                variable = self.loaded_parts[dirty_ind]
                str_num = str(dirty_ind).zfill(self.n_db_ind_places)
                pickle_path = os.path.join(
                    self.dump_db_fpath,
                    f"{self.db_name}_{str_num}.pickle",
                )
                # Dump only when the part is dirty
                # dump_variable_to_pickle(
                #     variable=variable,
                #     pickle_path=pickle_path,
                # )
                future = executor.submit(
                    dump_variable_to_pickle,
                    variable,
                    pickle_path,
                )
                futures.append(future)

        self.dirty_inds.clear()

        self.write_update_version()

    def write_update_version(self):
        # Stop file watcher before writing to avoid this file change.
        if self.use_sync_by_lock_file:
            if not self.is_file_watcher_disable:
                self.file_watcher.stop()

        # Set update version
        date_now_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.set_update_version(date_now_str)

        if os.path.exists(self.update_version_fpath):
            with open(self.update_version_fpath, "w") as file:
                file.write(date_now_str)

        if self.use_sync_by_lock_file:
            if not self.is_file_watcher_disable:
                self.file_watcher.start(self.db_fpath)

    def check_data_consistency(self):
        """
        Check data consistency by comparing
        the first data's datatype and the
        rest of the data.

        Store and return the data that is not consistent.
        """

        if not self.loaded_parts or len(self.loaded_parts) <= 1:
            self.data_inconsistency = {}
            return self.data_inconsistency

        data_type = None
        data_count = 0
        for loaded_part in self.loaded_parts:

            for data_key in loaded_part:
                if data_type is None and data_count == 0:
                    data_type = type(loaded_part[data_key])
                    data_count = len(loaded_part[data_key])
                elif data_type != type(loaded_part[data_key]) or data_count != len(
                    loaded_part[data_key]
                ):
                    self.data_inconsistency = {
                        data_key: loaded_part[data_key],
                    }
                    return self.data_inconsistency

        self.data_inconsistency = {}
        return self.data_inconsistency

    def query_as_list(
        self,
        keys: List[str],
        col: str,
    ):
        """
        Query on certain data (certain keys) on one "column"

        Note: no thread version, this is much faster than threaded version.
        (same as compared to a search in a dict which is very fast)

        Return a list.
        """
        # query_result = [self.loaded[key][column] for key in keys if key in self.loaded]
        query_results = []
        for loaded_part in self.loaded_parts:
            query_result = [loaded_part[key][col] for key in keys if key in loaded_part]
            query_results += query_result

        return query_results

    def query_key_col(
        self,
        key: str,
        col: str,
        default=None,
    ) -> Any:
        """
        Query on certain data (a certain key) on one "column"

        Note: no thread version, this is much faster than threaded version.
        (same as compared to a search in a dict which is very fast)

        Return a data.
        """
        # query_result = [self.loaded[key][column] for key in keys if key in self.loaded]
        query_result = None
        for loaded_part in self.loaded_parts:
            if key not in loaded_part:
                continue
            if col not in loaded_part[key]:
                continue
            query_result = loaded_part[key][col]

        if query_result is None:
            return default

        return query_result

    def query_key(
        self,
        key: str,
        default=None,
    ) -> Any:
        """
        Query on certain data with a key

        Note: no thread version, this is much faster than threaded version.
        (same as compared to a search in a dict which is very fast)

        Return a data.
        """
        # query_result = [self.loaded[key][column] for key in keys if key in self.loaded]
        query_result = None
        for loaded_part in self.loaded_parts:
            if key not in loaded_part:
                continue
            query_result = loaded_part[key]

        if query_result is None:
            return default

        return query_result

    def query_by_keys_list(
        self,
        keys_list: List[str],
        default: Any = None,
    ) -> Tuple[Any, int]:
        """
        Query for data using a list of keys.

        This function searches through the loaded parts of the database for the specified keys.
        If a matching key is found, it returns the corresponding value and the index of the loaded part.

        Note: no thread version, this is much faster than threaded version.
        (same as compared to a search in a dict which is very fast)

        Args:
            keys_list (List[str]): A list of keys to query.
            default (Any): The default value to return if no match is found.

        Returns:
            Tuple[Any, int]: The queried data and the index of the loaded part, or default and -1 if not found.

        Examples:
        ---------
        ```python
        result, index = query_by_keys_list(keys_list=["key1", "key2"], default=None)
        ```

        ```python
        result, index = query_by_keys_list(keys_list=["keyA", "keyB"], default="Not Found")
        ```
        """
        for i, loaded_part in enumerate(self.loaded_parts):
            query_result = get_nested_dict_value_by_key_list(
                loaded_part,
                keys_list,
                None,
            )
            if query_result is not None:
                return query_result, i

        return default, -1  # Return -1 if not found to indicate no valid index

    def is_no_duplicate(self):
        # Get sum of data count
        count0 = 0
        for part in self.loaded_parts:
            count0 += len(part)

        # Merge each part into a dict and count
        dict_tmp = {}
        for part in self.loaded_parts:
            dict_tmp = {**dict_tmp, **part}
        count1 = len(dict_tmp)

        # Compare this two count, if same, no duplicate
        if count0 == count1:
            return True
        else:
            return False

    def is_key_in(
        self,
        key: str,
    ):
        """
        Check if given key is in database.
        No thread is used as it should be faster.?

        Args:
            key: str

        Returns:
            is_in: bool
                Is in database or not.
        """
        is_in = False
        for loaded_part in self.loaded_parts:
            if key in loaded_part:
                is_in = True
                break

        return is_in

    def is_key_in_col(
        self,
        key: str,
        col: str,
    ):
        """
        Check if given key and col are together in database.
        No thread is used as it should be faster.?

        Args:
            key: str
            col: str

        Returns:
            is_in: bool
                Is in database or not.
        """
        is_in = False
        for loaded_part in self.loaded_parts:
            if key in loaded_part:
                if col in loaded_part[key]:
                    is_in = True
                    break

        return is_in

    def check_keys_in_db(
        self,
        keys: List[str],
    ):
        """
        Check if given keys are in database.
        No thread is used as it should be faster.?

        Args:
            keys: List[str]

        Returns:
            is_new:
                Are the keys in database.
            new_keys:
                A list of keys that are not in database.
            existing_keys:
                A list of keys that are not in database.
        """
        is_new = False
        new_keys = []
        existing_keys = []
        for key in keys:
            new_keys.append(key)  # assume in db
            for loaded_part in self.loaded_parts:
                if key in loaded_part:
                    existing_keys.append(key)
                    new_keys.remove(key)
                    break

        if new_keys:
            is_new = True

        return is_new, new_keys, existing_keys

    def fetch_or_func(
        self,
        key: str,
        col: str,
        func_to_value: callable,
        func_kwargs: Dict,
    ) -> FetchedResult:
        """
        Fetch -> function -> update.

        Try fetch a data from cache using a key or
        runs a function to get a data.

        If data_key is in picke_data,
        get value from pickle_data.

        If data_key not in picke_data,
        run func_to_value and store the value to picke_data,
        then dump the picke_data.

        Args:
        ----
        pickle_path: str
            used for dumping.

        Returns:
        ---
        CacheResult.
        """

        use_cache = True
        value = self.query_key_col(
            key=key,
            col=col,
        )

        if not value:
            use_cache = False
            value = func_to_value(**func_kwargs)

            # Store data
            self.update_data({key: {col: value}})

        return FetchedResult(use_cache, value)

    def dump_json(self):
        """
        Save loaded parts to a json file.
        """
        json_path = os.path.join(self.db_fpath, "db_apps.json")
        with open(json_path, "w") as outfile:
            json.dump(self.loaded_parts, outfile, indent=4)

    def to_mongodb(
        self,
        mongo_db_name: str,
        mongo_coll_name: str,
    ):
        """
        Export loaded parts to mongodb using insert_one method.

        ** This is a temp function, and not for general use.
        """
        from pymongo import MongoClient

        # Connection string
        uri = "mongodb://localhost:27017/"  # can be found from Mongo Compass (a software with gui).

        # Create a new client and connect to the server
        client = MongoClient(uri)

        # get/create the database and collection on which to run the operation
        collection = client[mongo_db_name][mongo_coll_name]

        # insert documents
        for loaded_part in self.loaded_parts:
            for dictx in loaded_part:
                for key in dictx.keys():
                    app_id = key
                    document = {
                        "app_id": app_id,
                        **dictx[key],
                    }
                    document["embd"] = document["embd"].tolist()
                    collection.insert_one(document=document)

    def get_db_num_keys(self):
        """
        Get the num of keys of the db.

        Get the num of parts.
        if num of parts is 1
            return the first part len
        if num of parts is greater than 1
            return the first part len + num of parts * part data count

        """
        num_keys = 0
        if not self.loaded_parts:
            return num_keys

        num_parts = len(self.loaded_parts)

        if num_parts == 1:
            num_keys = len(self.loaded_parts[0])
            return num_keys

        num_keys = (num_parts - 1) * self.part_data_count + len(self.loaded_parts[-1])

        return num_keys

    def get_num_data_by_key_value(self, key, value):
        """
        Get the number of data entries that contain a specific key-value pair.

        ```
        db.update_data(
            datas={
                "d1": {"key1": "value1"},
                "d2": {"key1": "value2"},
                "d3": {"key1": "value3"},
            }
        )

        num = db.get_num_data_by_key_value(key="key1", value="value1")
        expect = 1
        ```
        """
        count = 0
        if not self.loaded_parts:
            return count

        for loaded_part in self.loaded_parts:
            for data_key, data_value in loaded_part.items():
                if key in data_value and data_value[key] == value:
                    count += 1
        return count

    def get_db_keys(self):
        """
        Get the keys of the db.
        """
        keys_list = []
        if not self.loaded_parts:
            return keys_list

        for loaded_part_dict in self.loaded_parts:
            for key in loaded_part_dict.keys():
                keys_list.append(key)

        return keys_list

    def remove_pickles(self, dir_path=None):
        if not dir_path:
            dir_path = self.db_fpath
        remove_pickles(dir_path)

    def get_last_data(self):
        data_dict = {}
        if self.loaded_parts:
            last_part_num_keys = len(self.loaded_parts[-1])
            key = list(self.loaded_parts[-1].keys())[last_part_num_keys - 1]
            value = self.loaded_parts[-1][key]
            data_dict = {key: value}

        return data_dict

    def remove_all_empty_loaded_part(self):
        self.loaded_parts = [item for item in self.loaded_parts if item != {}]

    def reorganize_loaded_parts(self):
        """
        Reorganize loaded_parts by
        1. remove any {}
        2. move last data to the part that is not full ()
        """
        self.remove_all_empty_loaded_part()

        for ind, loaded_part_dict in enumerate(self.loaded_parts):
            if len(loaded_part_dict) < self.part_data_count:
                if ind != len(self.loaded_parts) - 1:
                    # Fill using the last data.
                    last_data = self.get_last_data()
                    loaded_part_dict[list(last_data.keys())[0]] = list(
                        last_data.values()
                    )[0]

                    # Remove the last data.
                    del self.loaded_parts[-1][list(last_data.keys())[0]]

                    # Remove the last part if it is {} (empty).
                    if self.loaded_parts[-1] == {}:
                        del self.loaded_parts[-1]

        self.remove_all_empty_loaded_part()

    def remove_key(self, key):
        success = False
        for loaded_part_dict in self.loaded_parts:
            if key in loaded_part_dict:
                success = True
                del loaded_part_dict[key]
                self.reorganize_loaded_parts()
                return success

        return success

    def remove_all_keys_data(self):
        for loaded_part_dict in self.loaded_parts:
            for key in loaded_part_dict.keys():
                loaded_part_dict[key] = {}

    def remove_all_keys(self) -> None:
        """
        Removes all keys from the loaded parts of the database.
        This operation clears all data stored in the database and marks all parts as dirty.

        Args:
            None

        Examples:
            ```python
            db.remove_all_keys()
            ```

            ```python
            db = PickleDatabaseSplit(db_name="test_db", part_data_count=2)
            db.remove_all_keys()
            ```

        Note:
            This function will empty the database and mark all parts as dirty,
            requiring a full rewrite on the next dump.
        """
        for ind, loaded_part in enumerate(self.loaded_parts):
            if ind not in self.dirty_inds:
                self.dirty_inds.append(ind)

        self.loaded_parts = []

    def clear_loaded_parts(self) -> None:
        """
        Clears all data from the database.

        This method removes all keys and values from the loaded parts,
        effectively resetting the database.

        Args:
            None

        Examples:
            ```python
            db.clear()
            ```

            ```python
            db = PickleDatabaseSplit(db_name="my_db", part_data_count=10)
            db.clear()
            ```

        Note:
            This function removes all data and marks all parts as dirty.
        """
        self.remove_all_keys()

    def create_update_version_file_if_not_exist(self):
        """
        Creates an empty update version file if it does not already exist.
        """
        if self.db_fpath and not os.path.exists(self.update_version_fpath):
            print(f"id {self.id} creating update version file")
            with open(self.update_version_fpath, "w") as file:
                file.write("")

    def create_lock_file(self):
        """
        Creates an empty lock file if it does not already exist to ensure exclusive database access.
        """
        if self.db_fpath and not os.path.exists(self.lock_file_path):
            with open(self.lock_file_path, "w") as file:
                file.write("")

    def _open_lock_file(self):
        """
        Attempts to open the lock file associated with the database.

        If the lock file does not exist, it prints an error message and returns.
        If the lock file exists, it opens the file in read and write mode.
        """
        if not os.path.exists(self.lock_file_path):
            print(
                f'Lock file "{self.lock_file_path}" does not exist, failed to obtain lock file'
            )
            return

        self.lock_file = open(self.lock_file_path, "r+")

    def close_lock_file(self):
        if self.lock_file:
            self.lock_file.close()
            print("Closed lock file")

    def acquire_lock(self):
        """
        Attempts to acquire an exclusive lock on the database's lock file.

        If the lock file is not held, it prints an error message.
        If held, it uses `file_lock_utils.acquire_lock` to lock the file.
        On success, it prints "Locked".

        Returns:
            None
        """
        if not self.lock_file:
            print("Lock file is None. Cannot acquire lock.")
            return

        acquire_lock(self.lock_file)

    def release_lock(self):
        """
        Releases the lock on the database file to allow other processes to access it.

        If a lock is held, it releases the lock using `file_lock_utils.release_lock`
        and closes the lock file.
        If not, it prints a message indicating that there is no lock to release.

        Returns:
            None
        """
        if not self.lock_file:
            print("Lock file is None. No lock to release.")
            return

        release_lock(self.lock_file)
        self.close_lock_file()

    def is_lock_file_locked(self):
        """
        Checks if the database's lock file is currently locked by another process.

        Returns:
            bool: `True` if the lock file is locked, `False` otherwise.
        """
        return is_lock_file_locked(self.lock_file_path)

    def append_and_squeeze_list_data(self, keys, data, list_fix_size):
        # Get the data list using the keys
        data_list, index = self.query_by_keys_list(
            keys_list=keys,
            default=None,
        )

        # Modify the data list
        if isinstance(data_list, list):
            new_data_list = append_and_squeeze_list(
                data_list=data_list,
                new_data=data,
                list_fixed_size=list_fix_size,
            )

            # Set the new list data to the keys
            self.update_data_with_keys(keys=keys, data=new_data_list)

            # Check if the current index is not already marked as dirty
            if index not in self.dirty_inds:
                self.dirty_inds.append(index)  # Mark the index as dirty

    def to_dict(self):
        data_dict = {}
        for loaded_part_dict in self.loaded_parts:
            for key in loaded_part_dict.keys():
                data_dict[key] = loaded_part_dict[key]

        return data_dict
