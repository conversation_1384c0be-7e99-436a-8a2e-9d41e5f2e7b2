from tomsze_utils.database_utils.pickle_database_split import PickleData<PERSON>Split
from const_path import proj_ser_api_db_block_ip_path
from server.api.end_to_end.utils import add_new_ip_to_db_block_ip_if_not_exist


def main():
    part_data_len = 2
    db_name = "db_block_ip"

    ip = "ip_xxx"

    db = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_block_ip_path,  # <<<
        db_name=db_name,
        part_data_count=part_data_len,
    )

    # Adding a new IP to the rate limit database if it does not already exist
    add_new_ip_to_db_block_ip_if_not_exist(
        ip=ip,
        db_block_ip=db,
    )

    db.dump_all_parts_to_pickles()


if __name__ == "__main__":
    main()
