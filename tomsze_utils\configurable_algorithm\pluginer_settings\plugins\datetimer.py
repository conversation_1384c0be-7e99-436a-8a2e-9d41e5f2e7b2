"""App plugin"""

from dataclasses import dataclass
import datetime
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginDatetimer:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        datetime_format = parse_data_and_store(
            logger,
            "datetime_format",
            data_obj,
            step_config,
            config,
        )

        datetime_buffer_to = parse_data_and_store(
            logger,
            "datetime_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
        )

        datetime_str = datetime.datetime.strftime(
            datetime.datetime.now(), datetime_format
        )
        data_obj.dict_var[f"{current_step}.{datetime_buffer_to}"] = datetime_str

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
