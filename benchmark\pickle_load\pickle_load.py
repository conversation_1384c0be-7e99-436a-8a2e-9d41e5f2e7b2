import sys
import os
import time
from tomsze_utils.variable_dump_read_utils import (
    dump_variable_to_pickle,
    load_pickle_file,
)
from dataclasses import dataclass, field
from tqdm import tqdm
from tomsze_utils.time_code_utils import timeit


@dataclass
class DBColEnum:
    IND: str = "ind"
    DESC: str = "desc"
    EMBD: str = "embd"
    KEYW: str = "keyw"


@timeit
def main():

    script_directory_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )
    pickle_path = os.path.join(
        script_directory_path,
        "one_pickle",
        "server_apps_db.pickle",
    )

    time_start = time.time()

    load_pickle_file(
        pickle_path,
        default={},
    )

    time_end = time.time()
    print("time used:" + "{:.2f}".format((time_end - time_start) * 1000) + " ms")


if __name__ == "__main__":
    sys.exit(main())
