import sys
import os
import shutil

from tqdm import tqdm
from const_path import proj_ser_api_db_path
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
    create_db_info,
)
from const_path import proj_bench_basic_path
from server.api.end_to_end.utils import update_db_apps
from random_word import RandomWords

"""
This script create db_apps folder of x data, part_data_count = y
"""


def main():
    num_data = 1000000
    part_data_count = 10000

    db_apps_folder_path = os.path.join(
        proj_bench_basic_path,
        f"db_apps_{num_data}_data_part_{part_data_count}",
    )

    # Re-Create a folder called db_apps_1000_data and add db_ini
    if os.path.exists(db_apps_folder_path):
        shutil.rmtree(db_apps_folder_path)
    os.mkdir(db_apps_folder_path)

    create_db_info(
        part_data_count=part_data_count,
        where_path=db_apps_folder_path,
    )

    # Load db and create the db
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )

    # r = RandomWords()
    # app_desc = " ".join([r.get_random_word() for _ in range(num_desc_word)])
    # list_kw = [r.get_random_word() for _ in range(num_keywords)]

    for n in tqdm(range(num_data)):
        # random_word1 = r.get_random_word()
        # random_word2 = r.get_random_word()
        # random_word3 = r.get_random_word()
        update_db_apps(
            db_apps=db_apps,
            app_id=f"com.{n}.{n}.{n}",
            app_ind=n,
            app_desc="",
            list_kw=[],
            app_desc_embedding=[],
        )

    db_apps.dump_dirty_parts_to_pickles_thread()


if __name__ == "__main__":
    sys.exit(main())
