from tomsze_utils.env_parser_utils import parse_env


def test_parse_env():
    env_path = r"./tests/.env_read_test"

    value_dict = parse_env(env_path)
    assert value_dict["VAR_LIST"] == [
        "line1",
        "line2",
        "line3",
    ]
    assert value_dict["VAR_LIST2"] == [
        "line1",
        "line2",
        "line3",
    ]
    assert value_dict["VAR_LIST3"] == [
        "line1",
        "line2",
        "line3",
    ]
    assert value_dict["VAR_LIST4"] == [
        "line1",
        "line2",
        "line3",
    ]
    assert value_dict["VAR1"] == "1"
    assert value_dict["VAR2"] == "2"


if __name__ == "__main__":
    test_parse_env()
