"""App plugin"""

from dataclasses import dataclass
from tomsze_utils.plugins.constant.plugin_constants import (
    PLUGIN_LOOP_COUNTER_VAR_NAME,
    PLUGIN_LOOP_LIST_INDEX_VAR_NAME,
    PLUGIN_LOOP_LIST_ITEM_VAR_NAME,
)
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginLoopStarter:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        use_num_loops = parse_data_and_store(
            logger,
            "use_num_loops",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        num_loops = parse_data_and_store(
            logger,
            "num_loops",
            data_obj,
            step_config,
            config,
            type="int",
            default=1,
        )

        use_loop_list = parse_data_and_store(
            logger,
            "use_loop_list",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        loop_list = parse_data_and_store(
            logger,
            "loop_list",
            data_obj,
            step_config,
            config,
        )

        assert (use_num_loops or use_loop_list) == True

        # Init loop counter when not exist.
        if use_num_loops:
            if not data_obj.dict_var.get(
                f"{current_step}.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None
            ):
                data_obj.dict_var[f"{current_step}.{PLUGIN_LOOP_COUNTER_VAR_NAME}"] = (
                    num_loops
                )

        elif use_loop_list:
            if not data_obj.dict_var.get(
                f"{current_step}.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None
            ):
                assert len(loop_list) > 0

                data_obj.dict_var[f"{current_step}.{PLUGIN_LOOP_COUNTER_VAR_NAME}"] = (
                    len(loop_list)
                )

                data_obj.dict_var[
                    f"{current_step}.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}"
                ] = 0

                data_obj.dict_var[
                    f"{current_step}.{PLUGIN_LOOP_LIST_ITEM_VAR_NAME}"
                ] = loop_list[0]

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
