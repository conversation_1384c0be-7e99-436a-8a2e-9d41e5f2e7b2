import flet as ft
import logging


class DirectoryOpener(ft.UserControl):
    def __init__(
        self,
        on_result_action=None,
    ):
        super().__init__()
        self.on_result_action = on_result_action
        self.get_directory_dialog = ft.FilePicker(
            on_result=self._get_directory_result,
        )

        self.selected_directory = ""

    def build(self):
        return self.get_directory_dialog

    def open_dialog(self):
        # Set dialog to visible so it is functionable.
        self.get_directory_dialog.visible = True
        self.get_directory_dialog.update()

        # Invoke the dialog.
        self.get_directory_dialog.get_directory_path()

    def _get_directory_result(self, e: ft.FilePickerResultEvent):
        self.selected_directory = e.path if e.path else "Cancelled!"

        # Invoke following action.
        if self.selected_directory != "Cancelled!":
            if self.on_result_action:
                self.on_result_action()

        # Set dialog back to invisible.
        self.get_directory_dialog.visible = False
        self.get_directory_dialog.update()

    def get_selected_path(self):
        return self.selected_directory

    def set_selected_path(self, path):
        self.selected_directory = path


def main(page: ft.Page):
    logging.basicConfig(
        # filename='HISTORYlistener.log',
        level=logging.DEBUG,  # if logging.INFO, logger.debug('xx') will not be logged
        format="%(asctime)s.%(msecs)03d %(levelname)s %(module)s - %(funcName)s: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    page.title = "DirectoryOpener"
    page.horizontal_alignment = "center"
    page.scroll = "adaptive"
    page.window_width = 500
    page.window_height = 500
    page.update()

    def on_result_action():
        print(f"on_result_action invoked")

    # Create control instance.
    directory_opener = DirectoryOpener(
        on_result_action=on_result_action,
    )

    def on_btn_open_directory(e):
        directory_opener.open_dialog()

    def on_btn_show_selected_directory(e):
        logging.info(directory_opener.get_selected_path())

    # Add control's root control to the page
    btn_open_directory = ft.ElevatedButton(
        text="open directory",
        on_click=on_btn_open_directory,
    )

    btn_show_directory = ft.ElevatedButton(
        text="show selected directory",
        on_click=on_btn_show_selected_directory,
    )

    page.add(
        directory_opener,
        btn_open_directory,
        btn_show_directory,
    )


if __name__ == "__main__":
    ft.app(target=main)
