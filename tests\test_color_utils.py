import pytest
import tempfile
import shutil
from tomsze_utils.color_utils import color_to_hex


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


class TestColorUtils:

    def test_color_to_hex_valid(self):
        assert color_to_hex("red") == "#FF0000"
        assert color_to_hex("green") == "#008000"
        assert color_to_hex("blue") == "#0000FF"
        assert color_to_hex("purple") == "#800080"

    def test_color_to_hex_invalid(self):
        assert color_to_hex("invalid") is None
        assert color_to_hex("") is None
        assert color_to_hex("not_a_color") is None
