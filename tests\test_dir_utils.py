import os
import tempfile
import pytest
from tomsze_utils.dir_utils import (
    clear_folder,
    create_nested_folders,
    get_current_working_dir_path,
    get_file_path_list_in_path,
    move_folder,
    remove_directory,
    rename_directory,
)


class TestGetFilesInDir:

    def test_get_files_in_empty_directory(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            files = get_file_path_list_in_path(temp_dir)
            assert files == []  # Expecting an empty list for an empty directory

    def test_get_files_in_directory_with_files(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test file
            test_file_path = os.path.join(temp_dir, "test_file.txt")
            with open(test_file_path, "w") as f:
                f.write("This is a test file.")

            files = get_file_path_list_in_path(temp_dir)
            assert len(files) == 1  # Expecting one file
            assert files[0] == test_file_path  # Expecting the path of the created file


class TestGetCurrentWorkingDirPath:

    def test_get_current_working_dir_path(self):
        current_dir = get_current_working_dir_path()
        assert (
            current_dir == os.getcwd()
        )  # Expecting the current working directory to match

    def test_get_current_working_dir_path_with_temp_dir(self):
        temp_dir = tempfile.TemporaryDirectory()
        orig_dir = os.getcwd()
        os.chdir(
            temp_dir.name
        )  # Change the current working directory to the temporary directory
        current_dir = get_current_working_dir_path()
        assert (
            current_dir == temp_dir.name
        )  # Expecting the current working directory to match the temp directory
        os.chdir(orig_dir)
        temp_dir.cleanup()  # Clean up the temporary directory


class TestCreateNestedFolders:

    def test_create_nested_folders_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            nested_folder_path = os.path.join(temp_dir, "parent/child/grandchild")
            result = create_nested_folders(nested_folder_path)
            assert result == f"Directory ({nested_folder_path}) is created"
            assert os.path.exists(
                nested_folder_path
            )  # Check if the directory was created

    def test_create_nested_folders_already_exists(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            nested_folder_path = os.path.join(temp_dir, "parent/child/grandchild")
            create_nested_folders(nested_folder_path)  # Create the directory first
            result = create_nested_folders(nested_folder_path)  # Try creating it again
            assert (
                result == f"Directory ({nested_folder_path}) is created"
            )  # Should not raise an error


class TestRemoveDirectory:

    def test_remove_directory_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test directory and a file inside it
            test_dir = os.path.join(temp_dir, "test_dir")
            os.makedirs(test_dir, exist_ok=True)
            test_file = os.path.join(test_dir, "test_file.txt")
            with open(test_file, "w") as f:
                f.write("Test content")

            # Call the function to remove the directory
            result = remove_directory(test_dir)

            # Verify that the directory has been removed
            assert result == f"Directory ({test_dir}) has been removed."
            assert not os.path.exists(test_dir)

    def test_remove_directory_non_existent(self):
        # Test removing a non-existent directory
        non_existent_dir = "/path/to/non/existent/directory"
        try:
            result = remove_directory(non_existent_dir)
        except FileNotFoundError:
            result = "done"  # Handle the exception gracefully

        # Verify that the function handles it gracefully
        assert (
            result == "done"
        )  # Assuming the function returns "done" for non-existent paths


class TestMoveFolder:

    def test_move_folder_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            source_dir = os.path.join(temp_dir, "source")
            destination_dir = os.path.join(temp_dir, "destination")
            os.makedirs(source_dir, exist_ok=True)
            test_file_path = os.path.join(source_dir, "test_file.txt")
            with open(test_file_path, "w") as f:
                f.write("Test content")

            result = move_folder(source_dir, destination_dir)

            # Verify that the folder has been moved
            assert result == f"Folder moved from {source_dir} to {destination_dir}."
            assert not os.path.exists(source_dir)
            assert os.path.exists(destination_dir)

    def test_move_folder_non_existent_source(self):
        non_existent_source = "/path/to/non/existent/source"
        destination_dir = "/path/to/destination"

        try:
            result = move_folder(non_existent_source, destination_dir)
        except FileNotFoundError:
            result = "done"  # Handle the exception gracefully

        # Verify that the function handles it gracefully
        assert result == "done"  # Assuming the function raises FileNotFoundError


class TestClearFolder:

    def test_clear_folder_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test folder and add some files
            test_folder = os.path.join(temp_dir, "test_folder")
            os.makedirs(test_folder, exist_ok=True)
            with open(os.path.join(test_folder, "file1.txt"), "w") as f:
                f.write("Test content 1")
            with open(os.path.join(test_folder, "file2.txt"), "w") as f:
                f.write("Test content 2")

            # Clear the folder
            result = clear_folder(test_folder)

            # Verify that the folder has been cleared
            assert result == f"Folder ({test_folder}) has been cleared."
            assert len(os.listdir(test_folder)) == 0  # Folder should be empty

    def test_clear_folder_non_existent(self):
        non_existent_folder = "/path/to/non/existent/folder"
        try:
            result = clear_folder(non_existent_folder)
        except FileNotFoundError:
            result = "done"  # Handle the exception gracefully

        # Verify that the function handles it gracefully
        assert result == "done"  # Assuming the function raises FileNotFoundError


class TestRenameDirectory:

    def test_rename_directory_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            original_dir = os.path.join(temp_dir, "original_dir")
            new_dir = os.path.join(temp_dir, "new_dir")
            os.makedirs(original_dir)

            result = rename_directory(original_dir, new_dir)

            # Verify that the directory has been renamed
            assert result == f"Directory renamed from {original_dir} to {new_dir}."
            assert not os.path.exists(original_dir)
            assert os.path.exists(new_dir)

    def test_rename_directory_non_existent(self):
        non_existent_dir = "/path/to/non/existent/dir"
        new_dir = "/path/to/new/dir"

        try:
            result = rename_directory(non_existent_dir, new_dir)
        except FileNotFoundError:
            result = "done"  # Handle the exception gracefully

        # Verify that the function handles it gracefully
        assert result == "done"  # Assuming the function raises FileNotFoundError
