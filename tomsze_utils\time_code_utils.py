import time


def timeit(method: callable) -> callable:
    """
    Decorator to measure the execution time of a function.

    This decorator can be used to measure the execution time of any function. It prints the execution time in milliseconds to the console.

    Example 1:
    >>> @timeit
    >>> def example_function():
    >>>     time.sleep(1)  # Simulate some work
    >>> example_function()
    'example_function'  1000.00 ms

    Example 2:
    >>> @timeit
    >>> def another_example_function():
    >>>     time.sleep(0.5)  # Simulate some work
    >>> another_example_function()
    'another_example_function'  500.00 ms
    """

    def timed(*args, **kw):
        ts = time.time()
        result = method(*args, **kw)
        te = time.time()
        print("%r  %2.2f ms" % (method.__name__, (te - ts) * 1000))
        return result

    return timed
