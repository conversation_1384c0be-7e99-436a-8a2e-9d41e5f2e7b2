from enum import Enum
from typing import List, Tuple
from tomsze_utils.list_utils import calculate_total_len_of_index_range_list
from tomsze_utils.scoring_utils import (
    calculate_matched_len_ratio,
    calculate_matched_word_ratio_using_index_range_list,
)


class Criteria_list(Enum):
    MatchedLenRatio = "matched_len_ratio"
    MatchedWordRatio = "matched_word_ratio"


def sort_text_list_by_match_criteria(
    text_list: List[str],
    matched_index_list_list: List[List[Tuple[int, int]]],
    criteria_list: List[Criteria_list] = [
        Criteria_list.MatchedLenRatio,
        Criteria_list.MatchedWordRatio,
    ],
) -> Tuple[List[str], List[float]]:
    """
    Sorts a list of texts based on specified matching criteria and returns the sorted texts along with their corresponding sorted ratio sums.

    Args:
        text_list (List[str]): A list of texts to be sorted.
        matched_index_list_list (List[List[Tuple[int, int]]]): A list of lists, where each inner list contains tuples representing the start and end indices of matched portions in the corresponding text.
        criteria_list (List[Criteria_list]): A list of `Criteria_list` enums specifying the criteria to use for sorting.  Defaults to `[Criteria_list.MatchedLenRatio, Criteria_list.MatchedWordRatio]`.

    Returns:
        Tuple[List[str], List[float]]: A tuple containing:
            - A sorted list of texts based on the matching criteria.
            - A sorted list of the sum of the ratios used for sorting, corresponding to the sorted texts.

    Example 1:
    ```python
    text_list = ["Hello world", "Hello world 2"]
    matched_index_list_list = [[(0, 5)], [(0, 5), (6,7)]]
    sorted_texts, sorted_ratios = sort_text_list_by_match_criteria(text_list=text_list, matched_index_list_list=matched_index_list_list)
    # Returns (['Hello world 2', 'Hello world'], [0.6666666666666666, 0.5])
    ```

    Example 2:
    ```python
    text_list = ["abc def", "abc ghi"]
    matched_index_list_list = [[(0, 3)], [(0, 3)]]
    criteria_list = [Criteria_list.MatchedLenRatio]
    sorted_texts, sorted_ratios = sort_text_list_by_match_criteria(text_list=text_list, matched_index_list_list=matched_index_list_list, criteria_list=criteria_list)
    # Returns (['abc def', 'abc ghi'], [1.0, 1.0]) (order may vary due to equal ratios)
    ```
    """

    # Calculate matched_len_ratio
    matched_len_ratio_list = []
    if Criteria_list.MatchedLenRatio in criteria_list:
        for i, text in enumerate(text_list):
            matched_len = calculate_total_len_of_index_range_list(
                index_range_list=matched_index_list_list[i]
            )
            mached_len_ratio = calculate_matched_len_ratio(
                matched_len=matched_len, whole_text_len=len(text)
            )
            matched_len_ratio_list.append(mached_len_ratio)

    # Calculate matched_len_ratio
    matched_word_ratio_list = []
    if Criteria_list.MatchedWordRatio in criteria_list:
        for i, text in enumerate(text_list):
            matched_word_ratio = calculate_matched_word_ratio_using_index_range_list(
                index_range_list=matched_index_list_list[i], text=text
            )
            matched_word_ratio_list.append(matched_word_ratio)

    # Sum up the ratio
    sum_ratio_list = []
    for i, text in enumerate(text_list):
        sum_ratio_list.append(
            sum(
                [
                    matched_len_ratio_list[i],
                    matched_word_ratio_list[i],
                ]
            )
        )

    # Sort the texts based on the sum of the ratios
    sorted_text_list = [
        x
        for _, x in sorted(
            zip(sum_ratio_list, text_list), reverse=True
        )  # reverse=True for descending order
    ]

    sorted_sum_ratio_list = sorted(
        sum_ratio_list, reverse=True
    )  # reverse=True for descending order

    return sorted_text_list, sorted_sum_ratio_list


def main():
    # Try sort_text_list_by_match_criteria
    # text_list = [
    #     "Hello world",
    #     "Hello world 2",
    # ]
    # matched_index_list_list = [
    #     [(0, 5)],
    #     [(0, 5)],
    # ]
    # sorted_text_list, sorted_sum_ratio_list = sort_text_list_by_match_criteria(
    #     text_list=text_list, matched_index_list_list=matched_index_list_list
    # )

    text_list = [
        "test test",
        "pp test",
    ]
    matched_index_list_list = [
        [(0, 4)],
        [(3, 7)],
    ]
    sorted_text_list, sorted_sum_ratio_list = sort_text_list_by_match_criteria(
        text_list=text_list, matched_index_list_list=matched_index_list_list
    )


if __name__ == "__main__":
    main()
