import webcolors


def color_to_hex(color_name: str) -> str:
    """
    Converts a color name to its corresponding hexadecimal representation.

    Args:
    -----
    color_name: str
        The name of the color to convert.

    Returns:
    --------
    str | None
        The hexadecimal representation of the color, or None if the color name is not recognized.

    Examples:
    ---------
    To convert a recognized color name:
    ```python
    hex_color = color_to_hex(color_name="red")  # Output: '#FF0000'
    ```

    To handle an unrecognized color name:
    ```python
    hex_color = color_to_hex(color_name="invalid")  # Output: None
    ```
    """
    try:
        hex_color = webcolors.name_to_hex(color_name, spec="css3").upper()
        return hex_color
    except ValueError:
        return None  # Return None if the color name is not recognized


def main():
    # Example usage:
    print(color_to_hex("red"))  # Output: #FF0000
    print(color_to_hex("green"))  # Output: #008000
    print(color_to_hex("blue"))  # Output: #0000FF
    print(color_to_hex("purple"))  # Output: #800080
    print(color_to_hex("invalid"))  # Output: None


if __name__ == "__main__":
    main()
