{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "read_env_backup_secrets",
            "current_datetime",
            "zip_secrets",
            
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "read_env_backup_secrets",
            "type": "PluginEnvReader",
            "use": true,
            "use_env_path": true,
            "env_path": "./.env_backup_secrets"
        },
        {
            "step_name": "current_datetime",
            "type": "PluginDatetimer",
            "use": true,
            "datetime_format":"%Y-%m-%d_H%HM%MS%S",
            "datetime_buffer_to":"datetime"
        },
        {
            "step_name": "zip_secrets",
            "type": "PluginDirZipper",
            "use": true,
            "folder_to_zip_path": "./",
            "zip_name": "backup_secrets_{current_datetime.datetime}.zip",
            "exclude_path_list": [
            ],
            "include_only_path_list": [
                './security/.env_security',
                './security/date_strings.json',
                './.env',
                './.env_backup_secrets',
                './.env_prod',
                './ScrapingAnt_api_key.txt',
            ],
            "save_to_folder_path": "{read_env_backup_secrets.BACK_TO_PATH}",
        },
       
    ]
   

    

}
