import requests
import json
from server.api.end_to_end.api_constant_keys import ClientColEnum
from server.api.end_to_end.data_class import TestBody, ClientSearchData


def trial_run_test_endpoint(ip="**************", port="8000"):
    # Define the URL for the test endpoint
    url = f"http://{ip}:{port}/test"

    # Create a sample TestBody object
    test_body = TestBody(test_field="This is a test field")

    # Convert the TestBody object to a dictionary
    payload = test_body.dict()

    # Set the headers for the request
    headers = {"Content-Type": "application/json"}

    try:
        # Send a POST request to the test endpoint
        response = requests.post(url, data=json.dumps(payload), headers=headers)

        # Check if the request was successful
        if response.status_code == 200:
            print("Test endpoint response:")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"Error: Received status code {response.status_code}")
            print("Response content:")
            print(response.text)

    except requests.exceptions.RequestException as e:
        print(f"An error occurred while making the request: {e}")


def trial_run_sua_endpoint(ip="**************", port="8000"):
    # Define the URL for the sua endpoint
    url = f"http://{ip}:{port}/sua"

    # Create a sample ClientSearchData object
    client_search_data = ClientSearchData(
        query="sample search query",
        allowSearch=True,
        appIds=["app1", "app2", "app3"],
        downloadSource=["playstore", "appstore"],
    )

    # Convert the ClientSearchData object to a dictionary
    payload = client_search_data.dict()

    # Set the headers for the request
    headers = {"Content-Type": "application/json"}

    try:
        # Send a POST request to the sua endpoint
        response = requests.post(url, data=json.dumps(payload), headers=headers)

        # Check if the request was successful
        if response.status_code == 200:
            print("SUA endpoint response:")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"Error: Received status code {response.status_code}")
            print("Response content:")
            print(response.text)

    except requests.exceptions.RequestException as e:
        print(f"An error occurred while making the request: {e}")


if __name__ == "__main__":
    ip = "**************"
    port = "8000"

    trial_run_test_endpoint(ip, port)
    trial_run_sua_endpoint(ip, port)
