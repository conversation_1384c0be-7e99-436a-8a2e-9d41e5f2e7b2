import os
import platform
import subprocess


def rename_file(old_file_path: str, new_file_path: str) -> str:
    """
    Renames a file from old_file_path to new_file_path.

    Args:
        old_file_path (str): The current path of the file to be renamed.
        new_file_path (str): The new path (including the new file name) for the file.

    Raises:
        FileNotFoundError: If the specified old file does not exist.
        FileExistsError: If the new file path already exists.

    Examples:
        ```python
        rename_file(old_file_path="old_file.txt", new_file_path="new_file.txt")
        ```

        ```python
        rename_file(old_file_path="path/to/old_file.txt", new_file_path="path/to/new_file.txt")
        ```
    """
    if not os.path.isfile(old_file_path):
        raise FileNotFoundError(f"The specified file does not exist: {old_file_path}")

    if os.path.isfile(new_file_path):
        raise FileExistsError(f"The new file path already exists: {new_file_path}")

    os.rename(old_file_path, new_file_path)
    return f"File {old_file_path} renamed to {new_file_path} successfully"


def move_file(source_path: str, destination_path: str) -> str:
    """
    Moves a file from source_path to destination_path.

    Args:
        source_path (str): The current path of the file to be moved.
        destination_path (str): The new path (including the new file name) for the file.

    Raises:
        FileNotFoundError: If the specified source file does not exist.
        FileExistsError: If the destination path already exists.

    Examples:
        ```python
        move_file(source_path="old_file.txt", destination_path="new_file.txt")
        ```

        ```python
        move_file(source_path="path/to/old_file.txt", destination_path="path/to/new_file.txt")
        ```
    """
    if not os.path.isfile(source_path):
        raise FileNotFoundError(f"The specified file does not exist: {source_path}")

    if os.path.isfile(destination_path):
        raise FileExistsError(
            f"The destination file path already exists: {destination_path}"
        )

    os.rename(source_path, destination_path)
    return f"File ({source_path}) moved to ({destination_path}) successfully"


def remove_file(file_path: str) -> str:
    """
    Removes the specified file.

    Args:
        file_path (str): The path to the file to be removed.

    Raises:
        FileNotFoundError: If the specified file does not exist.

    Examples:
        ```python
        remove_file(file_path="old_file.txt")
        ```

        ```python
        remove_file(file_path="path/to/file_to_remove.txt")
        ```
    """
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"The specified file does not exist: {file_path}")

    os.remove(file_path)
    return f"File {file_path} removed successfully"


def read_file_to_string(file_path: str) -> str:
    """
    Reads the contents of a text file and returns it as a string.

    Args:
        file_path (str): The path to the file to be read.

    Returns:
        str: The contents of the file.

    Raises:
        FileNotFoundError: If the specified file does not exist.
        IOError: If there is an error reading the file.

    Examples:
        ```python
        content = read_file_to_string("path/to/text_file.txt")
        print(content)
        ```
    """
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"The specified file does not exist: {file_path}")

    with open(file_path, "r") as file:
        return file.read()


def copy_file(source_path: str, destination_path: str) -> str:
    """
    Copies a file from the source path to the destination path.

    Args:
        source_path (str): The path to the file to be copied.
        destination_path (str): The path where the file should be copied to.

    Raises:
        FileNotFoundError: If the specified source file does not exist.
        FileExistsError: If the destination file already exists.
        IOError: If there is an error during the file copy operation.

    Examples:
        ```python
        copy_file("path/to/source_file.txt", "path/to/destination_file.txt")
        ```
    """
    if not os.path.isfile(source_path):
        raise FileNotFoundError(
            f"The specified source file does not exist: {source_path}"
        )

    if os.path.exists(destination_path):
        raise FileExistsError(
            f"The destination file already exists: {destination_path}"
        )

    with open(source_path, "rb") as src_file:
        with open(destination_path, "wb") as dest_file:
            dest_file.write(src_file.read())

    return f"File copied from {source_path} to {destination_path} successfully"


def open_file_folder_or_exe_with_default_software(file_path: str) -> str:
    """
    Opens a file, folder or executable using the current system's default software.

    Args:
        file_path (str): The path to the file to be opened.

    Raises:
        FileNotFoundError: If the specified file does not exist.
        Exception: If there is an error opening the file.

    Examples:
        ```python
        open_file_with_default_software(file_path="path/to/file.txt")
        ```

        ```python
        open_file_with_default_software(file_path="path/to/image.png")
        ```
    """
    file_path = os.path.realpath(file_path)

    if not os.path.exists(file_path):
        return f"The specified path does not exist: {file_path}"

    try:
        if platform.system() == "Windows":
            os.startfile(file_path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", file_path])
        else:  # Linux and other systems
            subprocess.run(["xdg-open", file_path])
        return f"Opened ({file_path}) successfully"
    except Exception as e:
        raise Exception(f"An error occurred while trying to open the given path: {e}")
