import os
import tempfile
from autogen import ConversableAgent

from tomsze_utils.ai_utils.autogen_utils import register_tool_functions_in_dir


class TestRegisterToolFunctionsInDir:

    def test_register_tool_functions_in_dir_valid(self, mocker):
        model = "qwen2.5:latestxxx"
        config_list = [
            {
                "model": model,  # ok
                "base_url": "http://localhost:11434/v1",
                "api_key": model,
                "price": [0, 0],
            }
        ]

        assistant = ConversableAgent(
            name="Assistant",
            system_message="You are a helpful AI assistant. "
            "Response '/TERMINATE' when if you think the task is done.",
            llm_config={"config_list": config_list},
        )

        # The user proxy agent is used for interacting with the assistant agent
        # and executes tool calls.
        user_proxy = ConversableAgent(
            name="User",
            llm_config=False,
            is_termination_msg=lambda msg: msg.get("content") is not None
            and "TERMINATE" in msg["content"],
            human_input_mode="NEVER",
            default_auto_reply="Have you finished your task?",
        )

        assert assistant.llm_config.get("tools", None) is None
        assert user_proxy.function_map == {}

        # Create a temporary directory and a Python file with a valid function
        temp_dir = tempfile.TemporaryDirectory()

        # os.chdir(temp_dir)
        script_content = """
def add(x:int, y:int)->int:
    '''add two numbers'''
    return x + y
    """
        script_path = os.path.join(temp_dir.name, "add_function.py")
        with open(script_path, "w") as f:
            f.write(script_content)

        # Call the function to register tool functions
        register_tool_functions_in_dir(temp_dir.name, assistant, user_proxy)

        # Check if the function was registered correctly
        assert assistant.llm_config.get("tools") is not None
        assert user_proxy.function_map is not None

        temp_dir.cleanup()
