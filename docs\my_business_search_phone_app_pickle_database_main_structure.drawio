<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="24.7.7">
  <diagram name="Page-1" id="c7488fd3-1785-93aa-aadb-54a6760d102a">
    <mxGraphModel dx="737" dy="329" grid="0" gridSize="10" guides="0" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="6-M5AASk3iZKS38XEw13-3" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="6-M5AASk3iZKS38XEw13-1" target="6-M5AASk3iZKS38XEw13-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6-M5AASk3iZKS38XEw13-6" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="6-M5AASk3iZKS38XEw13-1" target="6-M5AASk3iZKS38XEw13-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6-M5AASk3iZKS38XEw13-1" value="PickleDatabaseSplit" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="141" y="264" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6-M5AASk3iZKS38XEw13-2" value="db_apps" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="332" y="209" width="79" height="50" as="geometry" />
        </mxCell>
        <mxCell id="6-M5AASk3iZKS38XEw13-4" value="db_synonyms" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="325" y="407" width="79" height="50" as="geometry" />
        </mxCell>
        <mxCell id="6-M5AASk3iZKS38XEw13-7" value="loaded_parts" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;" parent="1" vertex="1">
          <mxGeometry x="582" y="106" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="JM_OnVRVLUatdFMSKE4B-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cNJxBx0jZrlAMHOpDaIE-1" target="JM_OnVRVLUatdFMSKE4B-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="cNJxBx0jZrlAMHOpDaIE-1" value="list of { &quot;com.a.b&quot;: {..}, &quot;com.a.b&quot;: {..} }" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;" parent="1" vertex="1">
          <mxGeometry x="511" y="216" width="214" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cNJxBx0jZrlAMHOpDaIE-2" value="part_data_count" style="text;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="577" y="175" width="94" height="26" as="geometry" />
        </mxCell>
        <mxCell id="cNJxBx0jZrlAMHOpDaIE-3" value="" style="verticalLabelPosition=bottom;shadow=0;dashed=0;align=center;html=1;verticalAlign=top;strokeWidth=1;shape=mxgraph.mockup.markup.curlyBrace;whiteSpace=wrap;strokeColor=#999999;" parent="1" vertex="1">
          <mxGeometry x="558" y="200" width="132" height="20" as="geometry" />
        </mxCell>
        <mxCell id="cNJxBx0jZrlAMHOpDaIE-4" value="list of { &quot;word_a&quot;: [..], &quot;word_b&quot;: [..] }" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;" parent="1" vertex="1">
          <mxGeometry x="506" y="416" width="214" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cNJxBx0jZrlAMHOpDaIE-5" value="part_data_count" style="text;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="572" y="375" width="94" height="26" as="geometry" />
        </mxCell>
        <mxCell id="cNJxBx0jZrlAMHOpDaIE-6" value="" style="verticalLabelPosition=bottom;shadow=0;dashed=0;align=center;html=1;verticalAlign=top;strokeWidth=1;shape=mxgraph.mockup.markup.curlyBrace;whiteSpace=wrap;strokeColor=#999999;" parent="1" vertex="1">
          <mxGeometry x="553" y="400" width="132" height="20" as="geometry" />
        </mxCell>
        <mxCell id="JM_OnVRVLUatdFMSKE4B-1" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;" vertex="1" parent="1">
          <mxGeometry x="800.0042717633929" y="167.99714285714288" width="164" height="189" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
