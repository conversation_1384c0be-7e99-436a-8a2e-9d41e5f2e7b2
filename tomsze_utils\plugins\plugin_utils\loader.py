"""A simple plugin loader."""

import importlib
import sys
from typing import List
from ...import_utils import approximated_import_module
from ...import_utils import import_module_by_path
import logging


class ModuleInterface:
    """Represents a plugin interface. A plugin has a single register function."""

    @staticmethod
    def register(type: str) -> None:
        """Register the necessary items in the app plugin factory."""


# def load_and_register_plugins(list_plugin_file: List[str]) -> None:
#     """Loads the plugins defined in the 'plugins' list."""
#     for plugin_file in list_plugin_file:
#         plugin = import_module_by_path(plugin_file)
#         plugin.register()


def load_and_register_plugin(plugin_file_path: str, type: str) -> None:
    """Loads the plugin file."""
    logging.info("load_and_register_plugin")
    load_error, plugin = import_module_by_path(plugin_file_path)
    if load_error == "":
        plugin.register(type)
    else:
        logging.error(f"load error: {load_error}")
        raise Exception(f"load error: {load_error}")


def validate_setting_json(dict_json, key) -> bool:
    """
    Validate the dict read from setting json for needed keys such as
    'type', 'use'.
    """
    if not key in dict_json:
        print(f'key "{key}" not in setting json')
        return False

    list_key_need = [
        "type",
        "use",
    ]

    list_setting = dict_json[key]
    for setting in list_setting:
        for key_need in list_key_need:
            if not key_need in setting:
                print(f'key "{key_need}" not in setting of {setting}')
                return False

    return True
