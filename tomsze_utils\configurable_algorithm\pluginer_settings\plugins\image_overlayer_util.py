from typing import Any, List, Union
import cv2


def recursive_paint_circle(
    data: Union[tuple, List[tuple]],
    im: Any,
    radius: int,
    color: tuple,
    thickness: int,
) -> None:
    """
    Recursively paints circles on an image at specified coordinates.

    This function checks if the provided data is a tuple or a list of tuples,
    and draws circles on the given image at the specified coordinates.

    Args:
        data (Union[tuple, List[tuple]]): A tuple representing a single coordinate (x, y)
            or a list of tuples representing multiple coordinates.
        im (Any): The image on which to draw the circles.
        radius (int): The radius of the circles to be drawn.
        color (tuple): The color of the circles in BGR format.
        thickness (int): The thickness of the circle outline.

    Examples:
    ```python
    recursive_paint_circle((100, 150), im=my_image, radius=5, color=(255, 0, 0), thickness=2)
    ```

    ```python
    recursive_paint_circle([(100, 150), (200, 250)], im=my_image, radius=10, color=(0, 255, 0), thickness=3)
    ```
    """
    if isinstance(data, tuple):
        center = (int(data[0]), int(data[1]))
        cv2.circle(
            im,
            center=center,
            radius=radius,
            color=color,
            thickness=thickness,
        )
    else:
        if isinstance(data, list):
            for n in data:
                recursive_paint_circle(
                    n,
                    im,
                    radius,
                    color,
                    thickness,
                )
