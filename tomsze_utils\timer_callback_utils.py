import threading
import time
import logging
from threading import Timer
from typing import Callable, Optional


class CountDownCaller:
    """
    A count down timer that utilizes threading.Timer. When time is up, it calls on_time_up method.
    Thread.thread cannot be used for this kind of job due to its simple design of not being able to cancel the thread when needed.
    """

    def __init__(
        self,
        seconds: int,
        on_time_up: Optional[Callable] = None,
    ) -> None:
        """
        Initialize the CountDownCaller.

        Args:
            seconds (int): The number of seconds for the timer.
            on_time_up (Optional[Callable], optional): The function to call when the timer is up. Defaults to None.
        """
        self.logger = logging.root
        self._seconds = seconds
        self._on_time_up = on_time_up
        self._time_remaining = self._seconds
        self.timer = Timer(self._seconds, self._on_time_up)

    def start(self) -> None:
        """
        Start the timer.
        """
        self.logger.debug("Timer start")
        self.timer = Timer(self._seconds, self._on_time_up)
        self.timer.start()

    def cancel(self) -> None:
        """
        Cancel the timer.
        """
        self.logger.debug("Timer cancel")
        self.timer.cancel()

    def restart(self) -> None:
        """
        Restart the timer.
        """
        self.logger.debug("Timer restart")
        self.cancel()
        self.start()


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s.%(msecs)03d %(levelname)s %(module)s - %(funcName)s: \t\t%(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    logger = logging.root

    def on_time_up():
        logger.info(f"The time's up!")
        # Add your callback logic here

    seconds = 2
    countdown_timer_a = CountDownCaller(seconds, on_time_up)

    logger.info("lets start the timer")
    countdown_timer_a.start()

    logger.info("lets wait 1 second")
    time.sleep(1)

    logger.info("lets restart the timer")
    countdown_timer_a.restart()

    time.sleep(3)
    logger.info("lets start timer again")
    countdown_timer_a.start()
    time.sleep(3)

    logger.info("Is the timer alive?")
    logger.info(countdown_timer_a.timer.is_alive())
