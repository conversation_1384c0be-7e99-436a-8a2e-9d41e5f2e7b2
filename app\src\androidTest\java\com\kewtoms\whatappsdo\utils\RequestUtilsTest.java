package com.kewtoms.whatappsdo.utils;

import static com.kewtoms.whatappsdo.utils.RequestUtils.sendPostEncryptedJsonVolleyFuture;
import static com.kewtoms.whatappsdo.utils.RequestUtils.sendPostJsonVolleyFuture;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import android.content.Context;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Constants;

import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;


@RunWith(AndroidJUnit4.class)
public class RequestUtilsTest {

  @Test
  public void testSendPostJsonVolleyFutureWithoutEncryptedData()
    throws
    JSONException {
    Context context =
      InstrumentationRegistry.getInstrumentation().getTargetContext();
    String url = "http://192.168.128.80:8000/";
    JSONObject jsonBodyJSONObject = new JSONObject();
    JSONObject dataJSONObject = new JSONObject();
    dataJSONObject.put(
      "test",
      "test"
    );
    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;
    jsonBodyJSONObject.put(
      requestEncryptedJsonKeyStr,
      dataJSONObject.toString()
    );
    Boolean isShowToastW = false;
    JSONObject responseJSONObject = sendPostJsonVolleyFuture(
      context,
      url,
      jsonBodyJSONObject,
      isShowToastW
    );

    int g = 1;
  }

  @Test
  public void testSendPostJsonVolleyFutureWithEncryptedData()
    throws
    Exception {
    Context context =
      InstrumentationRegistry.getInstrumentation().getTargetContext();
    String url = "http://192.168.128.80:8000/";
    JSONObject jsonBodyJSONObject = new JSONObject();
    JSONObject dataJSONObject = new JSONObject();
    dataJSONObject.put(
      "test",
      "test"
    ); // TODO encrypt
    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;
    jsonBodyJSONObject.put(
      requestEncryptedJsonKeyStr,
      dataJSONObject.toString()
    );
    Boolean isShowToastW = false;
    JSONObject responseJSONObject = sendPostEncryptedJsonVolleyFuture(
      context,
      url,
      jsonBodyJSONObject,
      isShowToastW
    );


    JSONObject decryptedJSONObject =
      responseJSONObject.optJSONObject("Response");
    JSONObject expectedJSONObject = new JSONObject();
    expectedJSONObject.put(
      "test",
      "test"
    );
    assert decryptedJSONObject != null;
    assertEquals(
      decryptedJSONObject.toString(),
      expectedJSONObject.toString()
    );
  }


}