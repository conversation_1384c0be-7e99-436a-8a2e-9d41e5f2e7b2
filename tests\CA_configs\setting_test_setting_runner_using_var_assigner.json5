{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "run_setting_variable_assigner"
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "run_setting_variable_assigner",
            "type": "PluginSettingRunner",
            "use": true,
            "config_folder_path":"./tests/CA_configs",
            "algo_setting_filename":"setting_variable_assigner.json5",
        }
    ]
   

}
