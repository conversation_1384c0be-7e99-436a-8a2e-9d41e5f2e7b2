{"general": {"init_steps": [], "steps": ["assign_variable_image_path", "assign_variable_pixel_size", "read_image_1", "overlay_text"], "variables": {"var_online": false}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "log_to_file": true, "folder_path": "./tests/tmp_CA_logs", "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_variable_assigner": [{"step_name": "assign_variable_image_path", "type": "PluginVariableAssigner", "use": true, "image_path": "./tests/CA_configs/test_image_reader_1.png"}, {"step_name": "assign_variable_pixel_size", "type": "PluginVariableAssigner", "use": true, "pixel_size": 1, "unit": "um"}], "all_image_reader": [{"step_name": "read_image_1", "type": "PluginImageReader", "use": true, "use_image_path": true, "image_path": "{image_path}", "loop_image": false, "use_image_folder_path": false, "image_folder_path": "", "loop_folder": false, "image_types": ["jpg", "JPG", "png"], "buffer_to_which": "image"}], "all_overlayer": [{"step_name": "overlay_text", "type": "PluginImageOverlayer", "use": true, "process_on_which_default": "image", "process_on_which": "image_overlay", "all_text": [{"use": true, "text": "some texts\nline2", "use_at": true, "at": [20, 20], "font_face": 1, "font_scale": "0.7 um", "color": [255, 0, 0], "thickness": "0.7 um", "line_gap": "5 um", "fill_background": true, "background_color": [255, 255, 255], "background_margin": "10 um"}], "buffer_to_which": "image_overlay", "save": true, "save_path": "./tests/temp/test_image_overlayer.png", "save2": true, "save_path2": "./tests/temp/test_image_overlayer_save_2.png"}]}