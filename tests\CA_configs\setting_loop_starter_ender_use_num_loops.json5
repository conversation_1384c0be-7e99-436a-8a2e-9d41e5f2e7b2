{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "assign_variable_1",
            "start_loop",
                "assign_variable_2",
                "assign_variable_3",
            "end_loop",
            "assign_variable_4",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "assign_variable_1",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1":1,
        },
        {
            "step_name": "assign_variable_2",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1":2,
        },
        {
            "step_name": "assign_variable_3",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1":3,
        },
        {
            "step_name": "assign_variable_4",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1":4,
        },
        {
            "step_name": "start_loop",
            "type": "PluginLoopStarter",
            "use": true,
            "use_num_loops": true,
            "num_loops": 2,
            "use_loop_list": false,
            "loop_list": [],
        },
        {
            "step_name": "end_loop",
            "type": "PluginLoopEnder",
            "use": true,
            "which_starter_step": "start_loop",
        },
    ]
   

}
