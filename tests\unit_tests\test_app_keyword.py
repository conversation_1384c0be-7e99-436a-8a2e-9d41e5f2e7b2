from dotenv import find_dotenv, load_dotenv
import inflect
import pytest
import spacy
from server.api.utils.app_keyword import verify_client_app_keyword
from server.api.utils.descriptions.get_description import scrape_play_store
from server.api.utils.generate_vectors.utils import (
    post_process_app_description,
    search_list_text_en_zh,
    split_text,
)
from server.api.utils.keywords.get_keywords import (
    extract_keyword_using_yake,
    lemmatization,
    post_process_keywords,
    remove_emoji,
    word_to_singular_plural,
)


def test_verify_client_app_keyword():
    client_app_keywords = [
        "eat",
        "drink",  # This is new to sever database
    ]

    server_app_keywords = {
        "eat": 1,
    }

    verify_result = verify_client_app_keyword(client_app_keywords, server_app_keywords)

    expect_new_keyword = {"drink": 1}
    expect_is_new = True

    assert expect_new_keyword == verify_result.new_keywords
    assert expect_is_new == verify_result.is_new


def test_post_process_app_description():
    app_description = ""
    app_description_original = ""
    device_app_id = "com.rovio.BadPiggies"
    lang = "en"
    scrape_success, result = scrape_play_store(
        device_app_id=device_app_id,
        lang=lang,
    )

    app_description_original, app_description = post_process_app_description(result)

    assert scrape_success == True
    assert app_description_original != ""
    assert app_description != ""


def test_extract_keyword_using_yake_and_post_process():
    lang = "en"
    max_ngram_size = 2
    deduplication_threshold = 0.9
    max_num_keywords = 20
    app_description_en_original = """
    You can add loyalty cards from your go-to grocer’s shops, such as Tesco, Morrisons, Waitrose - you name it. With custom cards you can even store those from your local small businesses. Scan, save, done!
    """

    list_kw = extract_keyword_using_yake(
        lang=lang,
        max_ngram_size=max_ngram_size,
        deduplication_threshold=deduplication_threshold,
        max_num_keywords=max_num_keywords,
        app_description_en_original=app_description_en_original,
    )
    assert len(list_kw) > 0


def test_post_process_keywords():
    list_kw = [
        "hi_there",
        "how",
        "are",
        "you",
        "I_am_fine",
        "thank_you",
        "bye😀",
    ]
    list_correct = [
        "hi_there",
        "hi",
        "there",
        "how",
        "are",
        "you",
        "i_am_fine",
        "i",
        "am",
        "fine",
        "thank_you",
        "thank",
        "you",
        "bye",
    ]
    list_kw_post = post_process_keywords(list_kw)

    assert len(list_kw_post) > 0
    assert list_kw_post == list_correct


def test_remove_emoji():
    text = "This string contains an emoji 😀"
    clean_text = remove_emoji(text)

    text_correct = "This string contains an emoji "
    assert clean_text == text_correct

    print("test_remove_emoji done")


def test_split_text():
    text = " one two  three "
    expect = ["one", "two", "three"]
    list_words = split_text(text)
    assert list_words == expect

    text = " one two  three 一 二   三 "
    expect = ["one", "two", "three", "一", "二", "三"]
    list_words = split_text(text)
    assert list_words == expect


def test_search_list_text_en_zh():
    dict_lang_listText = {
        "en": [
            "one one two four",
            "two two two three four ",
            "one three two three three four",
        ],
        "zh": [
            "一 一 二 四",
            "二 二 二 三 四",
            "一 三 二 三 三 四",
        ],
    }
    list_words = [
        "one",
        "two",
        "three",
        "一",
        "二",
        "三",
    ]
    expext = [6, 8, 10]
    result = search_list_text_en_zh(dict_lang_listText, list_words)
    assert result == expext

    dict_lang_listText = {
        "en": [
            "one two four",
            "two two four ",
            "one two three three four",
        ],
        "zh": [
            "一 一 二 四",
            "二 二 三 四",
            "一 二 三 三 四 五",
        ],
    }
    list_words = [
        "one",
        "two",
        "three",
        "一",
        "二",
        "三",
        "四",
        "五",
    ]
    expext = [6, 6, 10]
    result = search_list_text_en_zh(dict_lang_listText, list_words)
    assert result == expext


# # no use
# @pytest.mark.parametrize(
#     "word, expect_singluar, expect_plural",
#     [
#         ('apple', 'apple', 'apples'),
#         ('eat', 'eat', 'eats'),
#         ('eats', 'eat', 'eats'),
#         ('ate', 'eat', 'eats'),
#         ]
#     )
# def test_word_to_singular_plural(
#     word,
#     expect_singluar,
#     expect_plural,
#     ):
#     inflect_engine = inflect.engine()

#     singluar, plural = word_to_singular_plural(
#         inflect_engine,
#         word,
#     )
#     assert singluar == expect_singluar
#     assert plural == expect_plural

#     g=1


@pytest.fixture(scope="module")
def nlp_model():
    yield spacy.load("en_core_web_sm", disable=["parser", "ner"])


def test_lemmatization(nlp_model):
    text = "The striped bats are hanging on their feet for best"
    expect = "the stripe bat be hang on their foot for good"
    result = lemmatization(nlp_model, text)
    assert result == expect

    text = "eats apples and fishes"
    expect = "eat apple and fish"
    result = lemmatization(nlp_model, text)
    assert result == expect

    text = "shopping"
    expect = "shopping"
    result = lemmatization(nlp_model, text)
    assert result == expect

    text = "shop"
    expect = "shop"
    result = lemmatization(nlp_model, text)
    assert result == expect

    text = "eating"
    expect = "eat"
    result = lemmatization(nlp_model, text)
    assert result == expect
