import os
import sys
import traceback
from importlib import resources
from tomsze_utils.config_parser_utils import (
    load_toml_config_absolute_path,
    load_json_config,
)
from tomsze_utils.configurable_algorithm.utils import parse_data, get_all_data, Data
from tomsze_utils.dict_utils import extract_keys_from_dict_that_partly_contains
from tomsze_utils.plugins.constant import plugin_constants

import logging

from tomsze_utils.plugins.pluginer import Pluginer


class CA(object):
    def __init__(
        self,
        config_folder_path,
        algo_setting_filename,
    ):

        self.config_folder_path = config_folder_path
        ver_toml_path = os.path.join(config_folder_path, "versions.toml")
        ver = load_toml_config_absolute_path(ver_toml_path)["main"]
        self._version = "%s" % (ver)
        self.algo_setting_filename = algo_setting_filename
        self._isDebug = False
        if self._isDebug:
            self.logger.debug("init Debug mode")
        self.num_steps_ran = 0

        # read configs from setting.json
        # TODO add able to change .json setting file instead of hardcoded setting.json.
        self.setting_json_path = os.path.join(
            self.config_folder_path, algo_setting_filename
        )
        assert os.path.exists(self.setting_json_path)
        self.config_main = load_json_config(self.setting_json_path)
        self.config_main["project_name"] = self.config_folder_path.split(os.sep)[-1]

        # data
        self.data_obj = Data()
        self.setup_logger()
        self.data_obj.logger = self.logger
        self.data_obj.config = self.config_main

        self.get_variables_to_dict_var()

        # read worflows
        self.steps = self.config_main["general"]["steps"]
        self.init_steps = self.config_main["general"]["init_steps"]
        self.data_obj.list_steps = self.steps.copy()

        proj_plugin_setting_file_path = str(
            resources.files("tomsze_utils").joinpath(
                "configurable_algorithm",
                "pluginer_settings",
                "project_plugins_settings.json",
            )
        )

        self.get_dict_step_type()
        self.get_dict_step_config()
        self.data_obj.dict_step_type = self.dict_step_type
        self.data_obj.dict_step_config = self.dict_step_config

        self.logger.info("Initiating plugin scripts...")
        self.pluginer = Pluginer()
        self.pluginer.init_from_json(
            proj_plugin_setting_file_path,
            plugin_constants.PLUGIN_SETTING_MAIN_KEY,
            load_unspecified_use=True,  # For convenient of no need to specify plugin in proj_plugin_setting_file_path.
        )

    def version(self):
        return self._version

    def validate_setting(self, data):
        ok = True
        error = None
        for key in self.dict_step_type:
            if not self.dict_step_type[key]:
                ok = False
                error = f'missing "type" in step "{key}"'
                return ok, error

        # get variables in {}
        list_data, list_used_variable = get_all_data(self.config_main)

        # for used_variables in list_used_variable:
        #     used_variables = used_variables.replace("{", "").replace("}", "")
        #     ok = False
        #     # check if used variables exist in config variable
        #     if not ok:
        #         for variable in self.config_main["general"]["variables"].keys():
        #             if used_variables == variable:
        #                 ok = True

        #     # check if used variables exist in image variables(internal variables)
        #     if not ok:
        #         for (
        #             variable
        #         ) in data.dict_var.keys():  # TODO image internal variable setup
        #             if used_variables == variable:
        #                 ok = True

        #     # check if used variables first part exist in config step name
        #     if not ok:
        #         for step_name in self.config_main["general"]["steps"]:
        #             if used_variables.split(".")[0] == step_name:
        #                 ok = True

        #     # TODO what if i want to disable a step fast
        #     # assert ok == True, \
        #     #     print(f'"{used_variables}" is not correctly referencing any variable, please check.')

        return ok, error

    def run(self):
        self.logger.info("start AC run!")
        run_ok = True
        run_error = None

        # validate config
        validate_ok, validate_error = self.validate_setting(self.data_obj)
        if not validate_ok:
            run_ok = False
            run_error = f'Valide setting "{self.setting_json_path}" failed due to {validate_error}.'
            self.logger.info(run_error)
            return run_ok, run_error

        # Loop steps.
        not_reach_end = True
        self.data_obj.next_step_index = 0
        while not_reach_end:
            list_len = len(self.data_obj.list_steps)

            if list_len == 0:
                not_reach_end = False
                break

            # parse step for variable used
            step = self.data_obj.list_steps[self.data_obj.next_step_index]
            step_original = step
            step = parse_data(
                self.logger,
                step,
                self.data_obj,
                {step: step},
                self.config_main,
            )

            self.data_obj.current_step = (
                step  # Remember to update step for each plugin run
            )
            plugin_type = self.dict_step_type.get(step_original, None)
            if plugin_type:
                plugin_unique_key = extract_keys_from_dict_that_partly_contains(
                    self.pluginer.dict_obj_plugin, plugin_type
                )[0]
                self.pluginer.run_plugin(
                    dict_app_data=self.data_obj,
                    plugin_unique_key=plugin_unique_key,
                )
                self.num_steps_ran += 1

            # --- Update next_step_index ---
            # Note: place outside of if plugin_type: to allow useless stepname
            if not self.data_obj.hijack_next_step_index:
                self.data_obj.next_step_index += 1

            if self.data_obj.hijack_next_step_index:
                self.data_obj.hijack_next_step_index = False
            # ----------

            # When to exit loop.
            if self.data_obj.next_step_index + 1 > list_len:
                not_reach_end = False

        return run_ok, run_error

    def recursive_get_dict_step_type(self, dict_config: dict):
        for key in dict_config.keys():
            if isinstance(dict_config[key], dict):
                self.recursive_get_dict_step_type(dict_config[key])
            if isinstance(dict_config[key], list):
                for list_item in dict_config[key]:
                    if isinstance(list_item, dict):
                        self.recursive_get_dict_step_type(list_item)
            if key == "step_name":
                if isinstance(dict_config[key], str):
                    self.dict_step_type[dict_config[key]] = dict_config.get(
                        "type", None
                    )

    def get_dict_step_type(self):
        self.dict_step_type = {}
        self.recursive_get_dict_step_type(self.config_main)

    def recursive_get_dict_step_config(self, dict_config: dict):
        for key in dict_config.keys():
            if isinstance(dict_config[key], dict):
                self.recursive_get_dict_step_config(dict_config[key])
            if isinstance(dict_config[key], list):
                for list_item in dict_config[key]:
                    if isinstance(list_item, dict):
                        self.recursive_get_dict_step_config(list_item)
            if key == "step_name":
                if isinstance(dict_config[key], str):
                    self.dict_step_config[dict_config[key]] = dict_config

    def get_dict_step_config(self):
        self.dict_step_config = {}
        self.recursive_get_dict_step_config(self.config_main)

    def setup_logger(self):
        logger_config = self.config_main["general"]["logger"]
        self.logger = logging.Logger(name="logger")

        format = parse_data(
            self.logger,
            "format",
            self.data_obj,
            logger_config,
            self.config_main,
        )

        datefmt = parse_data(
            self.logger,
            "datefmt",
            self.data_obj,
            logger_config,
            self.config_main,
        )

        log_to_file = parse_data(
            self.logger,
            "log_to_file",
            self.data_obj,
            logger_config,
            self.config_main,
        )

        filename = parse_data(
            self.logger,
            "filename",
            self.data_obj,
            logger_config,
            self.config_main,
        )

        folder_path = parse_data(
            self.logger,
            "folder_path",
            self.data_obj,
            logger_config,
            self.config_main,
        )

        loglevel = parse_data(
            self.logger,
            "loglevel",
            self.data_obj,
            logger_config,
            self.config_main,
        )

        # Call this function to remove handlers
        self.disable_logging_handlers()

        log_path = os.path.join(folder_path, filename)
        if os.path.exists(log_path):
            os.remove(log_path)

        if not os.path.exists(folder_path):
            os.makedirs(folder_path, exist_ok=True)

        if log_to_file:
            logging.basicConfig(
                filename=log_path,
                format=format,
                datefmt=datefmt,
                encoding="utf-8",
                level=eval(loglevel),
            )
        else:
            logging.basicConfig(
                format=format,
                datefmt=datefmt,
                encoding="utf-8",
                level=eval(loglevel),
            )
        self.logger = logging.getLogger(name="logger")
        self.logger.addHandler(
            logging.StreamHandler(sys.stdout)
        )  # also print to screen.

        self.data_obj.log_file_path = log_path

    def disable_logging_handlers(self):
        logger = logging.getLogger()
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

    def get_variables_to_dict_var(self):
        for key in self.config_main["general"]["variables"].keys():
            variable_data = parse_data(
                self.logger,
                key,
                self.data_obj,
                self.config_main["general"]["variables"],
                self.config_main,
            )

            self.data_obj.dict_var[key] = variable_data
