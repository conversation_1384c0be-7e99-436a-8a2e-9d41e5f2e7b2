{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "run_convert_script",
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "run_convert_script",
            "type": "PluginScriptRunner",
            "use": true,
            "script_path": "D:/code/my_projects/WhatAppsDo/docs/flow/run_convert.py",
            "function_name": 'run_convert',
            "args_dict": {
                'jar_path':"d:/code/my_projects/WhatAppsDo/docs/flow/plantuml_version_has_interactive_svg.jar",
                'puml_dir':"D:/code/my_projects/WhatAppsDo/docs/flow",
                'output_dir':"D:/code/my_projects/WhatAppsDo/docs/flow",
                'method_mapping_json_path':"D:/code/my_projects/WhatAppsDo/docs/flow/method_mapping.json5"
            },
        }
    ]

    

}
