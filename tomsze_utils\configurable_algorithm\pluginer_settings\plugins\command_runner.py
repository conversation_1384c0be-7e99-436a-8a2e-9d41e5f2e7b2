"""App plugin"""

from dataclasses import dataclass
from tomsze_utils.byte_utils import try_decode_byte
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)
from tomsze_utils.subprocess_utils import subprocess_run


@dataclass
class PluginCommandRunner:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        command = parse_data_and_store(
            logger,
            "command",
            data_obj,
            step_config,
            config,
        )

        output_str_buffer_to = parse_data_and_store(
            logger,
            "output_str_buffer_to",
            data_obj,
            step_config,
            config,
        )

        out = subprocess_run(command=command, capture_output=True, shell=False)

        output_str = ""
        if out.stdout:
            output_str = try_decode_byte(out.stdout)

        if out.stderr:
            output_str = try_decode_byte(out.stderr)

        logger.info(output_str)

        data_obj.dict_var[f"{current_step}.{output_str_buffer_to}"] = output_str

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
