import os
from dotenv import load_dotenv, find_dotenv


env_path = find_dotenv()
load_dotenv(env_path)

# proj_dir_path = os.getenv("PROJ_ROOT_PATH")
proj_dir_path = os.path.dirname(os.path.abspath(__file__))

assert os.path.exists(proj_dir_path) == True

proj_tests_path = os.path.join(proj_dir_path, "tests")
proj_tests_end_to_end_path = os.path.join(proj_tests_path, "end_to_end")
proj_ser_path = os.path.join(proj_dir_path, "server")
proj_pickle_db_path = os.path.join(proj_dir_path, "pickle_db")
proj_ser_api_path = os.path.join(proj_ser_path, "api")
proj_ser_api_db_path = os.path.join(
    proj_pickle_db_path,
    "android",
)
proj_ser_api_db_early_path = os.path.join(
    proj_ser_api_db_path,
    "early_preprocess",
)
proj_ser_api_db_early_create_syn_path = os.path.join(
    proj_ser_api_db_early_path,
    "create_db_synonyms_with_source",
)
proj_ser_api_db_early_create_syn_lemmatize_nltk_words_path = os.path.join(
    proj_ser_api_db_early_create_syn_path,
    "run_lemmatize_nltk_words",
)
proj_ser_api_db_apps_path = os.path.join(
    proj_ser_api_db_path,
    "db_app",
)
proj_ser_api_db_apps_small_path = os.path.join(
    proj_ser_api_db_path,
    "db_app_small",
)

proj_ser_api_db_to_add_small_path = os.path.join(
    proj_ser_api_db_path,
    "db_to_add_small",
)
proj_ser_api_db_synonyms_path = os.path.join(
    proj_ser_api_db_path,
    "db_synonyms",
)
proj_ser_api_db_synonyms_small_path = os.path.join(
    proj_ser_api_db_path,
    "db_synonyms_small",
)
proj_ser_api_db_synonyms_need_powerthesaurus_path = os.path.join(
    proj_ser_api_db_path,
    "db_synonyms_need_powertheasaurus",
)
proj_ser_api_db_to_add_path = os.path.join(
    proj_ser_api_db_path,
    "db_to_add",
)
proj_ser_api_db_synonyms_need_powerthesaurus_small_path = os.path.join(
    proj_ser_api_db_path,
    "db_synonyms_need_powertheasaurus_small",
)
proj_ser_api_db_to_add_path = os.path.join(
    proj_ser_api_db_path,
    "db_to_add",
)
proj_ser_api_db_user_path = os.path.join(
    proj_ser_api_db_path,
    "db_user",
)
proj_ser_api_db_rate_limit_path = os.path.join(
    proj_ser_api_db_path,
    "db_rate_limit",
)
proj_ser_api_db_block_ip_path = os.path.join(
    proj_ser_api_db_path,
    "db_block_ip",
)

proj_ser_tools_path = os.path.join(proj_ser_path, "tools")

proj_bench_path = os.path.join(proj_dir_path, "benchmark")
proj_bench_basic_path = os.path.join(proj_bench_path, "basic")
