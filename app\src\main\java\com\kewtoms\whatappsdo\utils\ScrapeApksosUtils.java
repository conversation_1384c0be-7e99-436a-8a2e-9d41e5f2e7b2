package com.kewtoms.whatappsdo.utils;


import android.util.Log;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScrapeData;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

public class ScrapeApksosUtils {
 public static final String TAG = "APP:ScrapeApksosUtils";
 public static final String scriptSelector = "head > script";

 public static Callable<ScrapeData> createScrapeApksosCallable(String packageName) {
  return new Callable<ScrapeData>() {
   @Override
   public ScrapeData call() {
    String appName = "";
    List<HashMap<String, Object>> categories = new ArrayList<>();
    String description = "";
    String summary = "";
    String genre = "";
    String genreId = "";
    String source = Constants.scrape_source_apksos;
    ScrapeApksosUtils scrapeUtils = new ScrapeApksosUtils();

    String url = scrapeUtils.buildUrl(packageName);
    try {
     Document doc = Jsoup.connect(url).get();

     // Get other data from script tag.
     HashMap<String, Object> dataMap =
      getDatasetMapFromScriptTag(doc);
     appName = (String) dataMap.get("name");
     genre = (String) dataMap.get("applicationSubCategory");
     genreId =
      (String) dataMap.get("applicationCategory") + '_' + genre;
     summary = "";  // yes, correct for this site
     description =
      (String) dataMap.get("description");  // yes, correct for this site
     HashMap<String, Object> categoryItemMap = new HashMap<>();
     categoryItemMap.put(
      "name",
      genre
     );
     categoryItemMap.put(
      "id",
      genreId
     );
     categories.add(categoryItemMap);

    } catch (IOException e) {
     return new ScrapeData(
      packageName,
      appName,
      genre,
      genreId,
      categories,
      description,
      summary,
      source,
      false,
      e.toString()
     );
    }

    return new ScrapeData(
     packageName,
     appName,
     genre,
     genreId,
     categories,
     description,
     summary,
     source,
     true,
     ""
    );
   }

  };
 }

 public static HashMap<String, Object> getDatasetMapFromScriptTag(Document doc) {
  HashMap<String, Object> datasetMap = new HashMap<>();

  try {
   Elements scriptElements = doc.select(scriptSelector);

   if (!scriptElements.isEmpty()) {
    for (Element element : scriptElements) {
     String scriptData = element.data();

     if (scriptData.contains("\"applicationCategory\":")) {
      ObjectMapper objectMapper = new ObjectMapper();
      try {
       JsonNode rootNode = objectMapper.readTree(scriptData);

       // Convert to hashmap
       datasetMap =
        (HashMap<String, Object>) objectMapper.convertValue(
         rootNode,
         new TypeReference<Map<String, Object>>() {
         }
        );

      } catch (Exception e) {
       Log.e(
        TAG,
        "getDatasetMapFromScriptTag: " + e
       );
      }

      break;

     }
    }
   }

  } catch (Exception e) {
   Log.e(
    TAG,
    "getDatasetMap: " + e
   );
  }

  return datasetMap;
 }

 public String buildUrl(String packageName) {
  return "https://apksos.com/app/" + packageName;
 }

}
