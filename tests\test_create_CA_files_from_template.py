import os
from tomsze_utils.configurable_algorithm.create_CA_files_from_template import (
    create_CA_files_from_template,
)


def test_create_CA_files_from_template():
    plugin_name_undersocre = "xx_xxer"
    CA_test_script_path = r"./tests/fake_test_script/fake_test_script.py"
    overwrite_test_script = False
    CA_test_save_file_path, plugin_script_file_save_path, setting_save_file_path = (
        create_CA_files_from_template(
            plugin_name_undersocre=plugin_name_undersocre,
            CA_test_script_path=CA_test_script_path,
            overwrite_test_script=overwrite_test_script,
        )
    )

    assert os.path.exists(CA_test_save_file_path) == True
    data = open(CA_test_save_file_path).read()
    lines_list = data.splitlines()
    assert "test_xx_xxer()" in lines_list[4]
    assert "test_xx_xxer()" in lines_list[18]
    assert "#" in lines_list[17]

    assert os.path.exists(plugin_script_file_save_path) == True
    data = open(plugin_script_file_save_path).read()
    lines_list = data.splitlines()
    assert "PluginXxXxer" in lines_list[11]

    assert os.path.exists(setting_save_file_path) == True
    data = open(setting_save_file_path).read()
    lines_list = data.splitlines()
    assert "xxx" in lines_list[5]
    assert "xxx" in lines_list[25]
    assert "PluginXxXxer" in lines_list[26]

    os.remove(CA_test_save_file_path)
    os.remove(plugin_script_file_save_path)
    os.remove(setting_save_file_path)


if __name__ == "__main__":
    test_create_CA_files_from_template()
