{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "send_get_request",
            "parse_html",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "send_get_request",
            "type": "PluginSimpleGetRequester",
            "use": true,
            "url":"https://example.com/",
            "headers":{},
            "response_buffer_to":"response",
            "headers_buffer_to":"headers",
            "content_buffer_to":"content",
            "text_buffer_to":"text",
            "status_code_buffer_to":"status_code",
            "exception_buffer_to":"exception"
        },
        {
            "step_name": "parse_html",
            "type": "PluginHtmlParserB4soup",
            "use": true,
            "html_text": "{send_get_request.text}",
            "css_selector": "body div p",
            "use_filter_by_text": true,
            "filter_by_text_conditions": [
                "domain"
            ],
            "is_split_text": true,
            "split_text_delimiter": ".",
            "split_index": 0,
            "is_replace_text": true,
            "replace_what": "illustrative",
            "replace_as_what": "happy",
            "use_filter_by_attribute": false,
            "filter_by_attribute_conditions": [
            ],
            "is_get_element_text_results": true,
            "is_get_element_str_results": true,
            "is_get_element_results": true,
        }
    ]
   

}
