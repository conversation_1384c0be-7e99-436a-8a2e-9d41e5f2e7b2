package com.kewtoms.whatappsdo.data;

// TODO do not use this enum anymore, use Configuration class instead
public enum Environment {
  TEST("http://192.168.128.80:8000/__whatappsdo"),
  DEV("http://192.168.128.80:8000/__whatappsdo"),
  PROD("https://kewtomstmp.xyz/__whatappsdo");

  private final String baseUrl;

  Environment(String baseUrl) {
    this.baseUrl = baseUrl;
  }

  public String getBaseUrl() {
    return baseUrl;
  }
}
