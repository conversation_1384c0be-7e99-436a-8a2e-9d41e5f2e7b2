from datetime import datetime, timedelta
import pytest
from tomsze_utils.auth_utils import create_token, hash_password, verify_password


class TestCreateToken:

    def test_create_token_with_default_expiration(self):
        data = {"user_id": 1}
        token = create_token(data)
        assert isinstance(token, str)  # Ensure the token is a string

    def test_create_token_with_custom_expiration(self):
        data = {"user_id": 2}
        custom_expiration = timedelta(minutes=30)
        token = create_token(data, expires_delta=custom_expiration)
        assert isinstance(token, str)  # Ensure the token is a string


from tomsze_utils.auth_utils import validate_token


class TestValidateToken:

    def test_validate_token_with_invalid_token(self):
        invalid_token = "invalid.token.string"
        payload, error = validate_token(invalid_token)
        assert payload is None
        assert error is not None

    def test_validate_token_with_valid_token(self):
        data = {"user_id": 1}
        token = create_token(data)
        payload, error = validate_token(token)
        assert error is None
        assert payload.get("user_id") == data.get("user_id")

    def test_validate_token_with_man_in_the_middle(self):
        data = {"user_id": 1}
        token = create_token(data)

        # Assume hacker decoded the token and modified the payload
        hacked_payload = {"user_id": 2}

        # Create a new token with the modified payload and a different secret key
        hacked_token = create_token(
            hacked_payload,
            secret_key="19d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7",
        )

        payload, error = validate_token(hacked_token)

        assert payload is None
        assert error is not None

        # End of Selection

    def test_validate_token_with_missing_required_keys(self):
        data = {"user_id": 1}
        token = create_token(data)
        required_keys = ["user_id", "email"]  # 'email' is not in the token
        payload, error = validate_token(token, required_keys=required_keys)
        assert payload is None
        assert error == "Missing required key: email"

    def test_validate_token_with_expired_token(self):
        data = {"user_id": 1}
        token = create_token(
            data,
            current_datetime=datetime(
                2000, 1, 1, hour=0, minute=0, second=0, microsecond=0, tzinfo=None
            ),
        )
        payload, error = validate_token(token)
        assert payload is None
        assert error is not None


from tomsze_utils.auth_utils import decode_jwt_without_secret_key


class TestDecodeJWTWithoutSecretKey:

    def test_decode_valid_jwt(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJleHAiOjE3MzEyMTAwMDR9.N9vc-10_HQrMDV4r34WjgrJJwBvTSmbrpFpDhetQqnE"
        expected_data = {"user_id": 1, "exp": 1731210004}
        decoded_data = decode_jwt_without_secret_key(token)
        assert decoded_data == expected_data  # Ensure the decoded data matches expected

    def test_decode_invalid_jwt(self):
        invalid_token = "invalid.token.string"
        decoded_data = decode_jwt_without_secret_key(invalid_token)
        assert decoded_data is None  # Ensure it returns None for invalid token


from tomsze_utils.auth_utils import create_repeatable_token


class TestCreateRepeatableToken:

    def test_create_repeatable_token(self):
        payload = {"user_id": 1}
        secret_key = "secret"
        token = create_repeatable_token(payload, secret_key)
        expected_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxfQ.jYyRJbb0WImFoUUdcslQQfwnXTHJzne-6tsPd8Hrw0I"
        assert token == expected_token  # Ensure the token matches the expected value

    def test_create_repeatable_token_with_different_algorithm(self):
        payload = {"user_id": 2}
        secret_key = "my_secret"
        algorithm = "HS512"
        token = create_repeatable_token(payload, secret_key, algorithm)
        # The expected token will vary based on the algorithm and payload
        # Here we just check that a token is returned
        assert token is not None  # Ensure a token is generated


class TestHashPassword:

    def test_hash_password_creates_hash(self):
        password = "my_secure_password"
        hashed_password = hash_password(password)
        assert hashed_password is not None  # Ensure a hash is returned
        assert isinstance(hashed_password, bytes)  # Ensure the hash is of bytes type

    def test_hash_password_is_different_for_same_input(self):
        password = "my_secure_password"
        hashed_password_1 = hash_password(password)
        hashed_password_2 = hash_password(password)
        assert (
            hashed_password_1 != hashed_password_2
        )  # Ensure different hashes for the same password


class TestVerifyPassword:

    def test_verify_password_correct(self):
        password = "my_secure_password"
        hashed_password = hash_password(password)
        assert (
            verify_password(hashed_password, password) is True
        )  # Ensure it verifies correctly

    def test_verify_password_incorrect(self):
        password = "my_secure_password"
        wrong_password = "wrong_password"
        hashed_password = hash_password(password)
        assert (
            verify_password(hashed_password, wrong_password) is False
        )  # Ensure it fails for incorrect password


from tomsze_utils.auth_utils import create_verification_code


class TestCreateVerificationCode:

    def test_create_verification_code_default_length(self):
        code = create_verification_code()
        assert len(code) == 6  # Ensure the default length is 6
        assert code.isdigit()  # Ensure the code consists of digits

    def test_create_verification_code_custom_length(self):
        custom_length = 8
        code = create_verification_code(length=custom_length)
        assert len(code) == custom_length  # Ensure the length matches the custom length
        assert code.isdigit()  # Ensure the code consists of digits
