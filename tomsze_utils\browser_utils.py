import os
import sqlite3
import platform
import logging  # Import logging


def delete_brave_history(
    website_to_delete: str,
    history_file_path: str = None,
) -> str:
    """Deletes browsing history entries for the specified website from Brave's history.

    This function connects to the Brave browser's SQLite history database and removes
    all entries that match the specified website. If a custom history file path is not
    provided, it defaults to the standard location based on the operating system.

    Args:
        website_to_delete (str): The website for which browsing history entries should be deleted.
        history_file_path (str, optional): The path to the Brave history SQLite file. If None,
                                            the function will determine the default path based on the OS.

    Examples:
        ```python
        delete_brave_history("example.com")
        ```

        ```python
        delete_brave_history("test.com", "/path/to/custom/history/file")
        ```

    """
    # Determine the OS and set the path to Brave's user data directory if history_file is not provided
    if history_file_path is None:
        if platform.system() == "Windows":
            brave_path = os.path.join(
                os.getenv("LOCALAPPDATA"),
                "BraveSoftware",
                "Brave-Browser",
                "User Data",
                "Default",
            )
        elif platform.system() == "Darwin":  # macOS
            brave_path = os.path.join(
                os.path.expanduser("~"),
                "Library",
                "Application Support",
                "BraveSoftware",
                "Brave-Browser",
                "User Data",
                "Default",
            )
        else:  # Linux
            brave_path = os.path.join(
                os.path.expanduser("~"), ".config", "Brave-Browser", "Default"
            )

        # Path to the history file
        history_file_path = os.path.join(brave_path, "History")

    # Connect to the SQLite database
    connection = sqlite3.connect(history_file_path)
    cursor = connection.cursor()

    # Find and delete entries related to the specified website
    cursor.execute(
        "SELECT id FROM urls WHERE url LIKE ?", ("%" + website_to_delete + "%",)
    )
    ids_to_delete = cursor.fetchall()

    if ids_to_delete:
        # Prepare a list of IDs for deletion
        ids_to_delete = [(id[0],) for id in ids_to_delete]

        # Execute delete command for URLs matching the specified website
        cursor.executemany("DELETE FROM urls WHERE id = ?", ids_to_delete)

        # Commit changes
        connection.commit()
        logging.info(
            f"Deleted history entries for {website_to_delete}."
        )  # Changed to logging.info
    else:
        logging.info(
            f"No history found for {website_to_delete}."
        )  # Changed to logging.info

    # Commit changes and close the connection
    connection.commit()
    connection.close()

    logging.info(
        "All browsing history has been deleted from Brave."
    )  # Changed to logging.info
    return "done"
