from dataclasses import dataclass
from typing import List, Optional
from tomsze_utils.randome_utils import generate_random_string
from tomsze_utils.color_utils import color_to_hex
from tomsze_utils.logger_utils import SimpleLogger

logger = SimpleLogger(get_existing_instance=True)


class Node:
    def __init__(self, node_text, node_comment=None):
        """
        A node in a mermaid graph
        """
        self.node_comment = node_comment
        self.node_text = node_text
        self.id = generate_random_string(5, use_alphabetic=True)  # Generate a unique id

    def get_id(self):
        """
        Return the node's id
        """
        return self.id


class Subgraph:
    def __init__(
        self, subgraph_title=None, subgraph_color=None, node_list: List[Node] = []
    ):
        """
        A subgraph in a mermaid graph
        """
        self.subgraph_title = subgraph_title
        self.subgraph_color = subgraph_color
        self.node_list = node_list
        self.id = generate_random_string(5, use_alphabetic=True)  # Generate a unique id

    def init_by_subgraph_class(self, subgraph_class):
        subgraph_name = ""
        subgraph_color = None

        if not "subgraph_name" in subgraph_class.__dict__:
            logger.warning("subgraph_title is not defined")
        else:
            subgraph_name = subgraph_class.subgraph_name

        assert "node_comments" in subgraph_class.__dict__

        if "subgraph_color" in subgraph_class.__dict__:
            subgraph_color = color_to_hex(subgraph_class.subgraph_color)

        if subgraph_class.node_comments:
            self.__init__(
                subgraph_title=subgraph_name,
                subgraph_color=subgraph_color,
                node_list=[
                    Node(node_text=method_name, node_comment=method_comment)
                    for method_name, method_comment in zip(
                        subgraph_class.node_names, subgraph_class.node_comments
                    )
                ],
            )
        else:
            self.__init__(
                subgraph_title=subgraph_name,
                subgraph_color=subgraph_color,
                node_list=[
                    Node(node_text=method_name)
                    for method_name in subgraph_class.node_names
                ],
            )
        return self

    def node(self, node_name: str) -> Optional[Node]:
        """
        Return the node contained in this subgraph by name
        """
        for node in self.node_list:
            if node.node_text == node_name:
                return node
        return None

    def get_id(self):
        """
        Return the subgraph's id
        """
        return self.id

    def get_subgraph_title(self):
        '''
        """
        Return the title of the subgraph
        """
        '''
        return self.subgraph_title


class MM:
    def __init__(self, graph_title, output_folder):
        """
        Create mermaid graph

        Example mermaid graph code:
        ```mermaid
        flowchart TB
            subgraph tIfeF["class FF"]
                ReKKn["func_a"]
                DNgPG["func_b"]
                NtFSc["func_c"]
            end
            subgraph tyloE["fun func_a"]
                nBMME["func_b"]
                vyQGh["func_c"]
            end
            ReKKn --> tyloE
            nBMME --> vyQGh
        ```
        """
        self.graph_title = graph_title
        self.output_folder = output_folder
        self.line_first = "```mermaid"
        self.line_last = "```"
        self.line_str_list = [self.line_first, f"flowchart TB"]
        self.subgraphs: List[Subgraph] = []
        self.nodes: List[Node] = []

    def create_node(self, node_text) -> Node:
        """
        Create a node and return it
        """
        node = Node(node_text)
        self.nodes.append(node)
        return node

    def create_subgraph(self, subgraph_title, node_list: List[Node]) -> Subgraph:
        """
        Create a subgraph with a title and a list of nodes and return it
        """
        subgraph = Subgraph(subgraph_title, node_list)
        self.subgraphs.append(subgraph)
        return subgraph

    def add_subgraph(self, subgraph: Subgraph):
        """
        Add a subgraph to the graph

        Example mermaid subgraph code:
        ```
        subgraph tIfeF["class FF"]
            ReKKn["func_a"]
            DNgPG["func_b"]
            NtFSc["func_c"]
        end
        ```
        """
        self.subgraphs.append(subgraph)
        # Generate mermaid code for the subgraph
        self.line_str_list.append(
            f'subgraph {subgraph.get_id()}["{subgraph.subgraph_title}"]'
        )
        for node in subgraph.node_list:
            if node.node_comment:
                self.line_str_list.append(f"    {node.node_comment}")
            self.line_str_list.append(f'    {node.get_id()}["{node.node_text}"]')
        self.line_str_list.append("end")

        if "subgraph_color" in subgraph.__dict__:
            if subgraph.subgraph_color:
                self.line_str_list.append(
                    f"style {subgraph.get_id()} fill:{subgraph.subgraph_color}"
                )

    def add_subgraph_by_class(self, subgraph_class):
        """
        Add a subgraph by a dataclass
        """
        subgraph = Subgraph().init_by_subgraph_class(subgraph_class)
        self.add_subgraph(subgraph)

    def point_to_by_name(self, node_or_graph_1, node_or_graph_2, arrow_str="-->"):
        """
        Create an arrow from node_or_graph_1 to node_or_graph_2

        ```
        mm.point_to_by_name(
            node_or_graph_1=mm.get_node(
                subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.create_mm_object
            ),
            node_or_graph_2=mm.get_node(
                subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.add_subgraph
            ),
        )
        ```

        Example mermaid arrow code:
        ```
        ReKKn --> tyloE
        ```
        """
        # Assume node_or_graph_1 and node_or_graph_2 have a get_id() method
        self.line_str_list.append(
            f"{node_or_graph_1.get_id()} {arrow_str} {node_or_graph_2.get_id()}"
        )

    def get_node(self, subgraph_title: str, node_name: str) -> Optional[Node]:
        """
        Retrieve a node from a subgraph by name
        """
        assert node_name != "", f"node_name cannot be empty"

        for subgraph in self.subgraphs:
            if subgraph.subgraph_title == subgraph_title:
                node = subgraph.node(node_name)
                if node:
                    return node
        return None

    def get_subgraph_by_title(self, subgraph_text: str):
        """
        Retrieve a subgraph by subgraph title
        """
        for subgraph in self.subgraphs:
            if subgraph.subgraph_title == subgraph_text:
                return subgraph

    def write_graph(self):
        """
        Write the graph using self.line_str_list to file
        """
        # Add the last line
        self.line_str_list.append(self.line_last)
        # Write to file
        with open(f"{self.output_folder}/{self.graph_title}.md", "w") as f:
            for line in self.line_str_list:
                f.write(line + "\n")


import copy


class Fun:
    def __init__(self, doc_string=""):
        self.id = generate_random_string(5)
        self.doc_string = doc_string


class MMCode(MM):
    def __init__(self, graph_title, output_folder):
        """
        Create mermaid graph for script, class, function
        """
        super().__init__(graph_title=graph_title, output_folder=output_folder)

    def _create_coding_subgraph_class(self, subgraph_class):
        """
        subgraph_class example:
        ```
        @dataclass
        class ClassTextToVoice_sg:
            func = Fun("doc string xx")
        ```
        """
        all_attributes = dir(subgraph_class)

        # Filter out special variables
        non_special_attributes = [
            attr
            for attr in all_attributes
            if not (attr.startswith("__") and attr.endswith("__"))
        ]

        method_names = []
        method_comments = []
        for method_str in non_special_attributes:
            method_names.append(method_str)
            method_comments.append(
                f"%% {subgraph_class.__dict__[method_str].doc_string}"
            )

        subgraph_class_new = copy.deepcopy(subgraph_class)
        subgraph_class_new.node_names = method_names
        subgraph_class_new.node_comments = method_comments

        return subgraph_class_new

    def create_subgraph_title_from_class(self, subgraph_class):
        class_name = subgraph_class.__name__
        is_class = "Class" == class_name[:5]
        is_script = "Script" == class_name[:6]
        is_method = "Method" == class_name[:6]
        is_function = "Function" == class_name[:7]
        has_sg = "_sg" == class_name[-3:]

        subgraph_title = class_name
        if has_sg:
            subgraph_title = subgraph_title[:-3]

        if is_class:
            subgraph_title = "class " + subgraph_title[5:]
        if is_script:
            subgraph_title = "script " + subgraph_title[6:]
        if is_method:
            subgraph_title = "method " + subgraph_title[6:]
        if is_function:
            subgraph_title = "function " + subgraph_title[7:]

        return subgraph_title

    def create_subgraph_color_from_class(self, subgraph_class):
        class_name = subgraph_class.__name__
        is_class = "Class" == class_name[:5]
        is_script = "Script" == class_name[:6]
        is_method = "Method" == class_name[:6]
        is_function = "Function" == class_name[:7]

        subgraph_color = None
        if is_class:
            subgraph_color = color_to_hex("orange")
        if is_script:
            subgraph_color = color_to_hex("red")
        if is_method:
            subgraph_color = color_to_hex("green")
        if is_function:
            subgraph_color = color_to_hex("blue")

        return subgraph_color

    def init_by_subgraph_class(self, subgraph_class):
        subgraph_name = ""
        subgraph_color = None

        if not "subgraph_name" in subgraph_class.__dict__:
            logger.warning("subgraph_title is not defined")
        else:
            subgraph_name = subgraph_class.subgraph_name

        assert "node_comments" in subgraph_class.__dict__

        if "subgraph_color" in subgraph_class.__dict__:
            subgraph_color = color_to_hex(subgraph_class.subgraph_color)

        if subgraph_class.node_comments:
            self.__init__(
                subgraph_title=subgraph_name,
                subgraph_color=subgraph_color,
                node_list=[
                    Node(node_text=method_name, node_comment=method_comment)
                    for method_name, method_comment in zip(
                        subgraph_class.node_names, subgraph_class.node_comments
                    )
                ],
            )
        else:
            self.__init__(
                subgraph_title=subgraph_name,
                subgraph_color=subgraph_color,
                node_list=[
                    Node(node_text=method_name)
                    for method_name in subgraph_class.node_names
                ],
            )
        return self

    def create_subgraph_for_coding(self, subgraph_class):
        subgraph = Subgraph(
            subgraph_title="",
            subgraph_color=None,
        )

        # Add fun list
        if not "fun_list" in subgraph.__dict__:
            subgraph.fun_list = []

            # TODO put below to a function in class_utils
            all_attributes = dir(subgraph_class)

            # Filter out special variables
            non_special_attributes = [
                attr
                for attr in all_attributes
                if not (attr.startswith("__") and attr.endswith("__"))
            ]

            for method_str in non_special_attributes:
                fun = subgraph_class.__dict__[method_str]
                subgraph.fun_list.append(fun)

        # Add node list
        subgraph_class_new = self._create_coding_subgraph_class(subgraph_class)

        subgraph.node_list = [
            Node(node_text=method_name, node_comment=method_comment)
            for method_name, method_comment in zip(
                subgraph_class_new.node_names, subgraph_class_new.node_comments
            )
        ]

        # Create subgraph title if not provided
        if not subgraph.subgraph_title:
            subgraph_title = self.create_subgraph_title_from_class(subgraph_class)
            subgraph.subgraph_title = subgraph_title

        # Create subgraph color if not provided
        if not subgraph.subgraph_color:
            subgraph_color = self.create_subgraph_color_from_class(subgraph_class)
            subgraph.subgraph_color = subgraph_color

        return subgraph

    def add_class(self, subgraph_class):
        """
        subgraph_class example:
        ```
        @dataclass
        class ClassTextToVoice_sg:
            func = "doc string xx"
        ```
        """

        subgraph = self.create_subgraph_for_coding(subgraph_class)
        self.add_subgraph(subgraph)

    def inherit_class(self, child_class, parent_class, is_text_on_arrow=True):
        child_class_name = self.create_subgraph_title_from_class(child_class)
        parent_class_name = self.create_subgraph_title_from_class(parent_class)
        super().point_to_by_name(
            node_or_graph_1=super().get_subgraph_by_title(child_class_name),
            node_or_graph_2=super().get_subgraph_by_title(parent_class_name),
            arrow_str="--inherit-->" if is_text_on_arrow else "--|>",
        )

    def composite_class(self, child_class, parent_class, is_text_on_arrow=True):
        child_class_name = self.create_subgraph_title_from_class(child_class)
        parent_class_name = self.create_subgraph_title_from_class(parent_class)
        super().point_to_by_name(
            node_or_graph_1=super().get_subgraph_by_title(child_class_name),
            node_or_graph_2=super().get_subgraph_by_title(parent_class_name),
            arrow_str="--composite-->" if is_text_on_arrow else "--|>",
        )

    def aggregate_class(self, child_class, parent_class, is_text_on_arrow=True):
        child_class_name = self.create_subgraph_title_from_class(child_class)
        parent_class_name = self.create_subgraph_title_from_class(parent_class)
        super().point_to_by_name(
            node_or_graph_1=super().get_subgraph_by_title(child_class_name),
            node_or_graph_2=super().get_subgraph_by_title(parent_class_name),
            arrow_str="--aggregate-->" if is_text_on_arrow else "--|>",
        )

    def get_node_or_subgraph_by_var(self, class_or_mem_var):
        """
        Get node or subgraph by class or mem variable as input
        ```
        node_or_graph_1 = self.get_node_or_subgraph_by_var(class_or_mem_var_1)
        ```
        """
        if "doc_string" in dir(class_or_mem_var):
            # Is a Fun instance
            for subgraph in self.subgraphs:
                for fun, node in zip(subgraph.fun_list, subgraph.node_list):
                    if fun == class_or_mem_var:
                        return node
        else:
            # Is not a Fun instance
            return self.get_subgraph_by_title(
                self.create_subgraph_title_from_class(class_or_mem_var)
            )

    def point_to(self, class_or_mem_var_1, class_or_mem_var_2, arrow_str="-->"):
        """
        Point to a node or subgraph using class or mem variable as input
        ```
        mmc.point_to(
            class_or_mem_var_1=ClassMMCode.add_class,
            class_or_mem_var_2=MethodAddClass,
        )

        ```
        """
        node_or_graph_1 = self.get_node_or_subgraph_by_var(class_or_mem_var_1)
        node_or_graph_2 = self.get_node_or_subgraph_by_var(class_or_mem_var_2)

        self.line_str_list.append(
            f"{node_or_graph_1.get_id()} {arrow_str} {node_or_graph_2.get_id()}"
        )

    def point_class_method_to_impl(self, class_method, impl):
        """
        Point class method to method implementation
        ```
        mmc.point_class_method_to_impl(
            ClassMMCode.add_class,
            MethodAddClass,
        )
        ```
        """
        self.point_to(class_method, impl)

    def wrap_subgraphs(self, parent_class, child_classes):
        id = generate_random_string(5, use_alphabetic=True)
        # get subgraph title name of parent class
        parent_subgraph_title = self.create_subgraph_title_from_class(parent_class)

        self.line_str_list.append(f'subgraph {id}["{parent_subgraph_title}"]')
        # get subgraph title name of child classes
        for child_class in child_classes:
            subgraph_title = self.create_subgraph_title_from_class(child_class)
            subgraph = self.get_subgraph_by_title(subgraph_title)
            self.line_str_list.append(f"    {subgraph.get_id()}")
        self.line_str_list.append("end")


"""
How to use:
1. Create dataclass for subgraph and its nodes (class and methods)
2. Init MM with graph_title and output_folder
3. Add subgraph by mm.add_subgraph_by_class(Main_sg)
or
mm.add_subgraph(subgraph=Subgraph().init_by_subgraph_class(Node_sg))
4. Point subgraphs or nodes by mm.point_to_by_name(
        node_or_graph_1=mm.get_node(
            subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.create_mm_object
        ),
        node_or_graph_2=mm.get_node(
            subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.add_subgraph
        ),
    )
5. Write graph by mm.write_graph()
"""


def main():
    graph_title = "trial"
    output_folder = "./"
    mm = MM(
        graph_title=graph_title,
        output_folder=output_folder,
    )

    @dataclass
    class Node_sg:
        subgraph_name = "class Node"

        get_id_comment = "%% get id"
        get_id = "get_id"
        node_comments = [get_id_comment]  # must exist
        node_names = [get_id]  # must exist

    @dataclass
    class Subgraph_sg:
        subgraph_name = "class Subgraph"

        init_by_subgraph_class = "init_by_subgraph_class"
        node = "node"
        get_id = "get_id"
        node_comments = []  # must exist
        node_names = [init_by_subgraph_class, node, get_id]  # must exist

    @dataclass
    class MM_sg:
        subgraph_name = "class MM"

        create_node = "create_node"
        create_subgraph = "create_subgraph"
        add_subgraph = "add_subgraph"
        point_to_by_name = "point_to_by_name"
        get_node = "get_node"
        get_subgraph = "get_subgraph"
        write_graph = "write_graph"

        node_comments = []  # must exist
        node_names = [
            create_node,
            create_subgraph,
            add_subgraph,
            point_to_by_name,
            get_node,
            get_subgraph,
            write_graph,
        ]  # must exist

    @dataclass
    class Main_sg:
        subgraph_name = "fun main"

        create_mm_object = "create_mm_object"
        add_subgraph = MM_sg.add_subgraph
        point_to_by_name = MM_sg.point_to_by_name
        node_comments = []  # must exist
        node_names = [create_mm_object, add_subgraph, point_to_by_name]  # must exist

    mm.add_subgraph(subgraph=Subgraph().init_by_subgraph_class(Node_sg))
    mm.add_subgraph(subgraph=Subgraph().init_by_subgraph_class(Subgraph_sg))
    mm.add_subgraph(subgraph=Subgraph().init_by_subgraph_class(MM_sg))
    mm.add_subgraph_by_class(Main_sg)

    mm.point_to_by_name(
        node_or_graph_1=mm.get_node(
            subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.create_mm_object
        ),
        node_or_graph_2=mm.get_node(
            subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.add_subgraph
        ),
    )

    mm.point_to_by_name(
        node_or_graph_1=mm.get_node(
            subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.add_subgraph
        ),
        node_or_graph_2=mm.get_node(
            subgraph_title=Main_sg.subgraph_name, node_name=Main_sg.point_to_by_name
        ),
    )

    mm.write_graph()


if __name__ == "__main__":
    main()
