"""App plugin"""

import os
from dataclasses import dataclass
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginDirectoryFilesLister:  # Must start with Plugin
    """
    This plugin lists files in a directory and stores the list in the data object.
    """

    type: str  # must exist
    plugin_can_change_files: bool

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        directory_path = parse_data_and_store(
            logger,
            "directory_path",
            data_obj,
            step_config,
            config,
        )

        file_type_list = parse_data_and_store(
            logger,
            "file_type_list",
            data_obj,
            step_config,
            config,
            default=[],
        )

        # Check if the directory exists
        directory_path = os.path.abspath(directory_path)
        if not os.path.exists(directory_path):
            logger.error(f"Directory '{directory_path}' does not exist.")
            return False

        # List files in the directory according to file_type_list
        file_list = []
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if not file_type_list or any(
                    file.endswith(ft) for ft in file_type_list
                ):
                    file_path = os.path.join(root, file)
                    file_path = file_path.replace("\\", "/")
                    file_list.append(file_path)

        data_obj.dict_var[f"{current_step}.directory_path"] = directory_path
        data_obj.dict_var[f"{current_step}.file_list"] = file_list

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
