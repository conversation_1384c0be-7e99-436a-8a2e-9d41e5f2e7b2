package com.kewtoms.whatappsdo.utils;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class EncryptionUtilsTest {

    @Test
    public void testEncryptData_ReturnsEncryptedString() throws Exception {
        byte[] data = "Hello, World!".getBytes();
        byte[] key = "01234567890123456789012345678901".getBytes(); // 32 bytes key for Fernet
        String encrypted = EncryptionUtils.encryptData(data, key); // Updated to match the method signature

        // Check that the encrypted data is not the same as the original
        assertNotEquals(encrypted, new String(data)); // Compare as strings for clarity
    }

    @Test
    public void testDecryptData_ReturnsOriginalData() throws Exception {
        byte[] data = "Hello, World!".getBytes();
        byte[] key = "01234567890123456789012345678901".getBytes(); // 32 bytes key for Fernet
        String encrypted = EncryptionUtils.encryptData(data, key); // Use the correct method signature
        String decrypted = EncryptionUtils.decryptData(key, encrypted); // No need to convert encrypted to String again

        // Check that the decrypted data is the same as the original
        assertEquals(new String(data), decrypted); // Convert byte array to String for comparison
    }

    @Test
    public void testGenerateKey_ReturnsValidKey() {
        byte[] key = EncryptionUtils.generateKey();

        //assert key length is greater than 0
        assertTrue(key.length > 0);
    }

    @Test
    public void testGenerateKeyFromString_ReturnsValidKey() throws Exception {
        String inputString = "testString";
        byte[] salt = "randomSalt".getBytes();
        int iterations = 1000;
        int length = 32; // Length in bytes

        byte[] key = EncryptionUtils.generateKeyFromString(inputString, salt, iterations, length);

        // Assert that the generated key length is as expected
        int expect_key_length = 44;
        assertEquals(key.length, expect_key_length);
    }

    @Test
    public void testGenerateKeyFromString_SameSaltSameString() throws Exception {
        String inputString = "testString";
        byte[] salt = "randomSalt".getBytes();
        int iterations = 1000;
        int length = 32; // Length in bytes

        byte[] key = EncryptionUtils.generateKeyFromString(inputString, salt, iterations, length);
        byte[] key2 = EncryptionUtils.generateKeyFromString(inputString, salt, iterations, length);

        assertArrayEquals("The byte arrays are not equal", key, key2);
    }


}
