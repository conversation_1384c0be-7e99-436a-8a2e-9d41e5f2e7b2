<?xml version="1.0" encoding="us-ascii" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="572px" preserveAspectRatio="none" style="width:880px;height:572px;background:#FFFFFF;" version="1.1" viewBox="0 0 880 572" width="880px" zoomAndPan="magnify"><defs/><g><rect fill="#DDDDDD" height="52.2402" id="_legend" rx="7.5" ry="7.5" style="stroke:#000000;stroke-width:1.0;" width="169" x="354" y="22"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="35" x="359" y="45.0439">Class</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="84" x="426" y="45.0439">Authenticator</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="47" x="359" y="64.1641">Method</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="72" x="426" y="64.1641">silentSignIn</text><line style="stroke:#000000;stroke-width:1.0;" x1="359" x2="518" y1="29" y2="29"/><line style="stroke:#000000;stroke-width:1.0;" x1="359" x2="518" y1="48.1201" y2="48.1201"/><line style="stroke:#000000;stroke-width:1.0;" x1="359" x2="518" y1="67.2402" y2="67.2402"/><line style="stroke:#000000;stroke-width:1.0;" x1="359" x2="359" y1="29" y2="67.2402"/><line style="stroke:#000000;stroke-width:1.0;" x1="422" x2="422" y1="29" y2="67.2402"/><line style="stroke:#000000;stroke-width:1.0;" x1="518" x2="518" y1="29" y2="67.2402"/><ellipse cx="266.6875" cy="97.2402" fill="#222222" rx="10" ry="10" style="stroke:#222222;stroke-width:1.0;"/><polygon fill="#F1F1F1" points="207.6875,127.2402,325.6875,127.2402,337.6875,139.2402,325.6875,151.2402,207.6875,151.2402,195.6875,139.2402,207.6875,127.2402" style="stroke:#181818;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="118" x="207.6875" y="144.3347">self hasAccountStored?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="12" x="183.6875" y="136.8232">no</text><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="169" x="11" y="161.2402"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="149" x="21" y="184.9922">No account stored -&gt; return</text><ellipse cx="95.5" cy="228.6289" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="95.5" cy="228.6289" fill="#222222" rx="6" ry="6" style="stroke:#111111;stroke-width:1.0;"/><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="255" x="310.375" y="161.2402"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="235" x="320.375" y="184.9922">Get account info from SecurePrefsManager</text><polygon fill="#F1F1F1" points="342.375,217.6289,533.375,217.6289,545.375,229.6289,533.375,241.6289,342.375,241.6289,330.375,229.6289,342.375,217.6289" style="stroke:#181818;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="191" x="342.375" y="234.7234">accessToken or refreshToken missing?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="18" x="312.375" y="227.2119">yes</text><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="134" x="210" y="251.6289"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="114" x="220" y="275.3809">callback.onPostError</text><ellipse cx="277" cy="319.0176" fill="none" rx="11" ry="11" style="stroke:#222222;stroke-width:1.0;"/><ellipse cx="277" cy="319.0176" fill="#222222" rx="6" ry="6" style="stroke:#111111;stroke-width:1.0;"/><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="159" x="519.25" y="251.6289"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="139" x="529.25" y="275.3809">self validateAccessToken</text><polygon fill="#F1F1F1" points="567.75,308.0176,629.75,308.0176,641.75,320.0176,629.75,332.0176,567.75,332.0176,555.75,320.0176,567.75,308.0176" style="stroke:#181818;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="62" x="567.75" y="325.1121">Token Valid?</text><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="18" x="537.75" y="317.6006">yes</text><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="192" x="374" y="342.0176"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="172" x="384" y="365.7695">self signInUsingAccessToken()</text><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="163" x="388.5" y="398.4063"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="143" x="398.5" y="422.1582">callback.onPostSuccess()</text><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="283" x="586" y="342.0176"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="263" x="596" y="365.7695">self obtainNewAccessTokenAndRefreshToken()</text><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="249" x="603" y="398.4063"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="229" x="613" y="422.1582">self signInUsingAccessToken(newToken)</text><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="163" x="646" y="454.7949"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="143" x="656" y="478.5469">callback.onPostSuccess()</text><polygon fill="#F1F1F1" points="598.75,497.1836,610.75,509.1836,598.75,521.1836,586.75,509.1836,598.75,497.1836" style="stroke:#181818;stroke-width:0.5;"/><ellipse cx="266.6875" cy="551.1836" fill="none" rx="10" ry="10" style="stroke:#222222;stroke-width:1.5;"/><line style="stroke:#222222;stroke-width:2.5;" x1="260.5003" x2="272.8747" y1="544.9964" y2="557.3708"/><line style="stroke:#222222;stroke-width:2.5;" x1="272.8747" x2="260.5003" y1="544.9964" y2="557.3708"/><line style="stroke:#181818;stroke-width:1.0;" x1="95.5" x2="95.5" y1="197.6289" y2="217.6289"/><polygon fill="#181818" points="91.5,207.6289,95.5,217.6289,99.5,207.6289,95.5,211.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="277" x2="277" y1="288.0176" y2="308.0176"/><polygon fill="#181818" points="273,298.0176,277,308.0176,281,298.0176,277,302.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="470" x2="470" y1="378.4063" y2="398.4063"/><polygon fill="#181818" points="466,388.4063,470,398.4063,474,388.4063,470,392.4063" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="727.5" x2="727.5" y1="378.4063" y2="398.4063"/><polygon fill="#181818" points="723.5,388.4063,727.5,398.4063,731.5,388.4063,727.5,392.4063" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="727.5" x2="727.5" y1="434.7949" y2="454.7949"/><polygon fill="#181818" points="723.5,444.7949,727.5,454.7949,731.5,444.7949,727.5,448.7949" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="555.75" x2="470" y1="320.0176" y2="320.0176"/><line style="stroke:#181818;stroke-width:1.0;" x1="470" x2="470" y1="320.0176" y2="342.0176"/><polygon fill="#181818" points="466,332.0176,470,342.0176,474,332.0176,470,336.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="641.75" x2="727.5" y1="320.0176" y2="320.0176"/><line style="stroke:#181818;stroke-width:1.0;" x1="727.5" x2="727.5" y1="320.0176" y2="342.0176"/><polygon fill="#181818" points="723.5,332.0176,727.5,342.0176,731.5,332.0176,727.5,336.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="470" x2="470" y1="434.7949" y2="509.1836"/><line style="stroke:#181818;stroke-width:1.0;" x1="470" x2="586.75" y1="509.1836" y2="509.1836"/><polygon fill="#181818" points="576.75,505.1836,586.75,509.1836,576.75,513.1836,580.75,509.1836" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="727.5" x2="727.5" y1="491.1836" y2="509.1836"/><line style="stroke:#181818;stroke-width:1.0;" x1="727.5" x2="610.75" y1="509.1836" y2="509.1836"/><polygon fill="#181818" points="620.75,505.1836,610.75,509.1836,620.75,513.1836,616.75,509.1836" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="598.75" x2="598.75" y1="288.0176" y2="308.0176"/><polygon fill="#181818" points="594.75,298.0176,598.75,308.0176,602.75,298.0176,598.75,302.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="330.375" x2="277" y1="229.6289" y2="229.6289"/><line style="stroke:#181818;stroke-width:1.0;" x1="277" x2="277" y1="229.6289" y2="251.6289"/><polygon fill="#181818" points="273,241.6289,277,251.6289,281,241.6289,277,245.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="545.375" x2="598.75" y1="229.6289" y2="229.6289"/><line style="stroke:#181818;stroke-width:1.0;" x1="598.75" x2="598.75" y1="229.6289" y2="251.6289"/><polygon fill="#181818" points="594.75,241.6289,598.75,251.6289,602.75,241.6289,598.75,245.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="598.75" x2="598.75" y1="521.1836" y2="526.1836"/><line style="stroke:#181818;stroke-width:1.0;" x1="598.75" x2="266.6875" y1="526.1836" y2="526.1836"/><line style="stroke:#181818;stroke-width:1.0;" x1="266.6875" x2="266.6875" y1="526.1836" y2="541.1836"/><polygon fill="#181818" points="262.6875,531.1836,266.6875,541.1836,270.6875,531.1836,266.6875,535.1836" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="437.875" x2="437.875" y1="197.6289" y2="217.6289"/><polygon fill="#181818" points="433.875,207.6289,437.875,217.6289,441.875,207.6289,437.875,211.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="195.6875" x2="95.5" y1="139.2402" y2="139.2402"/><line style="stroke:#181818;stroke-width:1.0;" x1="95.5" x2="95.5" y1="139.2402" y2="161.2402"/><polygon fill="#181818" points="91.5,151.2402,95.5,161.2402,99.5,151.2402,95.5,155.2402" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="337.6875" x2="437.875" y1="139.2402" y2="139.2402"/><line style="stroke:#181818;stroke-width:1.0;" x1="437.875" x2="437.875" y1="139.2402" y2="161.2402"/><polygon fill="#181818" points="433.875,151.2402,437.875,161.2402,441.875,151.2402,437.875,155.2402" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="266.6875" x2="266.6875" y1="107.2402" y2="127.2402"/><polygon fill="#181818" points="262.6875,117.2402,266.6875,127.2402,270.6875,117.2402,266.6875,121.2402" style="stroke:#181818;stroke-width:1.0;"/><!--SRC=[dP31JiCm38RlUGeVjmCy09D05KA8mwQ90VUiTTjecOtYb0bf3q-SJ6i7WWCzj4dz-VUNUEoG6eZyRW2EzzwAW3v7g8RO8qLNsyWX_T3w4cFFJQcBywcySHqzaPOrnkSqOoJQ48rnBSm4VGkzbQgkUKXy2iFcRWvEO4Oy1nrYDF5cnM0B198fkBg5W748jCY4g2LwmS8_OZmtE6ePsi1xs60z15m7R6LfoNOOIhEw91f5NdY71EbCYGaeVTdldOYZxcJsYJ9Ho-Dgw_tMrhjhfZLBV0Y1Jz5dDrsS_N9JFls7zQwn4Qjn_DYeNiNWJR5Vvuznag_yLMqdYRFvufBzvhiPCZl5BbJ7VDv6wsY5XqbwHStpvBP-3FjBZF2G5_yGfCQrPboLjtw-0000]--></g></svg>