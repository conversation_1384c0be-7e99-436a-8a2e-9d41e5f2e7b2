from dataclasses import dataclass
from datetime import datetime
from collections import defaultdict
import os
import re
from typing import List

from tomsze_utils.text_file_parser_utils import (
    extract_data_from_lines_in_file,
    extract_data_from_a_string_of_lines,
)

from tomsze_utils.string_utils import (
    extract_block_of_lines_using_start_end_marker,
    extract_data_from_a_string_of_lines_as_tuple,
)


def detect_frequent_ips(
    ip_datetime_list: list,
    short_time_period: float = 1.0,
    appear_times: int = 20,
    datetime_format: str = "%Y-%m-%d %H:%M:%S",  # New argument for datetime format
) -> list:
    """
    Detects frequent IPs based on their appearance within a short time period.

    This function takes a list of tuples containing IP addresses and their corresponding datetime strings,
    a short time period in seconds, and the minimum number of appearances required to consider an IP frequent.
    It returns a list of IPs that have appeared at least 'appear_times' times within 'short_time_period' seconds.

    Examples:
        >>> ip_datetime_list = [("2022-01-01 00:00:00", "***********"), ("2022-01-01 00:00:01", "***********"),
        ...                       ("2022-01-01 00:00:02", "***********"), ("2022-01-01 00:00:03", "***********")]
        >>> detect_frequent_ips(ip_datetime_list, 3, 3)
        ['***********']

        >>> ip_datetime_list = [("2022-01-01 00:00:00", "***********"), ("2022-01-01 00:00:01", "***********"),
        ...                       ("2022-01-01 00:00:02", "***********"), ("2022-01-01 00:00:03", "***********")]
        >>> detect_frequent_ips(ip_datetime_list, 3, 4)
        []

        >>> ip_datetime_list = [(None, "***********"), ("2022-01-01 00:00:01", None)]
        >>> detect_frequent_ips(ip_datetime_list, 3, 3)
        []
    """
    ip_count_dict = defaultdict(list)

    for dt_str, ip in ip_datetime_list:
        if dt_str is None or ip is None:
            continue  # Skip if either datetime string or IP is None
        dt = datetime.strptime(
            dt_str, datetime_format
        )  # Use the provided datetime format
        ip_count_dict[ip].append(dt.timestamp())

    frequent_ips = []

    for ip, timestamp_list in ip_count_dict.items():
        timestamp_list.sort()
        for i in range(len(timestamp_list) - appear_times + 1):
            if (
                timestamp_list[i + appear_times - 1] - timestamp_list[i]
                <= short_time_period
            ):
                frequent_ips.append(ip)
                break

    return frequent_ips


def detect_frequent_ips_in_folder(
    folder_path: str,
    short_time_period: float = 1.0,
    appear_times: int = 20,
    datetime_format: str = "%Y-%m-%d %H:%M:%S",  # New argument for datetime format
) -> dict:
    """
    Scans a folder for log files, extracts datetime and IP information, and identifies frequent IPs based on their appearance within a short time period.

    Args:
        folder_path (str): The path to the folder containing log files.
        short_time_period (float, optional): The time period in seconds to consider for frequent IP detection. Defaults to 1.0.
        appear_times (int, optional): The minimum number of appearances required to consider an IP frequent. Defaults to 20.
        datetime_format (str, optional): The format of the datetime strings in the log files. Defaults to "%Y-%m-%d %H:%M:%S".

    Returns:
        dict: A dictionary where keys are log file names and values are lists of frequent IPs detected in each file.

    Examples:
        >>> folder_path = "path/to/logs"
        >>> result = detect_frequent_ips_in_folder(folder_path, 3, 3)
        >>> result
        {'log1.log': ['***********'], 'log2.log': ['***********']}

        >>> folder_path = "path/to/logs"
        >>> result = detect_frequent_ips_in_folder(folder_path, 3, 4)
        >>> result
        {}
    """
    frequent_ips_dict = {}

    for filename in os.listdir(folder_path):
        if re.match(
            r".*\.log\d*$", filename
        ):  # Match .log followed by any number of digits
            file_path = os.path.join(folder_path, filename)
            ip_datetime_list = []

            # Using extract_data_from_lines to extract datetime and ip
            extracted_data = extract_data_from_lines_in_file(
                file_path, ["time", "Request ip"]
            )
            for dt_str, ip in extracted_data:
                ip_datetime_list.append((dt_str, ip))

            frequent_ips = detect_frequent_ips(
                ip_datetime_list,
                short_time_period,
                appear_times,
                datetime_format,  # Pass the datetime format
            )
            if (
                frequent_ips
            ):  # Only add to the dictionary if there are frequent IPs detected
                frequent_ips_dict[filename] = frequent_ips

    return frequent_ips_dict


@dataclass
class IpVisitEndpointData:
    ip: str
    requested_endpoint: str
    method: str
    body: str = None


@dataclass
class LegitIPData:
    ip_list: List[str] = None  # Optional list of legitimate IPs
    ip_ranges_dict: dict[str, str] = (
        None  # Optional dictionary where keys are range names and values are IP ranges defined as "ip_a-ip_b"
    )


def detect_hacker_ips_by_strange_endpoints(
    base_endpoint_url: str,
    ipVisitEndpointData_list: List[IpVisitEndpointData],
    legitIPData: LegitIPData,
    legit_sub_endpoint_list: List[str] = [
        "favicon.ico",
        "static/favicon.ico",
        "robots.txt",
    ],
) -> dict[str, List[str]]:
    """
    Detects hacker IPs based on strange endpoints that are not in the legitimate IP list
    and do not match the legitimate sub-endpoints.

    Args:
        base_endpoint_url (str): The base URL to combine with legitimate sub-endpoints.
        ipVisitEndpointData_list (List[IpVisitEndpointData]): A list of IP visit endpoint data.
        legitIPData (LegitIPData): An object containing a list of legitimate IPs and ranges.
        legit_sub_endpoint_list (List[str], optional): A list of legitimate sub-endpoints.
            Defaults to ["favicon.ico", "static/favicon.ico", "robots.txt"].

    Returns:
        dict[str, List[str]]: A dictionary where keys are detected hacker IPs and values are lists of their strange endpoints.

    Examples:
        ```python
        result = detect_hacker_ips_by_strange_endpoints(
            base_endpoint_url="http://example.com",
            ipVisitEndpointData_list=[
                IpVisitEndpointData(ip="***********", requested_endpoint="/unknown", method="GET"),
                IpVisitEndpointData(ip="***********", requested_endpoint="/favicon.ico", method="GET"),
            ],
            legitIPData=LegitIPData(ip_list=["***********", "***********"], ip_ranges_dict={}),
        )
        # result should be {"***********": ["/unknown"]}
        ```

        ```python
        result = detect_hacker_ips_by_strange_endpoints(
            base_endpoint_url="http://example.com",
            ipVisitEndpointData_list=[
                IpVisitEndpointData(ip="***********", requested_endpoint="/static/favicon.ico", method="GET"),
                IpVisitEndpointData(ip="***********", requested_endpoint="/malicious", method="POST"),
            ],
            legitIPData=LegitIPData(ip_list=["***********", "***********"], ip_ranges_dict={}),
            legit_sub_endpoint_list=["/malicious"],
        )
        # result should be {"***********": ["/malicious"]}
        ```

        ```python
        result = detect_hacker_ips_by_strange_endpoints(
            base_endpoint_url="http://example.com",
            ipVisitEndpointData_list=[
                IpVisitEndpointData(ip="***********0", requested_endpoint="/malicious", method="GET"),
                IpVisitEndpointData(ip="***********5", requested_endpoint="/unknown", method="GET"),
            ],
            legitIPData=LegitIPData(ip_list=["***********", "***********"], ip_ranges_dict={"range1": "***********0-***********0"}),
        )
        # result should be {"***********0": ["/malicious"], "***********5": ["/unknown"]}
        ```
    """
    hacker_ips_dict = {}

    # Create a set of legitimate IPs for quick lookup
    legit_ips_set = set(legitIPData.ip_list) if legitIPData.ip_list else set()

    # Create a set of legitimate IP ranges for quick lookup
    legit_ip_ranges = legitIPData.ip_ranges_dict if legitIPData.ip_ranges_dict else {}

    # Combine base_endpoint_url with the legitimate sub-endpoints
    combined_legit_sub_endpoints = [
        f"{base_endpoint_url}/{sub_endpoint}"
        for sub_endpoint in legit_sub_endpoint_list
    ]
    combined_legit_sub_endpoints.append(base_endpoint_url + "/")

    # Iterate through the endpoint hacker data
    for data in ipVisitEndpointData_list:
        if data.ip not in legit_ips_set:
            # Check if the IP is within any of the legitimate IP ranges
            ip_is_legit = False
            for range_name, ip_range in legit_ip_ranges.items():
                ip_start, ip_end = ip_range.split("-")
                if ip_start <= data.ip <= ip_end:
                    ip_is_legit = True
                    break

            if not ip_is_legit:
                if data.requested_endpoint not in combined_legit_sub_endpoints:
                    if data.ip not in hacker_ips_dict:
                        hacker_ips_dict[data.ip] = []
                    hacker_ips_dict[data.ip].append(data.requested_endpoint)

    return hacker_ips_dict


def detect_hacker_ips_by_strange_endpoints_in_folder(
    folder_path: str,
    base_endpoint_url: str,
    legitIPData: LegitIPData,
    start_marker: str = "Request ip",
    end_marker: str = "----,",
) -> dict[str, List[str]]:
    """
    Detects hacker IPs from log files in a specified folder based on strange endpoints.

    Args:
        folder_path (str): The path to the folder containing log files.
        base_endpoint_url (str): The base URL for legitimate endpoints.
        legitIPData (LegitIPData): An object containing a list of legitimate IPs.
        start_marker (str): The marker indicating the start of a log entry (default "Request ip").
        end_marker (str): The marker indicating the end of a log entry (default "----,").

    Returns:
        dict[str, List[str]]: A dictionary where keys are log filenames and values are lists of detected hacker IPs.

    Examples:
        ```python
        result = detect_hacker_ips_by_strange_endpoints_in_folder(
            folder_path="/path/to/logs",
            base_endpoint_url="http://example.com",
            legitIPData=LegitIPData(ip_list=["***********", "***********"]),
        )
        # result might be {"log1.log": ["***********"]}

        result = detect_hacker_ips_by_strange_endpoints_in_folder(
            folder_path="/path/to/logs",
            base_endpoint_url="http://example.com",
            legitIPData=LegitIPData(ip_list=["***********"]),
        )
        # result might be {"log2.log": ["***********", "***********"]}
        ```
    """
    hacker_ips_dict = {}

    for filename in os.listdir(folder_path):
        if re.match(
            r".*\.log\d*$", filename
        ):  # Match .log followed by any number of digits
            file_path = os.path.join(folder_path, filename)

            with open(file_path) as f:
                file_content = f.read()

            # Extract blocks of lines from the log file content between the specified start and end markers
            block_string_list = extract_block_of_lines_using_start_end_marker(
                file_content=file_content,
                start_marker=start_marker,
                end_marker=end_marker,
                is_include_markers_line=True,
            )

            ipVisitEndpointData_list = []
            for block_string in block_string_list:
                # Using extract_data_from_lines to extract datetime and ip
                extracted_data = extract_data_from_a_string_of_lines_as_tuple(
                    input_string=block_string,
                    key_list=[
                        "Request ip",
                        "Request method",
                        "Request endpoint",
                        "Request body",
                    ],
                )

                ip, method, requested_endpoint, body = extracted_data
                ipVisitEndpointData_list.append(
                    IpVisitEndpointData(
                        ip=ip,
                        requested_endpoint=requested_endpoint,
                        method=method,
                        body=body,
                    )
                )

            # Detect hacker IPs based on strange endpoints
            result_dict = detect_hacker_ips_by_strange_endpoints(
                base_endpoint_url=base_endpoint_url,
                ipVisitEndpointData_list=ipVisitEndpointData_list,
                legitIPData=legitIPData,
            )

            if (
                result_dict
            ):  # Only add to the dictionary if there are frequent IPs detected
                hacker_ips_dict[filename] = result_dict

    return hacker_ips_dict
