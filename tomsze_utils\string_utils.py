import html
import logging
import os
import re
from typing import List, Tuple
import re2


def split_command(command: str) -> List[str]:
    """
    Splits a command string into a list of arguments, handling quoted substrings.

    Args:
        command (str): The command string to split.

    Returns:
        list: A list of command arguments.

    Raises:
        ValueError: If the number of double quotes in the command is odd.

    Examples:
        ```python
        split_command('echo "Hello World"')
        # Returns: ['echo', 'Hello World']

        split_command('ls -l "My Documents"')
        # Returns: ['ls', '-l', 'My Documents']
        ```
    """
    if command.count('"') % 2 != 0:
        count = command.count('"')
        raise ValueError(f"Number of double quotation marks {count} is not valid.")

    in_quote = False
    current_arg = ""
    args = []
    for char in command:
        if char == '"':
            in_quote = not in_quote
        elif char == " " and not in_quote:
            if current_arg:
                args.append(current_arg)
                current_arg = ""
        else:
            current_arg += char
    if current_arg:
        args.append(current_arg)
    return args


def split_text_to_words(text: str) -> List[str]:
    """
    Split a string into a list of words, removing extra whitespace.

    This function normalizes whitespace in the input text and splits it into individual words.

    Args:
        text (str): The input string containing words separated by whitespace.

    Returns:
        List[str]: A list of words extracted from the input string.

    Note:
    Example 1:
    ```python
    result = split_text_to_words(' one two  three ')
    # Returns: ['one', 'two', 'three']
    ```

    Example 2:
    ```python
    result = split_text_to_words(' one two  three 一 二   三 ')
    # Returns: ['one', 'two', 'three', '一', '二', '三']
    ```
    """
    list_words = re2.sub(r"\s+", " ", text).split()
    return list_words


def get_text_query_match_count_with_remove(text: str, query: str) -> int:
    """
    Get match count of each word of a query string within a text.

    Note: After a match is found, it is removed from original text.

    Args:
        text (str): The text in which to search for the query words.
        query (str): The query string containing words to match.

    Example 1:
    ```python
    result = get_text_query_match_count_with_remove(text='one two three ', query='one')
    # Returns: 3
    ```

    Example 2:
    ```python
    result = get_text_query_match_count_with_remove(text='one two three ', query='one two')
    # Returns: 6
    ```

    Example 3:
    ```python
    result = get_text_query_match_count_with_remove(text='one two three ', query='one one')
    # Returns: 3
    ```
    """
    query_word_list = split_text_to_words(query)
    text_copy = text
    match_count = 0
    for query_word in query_word_list:
        if query_word in text_copy:
            text_copy = text_copy.replace(query_word, " ", 1)
            match_count += len(query_word)

    return match_count


def get_text_list_query_match_count_ratio(
    text_list: List[str], query: str
) -> List[float]:
    """
    Calculate the ratio of matched characters for each string in a list against a query.

    Note: The match count is based on the number of characters matched for each word in the query.

    Args:
        text_list (List[str]): A list of strings to be evaluated against the query.
        query (str): The query string containing words to match.

    Example 1:
    ```python
    text_list = [
            "one two three",
            #~~~ 3/11
            "one one two",
            #~~~ ~~~ 6/9
            "two two three three",
            # 0
            "oneonetwo threethree",
            #~~~~~~ 6/19
        ]
    query = 'one'
    result = get_text_list_query_match_count_ratio(text_list=text_list, query=query)
    # Returns: [3/11, 6/9, 0, 6/19]
    ```

    Example 2:
    ```python
    text_list = [
        "one two three",
        # ~~~ 6/11
        "one one two",
        # ~~~ ~~~ 9/9
        "two two three three",
        # ~~~ ~~~ 6/16
        "oneonetwo threethree",
        # ~~~~~~~~~ 9/19
    ]
    query = 'one two'
    result = get_text_list_query_match_count_ratio(text_list=text_list, query=query)
    # Returns: [6/11, 9/9, 6/16, 9/19]
    ```
    """
    match_count_ratio_list = []
    query_word_list = split_text_to_words(query)

    for text in text_list:
        match_count = 0
        text_copy = text
        for query_word in query_word_list:
            if query_word in text_copy:
                match_count += text_copy.count(query_word) * len(query_word)
                text_copy = text_copy.replace(query_word, "")

        text_char_count = len(text.replace(" ", ""))
        match_count_ratio = match_count / text_char_count
        match_count_ratio_list.append(match_count_ratio)

    return match_count_ratio_list


def count_space_in_front(text: str) -> int:
    """Counts the number of leading spaces in a string.

    Examples:
    ```python
    count_space_in_front("  hello")  # Returns 2
    count_space_in_front("hello")  # Returns 0
    ```

    Args:
        text (str): The input string.

    Returns:
        int: The number of leading spaces.
    """
    count = 0
    for ch in text:
        if ch == " ":
            count += 1
        else:
            break

    return count


def mix(string1: str, string2: str, use_odd: bool = True) -> str:
    """
    Mixes two strings by alternating characters from each string based on the specified index type.

    If use_odd is True, characters from string2 are taken from odd indices; otherwise, they are taken from even indices.

    Examples:
    ```python
    # Test case 1: Basic mix with odd indices
    result = mix("123456", "abcdef", use_odd=True)
    print(result)  # Output: "1b3d5f"

    # Test case 2: Basic mix with even indices
    result = mix("abcdef", "123456", use_odd=False)
    print(result)  # Output: "a2c4e6"
    ```

    Raises:
        ValueError: If the lengths of the two strings are not the same.
    """
    if len(string1) != len(string2):
        raise ValueError("Both strings must be of the same length.")

    result = []
    for i in range(
        len(string1)
    ):  # Use the length of the strings since they are the same
        if (i % 2 == 1) == use_odd:  # Check if we are using odd or even index
            result.append(string2[i])
        else:
            result.append(string1[i])
    return "".join(result)


def mix_with_index(
    string1: str,
    string2: str,
    indices: list,
) -> str:
    """
    Mixes two strings by taking characters from each string based on the specified indices.
    The characters from string2 will override those from string1 at the specified indices.

    Examples:
    ```python
    # Test case 1: Basic mix with specified indices
    result = mix_with_index("abcdef", "012345", [0, 1, 2, 3, 4, 5])
    print(result)  # Output: "012345"

    # Test case 2: Using a subset of indices
    result = mix_with_index("abcdef", "012345", [0, 2, 4])
    print(result)  # Output: "0b2d4f"
    ```

    Raises:
        ValueError: If any index in the list is out of range for the strings.
    """
    if any(index >= len(string1) or index >= len(string2) for index in indices):
        raise ValueError("One or more indices are out of range for the strings.")

    result = list(string1)  # Start with a list of characters from string1
    for index in indices:
        result[index] = string2[index]  # Override with characters from string2

    return "".join(result)  # Join the list back into a string


def is_string_list_any_in(
    string_list: List[str],
    string: str,
) -> bool:
    """
    Check if any string in the list is contained within the given string.

    Args:
        string_list (list[str]): A list of strings to check.
        string (str): The string to check against.

    Returns:
        bool: True if any string in the list is found in the given string, False otherwise.

    Examples:
    ```python
    # Example 1: Basic usage
    result = is_string_list_any_in(["abc", "def", "ghi"], "abcdef")
    print(result)  # Output: True

    # Example 2: No matches found
    result = is_string_list_any_in(["xyz", "uvw"], "abcdef")
    print(result)  # Output: False
    ```

    """
    return any(each_string in string for each_string in string_list)


def extract_substring_within_marker(
    input_string: str,
    start_marker: str = "~~~start~~~",
    end_marker: str = "~~~end~~~",
) -> str:
    """
    Extracts a substring from the given string that is located between the specified start and end markers.

    Args:
        input_string (str): The input string from which to extract the substring.
        start_marker (str): The marker indicating the start of the substring. Default is "~~~start~~~".
        end_marker (str): The marker indicating the end of the substring. Default is "~~~end~~~".

    Returns:
        str: The extracted substring with quotes replaced by single quotes.

    Examples:
    ```python
    result = extract_substring_within_marker("This is a test ~~~start~~~Hello World~~~end~~~ string.")
    print(result)  # Output: "Hello World"

    result = extract_substring_within_marker("~~~start~~~Sample Text~~~end~~~ is here.")
    print(result)  # Output: "Sample Text"
    ```

    """
    start_index = input_string.find(start_marker)
    if start_index == -1:
        return ""  # Return an empty string if the start_marker is not found

    start_index += len(start_marker)
    end_index = input_string.find(end_marker)

    if end_index == -1:
        return ""  # Return an empty string if the end_marker is not found

    sub_string = input_string[start_index:end_index].strip()
    sub_string = sub_string.replace('"', "'")
    return sub_string


def extract_data_from_a_string_of_lines(input_string: str, data_list: list) -> list:
    """
    Extracts data from each line in a string based on a list of strings, handling missing data.
    The value extracted is between ':' and ','.

    Args:
        input_string (str): The input string containing lines of data.
        data_list (list): The list of strings to extract from each line.

    Returns:
        list: A list of tuples containing extracted data, with missing data represented as None.

    Examples:
        >>> input_string = "time: 2022-01-01 00:00:00, name: Tom, age: 5\\ntime: 2022-01-01 00:00:01, name: Jane, age: 6\\ntime: 2022-01-01 00:00:02, name: Bob"
        >>> data_list = ["time", "name", "age"]
        >>> result = extract_data_from_string(input_string, data_list)
        >>> result
        [('2022-01-01 00:00:00', 'Tom', '5'), ('2022-01-01 00:00:01', 'Jane', '6'), ('2022-01-01 00:00:02', 'Bob', None)]

        >>> input_string = "time: 2022-01-01 00:00:00, name: Tom, age: 5\\ntime: 2022-01-01 00:00:01, name: Jane, age: 6\\ntime: 2022-01-01 00:00:02, name: Bob, age: 7, location: New York"
        >>> data_list = ["time", "name", "age", "location"]
        >>> result = extract_data_from_string(input_string, data_list)
        >>> result
        [('2022-01-01 00:00:00', 'Tom', '5', None), ('2022-01-01 00:00:01', 'Jane', '6', None), ('2022-01-01 00:00:02', 'Bob', '7', 'New York')]
    """
    result_list = []

    # If data_list is empty, return an empty list
    if not data_list:
        return []

    lines_list = input_string.splitlines()

    for line in lines_list:
        line = line.replace("\n", "")
        split_list = line.split(",")

        # Initialize a dictionary to hold extracted data with default value as None
        data_dict = {data: None for data in data_list}

        # Extract data from the line
        for data in split_list:
            key, value = data.split(": ", 1) if ": " in data else (data, None)
            key = key.strip()
            if value:
                value = value.strip()
            if key in data_dict:
                data_dict[key] = value

        # Convert the dictionary to a tuple and append to the result list
        tuple_result = tuple(data_dict[data] for data in data_list)
        result_list.append(tuple_result)

    return result_list


def extract_data_from_a_string_of_lines_as_tuple(
    input_string: str,
    key_list: List[str],
    delimiters: List[str] = [",", "||"],
) -> tuple:
    """
    Extracts data from a string of lines based on specified keys.

    Given an input string, this function extracts data that is behind ":" and before the specified delimiters according to each given key in the key list.

    Args:
        input_string (str): The input string containing key-value pairs.
        key_list (List[str]): A list of keys to extract values for.
        delimiters (List[str]): A list of delimiters to split key-value pairs. Defaults to [",", "||"].

    Returns:
        tuple: A tuple of extracted data, with the length matching the key list.

    Examples:
        ```python
        result = extract_data_from_a_string_of_lines_as_tuple(
            input_string="time:aa, name:tom,\ntime:bb, name:may,\n",
            key_list=["time", "name"]
        )
        # result will be ("bb", "may")
        ```

        ```python
        result = extract_data_from_a_string_of_lines_as_tuple(
            input_string="time:aa,\nname:tom,\n",
            key_list=["time", "name"]
        )
        # result will be ("aa", "tom")
        ```
    """
    # Split the input string into lines
    lines = input_string.strip().split("\n")

    # Initialize a dictionary to store the latest values for each key
    data = {key: None for key in key_list}

    # Iterate over each line
    for line in lines:
        # Split the line into key-value pairs using the specified delimiters
        pairs = []
        for delimiter in delimiters:
            parts = line.split(delimiter)
            for part in parts:
                pairs.extend(part.strip().split(":"))

        # Process each pair
        for i in range(0, len(pairs), 2):
            if i + 1 < len(pairs):
                key = pairs[i].strip()
                value = pairs[i + 1].strip()
                if key in key_list:
                    data[key] = value

    # Extract the values in the order of the key list and return as a tuple
    return tuple(data[key] for key in key_list)


def extract_block_of_lines_using_start_end_marker(
    file_content: str,
    start_marker: str,
    end_marker: str,
    is_include_markers_line: bool = False,
) -> List[str]:
    """
    Extracts blocks of lines from the given file content between specified start and end markers.

    Args:
        file_content (str): The content of the file as a string.
        start_marker (str): The marker indicating the start of the block.
        end_marker (str): The marker indicating the end of the block.
        is_include_markers_line (bool): If True, includes the markers in the output. Defaults to False.

    Returns:
        List[str]: A list of extracted blocks of lines, each block is a string.

    Raises:
        ValueError: If start_marker and end_marker are the same.

    Examples:
        ```python
        file_content = "START\nLine 1\nLine 2\nEND\n"
        start_marker = "START"
        end_marker = "END"
        expected_output = ["START\nLine 1\nLine 2\nEND\n"]

        result = extract_block_of_lines_using_start_end_marker(
            file_content, start_marker, end_marker, is_include_markers_line=True
        )
        assert result == expected_output
        ```

        ```python
        file_content = "BEGIN\nLine A\nLine B\nFINISH\nExtra Line\n"
        start_marker = "BEGIN"
        end_marker = "FINISH"
        expected_output = ["Line A\nLine B\n"]

        result = extract_block_of_lines_using_start_end_marker(
            file_content, start_marker, end_marker, is_include_markers_line=False
        )
        assert result == expected_output
        ```

        ```python
        file_content = "START\nLine 1\nLine 2\nEND\nSTART\nLine 3\nLine 4\nEND\n"
        start_marker = "START"
        end_marker = "END"
        expected_output = ["START\nLine 1\nLine 2\nEND\n", "START\nLine 3\nLine 4\nEND\n"]

        result = extract_block_of_lines_using_start_end_marker(
            file_content, start_marker, end_marker, is_include_markers_line=True
        )
        assert result == expected_output
        ```
    """
    if start_marker == end_marker:
        raise ValueError("start_marker and end_marker must be different.")

    blocks = []
    in_block = False
    for line in file_content.split("\n"):
        if start_marker in line:
            in_block = True
            block = []
            if is_include_markers_line:
                block.append(line)
            continue
        elif end_marker in line and in_block:
            in_block = False
            if is_include_markers_line:
                block.append(line)
            blocks.append("\n".join(block) + "\n")
        elif in_block:
            block.append(line)

    return blocks


def remove_string_after_tag(
    text: str,
    tag: str = "<tool_call>",
) -> str:
    """Remove all the string after the specified tag, including the tag itself.

    Args:
        text (str): The input string from which to remove content.
        tag (str, optional): The tag after which to remove content. Defaults to "<tool_call>".

    Returns:
        str: The modified string with content after the tag removed.

    Examples:
        ```python
        result = remove_string_after_tag("This is a test string <tool_call> with some content.")
        # result will be "This is a test string"
        ```

        ```python
        result = remove_string_after_tag("This string does not contain the tag.")
        # result will be "This string does not contain the tag."
        ```
    """
    start_index = text.find(tag)
    if start_index != -1:
        return text[:start_index].rstrip()  # Return the text before the specified tag
    return text  # If the tag is not found, return the original text


def extract_list_elements_containing_string(
    string_list: List[str], substring: str
) -> List[str]:
    """
    Extracts elements from a list of strings that contain a specified substring.

    Args:
        string_list (List[str]): The list of strings to search through.
        substring (str): The substring to look for in each string.

    Returns:
        List[str]: A list of strings that contain the specified substring.

    Examples:
        ```python
        result = extract_list_elements_containing_string(
            string_list=["apple", "banana", "cherry"], substring="an"
        )
        # result will be ["banana"]
        ```

        ```python
        result = extract_list_elements_containing_string(
            string_list=["apple", "banana", "cherry"], substring="xyz"
        )
        # result will be []
        ```
    """
    return [s for s in string_list if substring in s]


def find_best_match_index(
    text: str, query: str, ignore_ranges: List[Tuple[int, int]] = None
) -> int:
    """
    Finds the best match index of a query within a text, prioritizing whole word matches,
    while ignoring specified index ranges.

    Args:
        text (str): The text to search within.
        query (str): The query to search for.
        ignore_ranges (List[Tuple[int, int]], optional): A list of tuples, where each tuple
            represents a range of indices to ignore (start, end). Defaults to None.

    Returns:
        int: The index of the best match, or -1 if no match is found.

    Examples:
        ```python
        result = find_best_match_index(text="hello world", query="world")
        # result will be 6
        ```

        ```python
        result = find_best_match_index(text="hello world", query="orl")
        # result will be 7
        ```

        ```python
        result = find_best_match_index(text="world world", query="world", ignore_ranges=[(0, 5)])
        # result will be 6
        ```

        ```python
        result = find_best_match_index(text="world world", query="xx")
        # result will be -1
        ```
    """
    import re

    def is_index_ignored(index: int) -> bool:
        if ignore_ranges:
            for start, end in ignore_ranges:
                if start <= index < end:
                    return True
        return False

    # First, try to find a whole word match
    pattern = r"\b" + re.escape(query) + r"\b"  # \b ensures word boundary
    for match in re.finditer(pattern, text):
        if not is_index_ignored(match.start()):
            return match.start()

    # If no whole word match is found, find any match
    index = text.find(query)
    while index != -1:
        if not is_index_ignored(index):
            return index
        index = text.find(query, index + 1)  # Find the next occurrence

    return -1


def highlight_match_using_html(match_text: str) -> str:
    """Highlight the given text in blue using HTML.

    Args:
        match_text (str): The text to be highlighted.

    Returns:
        str: The HTML-formatted string with the text highlighted in blue.

    Examples:
        ```python
        highlight_match(match_text="example")
        # Expected output: "<span style='color:blue;font-weight:bold'>example</span>"
        ```

        ```python
        highlight_match(match_text="<script>alert('XSS')</script>")
        # Expected output: "<span style='color:blue;font-weight:bold'>&lt;script&gt;alert(&#x27;XSS&#x27;)&lt;/script&gt;</span>"
        ```
    """
    escaped_match: str = html.escape(match_text)
    return f"<span style='color:blue;font-weight:bold'>{escaped_match}</span>"


def highlight_matches(self, item: str, search_text: str) -> str:
    """Highlight matching text in blue using HTML.

    Args:
        item (str): The string in which to highlight matches.
        search_text (str): The string to search for and highlight.

    Returns:
        str: The HTML-formatted string with matches highlighted in blue.

    Examples:
        ```python
        highlight_matches(item="hello world", search_text="world")
        # Expected output: hello <span style='color:blue;font-weight:bold'>world</span>
        ```

        ```python
        highlight_matches(item="hello world world", search_text="world")
        # Expected output: hello <span style='color:blue;font-weight:bold'>world</span> <span style='color:blue;font-weight:bold'>world</span>
        ```
    Note:
        Return HTML fragment without <html> tags
    """
    if not search_text:
        return html.escape(item)

    # Iterate through the item to find and highlight matches
    result = ""
    pos = 0
    while True:
        idx = self.simple_match(item, search_text, pos)
        if idx == -1:
            result += html.escape(item[pos:])
            break
        result += html.escape(item[pos:idx])
        match_text = item[idx : idx + len(search_text)]
        result += self.highlight_match(match_text)
        pos = idx + len(search_text)
    return result


def highlight_for_terminal(s):
    # Using ANSI escape codes for terminal output; adapt for HTML if needed
    return "\033[1;34m" + s + "\033[0m"


def highligh_text_by_index_range(text, index_range_list, highlight_type="terminal"):
    """
    Highlights the given text by applying formatting to the index ranges specified in index_range_list.

    Args:
        text (str): The text to highlight.
        index_range_list (list of tuples): A list of tuples, where each tuple represents a range of indices to highlight (start, end).
        highlight_type (str, optional): "terminal" for terminal formatting, "html" for HTML formatting. Defaults to "terminal".

    Returns:
        str: The highlighted text.

    Examples:
        ```python
        text = "hello world"
        index_range_list = [(0, 5)]
        result = highligh_text_by_index_range(text, index_range_list)
        # result will be "\033[1;34mhello\033[0m world"
        ```

        ```python
        text = "hello world"
        index_range_list = [(0, 5), (6, 11)]
        result = highligh_text_by_index_range(text, index_range_list)
        # result will be "\033[1;34mhello\033[0m \033[1;34mworld\033[0m"
        ```
    """
    if not index_range_list:
        return text

    index_range_list = sorted(index_range_list)  # Sort ranges to process in order

    highlighted_text = ""
    last_index = 0

    for start, end in index_range_list:
        if start < last_index:
            start = last_index  # Adjust start if overlaps with previous range

        if start > len(text):
            break  # Stop if start is beyond the text length

        highlighted_text += text[last_index:start]
        if end > len(text):
            end = len(text)

        if highlight_type == "terminal":
            highlighted_text += highlight_for_terminal(text[start:end])
        elif highlight_type == "html":
            highlighted_text += highlight_match_using_html(text[start:end])
        else:
            raise ValueError("Invalid highlight_type. Choose 'terminal' or 'html'.")

        last_index = end

    highlighted_text += text[last_index:]  # Add the remaining text

    return highlighted_text


def remove_html_tags(text: str) -> str:
    """Remove html tags from a string.

    Args:
        text (str): The string to remove html tags from.

    Returns:
        str: The string without html tags.

    Examples:
        ```python
        text = "<p>hello world</p>"
        result = remove_html_tags(text)
        # result will be "hello world"
        ```

        ```python
        text = "hello <b>world</b>"
        result = remove_html_tags(text)
        # result will be "hello world"
        ```
    """
    clean = re2.compile("<.*?>")
    return re2.sub(clean, "", text)


def get_number_of_words(text: str) -> int:
    """Get number of words (separated by space) in a string.

    Args:
        text (str): The string to get number of words from.

    Returns:
        int: The number of words in the string.

    Examples:
        ```python
        text = "hello world"
        result = get_number_of_words(text)
        # result will be 2
        ```

        ```python
        text = "hello world  "
        result = get_number_of_words(text)
        # result will be 2
        ```

        ```python
        text = ""
        result = get_number_of_words(text)
        # result will be 0
        ```
    """
    if not text:
        return 0
    words = text.split()
    return len(words)


def get_number_of_words_by_index_range_list(text: str, index_range_list: list) -> int:
    """Get number of words (separated by space) in a string by index range list.

    Args:
        text (str): The string to get number of words from.
        index_range_list (list): The list of index ranges.

    Returns:
        int: The number of words in the string.

    Examples:
        ```python
        text = "hello world"
        index_range_list = [(0, 5)]
        result = get_number_of_words_by_index_range_list(text, index_range_list)
        # result will be 1
        ```

        ```python
        text = "hello world  abc"
        index_range_list = [(0, 5), (6, 11)]
        result = get_number_of_words_by_index_range_list(text, index_range_list)
        # result will be 2
        ```

        ```python
        text = "test test"
        index_range_list = [(0, 4)]
        result = get_number_of_words_by_index_range_list(text, index_range_list)
        # result will be 1
        ```

        ```python
        text = ""
        index_range_list = [(0, 5)]
        result = get_number_of_words_by_index_range_list(text, index_range_list)
        # result will be 0
        ```
    """
    count = 0
    if not text:
        return 0

    words = text.split()
    for start, end in index_range_list:
        for i, word in enumerate(words):
            word_start = text.find(word)
            word_end = word_start + len(word)

            current_index = 0
            for j in range(i):
                current_index += len(words[j]) + 1

            if current_index >= start and current_index + len(word) <= end:
                count += 1
    return count


def wrap_text_by_indices(
    text: str,
    start_index: int,
    end_index: int,
    start_wrap_text: str,
    end_wrap_text: str,
) -> str:
    """
    Wraps a substring within specified indices in the input text with given wrapper strings.

    Args:
        text (str): The original string to be modified.
        start_index (int): The starting index of the substring to wrap.
        end_index (int): The ending index of the substring to wrap.
        start_wrap_text (str): The string to prepend to the targeted substring.
        end_wrap_text (str): The string to append to the targeted substring.

    Note:
        Using end_index+1 since we assume indices are inclusive

    Examples:
    ```python
    wrap_text_by_indices(text="hello world", start_index=0, end_index=4, start_wrap_text="<b>", end_wrap_text="</b>")
    # Returns: "<b>hello</b> world"
    ```

    ```python
    wrap_text_by_indices(text="abcdefgh", start_index=2, end_index=5, start_wrap_text="[", end_wrap_text="]")
    # Returns: "ab[cd ef]gh"
    ```
    """
    return (
        text[:start_index]
        + start_wrap_text
        + text[start_index : end_index + 1]
        + end_wrap_text
        + text[end_index + 1 :]
    )


def main():
    result = find_best_match_index(text="hello world", query="world")
    # result will be 6
    print(result)

    result = find_best_match_index(text="hello world", query="orl")
    # result will be 7
    print(result)

    result = find_best_match_index(
        text="hello world", query="world", ignore_ranges=[(0, 5)]
    )
    # result will be 6
    print(result)

    result = find_best_match_index(
        text="world world", query="world", ignore_ranges=[(0, 5)]
    )
    # result will be 6
    print(result)

    result = find_best_match_index(text="world world", query="xx")
    # result will be -1
    print(result)


if __name__ == "__main__":
    main()
