{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "zip_folder",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "zip_folder",
            "type": "PluginDirZipper",
            "use": true,
            "folder_to_zip_path": "./tests/fake_test_script",
            "zip_name": "new.zip",
            "exclude_path_list": [
                './tests/fake_test_script/include/fake_test_script_exclude.py',
                './tests/fake_test_script/exclude',
            ],
            "include_only_path_list": [
                './tests/fake_test_script/include/',
            ],
            "save_to_folder_path": "./tests/zips",
        }
    ]
   

}
