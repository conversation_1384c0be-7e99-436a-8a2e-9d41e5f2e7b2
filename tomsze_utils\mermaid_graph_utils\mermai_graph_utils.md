```mermaid
flowchart TB
subgraph Jsszd["class Node"]
    %% doc string
    eLVZP["func"]
end
style Jsszd fill:#FFA500
subgraph MKxeG["class Subgraph"]
    %% doc string
    fvRJL["func"]
end
style MKxeG fill:#FFA500
subgraph Xwirt["class MM"]
    %% doc string
    igRyT["func"]
end
style Xwirt fill:#FFA500
subgraph hvZmS["class Fun"]
    %% doc string
    Dvmnj["func"]
end
style hvZmS fill:#FFA500
subgraph pbpQe["class MMCode"]
    %% 
    SmfCM["_create_coding_subgraph"]
    %% add class doc string
    NdCsI["add_class"]
    %% 
    MNlYj["aggregate_class"]
    %% 
    knyce["composite_class"]
    %% 
    jGPlx["inherit_class"]
end
style pbpQe fill:#FFA500
subgraph KeDkO["method AddClass"]
    %% 
    QzUaX["_create_coding_subgraph"]
    %% 
    wUFRW["create_subgraph_title_from_class"]
end
style KeDkO fill:#008000
subgraph xTTGe["script Main"]
    %% 
    hDPZi["main"]
end
style xTTGe fill:#FF0000
NdCsI --> KeDkO
Jsszd --composite--> MKxeG
MKxeG --composite--> Xwirt
pbpQe --inherit--> Xwirt
```
