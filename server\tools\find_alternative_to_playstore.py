import sys
import os
from tqdm import tqdm
import datetime
import shutil
from const_path import proj_ser_tools_path, proj_ser_api_db_apps_path
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
)
from tomsze_utils.scraping_utils import scrape_apkgk, scrape_apksos, scrape_apksupport
from server.api.end_to_end.api_constant_keys import DBAppsColEnum
from server.api.end_to_end.utils import ppd_emb_kw_kwpp_update_using_keys

from server.api.utils.descriptions.get_description import scrape_play_store
from sentence_transformers import SentenceTransformer

from const_model_settings import (
    max_ngram_size,
    deduplication_threshold,
    max_num_keywords,
    model_id,
)

"""
To rebuild db_apps
"""


def find_alternative_to_playstore(
    db_apps_folder_path,
):

    # Load the db_apps from folder
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        dump_db_fpath=db_apps_folder_path,
        load_by_thread=False,
    )

    # For each app_id in original db_apps.
    app_id_list = db_apps.get_db_keys()
    for app_id in tqdm(app_id_list):
        scrap_success, result = scrape_apksupport(app_id=app_id)

        if not scrap_success:
            # de.stocard.stocard 410 page removed
            g = 1
    # End of for loop.


def run_find_alternative_to_playstore():
    db_apps_folder_path = proj_ser_api_db_apps_path

    find_alternative_to_playstore(
        db_apps_folder_path=db_apps_folder_path,
    )


def main():
    # Run function
    run_find_alternative_to_playstore()


if __name__ == "__main__":
    sys.exit(main())
