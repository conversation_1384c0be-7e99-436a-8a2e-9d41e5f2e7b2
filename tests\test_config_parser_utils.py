import tempfile
import os
import pytest
from tomsze_utils.config_parser_utils import (
    create_config_parser_from_path,
    evaluate_text,
    load_json_config,
    load_toml_config_absolute_path,
    read_from_config_parser,
    set_and_write_parser_to_file,
)
from configparser import ConfigParser


class TestCreateConfigParserFromPath:

    def test_create_config_parser_from_path_creates_new_file(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.ini")
            parser = create_config_parser_from_path(config_path)

            assert os.path.exists(config_path)  # Check if the config file was created
            assert isinstance(
                parser, ConfigParser
            )  # Corrected to check parser type directly

    def test_create_config_parser_from_path_reads_existing_file(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.ini")

            # Create a config file with some initial data
            with open(config_path, "w") as configfile:
                configfile.write("[DEFAULT]\nkey=value\n")

            parser = create_config_parser_from_path(config_path)

            assert (
                parser["DEFAULT"]["key"] == "value"
            )  # Check if the value is read correctly


class TestSetAndWriteParserToFile:

    def test_creates_new_section(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.ini")
            parser = create_config_parser_from_path(config_path)

            result = set_and_write_parser_to_file(
                parser, config_path, "Settings", "theme", "dark"
            )

            assert result == "done"
            assert (
                parser["Settings"]["theme"] == "dark"
            )  # Accessing directly from parser
            assert os.path.exists(config_path)  # Check if the config file was created

    def test_updates_existing_key(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.ini")
            parser = create_config_parser_from_path(config_path)

            # Initially set a value
            set_and_write_parser_to_file(
                parser, config_path, "Settings", "theme", "light"
            )

            # Update the same key
            result = set_and_write_parser_to_file(
                parser, config_path, "Settings", "theme", "dark"
            )

            assert result == "done"
            assert (
                parser["Settings"]["theme"] == "dark"
            )  # Check if the value is updated


class TestReadFromConfigParser:

    def test_read_existing_key(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.ini")
            parser = create_config_parser_from_path(config_path)
            set_and_write_parser_to_file(
                parser, config_path, "Settings", "theme", "dark"
            )

            result = read_from_config_parser(parser, "Settings", "theme")
            assert result == "dark"  # Check if the value is read correctly

    def test_read_non_existing_key(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.ini")
            parser = create_config_parser_from_path(config_path)

            result = read_from_config_parser(
                parser,
                "Settings",
                "non_existing_key",
                default="default_value",
            )
            assert result == "default_value"  # Check if the default value is returned


class TestEvaluateText:

    def test_evaluate_text_integer(self):
        result = evaluate_text("123", is_string=False)
        assert result == 123  # Check if the string is converted to integer

    def test_evaluate_text_boolean(self):
        result_true = evaluate_text("true", is_string=False)
        assert result_true is True  # Check if "true" is converted to boolean True

        result_false = evaluate_text("false", is_string=False)
        assert result_false is False  # Check if "false" is converted to boolean False


class TestLoadJsonConfig:

    def test_load_valid_json(self):
        valid_json_content = '{"key": "value"}'
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.json")
            with open(config_path, "w") as json_file:
                json_file.write(valid_json_content)

            config = load_json_config(config_path)
            assert config == {"key": "value"}  # Check if the loaded config matches

    def test_load_either_json_or_json5(self):
        valid_json_content = '{"key": "value"}'
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.json")
            with open(config_path, "w") as json_file:
                json_file.write(valid_json_content)

            config_path_fake = os.path.join(temp_dir, "config.json5")
            config = load_json_config(config_path_fake)
            assert config == {"key": "value"}  # Check if the loaded config matches

    def test_load_non_existent_json(self):
        config_path = "non_existent_file.json"
        config = load_json_config(config_path)
        assert config is None  # Check if None is returned for non-existent file


class TestLoadTomlConfig:

    def test_load_valid_toml(self):
        valid_toml_content = "key = 'value'"
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "config.toml")
            with open(config_path, "w") as toml_file:
                toml_file.write(valid_toml_content)

            config = load_toml_config_absolute_path(config_path)
            assert config == {"key": "value"}  # Check if the loaded config matches

    def test_load_non_existent_toml(self):
        config_path = "non_existent_file.toml"
        config = load_toml_config_absolute_path(config_path)
        assert config is None  # Check if None is returned for non-existent file
