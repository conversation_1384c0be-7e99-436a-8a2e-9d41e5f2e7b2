package com.kewtoms.whatappsdo.data;


import java.util.List;

public class ScanAppData {
 private final List<String> packageNames;
 private final List<String> appNames;
 private final List<String> imagePaths;

 // Constructor
 public ScanAppData(
  List<String> packageNames,
  List<String> appNames,
  List<String> imagePaths) {
  this.packageNames = packageNames;
  this.appNames = appNames;
  this.imagePaths = imagePaths;
 }

 public List<String> getPackageNames() {
  return packageNames;
 }


 public List<String> getAppNames() {
  return appNames;
 }


 public List<String> getImagePaths() {
  return imagePaths;
 }
}
