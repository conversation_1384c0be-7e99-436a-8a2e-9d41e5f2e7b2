import pytest
import tempfile
import os
from tomsze_utils.import_utils import (
    get_callable_from_module_by_function_name,
    import_module_by_path,
)


class TestImportModuleByPath:

    def test_import_module_by_path_valid_module(self):
        # Create a temporary directory and a Python file within it
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = os.path.join(tmpdir, "temp_module.py")
            with open(tmp_path, "wb") as tmp:
                tmp.write(b"def add(a, b): return a + b")
            # Try to import the module
            error, module = import_module_by_path(tmp_path)
            # Check if the import was successful
            assert error == ""
            assert module is not None
            # Check if the module has the expected function
            assert hasattr(module, "add")
            assert callable(module.add)
            # Clean up is handled by the context manager

    def test_import_module_by_path_invalid_module(self):
        # Create a temporary directory and a Python file within it with invalid syntax
        with tempfile.TemporaryDirectory() as tmpdir:
            tmp_path = os.path.join(tmpdir, "temp_module.py")
            with open(tmp_path, "wb") as tmp:
                tmp.write(b"def add(a, b): return a +")
            # Try to import the module
            error, module = import_module_by_path(tmp_path)
            # Check if the import failed
            assert error != ""
            assert module is None
            # Clean up is handled by the context manager

    def test_import_module_by_path_non_existent_file(self):
        # Try to import a non-existent file
        error, module = import_module_by_path("non_existent_file.py")
        # Check if the import failed
        assert error != ""
        assert module is None

    def test_import_module_by_path_plugin_script(self):
        # Try to import plugin_sample.py
        script_path = r"./tomsze_utils/configurable_algorithm/pluginer_settings/plugins/plugin_sample.py"
        assert os.path.exists(script_path)
        error, module = import_module_by_path(script_path)
        # Check if the import failed
        assert error == ""
        assert module is not None

        # Try to import plugin_check_git_actions
        script_path = r"./tomsze_utils/configurable_algorithm/pluginer_settings/plugins/plugin_check_git_actions.py"
        assert os.path.exists(script_path)
        error, module = import_module_by_path(script_path)
        # Check if the import failed
        assert error == ""
        assert module is not None


class TestGetCallableFromModuleByFunctionName:

    def test_get_callable_from_module_by_function_name_valid(self):
        # Create a temporary directory and a Python file with a valid function
        with tempfile.TemporaryDirectory() as tmpdir:
            original_dir = os.getcwd()
            os.chdir(tmpdir)
            script_content = """
def multiply(x, y):
    return x * y
"""
            script_path = os.path.join(tmpdir, "temp_module.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Import the module
            error, module = import_module_by_path(script_path)
            assert error == ""
            assert module is not None

            # Get the callable function
            func = get_callable_from_module_by_function_name(module, "multiply")
            assert func is not None
            assert callable(func)
            assert func(2, 3) == 6  # Test the function's behavior

            os.chdir(original_dir)

    def test_get_callable_from_module_by_function_name_invalid(self):
        # Create a temporary directory and a Python file without the desired function
        with tempfile.TemporaryDirectory() as tmpdir:
            original_dir = os.getcwd()
            os.chdir(tmpdir)
            script_content = """
def divide(x, y):
    return x / y
"""
            script_path = os.path.join(tmpdir, "temp_module.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Import the module
            error, module = import_module_by_path(script_path)
            assert error == ""
            assert module is not None

            # Try to get a callable function that does not exist
            func = get_callable_from_module_by_function_name(module, "multiply")
            assert func is None  # The function should not exist

            os.chdir(original_dir)
