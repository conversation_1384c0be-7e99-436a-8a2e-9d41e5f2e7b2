import os
import unittest
import pickle
from tomsze_utils.encrypt_utils import decrypt_data
from tomsze_utils.variable_dump_read_utils import (
    load_pickle_file,
    dump_variable_to_pickle,
    dump_list_str_to_dict_pickle,
)
from cryptography.fernet import Fernet


class TestLoadPickleFile(unittest.TestCase):

    def test_load_pickle_file_success(self):
        # Arrange
        test_pickle_path = "test_pickle.pkl"
        test_obj = {"key": "value"}
        with open(test_pickle_path, "wb") as f:
            pickle.dump(test_obj, f)

        # Act
        result = load_pickle_file(test_pickle_path, "default")

        # Assert
        self.assertEqual(result, test_obj)

        if os.path.exists(test_pickle_path):
            os.remove(test_pickle_path)

    def test_load_pickle_file_failure(self):
        # Arrange
        test_pickle_path = "nonexistent_file.pkl"

        # Act
        result = load_pickle_file(test_pickle_path, "default")

        # Assert
        self.assertEqual(result, "default")


class TestDumpVariableToPickle(unittest.TestCase):

    def test_dump_variable_to_pickle_default(self):
        # Arrange
        test_var = {"a": 1}
        test_path = "./test.pkl"

        with open(test_path, "wb") as f:
            pickle.dump({}, f)

        # Act
        result = dump_variable_to_pickle(test_var, test_path)

        # Assert
        expect = load_pickle_file(test_path, "default")

        self.assertTrue(expect, test_var)

        if os.path.exists(test_path):
            os.remove(test_path)

    def test_dump_variable_to_pickle_not_exist(self):
        # Arrange
        test_var = {"a": 1}
        test_path = "./test.pkl"

        # Act
        result = dump_variable_to_pickle(test_var, test_path, True)

        # Assert
        self.assertTrue(result)

        if os.path.exists(test_path):
            os.remove(test_path)

    def test_dump_variable_to_pickle_with_encryption(self):
        """
        Test the dumping of a variable to a pickle file with encryption.

        This test verifies that a variable can be successfully dumped to a
        pickle file while being encrypted. It checks that the variable can
        be loaded back correctly after decryption.

        Steps:
        1. Arrange: Set up the test variable and encryption key.
        2. Act: Dump the variable to a pickle file with encryption.
        3. Assert: Verify the result of the dump operation and ensure
           the loaded variable matches the original after decryption.
        """
        # Arrange
        test_var = {"a": 1}
        test_path = "./test_encrypted.pkl"
        encryption_key = Fernet.generate_key()  # Generate a key for encryption

        # Act
        result = dump_variable_to_pickle(
            test_var,
            test_path,
            encrypt_variable=True,
            encryption_key=encryption_key,
        )

        # Assert
        self.assertTrue(result)

        # Load the encrypted data and decrypt it
        with open(test_path, "rb") as f:
            encrypted_data = f.read()
        decrypted_data = decrypt_data(encrypted_data, encryption_key)
        loaded_var = pickle.loads(decrypted_data)

        self.assertEqual(loaded_var, test_var)

        # Clean up
        if os.path.exists(test_path):
            os.remove(test_path)


def test_dump_list_str_to_dict_pickle():
    """
    Test the dumping of a list of strings into a dictionary and saving it to a pickle file.

    This test verifies that a list of strings can be successfully added as keys to a dictionary
    with empty values and saved to a pickle file. It checks that the resulting pickle file contains
    the expected dictionary structure.

    Steps:
    1. Arrange: Set up the test list of strings and the expected dictionary.
    2. Act: Call the dump_list_str_to_dict_pickle function.
    3. Assert: Verify that the pickle file contains the expected dictionary.
    """
    # Arrange
    test_list = ["key1", "key2", "key3"]
    expected_dict = {"key1": "", "key2": "", "key3": ""}
    test_pickle_path = "./test_dict.pkl"

    # Act
    dump_list_str_to_dict_pickle(test_list, test_pickle_path, dict_var={})

    # Assert
    with open(test_pickle_path, "rb") as f:
        loaded_dict = pickle.load(f)

    assert loaded_dict == expected_dict

    # Clean up
    if os.path.exists(test_pickle_path):
        os.remove(test_pickle_path)


if __name__ == "__main__":
    unittest.main()
