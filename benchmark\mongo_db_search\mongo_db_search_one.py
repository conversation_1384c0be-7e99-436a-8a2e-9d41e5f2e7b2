# https://www.mongodb.com/docs/atlas/tutorial/insert-data-into-your-cluster/#insert-and-view-data
import time
from pprint import pprint
from pymongo import MongoClient

# Connect
uri = "mongodb://localhost:27017/"  # can be found from Mongo Compass (a software with gui).
client = MongoClient(uri)

# Get collection
collection = client["test_database"]["coll1"]

# find documents
time_start = time.time()
results = collection.find_one(
    {
        "app_id": "com.mcdonalds.app",
    }
)
time_end = time.time()
print("time used:" + "{:.2f}".format((time_end - time_start) * 1000) + " ms")

# 7 - 45ms (release / debug mode)
