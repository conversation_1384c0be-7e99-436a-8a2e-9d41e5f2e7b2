"""App plugin"""

from dataclasses import dataclass
import os
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)
from tomsze_utils.config_parser_utils import (
    load_json_config,
)


@dataclass
class PluginJsonFileReader:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        json_file_path = parse_data_and_store(
            logger,
            "json_file_path",
            data_obj,
            step_config,
            config,
            type="str",
            default="",
        )

        if not use:
            return True

        """code start"""
        if not os.path.exists(json_file_path):
            logger.error(f"File {json_file_path} does not exist")
            return True

        file_content_dict = load_json_config(json_file_path)
        data_obj.dict_var[f"{current_step}.file_content"] = file_content_dict

        for key, value in file_content_dict.items():
            data_obj.dict_var[f"{current_step}.{key}"] = value
        """code end"""

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
