import string
import pytest
import tempfile
from tomsze_utils.randome_utils import (
    generate_date_string_dict,
    generate_random_integer_string,
    generate_random_string,
    generate_random_string_list,
)


def test_generate_random_string_length():
    length = 10
    result = generate_random_string(length)
    assert len(result) == length, f"Expected length {length}, but got {len(result)}"


def test_generate_random_string_list_length():
    string_length = 10
    count = 5
    result = generate_random_string_list(string_length, count)
    assert len(result) == count, f"Expected {count} strings, but got {len(result)}"
    for random_string in result:
        assert (
            len(random_string) == string_length
        ), f"String '{random_string}' does not have the expected length of {string_length}"


def test_generate_random_string_list_empty():
    string_length = 10
    count = 0
    result = generate_random_string_list(string_length, count)
    assert result == [], "Expected an empty list when count is 0"


def test_generate_date_string_dict_default():
    """Test the default behavior of generate_date_string_dict."""
    result = generate_date_string_dict()
    assert len(result) == 12 * 30  # 12 months * 30 days
    for key in result.keys():
        assert len(result[key]) == 2  # Default is 2 random strings per date
        assert all(
            isinstance(s, str) and len(s) == 10 for s in result[key]
        )  # Each string should be of length 10


def test_generate_date_string_dict_custom():
    """Test generate_date_string_dict with custom parameters."""
    num_strings = 3
    string_length = 12
    result = generate_date_string_dict(
        num_strings=num_strings, string_length=string_length
    )
    assert len(result) == 12 * 30  # 12 months * 30 days
    for key in result.keys():
        assert (
            len(result[key]) == num_strings
        )  # Should return 3 random strings per date
        assert all(
            isinstance(s, str) and len(s) == string_length for s in result[key]
        )  # Each string should be of length 12


class TestGenerateRandomIntegerString:
    def test_generate_random_integer_string_length(self):
        length = 7
        result = generate_random_integer_string(length)
        assert len(result) == length, f"Expected length {length}, but got {len(result)}"
        assert result.isdigit(), "Expected the string to contain only digits"

    def test_generate_random_integer_string_empty(self):
        length = 0
        result = generate_random_integer_string(length)
        assert len(result) == length, f"Expected length {length}, but got {len(result)}"
        assert result == "", "Expected an empty string"
