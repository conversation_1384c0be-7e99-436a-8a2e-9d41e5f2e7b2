from dataclasses import dataclass
import os
from tomsze_utils.mermaid_graph_utils.mermaid_graph_utils import <PERSON><PERSON><PERSON>, Fun

output_folder = os.path.dirname(os.path.abspath(__file__))
mmc = MMCode(graph_title="mermai_graph_utils", output_folder=output_folder)


@dataclass
class ClassNode:
    func = Fun("doc string")


@dataclass
class ClassSubgraph:
    func = Fun("doc string")


@dataclass
class ClassMM:
    func = Fun("doc string")


@dataclass
class ClassFun:
    func = Fun("doc string")


@dataclass
class ClassMMCode:
    _create_coding_subgraph = Fun()
    add_class = Fun("add class doc string")
    inherit_class = Fun()
    composite_class = Fun()
    aggregate_class = Fun()


@dataclass
class MethodAddClass:
    _create_coding_subgraph = Fun()
    create_subgraph_title_from_class = Fun()


@dataclass
class ScriptMain:
    main = Fun()


mmc.add_class(subgraph_class=ClassNode)
mmc.add_class(subgraph_class=ClassSubgraph)
mmc.add_class(subgraph_class=ClassMM)
mmc.add_class(subgraph_class=ClassFun)
mmc.add_class(subgraph_class=ClassMMCode)
mmc.add_class(subgraph_class=MethodAddClass)
mmc.add_class(subgraph_class=ScriptMain)


mmc.point_class_method_to_impl(
    ClassMMCode.add_class,
    MethodAddClass,
)


mmc.composite_class(
    child_class=ClassNode,
    parent_class=ClassSubgraph,
)

mmc.composite_class(
    child_class=ClassSubgraph,
    parent_class=ClassMM,
)


mmc.inherit_class(
    child_class=ClassMMCode,
    parent_class=ClassMM,
)


mmc.write_graph()
