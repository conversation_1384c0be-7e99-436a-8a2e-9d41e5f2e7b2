import pytest
from tomsze_utils.datetime_utils import get_current_system_datetime
import tempfile
import os


def test_get_current_system_datetime_default_format():
    result = get_current_system_datetime()
    assert isinstance(result, str)  # Check if the result is a string
    assert len(result) == 19  # Check if the length matches 'YYYY-MM-DD HH:MM:SS'


def test_get_current_system_datetime_custom_format():
    custom_format = "%d-%m-%Y %H:%M"
    result = get_current_system_datetime(format=custom_format)
    assert isinstance(result, str)  # Check if the result is a string
    assert len(result) == 16  # Check if the length matches 'DD-MM-YYYY HH:MM'


@pytest.fixture
def datetime_string():
    return "2024-01-01 12:00:00"


@pytest.fixture
def datetime_string_custom():
    return "01/01/2024 12:00"


class TestExtractDatetimeComponents:
    def test_extract_datetime_components_valid(self, datetime_string):
        from tomsze_utils.datetime_utils import extract_datetime_components
        from tomsze_utils.datetime_utils import DateTimeComponents

        result = extract_datetime_components(datetime_string)
        assert result is not None
        assert isinstance(result, DateTimeComponents)
        assert result.year == 2024
        assert result.month == 1
        assert result.day == 1
        assert result.hour == 12
        assert result.minute == 0
        assert result.second == 0

    def test_extract_datetime_components_invalid(self):
        from tomsze_utils.datetime_utils import extract_datetime_components

        result = extract_datetime_components("invalid datetime string")
        assert result is None

    def test_extract_datetime_components_custom_format(self, datetime_string_custom):
        from tomsze_utils.datetime_utils import extract_datetime_components
        from tomsze_utils.datetime_utils import DateTimeComponents

        result = extract_datetime_components(
            datetime_string_custom, format="%d/%m/%Y %H:%M"
        )
        assert result is not None
        assert isinstance(result, DateTimeComponents)
        assert result.year == 2024
        assert result.month == 1
        assert result.day == 1
        assert result.hour == 12
        assert result.minute == 0
        assert result.second == 0
