from dataclasses import dataclass
from typing import Any, Dict
from tomsze_utils.variable_dump_read_utils import dump_variable_to_pickle


@dataclass
class CacheResult:
    """
    ```
    use_cache: bool
    obtained_value: Any
    cached_value: Any | Dict
    ```
    """

    use_cache: bool
    obtained_value: Any
    cached_value: Any | Dict


def cache_or_func(
    pickle_path: str,
    pickle_data: Dict,
    data_key: str,
    func_to_value: callable,
    func_kwargs: Dict,
) -> CacheResult:
    """
    Summary:
    --------
    A function that get a data from cache using a key or
    runs a function to get a data.

    If data_key not in picke_data,
    run func_to_value and store the value to picke_data,
    then dump the picke_data.

    If data_key is in picke_data,
    get value from pickle_data.

    Parameters:
    -----------
    pickle_path: str
        used for dumping.

    Returns:
    --------
    CacheResult.
    """

    if data_key not in pickle_data.keys():
        value = func_to_value(**func_kwargs)
        pickle_data[data_key] = value
        dump_variable_to_pickle(
            pickle_data,
            pickle_path,
        )
        return CacheResult(False, value, pickle_data)

    value = pickle_data[data_key]
    return CacheResult(True, value, pickle_data)


# Note: change Dict in CacheResult will change picke_data!
