{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "assign_variable_1",
            "print_var",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "assign_variable_1",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1": "some texts",
        },
        {
            "step_name": "print_var",
            "type": "PluginTextPrinter",
            "use": true,
            "print_text": "{assign_variable_1.var1}",
        }
    ]
   

}
