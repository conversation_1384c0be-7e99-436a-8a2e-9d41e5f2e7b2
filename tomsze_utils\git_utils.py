import os
import re
import tempfile
from typing import List, Tuple
from tomsze_utils.subprocess_utils import subprocess_run, subprocess_run_with_str_output
import logging

logger = logging.getLogger(__name__)


def git_commit_using_subprocess(
    commit_type: str,
    commit_message: str,
    cwd: str = "./",
) -> str:
    """
    Commits changes to the git repository with a specified commit type and message.

    This function formats the commit message and executes the git commit command.

    Args:
        commit_type (str): The type of the commit (e.g., "feat", "fix").
        commit_message (str): The message describing the changes.

    Returns:
        str: The output from the git commit command.
        str: The command that was executed.

    Examples:
        ```python
        output_str, command_str = git_commit("feat", "Add new feature")
        print(output_str)
        ```

        ```python
        output_str, command_str = git_commit("fix", "Fix bug in feature")
        print(output_str)
        ```
    """
    if commit_message and commit_message[-1] == "\n":
        commit_message_mod = commit_message[:-1]
    else:
        commit_message_mod = commit_message

    commit_message_mod = commit_message_mod.replace("\n\n", "\n")
    commit_message_mod = commit_message_mod.replace("\n", '" -m "')

    # Commit.
    command_str = f'git commit -m "{commit_type}: {commit_message_mod}"'
    logger.info(f"git command ran: {command_str}")
    out, output_str = subprocess_run_with_str_output(
        command_str,
        shell=False,
        cwd=cwd,
    )
    return output_str, command_str


def find_git_action_by_changed_files(
    changed_file_list: List[str],
    monitoring_directory_path: str,
) -> tuple:
    """
    Determines the git action based on the changed files and the monitoring directory.

    This function checks the HEAD log and the index file to identify the most recent git action
    (commit, reset, checkout) and any associated messages.

    Args:
        changed_file_list (List[str]): A list of changed files to check for git actions.
        monitoring_directory_path (str): The path to the directory being monitored for changes.

    Returns:
        tuple: A tuple containing the current git action, git message, previous git action, and previous git message.

    Examples:
        ```python
        action, message, prev_action, prev_message = find_git_action_by_changed_files(
            changed_file_list=["/path/to/repo/.git/logs/HEAD", "/path/to/repo/.git/index"],
            monitoring_directory_path="/path/to/repo"
        )
        ```

        ```python
        action, message, prev_action, prev_message = find_git_action_by_changed_files(
            changed_file_list=["/path/to/repo/.git/logs/HEAD"],
            monitoring_directory_path="/path/to/repo"
        )
        ```
    """
    git_action = None
    git_message = None
    previous_git_action = None
    previous_git_message = None

    logger.info("----------find_git_action----------")
    for changed_file in changed_file_list:
        # Check if the changed file is the HEAD log
        if re.match(f".*/logs/HEAD", changed_file):

            # Open the changed file and read its lines to determine the latest git action
            with open(changed_file, "r") as f:
                list_lines = f.readlines()
                logger.info(list_lines[-1])

            # Get current git action.
            if list_lines:
                current_line = list_lines[-1]
                git_message = current_line.split("+0800\t")[-1]
                git_message = git_message[:-1]  # To remove last \n

                # Get previous git action and message
                if len(list_lines) > 1:
                    previous_line = list_lines[-2]
                    previous_git_message = previous_line.split("+0800\t")[-1].strip()
                    if "+0800\tcommit" in previous_line:
                        previous_git_action = "commit"
                    elif "+0800\treset" in previous_line:
                        previous_git_action = "reset"
                    elif "+0800\tcheckout" in previous_line:
                        previous_git_action = "checkout"

                # Determine the type of git action based on the log line
                if "+0800\tcommit" in current_line:
                    git_action = "commit"
                    break
                if "+0800\treset" in current_line:
                    git_action = "reset"
                    break
                if "+0800\tcheckout" in current_line:
                    git_action = "checkout"
                    break

    # Deal with the case for stage or unstage when not git_action is not determined
    if not git_action:
        for changed_file in changed_file_list:
            # Look for changes in the index file
            if re.match(f".*/index$", changed_file):
                # Change the working directory to the monitoring path
                os.chdir(monitoring_directory_path)
                # Run the git diff command to check staged changes
                out, output_str = subprocess_run_with_str_output(
                    command=f"git diff --staged"
                )

                # If there is output from the command, set the git action and message
                if output_str:
                    git_action = "stage/unstage"
                    git_message = output_str

                break

    logger.info(f"git action: {git_action}, previous git action: {previous_git_action}")
    logger.info(
        f"git message: \n{git_message}, previous git message: \n{previous_git_message}"
    )
    logger.info("----------End find_git_action----------")
    return git_action, git_message, previous_git_action, previous_git_message


def git_create_branch_using_subprocess(
    branch_name: str,
    cwd: str = None,
) -> Tuple[str, str]:
    """
    Create a new git branch using a subprocess call.

    This function checks if the master or main branch exists before creating a new branch.

    Args:
        branch_name (str): The name of the branch to create.
        cwd (str, optional): The directory in which to run the git command. Defaults to None.

    Returns:
        Tuple[str, str]: A tuple containing the output message and the command string used.

    Examples:
        ```python
        output, command = git_create_branch_using_subprocess("feature_branch", cwd="/path/to/repo")
        ```

        ```python
        output, command = git_create_branch_using_subprocess("bugfix_branch")
        ```
    """
    # Check if the master branch exists
    master_branch_check_command = "git branch --list main"
    _, master_branch_output = subprocess_run_with_str_output(
        master_branch_check_command,
        cwd=cwd,
        shell=False,
    )

    master_branch_check_command = "git branch --list master"
    _, master_branch_output2 = subprocess_run_with_str_output(
        master_branch_check_command,
        cwd=cwd,
        shell=False,
    )

    if not master_branch_output.strip() and not master_branch_output2.strip():
        output_str = "The master/main branch does not exist."
        logger.warning(output_str)
        return output_str, master_branch_check_command

    command_str = f"git branch {branch_name}"
    out, output_str = subprocess_run_with_str_output(
        command_str,
        cwd=cwd,
        shell=False,
    )
    output_str = f"Created branch '{branch_name}' with command: {command_str}"
    logger.info(output_str)
    return output_str, command_str


import sys


def main():
    original_dir = os.getcwd()
    with tempfile.TemporaryDirectory() as tmpdir:
        # Setup a temporary git repository
        os.chdir(tmpdir)
        output_str = subprocess_run_with_str_output("git init")

        with open("test_file.txt", "w") as f:
            f.write("Test content")

        output_str = subprocess_run_with_str_output("git add test_file.txt")

        # Test the git_commit function
        output_str, command_str = git_commit_using_subprocess("feat", "Add test file")
        assert "feat: Add test file" in output_str
        logger.info(f"Executed command: {command_str}")

        os.chdir(original_dir)


if __name__ == "__main__":
    sys.exit(main())
