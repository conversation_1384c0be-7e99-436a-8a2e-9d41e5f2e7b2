from googletrans import Translator


def check_is_eng(translator: Translator, text):
    """Checks if the given text is English using the Google Translate API.

    Args:
      translator: googletrans.Translator instance
      text: Text to check if it is English

    Returns:
      success: Bool indicating if the API request succeeded
      is_eng: Bool indicating if text is detected as English
    """

    success = False
    is_eng = False
    result = translator.detect(text)

    if result._response.status_code == 200:
        success = True
        if result.lang == "en":
            is_eng = True

    return success, is_eng


def translate_to_eng(translator: Translator, text):
    success = False
    translated_text = None

    translated = translator.translate(text)

    if translated._response.status_code == 200:
        success = True
        translated_text = translated.text

    return success, translated_text


if __name__ == "__main__":
    translator = Translator()
    success, lang = check_is_eng(translator, "hi")
    success, lang = check_is_eng(translator, "hi")

    success, translated = translate_to_eng(translator, "你好")
    success, translated = translate_to_eng(translator, "外卖")
    success, translated = translate_to_eng(translator, "外賣")
