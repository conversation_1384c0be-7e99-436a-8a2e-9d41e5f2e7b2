import datetime
import shutil
import sys
import os
from const_path import proj_ser_tools_path, proj_ser_api_db_apps_path
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
    create_db_info,
)
from server.api.end_to_end.api_constant_keys import DBAppsColEnum
from server.api.end_to_end.utils import ppd_emb_kw_kwpp_update_using_keys

from server.api.utils.descriptions.get_description import scrape_play_store
from sentence_transformers import SentenceTransformer

"""
To fill in new apps (from a txt) to db_apps
"""
max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20


def fix_database_loaded_part_is_list(
    db_apps_folder_path,
    db_apps_output_folder_path,
    db_apps_backup_zip_path,
    model_id: str = "multi-qa-MiniLM-L6-cos-v1",
):
    embedder = SentenceTransformer(model_id)

    # Load the db_apps from folder
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="apps",
        dump_db_fpath=db_apps_output_folder_path,
        load_by_thread=False,
    )

    if not isinstance(db_apps.loaded_parts[0], list):
        print("This database has no problem with each loaded part is list")
        return
    else:
        print("This database DO has problem with each loaded part is list!!!")
        # Backup the db_apps folder first.
        shutil.make_archive(db_apps_backup_zip_path, "zip", db_apps_folder_path)

    copy_loaded_parts = db_apps.loaded_parts
    db_apps.loaded_parts = []
    for loaded_part_list in copy_loaded_parts:
        for each_dict in loaded_part_list:
            for app_id in each_dict.keys():
                for key in each_dict[app_id].keys():
                    db_apps.update_data_with_keys(
                        keys=[app_id, key],
                        data=each_dict[app_id][key],
                    )

                    g = 1

    # Clear all old pickles.
    db_apps.remove_pickles(db_apps_folder_path)

    # Dump db_apps (all parts) to folder.
    db_apps.dump_all_parts_to_pickles()


def run_fix_database_loaded_part_is_list():
    db_apps_folder_path = proj_ser_api_db_apps_path
    db_apps_output_folder_path = proj_ser_api_db_apps_path

    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    db_apps_backup_zip_path = os.path.join(
        proj_ser_tools_path,
        "run_fix_database_loaded_part_is_list_backup_zip_folder",
        f"db_apps_{time_str}",
    )

    fix_database_loaded_part_is_list(
        db_apps_folder_path=db_apps_folder_path,
        db_apps_backup_zip_path=db_apps_backup_zip_path,
        db_apps_output_folder_path=db_apps_output_folder_path,
    )


def main():
    # Run function
    run_fix_database_loaded_part_is_list()


if __name__ == "__main__":
    sys.exit(main())
