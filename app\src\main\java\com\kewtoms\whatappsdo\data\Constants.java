package com.kewtoms.whatappsdo.data;

import android.util.Log;

public class Constants {
  private static Constants instance;
  private boolean isInitialized = false;

  public static final String TAG = "APP:Constants";

  // true: click SEARCH button will not actually send request to server
  public static final String test_email_address =
    ConstantsPrivate.private_test_email_address;
  public static final String test_password = "passwordABC0!";
  public static final String test_confirm_password = "passwordABC0!";
  public static final String test_verification_code = "123456789";
  public static final String scrape_source_apkgk = "apkgk";
  public static final String scrape_source_apksos = "apksos";
  public static final String scrape_source_apksupport = "apksupport";
  public static final String scrape_source_playstore = "playstore";
  public static final Integer FREE_USAGE_COUNT_MONTHLY = 100;
  public static final String BASE_URL = ConstantsPrivate.getBaseUrl();
  public static final String get_is_server_online_link =
    BASE_URL + "/__is_server_online";
  public static final String post_req_url_request_datetime_link =
    BASE_URL + "/__request_datetime";
  public static final String post_req_url_search_user_app_link =
    BASE_URL + "/__search_user_app";
  public static final String post_req_url_check_client_app_link =
    BASE_URL + "/__check_client_apps";
  public static final String post_req_url_usa_link =
    BASE_URL + "/usa";
  public static final String post_req_url_sign_in_link =
    BASE_URL + "/__sign_in";
  public static final String post_req_url_sign_up_link =
    BASE_URL + "/__sign_up";
  public static final String post_req_url_sign_in_access_token_link =
    BASE_URL + "/__sign_in_access_token";
  public static final String post_req_url_remove_user_link =
    BASE_URL + "/__remove_user";
  public static final String post_req_url_is_user_signed_up_link =
    BASE_URL + "/__is_user_signed_up";
  public static final String
    post_req_url_validate_user_access_token_link =
    BASE_URL + "/__validate_user_access_token";
  // create a link for obtaining new access token
  public static final String
    post_req_url_obtain_new_access_token_by_refresh_token_link =
    BASE_URL + "/__obtain_new_access_token_by_refresh_token";
  public static final String
    post_req_url_obtain_verification_code_link =
    BASE_URL + "/__obtain_verification_code";

  // Request related key constants
  public static final String response_json_error_key = "error";
  public static final String response_json_result_key = "Result";
  public static final String request_encrypted_json_key = "data_str";
  public static final String response_json_apiFunResult_key =
    "ApiFunResult";

  // Mock test related constants
  public static final String mockSignInUrlKey = "mock_sign_in_url";
  public static final String mock_post_req_url_sign_in_link =
    "/__mock_sign_in";
  public static final String mockIsEncryptDecryptKey =
    "is_encrypt_decrypt";
  public static final String mockDeploymentModeKey =
    "mock_deployment_mode";
  public static final String mockEnvModeKey = "mock_env_mode";
  public static final String mockSignInAccessTokenUrlKey =
    "mock_sign_in_access_token_url";
  public static final String
    mock_post_req_url_sign_in_access_token_link =
    "/__mock_sign_in_access_token";
  public static final String mockValidateAccessTokenUrlKey =
    "mock_validate_access_token_url";
  public static final String
    mock_post_req_url_validate_access_token_link =
    "/__mock_validate_user_access_token";
  public static final String mockGetIsServerOnlineUrlKey =
    "mock_get_is_server_online_url";
  public static final String
    mock_post_req_url_get_is_server_online_link =
    "/__mock_get_is_server_online";
  public static final String mockIsServerOnlineKey =
    "mock_is_server_online";
  public static final String mockObtainNewAccessTokenUrlKey =
    "mock_obtain_new_access_token_url";
  public static final String
    mock_post_req_url_obtain_new_access_token_link =
    "/__mock_obtain_new_access_token_by_refresh_token";
  public static final String mockSearchUserAppUrlKey =
    "mock_search_user_app_url";
  public static final String mock_post_req_url_search_user_app_link =
    "/__mock_search_user_app";

  public static synchronized Constants getInstance() {
    if (instance == null) {
      instance = new Constants();
    }
    return instance;
  }

  public void initializeData(
    Configuration.DeploymentMode deploymentMode,
    Configuration.EnvironmentMode environmentMode) {

    String methodName = "initializeData: ";
    Log.d(
      TAG,
      methodName + "run"
    );

    if (!isInitialized) {
      // Perform one-time initialization
      isInitialized = true;

      ConstantsPrivate.setModes(
        deploymentMode,
        environmentMode
      );
    }

    Log.d(
      TAG,
      methodName + "done"
    );
  }

}
