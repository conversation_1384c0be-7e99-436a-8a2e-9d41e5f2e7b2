{"general": {"init_steps": [], "steps": ["assign_variable_image_path", "read_image_1"], "variables": {"var_online": false}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "log_to_file": true, "folder_path": "./tests/tmp_CA_logs", "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_variable_assigner": [{"step_name": "assign_variable_image_path", "type": "PluginVariableAssigner", "use": true, "image_path": "./tests/CA_configs/test_image_reader_1.png"}], "all_image_reader": [{"step_name": "read_image_1", "type": "PluginImageReader", "use": true, "use_image_path": true, "image_path": "{image_path}", "loop_image": false, "use_image_folder_path": false, "image_folder_path": "", "loop_folder": false, "image_types": ["jpg", "JPG", "png"], "buffer_to_which": "image"}]}