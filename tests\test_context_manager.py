import os
from tomsze_utils.context_manager_utils import change_dir


def test_change_dir():
    original_cwd = os.getcwd()

    tmp_cwd_path = "./tests"
    tmp_cwd_path = os.path.realpath(tmp_cwd_path)
    with change_dir(tmp_cwd_path):
        # Current working directory is now /path/to/directory
        cwd = os.getcwd()
        assert cwd == tmp_cwd_path

    cwd = os.getcwd()
    assert original_cwd == cwd
