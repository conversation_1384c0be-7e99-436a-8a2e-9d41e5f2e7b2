from dataclasses import dataclass
from typing import Dict, List

@dataclass
class VerifiedKeywordResult:
    new_keywords: Dict
    is_new: bool
    
    
def verify_client_app_keyword(
    client_app_keywords: List,
    server_app_keywords: Dict,
) -> VerifiedKeywordResult:
    
    print('verify_client_app_keyword')
    
    new_keywords = {}
    is_new = False
    for keyword in client_app_keywords:
        if keyword not in server_app_keywords:
            new_keywords[keyword] = 1;
            is_new = True
    
    return VerifiedKeywordResult(new_keywords, is_new)