from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from const_path import proj_ser_api_db_rate_limit_path
from server.api.end_to_end.utils import add_new_ip_to_db_rate_limit_if_not_exist


def main():
    part_data_len = 2
    db_name = "db_rate_limit"
    n_data = 40  # num pickles 20

    db = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_rate_limit_path,  # <<<
        db_name=db_name,
        part_data_count=part_data_len,
    )

    # Adding a new IP to the rate limit database if it does not already exist
    add_new_ip_to_db_rate_limit_if_not_exist(
        ip="ip_xyz",
        db_rate_limit=db,
    )

    db.dump_all_parts_to_pickles()


if __name__ == "__main__":
    main()
