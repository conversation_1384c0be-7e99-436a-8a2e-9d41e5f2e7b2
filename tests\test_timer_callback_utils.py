import pytest
import time
from tomsze_utils.timer_callback_utils import CountD<PERSON>Caller


def test_count_down_caller_start_and_cancel():
    # <PERSON>rrange
    def on_time_up() -> None:
        """
        Callback function to be called when the countdown timer reaches zero.

        This function is expected to be used as a callback for the CountDownCaller.
        If called, it indicates that the timer has not been canceled as expected.

        Args:
            None

        Raises:
            AssertionError: If this function is called, indicating a failure in the timer logic.

        Examples:
        ```python
        countdown_timer = CountDownCaller(1, on_time_up)
        countdown_timer.start()  # Starts the timer
        ```

        ```python
        countdown_timer = CountDownCaller(2, on_time_up)
        countdown_timer.start()  # Starts the timer for 2 seconds
        ```
        """
        assert False, "This should not be called"

    countdown_timer = CountDownCaller(1, on_time_up)

    # Act
    countdown_timer.start()
    time.sleep(0.5)
    countdown_timer.cancel()

    # Assert
    # Wait until we can confirm it's not alive
    for _ in range(5):  # Retry a few times
        if not countdown_timer.timer.is_alive():
            break
        time.sleep(0.1)  # Short wait before checking again

    # Assert
    assert (
        not countdown_timer.timer.is_alive()
    ), "Timer should be canceled but is still alive"


def test_count_down_caller_restart():
    # Arrange
    call_count = 0

    def on_time_up():
        nonlocal call_count
        call_count += 1

    countdown_timer = CountDownCaller(1, on_time_up)

    # Act
    countdown_timer.start()
    time.sleep(0.5)
    countdown_timer.restart()
    time.sleep(1.5)

    # Assert
    assert call_count == 1


def test_count_down_caller_run_to_completion():
    # Arrange
    call_count = 0

    def on_time_up():
        nonlocal call_count
        call_count += 1

    countdown_timer = CountDownCaller(1, on_time_up)

    # Act
    countdown_timer.start()
    time.sleep(1.5)

    # Assert
    assert call_count == 1
