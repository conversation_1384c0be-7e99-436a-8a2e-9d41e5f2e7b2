package com.kewtoms.whatappsdo.utils;

import android.content.Context;
import android.util.Log;

import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;

import com.kewtoms.whatappsdo.data.generated.user_database_constants.ResponseKeys;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.security.GeneralSecurityException;

public class SecurePrefsManager {
  public static final String TAG = "APP:SecurePrefsManager";
  private static final String PREFS_NAME = "secure_prefs";
  private static final String ACCESS_TOKEN_KEY = "ACCESS_TOKEN";
  private static final String REFRESH_TOKEN_KEY = "REFRESH_TOKEN";
  private static final String ACCOUNT_EMAIL_KEY = "ACCOUNT_EMAIL";
  private static final String HAS_LOGGED_IN_KEY =
    "HAS_SUCCESS_LOGGED_IN";
  private static final String SEARCH_TIMES_KEY = "SEARCH_TIMES";
  private static final String USER_TYPE_KEY = "USER_TYPE";
  private static final String LOGIN_TIME_KEY = "LOGIN_TIME";

  /**
   * Retrieves an instance of {@link EncryptedSharedPreferences} for
   * secure data storage.
   * <p>
   * This method initializes and returns an
   * {@link EncryptedSharedPreferences} object, which provides a
   * secure way to store sensitive data using encryption. It utilizes
   * the Android Jetpack Security library to encrypt the shared
   * preferences.
   *
   * @param context (Context): The Android {@link Context} used to
   * access application resources.
   * @return EncryptedSharedPreferences: An instance of
   * {@link EncryptedSharedPreferences} if successful, `null`
   * otherwise.
   * @throws GeneralSecurityException if there's an issue with the
   *                                  security provider.
   * @throws IOException              if there's an issue with file
   *                                  I/O during initialization.
   * @example Usage:
   * <pre>{@code
   * import android.content.Context;
   * import androidx.security.crypto.EncryptedSharedPreferences;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * public class ExampleClass {
   *     public void exampleMethod(Context context) {
   *         // Retrieve the EncryptedSharedPreferences instance
   *         EncryptedSharedPreferences prefs = SecurePrefsManager.getSharedPreferences(context);
   *
   *         if (prefs != null) {
   *             // Use the 'prefs' object to store and retrieve data
   *             prefs.edit().putString("user_id", "someUserId").apply();
   *         } else {
   *             // Handle the error case where initialization failed
   *             // Potentially retry or inform the user
   *         }
   *     }
   * }
   * }</pre>
   * @example Error Handling:
   * <pre>{@code
   * import android.content.Context;
   * import androidx.security.crypto.EncryptedSharedPreferences;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * public class ExampleClass {
   *     public void exampleMethod(Context context) {
   *         // Attempt to retrieve the EncryptedSharedPreferences instance
   *         EncryptedSharedPreferences prefs = SecurePrefsManager.getSharedPreferences(context);
   *
   *         if (prefs == null) {
   *             // Initialization failed, handle the error appropriately
   *             // Log the error, display a message to the user, or retry
   *             System.err.println("Failed to initialize EncryptedSharedPreferences");
   *         } else {
   *             // Proceed with using the 'prefs' object
   *             String userId = prefs.getString("user_id", null);
   *             System.out.println("User ID: " + userId);
   *         }
   *     }
   * }
   * }</pre>
   */
  private static EncryptedSharedPreferences getSharedPreferences(Context context) {
    String methodName = "getSharedPreferences";
    Log.d(
      TAG,
      methodName + ": run"
    );

    try {
      MasterKey masterKey =
        new MasterKey.Builder(context).setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
          .build();

      Log.d(
        TAG,
        methodName + ": done"
      );

      return (EncryptedSharedPreferences) EncryptedSharedPreferences.create(
        context,
        PREFS_NAME,
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
      );
    } catch (GeneralSecurityException | IOException e) {
      Log.e(
        TAG,
        "Error initializing encrypted preferences",
        e
      );
      return null;
    }


  }

  /**
   * Saves the access token securely using
   * EncryptedSharedPreferences.
   *
   * @param context (Context): The application context.
   * @param token (String): The access token to save.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Saving a token
   * String myToken = "your_auth_token_here";
   * SecurePrefsManager.saveAccessToken(context = getApplicationContext(), token = myToken);
   *
   * // Example 2: Saving a different token
   * String anotherToken = "another_valid_token";
   * SecurePrefsManager.saveAccessToken(context = getApplicationContext(), token = anotherToken);
   * }</pre>
   */
  public static void saveAccessToken(
    Context context,
    String token) {
    String methodName = "saveAccessToken";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);
    if (prefs != null) {
      prefs.edit().putString(
        ACCESS_TOKEN_KEY,
        token
      ).apply();

      Log.d(
        TAG,
        methodName + ": done"
      );
    } else {
      Log.e(
        TAG,
        methodName + ": Failed to save token"
      );
    }
  }


  /**
   * Saves the refresh token securely using
   * EncryptedSharedPreferences.
   *
   * @param context (Context): The application context.
   * @param token (String): The refresh token to save.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Saving a token
   * String myToken = "your_auth_token_here";
   * SecurePrefsManager.saveRefreshToken(context = getApplicationContext(), token = myToken);
   *
   * // Example 2: Saving a different token
   * String anotherToken = "another_valid_token";
   * SecurePrefsManager.saveRefreshToken(context = getApplicationContext(), token = anotherToken);
   * }</pre>
   */
  public static void saveRefreshToken(
    Context context,
    String token) {
    String methodName = "saveRefreshToken";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);
    if (prefs != null) {
      prefs.edit().putString(
        REFRESH_TOKEN_KEY,
        token
      ).apply();

      Log.d(
        TAG,
        methodName + ": done"
      );
    } else {
      Log.e(
        TAG,
        methodName + ": Failed to save token"
      );
    }
  }


  /**
   * Retrieves the access token securely.
   *
   * @param context (Context): The application context.
   * @return accessToken (String): The access token, or null if not
   * found.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Retrieving the token
   * String token = SecurePrefsManager.getAccessToken(context = getApplicationContext());
   * if (token != null) {
   *     // Use the token
   *     System.out.println("Token: " + token);
   * } else {
   *     // Handle the case where the token is not found
   *     System.out.println("Token not found");
   * }
   *
   * // Example 2: Retrieving the token when context might be null
   * Context appContext = getApplicationContext(); // or null in some scenarios
   * String anotherToken = (appContext != null) ? SecurePrefsManager.getAccessToken(context = appContext) : null;
   * if (anotherToken != null) {
   *     System.out.println("Another Token: " + anotherToken);
   * } else {
   *     System.out.println("Another Token not found or context is null");
   * }
   * }</pre>
   */
  public static String getAccessToken(Context context) {
    String methodName = "getAccessToken";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);

    String accessToken = prefs != null ? prefs.getString(
      ACCESS_TOKEN_KEY,
      null
    ) : null;

    Log.d(
      TAG,
      methodName + ": done"
    );

    return accessToken;
  }


  /**
   * Retrieves the refresh token securely.
   *
   * @param context (Context): The application context.
   * @return accessToken (String): The refresh token, or null if not
   * found.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Retrieving the token
   * String token = SecurePrefsManager.getRefreshToken(context = getApplicationContext());
   * if (token != null) {
   *     // Use the token
   *     System.out.println("Token: " + token);
   * } else {
   *     // Handle the case where the token is not found
   *     System.out.println("Token not found");
   * }
   *
   * // Example 2: Retrieving the token when context might be null
   * Context appContext = getApplicationContext(); // or null in some scenarios
   * String anotherToken = (appContext != null) ? SecurePrefsManager.getRefreshToken(context = appContext) : null;
   * if (anotherToken != null) {
   *     System.out.println("Another Token: " + anotherToken);
   * } else {
   *     System.out.println("Another Token not found or context is null");
   * }
   * }</pre>
   */
  public static String getRefreshToken(Context context) {
    String methodName = "getRefreshToken";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);

    String accessToken = prefs != null ? prefs.getString(
      REFRESH_TOKEN_KEY,
      null
    ) : null;

    Log.d(
      TAG,
      methodName + ": done"
    );

    return accessToken;
  }

  /**
   * Retrieves the number of search times from secure shared
   * preferences.
   *
   * @param context (Context): The application context.
   * @return searchTimes (int): The number of search times, or 0 if
   * not found.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Retrieving search times when available
   * Context appContext = getApplicationContext();
   * int searchTimes = SecurePrefsManager.getSearchTimes(context = appContext);
   * System.out.println("Search times: " + searchTimes);
   *
   * // Example 2: Retrieving search times when context is null
   * Context nullContext = null;
   * int searchTimesWithNullContext = (nullContext != null) ? SecurePrefsManager.getSearchTimes(context = nullContext) : 0;
   * System.out.println("Search times with null context: " + searchTimesWithNullContext);
   * }</pre>
   */
  public static int getSearchTimes(Context context) {
    String methodName = "getSearchTimes";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);

    int searchTimes = prefs != null ? prefs.getInt(
      SEARCH_TIMES_KEY,
      0
    ) : 0;

    Log.d(
      TAG,
      methodName + ": done"
    );

    return searchTimes;
  }


  /**
   * Saves the number of search times to secure shared preferences.
   *
   * @param context (Context): The application context.
   * @param searchTimes (int): The number of search times to save.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Saving search times
   * Context appContext = getApplicationContext();
   * SecurePrefsManager.saveSearchTimes(context = appContext, searchTimes = 10);
   *
   * // Example 2: Saving search times when context might be null
   * Context nullContext = null;
   * if (nullContext != null) {
   *     SecurePrefsManager.saveSearchTimes(context = nullContext, searchTimes = 5);
   * } else {
   *     System.out.println("Context is null, cannot save search times");
   * }
   * }</pre>
   */
  public static void saveSearchTimes(
    Context context,
    int searchTimes) {
    String methodName = "saveSearchTimes";

    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);
    if (prefs != null) {
      prefs.edit().putInt(
        SEARCH_TIMES_KEY,
        searchTimes
      ).apply();

      Log.d(
        TAG,
        methodName + ": done"
      );
    }
  }


  /**
   * Saves the account email securely using
   * EncryptedSharedPreferences.
   *
   * @param context (Context): The application context.
   * @param email (String): The account email to be saved.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Saving the email
   * SecurePrefsManager.saveAccountEmail(context = getApplicationContext(), email = "<EMAIL>");
   *
   * // Example 2: Saving the email when context might be null
   * Context appContext = getApplicationContext(); // or null in some scenarios
   * if (appContext != null) {
   *     SecurePrefsManager.saveAccountEmail(context = appContext, email = "<EMAIL>");
   * } else {
   *     System.out.println("Context is null, cannot save email");
   * }
   * }</pre>
   */
  public static void saveAccountEmail(
    Context context,
    String email) {
    String methodName = "saveAccountEmail";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);
    if (prefs != null) {
      prefs.edit().putString(
        ACCOUNT_EMAIL_KEY,
        email
      ).apply();
      Log.d(
        TAG,
        methodName + ": done"
      );
    } else {
      Log.e(
        TAG,
        methodName + ": Failed to save email"
      );
    }
  }

  /**
   * Retrieves the account email securely from
   * EncryptedSharedPreferences.
   *
   * @param context (Context): The application context.
   * @return email (String): The account email, or null if not found.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Retrieving the email
   * String email = SecurePrefsManager.getAccountEmail(context = getApplicationContext());
   * if (email != null) {
   *     System.out.println("Account email: " + email);
   * } else {
   *     System.out.println("Account email not found.");
   * }
   *
   * // Example 2: Retrieving the email when context might be null
   * Context appContext = getApplicationContext(); // or null in some scenarios
   * if (appContext != null) {
   *     String anotherEmail = SecurePrefsManager.getAccountEmail(context = appContext);
   *     if (anotherEmail != null) {
   *         System.out.println("Account email: " + anotherEmail);
   *     } else {
   *         System.out.println("Account email not found.");
   *     }
   * } else {
   *     System.out.println("Context is null, cannot retrieve email.");
   * }
   * }</pre>
   */
  public static String getAccountEmail(Context context) {
    String methodName = "getAccountEmail";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);

    String accountEmail = prefs != null ? prefs.getString(
      ACCOUNT_EMAIL_KEY,
      null
    ) : null;

    Log.d(
      TAG,
      methodName + ": done"
    );

    return accountEmail;
  }


  /**
   * Saves the login status to secure preferences.
   *
   * @param context (Context): The application context.
   * @param hasLoggedIn (boolean): The login status to save.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Saving successful login status
   * SecurePrefsManager.saveHasSuccessLoggedIn(context = getApplicationContext(), hasLoggedIn = true);
   *
   * // Example 2: Saving logout status
   * Context appContext = getApplicationContext();
   * if (appContext != null) {
   *     SecurePrefsManager.saveHasSuccessLoggedIn(context = appContext, hasLoggedIn = false);
   * } else {
   *     // Handle the case where context is null
   *     System.out.println("Context is null, cannot save login status.");
   * }
   * }</pre>
   */
  public static void saveHasSuccessLoggedIn(
    Context context,
    boolean hasLoggedIn) {
    String methodName = "saveHasSuccessLoggedIn";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);
    if (prefs != null) {
      prefs.edit().putBoolean(
        HAS_LOGGED_IN_KEY,
        hasLoggedIn
      ).apply();

      Log.d(
        TAG,
        methodName + ": done"
      );
    } else {
      Log.e(
        TAG,
        methodName + ": Failed to save login status"
      );
    }
  }


  /**
   * Retrieves the login status from SharedPreferences.
   *
   * @param context (Context): The application context.
   * @return hasSuccessLoggedIn (boolean): True if the user has
   * successfully logged in, false otherwise.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Checking login status when the app starts
   * Context appContext = getApplicationContext();
   * boolean isLoggedIn = SecurePrefsManager.getHasSuccessLoggedIn(context = appContext);
   * if (isLoggedIn) {
   *     // Proceed to the main activity
   *     System.out.println("User is logged in");
   * } else {
   *     // Redirect to the login screen
   *     System.out.println("User is not logged in");
   * }
   *
   * // Example 2: Checking login status before accessing sensitive data
   * Context anotherAppContext = getApplicationContext();
   * if (SecurePrefsManager.getHasSuccessLoggedIn(context = anotherAppContext)) {
   *     // Access sensitive data
   *     System.out.println("Accessing sensitive data");
   * } else {
   *     // Show an error message or redirect to login
   *     System.out.println("Redirect to login");
   * }
   * }</pre>
   */
  public static boolean getHasSuccessLoggedIn(Context context) {
    String methodName = "getHasSuccessLoggedIn";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);

    boolean hasSuccessLoggedIn = prefs != null && prefs.getBoolean(
      HAS_LOGGED_IN_KEY,
      false
    );

    Log.d(
      TAG,
      methodName + ": done"
    );

    return hasSuccessLoggedIn;
  }


  /**
   * Retrieves the user type from secure shared preferences.
   *
   * @param context (Context): The application context.
   * @return userType (String): The user type, or null if not found.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Retrieving user type when available
   * Context appContext = getApplicationContext();
   * String userType = SecurePrefsManager.getUserType(context = appContext);
   * if (userType != null) {
   *     System.out.println("User type: " + userType);
   * } else {
   *     System.out.println("User type not found");
   * }
   *
   * // Example 2: Handling the case where user type is not set
   * Context anotherAppContext = getApplicationContext();
   * String anotherUserType = SecurePrefsManager.getUserType(context = anotherAppContext);
   * if (anotherUserType != null && anotherUserType.equals("admin")) {
   *     // Perform admin-specific tasks
   *     System.out.println("Performing admin tasks");
   * } else {
   *     // Perform regular user tasks or handle the absence of user type
   *     System.out.println("Performing regular user tasks");
   * }
   * }</pre>
   */
  public static String getUserType(Context context) {
    String methodName = "getUserType";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);

    String userType = prefs != null ? prefs.getString(
      USER_TYPE_KEY,
      null
    ) : null;

    Log.d(
      TAG,
      methodName + ": done"
    );

    return userType;
  }

  public static long getLoginTime(Context context) {
    String methodName = "getLoginTime";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);

    long loginTime = prefs != null ? prefs.getLong(
      LOGIN_TIME_KEY,
      0
    ) : 0;
    Log.d(
      TAG,
      methodName + ": done"
    );

    return loginTime;
  }


  /**
   * Saves the user type securely using EncryptedSharedPreferences.
   *
   * @param context (Context): The application context.
   * @param userType (String): The user type to be saved.
   * @example <pre>{@code
   * // Import necessary classes
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.utils.SecurePrefsManager;
   *
   * // Example 1: Saving the user type
   * SecurePrefsManager.saveUserType(context = getApplicationContext(), userType = "admin");
   *
   * // Example 2: Saving the user type when context might be null
   * Context appContext = getApplicationContext(); // or null in some scenarios
   * if (appContext != null) {
   *     SecurePrefsManager.saveUserType(context = appContext, userType = "regular");
   * } else {
   *     System.out.println("Context is null, cannot save user type");
   * }
   * }</pre>
   */
  public static void saveUserType(
    Context context,
    String userType) {
    String methodName = "saveUserType";
    Log.d(
      TAG,
      methodName + ": run"
    );

    EncryptedSharedPreferences prefs = getSharedPreferences(context);
    if (prefs != null) {
      prefs.edit().putString(
        USER_TYPE_KEY,
        userType
      ).apply();

      Log.d(
        TAG,
        methodName + ": done"
      );
    }
  }

  public static void saveSignInData(
    Context context,
    String accessToken,
    String refreshToken,
    String email,
    boolean hasSuccessLoggedIn) {
    String methodName = "saveSignInData";
    Log.d(
      TAG,
      methodName + ": run"
    );

    saveAccessToken(
      context,
      accessToken
    );
    Log.i(
      TAG,
      methodName + ": saved access token"
    );

    saveRefreshToken(
      context,
      refreshToken
    );
    Log.i(
      TAG,
      methodName + ": saved refresh token"
    );

    saveAccountEmail(
      context,
      email
    );
    Log.i(
      TAG,
      methodName + ": saved email"
    );

    saveHasSuccessLoggedIn(
      context,
      hasSuccessLoggedIn
    );
    Log.i(
      TAG,
      methodName + ": saved hasSuccessLoggedIn"
    );

    saveLoginTime(context);
    Log.i(
      TAG,
      methodName + ": saved login time"
    );

    Log.d(
      TAG,
      methodName + ": done"
    );
  }

  private static void saveLoginTime(Context context) {
    String methodName = "saveLoginTime";
    Log.d(
      TAG,
      methodName + ": run"
    );
    EncryptedSharedPreferences prefs = getSharedPreferences(context);
    if (prefs != null) {
      prefs.edit().putLong(
        LOGIN_TIME_KEY,
        System.currentTimeMillis()
      ).apply();
    }
    Log.d(
      TAG,
      methodName + ": done"
    );
  }

  public static void saveSignInDataFromResponseJSONObject(
    Context context,
    JSONObject responseJsonObj) {
    String methodName = "saveSignInDataFromResponseJSONObject";
    Log.d(
      TAG,
      methodName + ": run"
    );

    try {
      // Updated to use new API response parsing methods
      boolean isSuccess = RequestUtils.isApiResponseSuccessful(context, responseJsonObj);

      if (isSuccess) {
        JSONObject responseData = RequestUtils.getApiResponseDataFromResponseJSONObject(
          context, responseJsonObj);

        String accessToken = responseData.getString("access_token");
        String refresh_token = responseData.getString("refresh_token");

        String email = SecurePrefsManager.getAccountEmail(context);

        saveSignInData(
          context,
          accessToken,
          refresh_token,
          email,
          true
        );
      }
    } catch (JSONException e) {
      Log.e(
        TAG,
        methodName + ": JSONException: " + e
      );
      throw new RuntimeException(e);
    }


    Log.d(
      TAG,
      methodName + ": done"
    );
  }

}