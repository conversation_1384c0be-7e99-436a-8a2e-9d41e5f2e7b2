INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.auth.SignInTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stream=
com.kewtoms.whatappsdo.auth.SignInTest:
INSTRUMENTATION_STATUS: test=test_sign_in_success_mock
INSTRUMENTATION_STATUS_CODE: 1
s_glBindAttribLocation: bind attrib 0 name position
s_glBindAttribLocation: bind attrib 1 name color
s_glBindAttribLocation: bind attrib 2 name localCoord
INSTRUMENTATION_STATUS: class=com.kewtoms.whatappsdo.auth.SignInTest
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=1
INSTRUMENTATION_STATUS: stack=androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 539, 1040 and precision: 16, 16' on view 'view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)
... 33 trimmed
Caused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.
at androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

INSTRUMENTATION_STATUS: stream=
Error in test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest):
androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 539, 1040 and precision: 16, 16' on view 'view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)
... 33 trimmed
Caused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.
at androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

INSTRUMENTATION_STATUS: test=test_sign_in_success_mock
INSTRUMENTATION_STATUS_CODE: -2
INSTRUMENTATION_RESULT: stream=

Time: 67.005
There was 1 failure:
1) test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest)
androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 539, 1040 and precision: 16, 16' on view 'view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)
... 33 trimmed
Caused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.
at androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

FAILURES!!!
Tests run: 1,  Failures: 1


INSTRUMENTATION_CODE: -1

