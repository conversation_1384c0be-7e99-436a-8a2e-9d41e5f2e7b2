@startuml test_verify_client_apps_trace
'left to right direction
skinparam ranksep 0
allowmixing

' --------------------------
rectangle Modes {
  rectangle "is_mock_import_time_consuming_import"
}

struct fastapi_app {
  {method} check_access_token_middleware
  {method} send_verification_email
  {method} sign_up
  {method} sign_in
  {method} sign_out
  {method} search_user_app
  {method} <color:grey>request_fake_resource </color>
}

note top of fastapi_app
  Each endpoint has input data of type Data
  ---
  class Data(BaseModel):
    """
    A Pydantic model representing the data structure for incoming requests.

    Attributes:
        data_str (str): A string containing the data to be processed.
                         Defaults to an empty string.
    """

    data_str: str = ""
  ---
    Each endpoint has response format
    {"ApiResult": data_dict_or_str}
    If there is error, 
    {"ApiError": data_dict_or_str}
  ---
  Middlewares:
  1. check access token on specific endpoint:
  except
  ignore_check_access_token_endpoint_list = [
    send_verification_email,
    sign_up,
    sign_in,
    sign_out,
  ]

end note

note left of fastapi_app::sign_up
  {{

    user --> app : fill in email, password, \n press get verification code button
    app --> server: send post request to server using the sign up endpoint
    server --> server: create user in db_user \n with is_verfied false \n if user does not exist
    server --> server: send email with verification code
    server --> app: send response to app
    user --> app : fill verification code, \n press sign up button
    app --> server: send post request \n with verficiation code to server
    server --> server: check verification code \n and update db_user is_verified to True
    server --> app: send response to app
    app --> user: display success message
  }}

  {{
    start
      :user fills in email, password;
      :user presses "get verification code" button;

      :app sends post request to server using the send_verification_email endpoint;

      if (user email is verified in db_user) then (yes)
        :server response to app with user registered message;
        :app displays with user registered message;
        end
      else (no)
        :server send email with verification code;
        :app displays verification email has been sent message;
      endif

      :user gets verification code from email;
      :user fills verification code then press sign up button;

      if (server verifies the code from user matches db_user) then (yes)
        :server set db_user is_verified to True;
        :server clear verification code;
        
        :server response to app with sign up success message;
        :app displays success message;
      else (no)
        :server response to app with error message;
        :app displays error message;
      endif
    end

  }}
  
end note


note left of fastapi_app::sign_in

  {{
    start
      :user fills in email, password;
      :user presses "sign in" button;

      :app sends post request to server using the sign in endpoint;

      if (user email is verified in db_user) then (yes)
        :server generates an access token;
        :server responses to app with \n sign in success message \n and access token (JWT);
        :app stores the access token for further use;
      else (no)
        :server responses to app with error message;
        :app displays error message;
      endif

    end
  }}
  
end note

note left of fastapi_app::check_access_token_middleware
{{
  start
    :app gets access token from request header Authorization;

  
    if (server api middleware checks if the endpoint \n is in ignore_check_access_token_endpoint_list) then (yes)
      :server api continue to next middleware;
      end
    else (no)

    endif
    
    if (server api middleware checks if \n there is access token) then (yes)
    else (no)
      :server responses with access token is missing;
      end
    endif

    if (server api middleware checks if \n access token is valid) then (yes)
      :server api continue to the next middleware;
    else (no)
      :server responses with access token is invalid;
      end
    endif

  end
}}
end note

note left of fastapi_app::request_fake_resource

  {{
    start
      :user requests for resource in app;
      :app sends post request to server using \n some request resource endpoint;
      :server gets access token from request header Authorization;

      if (server api middleware checks if \n access token is valid) then (yes)
        :server responses with resouce;
        :app displays resource;
      else (no)
        :server responses to app with error message;
        :app displays error message;
      endif

    end
  }}
  
end note
' --------------------------
struct api_functions {
  {method} send_verification_email
  {method} search_user_app
  {method} <color:grey>request_fake_resource</color>
}

note top of api_functions
  Each function has return format
  {"ApiFunResult": data_dict_or_str}
  If there is error, 
  {"ApiFunError": error_message}
end note

' --------------------------
class PickleDatabaseSplit {
}

note right of PickleDatabaseSplit
  to import: from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
  is_regenerate: False
end note
' --------------------------
class UserDatabase {
{method} send_verification_email
{method} sign_in
{method} sign_up
{method} sign_out
{method} delete
{method} <color:grey>request_fake_resource </color>
}

note top of UserDatabase
  Each method has response format
  {"db_result": data_dict}
  ---
  Structure of each data in UserDatabase:
  {
    email_xxx:{
      "email": xx,
      "hashed_password": xx,
      "is_verified": xx,
      "verification_code": xx,
      "reset_token": xx,
      "reset_token_expires": xx,
    }
  }
end note

note right of UserDatabase::send_verification_email
  doc string: Sign in
  in: email_address:str
  out: suitable dict
  is_regenerate: False
end note

note right of UserDatabase::sign_in
  doc string: Sign in
  in: email_address:str, password:str
  out: suitable dict
  is_regenerate: False
end note

note right of UserDatabase::sign_up
  doc string: Sign up
  in: email_address:str, password:str, verification_code:str
  out: suitable dict
  is_regenerate: False
end note

note right of UserDatabase::sign_out
  doc string: Sign out
  in: email_address:str
  out: suitable dict
  is_regenerate: True
end note

note right of UserDatabase::request_fake_resource
  doc string: Request fake resource
  in: access_token:str
  out: suitable dict
  is_regenerate: True
end note
' --------------------------
class ApiFunResponseKeys{
  Result: str = "ApiFunResult"
  Error: str = "ApiFunError"
}


fastapi_app::search_user_app -right-> api_functions::search_user_app

api_functions --> PickleDatabaseSplit: uses
api_functions -right-> ApiFunResponseKeys: uses
api_functions -right-> UserDatabase: uses


@enduml


