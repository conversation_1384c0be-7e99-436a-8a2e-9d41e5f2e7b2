import datetime
import os
import shutil
from const_path import proj_ser_api_db_path

# -----Settings----------
# database folder
# db_fpath = r"D:/code/my_projects/search_phone_app_server/server/tools/test_backup_database_folder"
# output_dir = r"D:/code/my_projects/search_phone_app_server/server/tools/test_backup_database_output_folder"
db_fpath = proj_ser_api_db_path
output_dir = r"D:/code/my_projects/search_phone_app_server_db_backups"

# -----------------------
# Use date to represent version number
date_now = datetime.datetime.now()
str_datetime = (
    "y"
    + str(date_now.year)
    + "_m"
    + str(date_now.month)
    + "_d"
    + str(date_now.day)
    + "_h"
    + str(date_now.hour)
    + "_m"
    + str(date_now.minute)
    + "_s"
    + str(date_now.second)
)

# output_filename = f"search_phone_server_dbs_{date_now}"
output_fpath = os.path.join(
    output_dir,
    f"search_phone_server_dbs_{str_datetime}",
)

shutil.make_archive(output_fpath, "zip", db_fpath)

print(f"Finished backing up to {output_fpath}")
