package com.kewtoms.whatappsdo.data;


import java.util.HashMap;
import java.util.List;

public class ScrapeData {
 private final String packageName;
 private final String appName;
 private final String genre;
 private final String genreId;
 private final List<HashMap<String, Object>> categories;
 private final String description;
 private final String summary;
 private final String source;
 private final Boolean success;
 private final String exception;

 // Constructor
 public ScrapeData(
  String packageName,
  String appName,
  String genre,
  String genreId,
  List<HashMap<String, Object>> categories,
  String description,
  String summary,
  String source,
  Boolean success,
  String exception) {
  this.packageName = packageName;
  this.appName = appName;
  this.genre = genre;
  this.genreId = genreId;
  this.categories = categories;
  this.description = description;
  this.summary = summary;
  this.source = source;
  this.success = success;
  this.exception = exception;
 }

 public String getPackageName() {
  return packageName;
 }

 public String getAppName() {
  return appName;
 }

 public List<HashMap<String, Object>> getCategories() {
  return categories;
 }

 public String getDescription() {
  return description;
 }

 public Boolean getSuccess() {
  return success;
 }

 public String getException() {
  return exception;
 }

 public String getSummary() {
  return summary;
 }

 public String getGenre() {
  return genre;
 }

 public String getGenreId() {
  return genreId;
 }

 public String getSource() {
  return source;
 }
}
