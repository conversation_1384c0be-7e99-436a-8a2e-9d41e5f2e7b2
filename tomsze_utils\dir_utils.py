import os
import shutil
from typing import List


def get_file_path_list_in_path(path: str) -> List[str]:
    """
    Retrieve a list of all files paths in the specified path and its subdirectories.

    Args:
        path (str): The path to search for files.

    Returns:
        List[str]: A list of file paths found in the path.

    Examples:
        ```python
        files = get_files_in_path(path="/path/to/directory")  # returns a list of all files in the specified path
        ```

        ```python
        files = get_files_in_path(path="./")  # returns a list of all files in the current directory
        ```
    """
    file_list = []
    for root, dirs, files in os.walk(path):
        for file in files:
            file_list.append(os.path.join(root, file))
    return file_list


def get_current_working_dir_path() -> str:
    """
    Retrieve the current working directory path.

    Returns:
        str: The path of the current working directory.

    Args:
        None

    Examples:
        ```python
        cwd = get_current_working_directory()  # returns the current working directory path
        ```

        ```python
        current_dir = get_current_working_directory()  # current_dir will hold the path of the current directory
        ```
    """
    return os.getcwd()


def create_nested_folders(directory_path: str) -> str:
    """
    Create nested folders specified by the directory path.

    Args:
        directory (str): The path of the nested folders to create.

    Raises:
        OSError: If there is an error creating the directories.

    Examples:
        ```python
        create_nested_folders(directory="parent/child/grandchild")
        ```
    """
    try:
        os.makedirs(directory_path, exist_ok=True)
    except OSError as e:
        raise OSError(f"Error creating directories: {e}")

    return f"Directory ({directory_path}) is created"


def remove_directory(directory_path: str) -> str:
    """
    Remove a directory and all its contents.

    Args:
        directory_path (str): The path of the directory to remove.

    Raises:
        FileNotFoundError: If the directory does not exist.
        OSError: If there is an error removing the directory.

    Examples:
        ```python
        remove_directory("path/to/directory")
        ```
    """
    if not os.path.exists(directory_path):
        raise FileNotFoundError(f"The directory {directory_path} does not exist.")

    shutil.rmtree(directory_path)
    return f"Directory ({directory_path}) has been removed."


def move_folder(source: str, destination: str) -> str:
    """
    Move a folder from the source path to the destination path.

    Args:
        source (str): The path of the folder to move.
        destination (str): The path where the folder should be moved.

    Raises:
        FileNotFoundError: If the source folder does not exist.
        OSError: If there is an error moving the folder.

    Examples:
        ```python
        move_folder("path/to/source_folder", "path/to/destination_folder")
        ```
    """
    if not os.path.exists(source):
        raise FileNotFoundError(f"The source folder {source} does not exist.")

    try:
        shutil.move(source, destination)
    except OSError as e:
        raise OSError(f"Error moving folder: {e}")

    return f"Folder moved from {source} to {destination}."


def clear_folder(folder_path: str) -> str:
    """
    Clear all contents of a folder without removing the folder itself.

    Args:
        folder_path (str): The path of the folder to clear.

    Raises:
        FileNotFoundError: If the folder does not exist.
        OSError: If there is an error clearing the folder.

    Examples:
        ```python
        clear_folder("path/to/folder")
        ```
    """
    if not os.path.isdir(folder_path):
        raise FileNotFoundError(f"The folder {folder_path} does not exist.")

    # Clear the contents of the folder
    for item in os.listdir(folder_path):
        item_path = os.path.join(folder_path, item)
        if os.path.isfile(item_path):
            os.remove(item_path)
        else:
            shutil.rmtree(item_path)

    return f"Folder ({folder_path}) has been cleared."


def rename_directory(old_path: str, new_path: str) -> str:
    """
    Rename a directory from old_path to new_path.

    Args:
        old_path (str): The current path of the directory to rename.
        new_path (str): The new path for the directory.

    Raises:
        FileNotFoundError: If the directory old_path does not exist.

    Examples:
        ```python
        rename_directory(old_path="path/to/old_directory", new_path="path/to/new_directory")
        ```

        ```python
        rename_directory(old_path="old_folder", new_path="new_folder")
        ```
    """
    if not os.path.exists(old_path):
        raise FileNotFoundError(f"The directory {old_path} does not exist.")

    os.rename(old_path, new_path)
    return f"Directory renamed from {old_path} to {new_path}."
