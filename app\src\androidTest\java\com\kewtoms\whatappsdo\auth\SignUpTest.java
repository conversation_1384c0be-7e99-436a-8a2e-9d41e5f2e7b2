package com.kewtoms.whatappsdo.auth;

import static android.provider.Settings.System.getString;
import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.matcher.ViewMatchers.withContentDescription;
import static androidx.test.espresso.matcher.ViewMatchers.withText;

import android.content.Context;
import android.util.Log;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.rules.ActivityScenarioRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import static androidx.test.espresso.matcher.ViewMatchers.withId;

import com.kewtoms.whatappsdo.MainActivity;
import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;
import com.kewtoms.whatappsdo.model.auth.Authenticator;

import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(AndroidJUnit4.class)
public class SignUpTest {
  public static final String TAG = "APP:SignUpTest";

  // Rule to launch the MainActivity
  @Rule
  public ActivityScenarioRule<MainActivity> activityScenarioRule =
    new ActivityScenarioRule<>(MainActivity.class);


  @Test
  public void test_sign_up_success() {
    // Open navigation drawer
    Context context = ApplicationProvider.getApplicationContext();
    onView(withContentDescription(context.getString(R.string.navigation_drawer_open))).perform(click());


    // Click on "Sign-up" item on drawer menu
    onView(withText(context.getString(R.string.menu_sign_up_name))).perform(click());

    activityScenarioRule.getScenario().onActivity(currentActivity -> {
      PostResponseCallback removeUserCallback =
        new PostResponseCallback() {

          @Override
          public void onPostSuccess(JSONObject response) {
            Log.i(
              TAG,
              response.toString()
            );
            int g = 1;
          }

          @Override
          public void onPostError(String errorMessage) {
            Log.i(
              TAG,
              errorMessage
            );
          }
        };
      Authenticator authenticator =
        new Authenticator(currentActivity);

      // Remove the test user from database
      String email = Constants.test_email_address;
      boolean isWaitForThread = true;
      try {
        authenticator.removeUser(
          email,
          removeUserCallback,
          isWaitForThread
        );
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }


    }); // end of onActivity

    // Click the sign-up button
    onView(withId(R.id.button_sign_up_large)).perform(click());

    activityScenarioRule.getScenario().onActivity(currentActivity -> {
      Authenticator authenticator =
        new Authenticator(currentActivity);

      // Check if the user is registered
      PostResponseCallback isUserRegisteredCallback =
        new PostResponseCallback() {

          @Override
          public void onPostSuccess(JSONObject response) {
            Log.i(
              TAG,
              response.toString()
            );
            Assert.assertTrue(response.toString()
              .contains("is signed up"));
          }

          @Override
          public void onPostError(String errorMessage) {
            Log.i(
              TAG,
              errorMessage
            );
          }
        };

      String email = Constants.test_email_address;

      try {
        authenticator.isUserRegistered(
          email,
          isUserRegisteredCallback,
          true
        );
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
    });


  }
}