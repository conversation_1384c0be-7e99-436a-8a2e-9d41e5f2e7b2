{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "assign_variable_1",
            "extract_dict_var",
            "assign_variable_2",
            "extract_dict_var2",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "assign_variable_1",
            "type": "PluginVariableAssigner",
            "use": true,
            "dictx": {
                'key1':'val1',
                'key2':'val2',
            },
        },
        {
            "step_name": "extract_dict_var",
            "type": "PluginDictVarExtractor",
            "use": true,
            "which_dict": "{assign_variable_1.dictx}",
        },
        {
            "step_name": "assign_variable_2",
            "type": "PluginVariableAssigner",
            "use": true,
            "key4": "val4",
        },
        {
            "step_name": "extract_dict_var2",
            "type": "PluginDictVarExtractor",
            "use": true,
            "which_dict": {
                "key3":"val3",
                "key4":"{assign_variable_2.key4}",
            },
        }
    ]
   

}
