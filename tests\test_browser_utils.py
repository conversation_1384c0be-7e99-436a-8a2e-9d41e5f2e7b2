import pytest
import tempfile
import os
import sqlite3

from tomsze_utils.browser_utils import delete_brave_history


@pytest.fixture
def setup_brave_history_db():
    """Fixture to set up a temporary Brave history database."""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create a temporary SQLite database to simulate <PERSON>'s history
        history_file = os.path.join(tmpdir, "History")
        connection = sqlite3.connect(history_file)
        cursor = connection.cursor()

        # Create the necessary table
        cursor.execute("CREATE TABLE urls (id INTEGER PRIMARY KEY, url TEXT)")

        # Insert some test data
        cursor.execute("INSERT INTO urls (url) VALUES ('https://example.com')")
        cursor.execute("INSERT INTO urls (url) VALUES ('https://test.com')")
        connection.commit()
        connection.close()

        yield history_file  # Provide the path to the temporary database


def test_delete_brave_history_success(setup_brave_history_db):
    """Test deleting browsing history entries successfully."""
    history_file_path = setup_brave_history_db
    delete_brave_history(
        "example.com", history_file_path
    )  # Pass the history_file to the function

    # Check if the entry was deleted
    connection = sqlite3.connect(history_file_path)
    cursor = connection.cursor()
    cursor.execute("SELECT * FROM urls WHERE url = 'https://example.com'")
    result = cursor.fetchall()
    connection.close()

    assert len(result) == 0  # The entry should be deleted


def test_delete_brave_history_no_entry(setup_brave_history_db):
    """Test deleting browsing history when no entry exists."""
    history_file_path = setup_brave_history_db
    delete_brave_history(
        "nonexistent.com", history_file_path  # Pass the history_file to the function
    )

    # Check if the database is unchanged
    connection = sqlite3.connect(history_file_path)
    cursor = connection.cursor()
    cursor.execute("SELECT * FROM urls")
    result = cursor.fetchall()
    connection.close()

    assert len(result) == 2  # The original entries should still exist
