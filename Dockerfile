# FROM python:3.10.0
FROM pytorch/pytorch:2.1.0-cuda12.1-cudnn8-runtime

# Set working directory in the image to be /code
WORKDIR /code

# Pip install packages.
COPY ./requirements.txt /code/requirements.txt
COPY ./local_wheels /code/local_wheels
RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

# Download spacy model.
RUN python -m spacy download en_core_web_sm

# Set environment variable
RUN apt-get update 
RUN DEBIAN_FRONTEND="noninteractive" apt-get install -y tzdata
ENV TZ="Asia/Hong_Kong"

# Copy server folder to /code/app.
COPY ./server /code/server

# Copy business folder to /code/app.
COPY ./business /code/business

# Copy setup.py to /code
COPY ./setup.py /code