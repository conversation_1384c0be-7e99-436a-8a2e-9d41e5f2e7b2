import pytest
import tempfile
import os
from tomsze_utils.git_utils import (
    find_git_action_by_changed_files,
    git_commit_using_subprocess,
    git_create_branch_using_subprocess,
)
from tomsze_utils.subprocess_utils import subprocess_run_with_str_output


class TestGitCommit:

    def test_git_commit_success(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            subprocess_run_with_str_output(
                "git init",
                cwd=tmpdir,
                shell=False,
            )

            text_path = os.path.join(tmpdir, "test_file.txt")
            with open(text_path, "w") as f:
                f.write("Test content")

            subprocess_run_with_str_output(
                "git add test_file.txt",
                cwd=tmpdir,
                shell=False,
            )

            # Test the git_commit function
            output_str, command_str = git_commit_using_subprocess(
                "feat",
                "Add test file",
                cwd=tmpdir,
            )
            assert "feat: Add test file" in output_str
            assert 'git commit -m "feat: Add test file"' in command_str


class TestFindGitAction:

    def test_find_git_action_commit(self):
        use_shell = False
        original_dir = os.getcwd()
        with tempfile.TemporaryDirectory() as tmpdir:
            os.chdir(tmpdir)

            # Setup a temporary git repository
            subprocess_run_with_str_output("git init", shell=use_shell)

            # Create a file and commit it
            with open("test_file.txt", "w") as f:
                f.write("Test content")
            subprocess_run_with_str_output("git add test_file.txt", shell=use_shell)
            subprocess_run_with_str_output(
                'git commit -m "feat: Add test file"', shell=use_shell
            )

            # Check the git action
            changed_files = [f"{tmpdir}/.git/logs/HEAD", f"{tmpdir}/.git/index"]
            git_action, git_message, _, _ = find_git_action_by_changed_files(
                changed_files, tmpdir
            )

            assert git_action == "commit"
            assert "feat: Add test file" in git_message

            os.chdir(original_dir)

    def test_find_git_action_stage(self):
        use_shell = False
        original_dir = os.getcwd()
        with tempfile.TemporaryDirectory() as tmpdir:
            # Setup a temporary git repository
            os.chdir(tmpdir)
            subprocess_run_with_str_output("git init", shell=use_shell)

            # Create a file and add it to the staging area
            with open("test_file.txt", "w") as f:
                f.write("Test content")
            subprocess_run_with_str_output("git add test_file.txt", shell=use_shell)

            # Check the git action
            changed_files = [f"{tmpdir}/index"]
            git_action, git_message, _, _ = find_git_action_by_changed_files(
                changed_files, tmpdir
            )

            assert git_action == "stage/unstage"
            assert "Test content" in git_message

            os.chdir(original_dir)

    def test_find_git_action_previous_commit(self):
        use_shell = False
        original_dir = os.getcwd()
        with tempfile.TemporaryDirectory() as tmpdir:
            # Setup a temporary git repository
            os.chdir(tmpdir)
            subprocess_run_with_str_output("git init", shell=use_shell)

            # Create a file and commit it
            with open("test_file.txt", "w") as f:
                f.write("Test content")
            subprocess_run_with_str_output("git add test_file.txt", shell=use_shell)
            subprocess_run_with_str_output(
                'git commit -m "feat: Add test file"', shell=use_shell
            )

            # Create another file and commit it
            with open("another_file.txt", "w") as f:
                f.write("Another test content")
            subprocess_run_with_str_output("git add another_file.txt", shell=use_shell)
            subprocess_run_with_str_output(
                'git commit -m "fix: Update another file"', shell=use_shell
            )

            # Check the git action
            changed_files = [f"{tmpdir}/.git/logs/HEAD", f"{tmpdir}/.git/index"]
            git_action, git_message, previous_git_action, previous_git_message = (
                find_git_action_by_changed_files(changed_files, tmpdir)
            )

            assert git_action == "commit"
            assert "fix: Update another file" in git_message
            assert previous_git_action == "commit"
            assert "feat: Add test file" in previous_git_message

            os.chdir(original_dir)


class TestGitUtils:
    def test_create_branch_without_first_commit(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize a temporary git repository
            subprocess_run_with_str_output(
                "git init",
                cwd=temp_dir,
                shell=False,
            )

            # Create a new branch
            branch_name = "new_feature_branch"
            output_str, command_str = git_create_branch_using_subprocess(
                branch_name,
                cwd=temp_dir,
            )

            # Check if the branch was created successfully
            assert "The master/main branch does not exist." in output_str

    def test_create_branch_with_first_commit(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize a temporary git repository
            subprocess_run_with_str_output(
                "git init",
                cwd=temp_dir,
                shell=False,
            )

            # Create a file and commit it
            text_path = os.path.join(temp_dir, "test_file.txt")
            with open(text_path, "w") as f:
                f.write("Test content")
            subprocess_run_with_str_output(
                "git add test_file.txt", shell=False, cwd=temp_dir
            )
            subprocess_run_with_str_output(
                'git commit -m "feat: Add test file"', shell=False, cwd=temp_dir
            )

            # Create a new branch
            branch_name = "new_feature_branch"
            output_str, command_str = git_create_branch_using_subprocess(
                branch_name,
                cwd=temp_dir,
            )

            # Check if the branch was created successfully
            assert branch_name in output_str
