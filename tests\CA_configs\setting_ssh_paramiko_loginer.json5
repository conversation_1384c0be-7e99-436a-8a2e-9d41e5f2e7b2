{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "ssh_login",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "ssh_login",
            "type": "PluginSSHParamikoLoginer",
            "use": true,
            "ssh_username": "root",
            "ssh_host": "127.0.0.1",
            "ssh_private_key": "",
            "port": 7423,
        }
    ]
   

}
