from dataclasses import dataclass
import os
from tomsze_utils.mermaid_graph_utils.mermaid_graph_utils import <PERSON><PERSON><PERSON>, Fun

output_folder = os.path.dirname(os.path.abspath(__file__))
mmc = MMCode(graph_title="mermai_graph_proj", output_folder=output_folder)


# ------------- classes ------------


@dataclass
class ClassPickleDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassPickleDatabase)


@dataclass
class ClassUserDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassUserDatabase)


@dataclass
class ClassAppDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassAppDatabase)


@dataclass
class ClassSynonymDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassSynonymDatabase)


@dataclass
class classNoSynonymsDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=classNoSynonymsDatabase)


@dataclass
class ClassSynonymsNeedPowerthesaurusDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassSynonymsNeedPowerthesaurusDatabase)


@dataclass
class ClassToAddDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassToAddDatabase)


@dataclass
class ClassEmailVerificationDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassEmailVerificationDatabase)


@dataclass
class ClassUserDataDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassUserDataDatabase)


@dataclass
class ClassRateLimitDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassRateLimitDatabase)


@dataclass
class ClassBlockIpDatabase:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ClassBlockIpDatabase)


# ------------- scripts ------------
@dataclass
class ScriptFastApi:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ScriptFastApi)


@dataclass
class ScriptTestApiFunctions:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ScriptTestApiFunctions)


@dataclass
class ScriptApiFunction:
    func = Fun("doc string")


mmc.add_class(subgraph_class=ScriptApiFunction)


@dataclass
class WrapTest:
    func = Fun("doc string")


mmc.add_class(subgraph_class=WrapTest)


@dataclass
class ClassMMCode:
    _create_coding_subgraph = Fun()
    add_class = Fun("add class doc string")
    inherit_class = Fun()
    composite_class = Fun()
    aggregate_class = Fun()


mmc.add_class(subgraph_class=ClassMMCode)


@dataclass
class MethodAddClass:
    _create_coding_subgraph = Fun()
    create_subgraph_title_from_class = Fun()


mmc.add_class(subgraph_class=MethodAddClass)


@dataclass
class ScriptMain:
    main = Fun()


mmc.add_class(subgraph_class=ScriptMain)

mmc.point_to(ClassPickleDatabase, ClassUserDatabase)

mmc.wrap_subgraphs(
    parent_class=WrapTest,
    child_classes=[ClassPickleDatabase, ClassUserDatabase],
)

mmc.point_class_method_to_impl(
    ClassMMCode.add_class,
    MethodAddClass,
)


# mmc.composite_class(
#     child_class=ClassNode,
#     parent_class=ClassSubgraph,
# )

# mmc.composite_class(
#     child_class=ClassSubgraph,
#     parent_class=ClassMM,
# )


# mmc.inherit_class(
#     child_class=ClassMMCode,
#     parent_class=ClassMM,
# )


mmc.write_graph()
