import pytest
from tomsze_utils.api_utils import api_get_current_time_in_zone


@pytest.fixture
def valid_time_zone():
    return "Europe/London"


@pytest.fixture
def invalid_time_zone():
    return "Invalid/Zone"


def test_get_current_time_in_zone_valid(valid_time_zone):
    result = api_get_current_time_in_zone(valid_time_zone)
    assert result is not None
    assert "dateTime" in result  # Check if the response contains the expected key


def test_get_current_time_in_zone_invalid(invalid_time_zone):
    result = api_get_current_time_in_zone(invalid_time_zone)
    assert result is None  # Expecting None for an invalid time zone
