<?xml version="1.0" encoding="us-ascii" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="375px" preserveAspectRatio="none" style="width:634px;height:375px;background:#FFFFFF;" version="1.1" viewBox="0 0 634 375" width="634px" zoomAndPan="magnify"><defs/><g><rect fill="#DDDDDD" height="52.2402" id="_legend" rx="7.5" ry="7.5" style="stroke:#000000;stroke-width:1.0;" width="182" x="224.5" y="22"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="35" x="229.5" y="45.0439">Class</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="97" x="296.5" y="45.0439">HomeFragment</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="47" x="229.5" y="64.1641">Method</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="88" x="296.5" y="64.1641">onCreateView</text><line style="stroke:#000000;stroke-width:1.0;" x1="229.5" x2="401.5" y1="29" y2="29"/><line style="stroke:#000000;stroke-width:1.0;" x1="229.5" x2="401.5" y1="48.1201" y2="48.1201"/><line style="stroke:#000000;stroke-width:1.0;" x1="229.5" x2="401.5" y1="67.2402" y2="67.2402"/><line style="stroke:#000000;stroke-width:1.0;" x1="229.5" x2="229.5" y1="29" y2="67.2402"/><line style="stroke:#000000;stroke-width:1.0;" x1="292.5" x2="292.5" y1="29" y2="67.2402"/><line style="stroke:#000000;stroke-width:1.0;" x1="401.5" x2="401.5" y1="29" y2="67.2402"/><ellipse cx="322" cy="97.2402" fill="#222222" rx="10" ry="10" style="stroke:#222222;stroke-width:1.0;"/><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="195" x="224.5" y="127.2402"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="175" x="234.5" y="150.9922">user starts app (signed in once)</text><line style="stroke:#181818;stroke-width:1.5;" x1="156" x2="469" y1="183.6289" y2="183.6289"/><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="290" x="11" y="235.6289"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="270" x="21" y="259.3809">self runThreadCheckAndCachePackagesRelated</text><polygon fill="#F1F1F1" points="433,203.6289,505,203.6289,517,215.6289,505,227.6289,433,227.6289,421,215.6289,433,203.6289" style="stroke:#181818;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="72" x="433" y="220.7234">config is prod?</text><text fill="#0000FF" font-family="sans-serif" font-size="11" lengthAdjust="spacing" textLength="18" x="403" y="213.2119">yes</text><a href='D:/code/my_projects/WhatAppsDo/docs/flow/HomeFragment_silentSignIn_flow.svg'><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="108" x="339" y="237.6289"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="88" x="349" y="261.3809">self silentSignIn</text></a><rect fill="#F1F1F1" height="36.3887" rx="12.5" ry="12.5" style="stroke:#181818;stroke-width:0.5;" width="156" x="467" y="237.6289"/><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="65" x="477" y="261.3809">print not red</text><a href="D:\Check.csv" target="_top" title="D:\Check.csv" xlink:actuate="onRequest" xlink:href="D:\Check.csv" xlink:show="new" xlink:title="D:\Check.csv" xlink:type="simple"><text fill="#0000FF" font-family="sans-serif" font-size="12" lengthAdjust="spacing" text-decoration="underline" textLength="71" x="542" y="261.3809">D:\Check.csv</text></a><polygon fill="#F1F1F1" points="469,280.0176,481,292.0176,469,304.0176,457,292.0176,469,280.0176" style="stroke:#181818;stroke-width:0.5;"/><line style="stroke:#181818;stroke-width:1.5;" x1="156" x2="469" y1="324.0176" y2="324.0176"/><ellipse cx="322" cy="354.0176" fill="none" rx="10" ry="10" style="stroke:#222222;stroke-width:1.5;"/><line style="stroke:#222222;stroke-width:2.5;" x1="315.8128" x2="328.1872" y1="347.8304" y2="360.2048"/><line style="stroke:#222222;stroke-width:2.5;" x1="328.1872" x2="315.8128" y1="347.8304" y2="360.2048"/><line style="stroke:#181818;stroke-width:1.0;" x1="322" x2="322" y1="107.2402" y2="127.2402"/><polygon fill="#181818" points="318,117.2402,322,127.2402,326,117.2402,322,121.2402" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="421" x2="393" y1="215.6289" y2="215.6289"/><line style="stroke:#181818;stroke-width:1.0;" x1="393" x2="393" y1="215.6289" y2="237.6289"/><polygon fill="#181818" points="389,227.6289,393,237.6289,397,227.6289,393,231.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="517" x2="545" y1="215.6289" y2="215.6289"/><line style="stroke:#181818;stroke-width:1.0;" x1="545" x2="545" y1="215.6289" y2="237.6289"/><polygon fill="#181818" points="541,227.6289,545,237.6289,549,227.6289,545,231.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="393" x2="393" y1="274.0176" y2="292.0176"/><line style="stroke:#181818;stroke-width:1.0;" x1="393" x2="457" y1="292.0176" y2="292.0176"/><polygon fill="#181818" points="447,288.0176,457,292.0176,447,296.0176,451,292.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="545" x2="545" y1="274.0176" y2="292.0176"/><line style="stroke:#181818;stroke-width:1.0;" x1="545" x2="481" y1="292.0176" y2="292.0176"/><polygon fill="#181818" points="491,288.0176,481,292.0176,491,296.0176,487,292.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="156" x2="156" y1="185.1289" y2="235.6289"/><polygon fill="#181818" points="152,225.6289,156,235.6289,160,225.6289,156,229.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="469" x2="469" y1="185.1289" y2="203.6289"/><polygon fill="#181818" points="465,193.6289,469,203.6289,473,193.6289,469,197.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="156" x2="156" y1="272.0176" y2="324.0176"/><polygon fill="#181818" points="152,314.0176,156,324.0176,160,314.0176,156,318.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="469" x2="469" y1="304.0176" y2="324.0176"/><polygon fill="#181818" points="465,314.0176,469,324.0176,473,314.0176,469,318.0176" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="322" x2="322" y1="163.6289" y2="183.6289"/><polygon fill="#181818" points="318,173.6289,322,183.6289,326,173.6289,322,177.6289" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="322" x2="322" y1="325.5176" y2="344.0176"/><polygon fill="#181818" points="318,334.0176,322,344.0176,326,334.0176,322,338.0176" style="stroke:#181818;stroke-width:1.0;"/><!--SRC=[JK-zJiGm3Dxp51xRXGTeqI5KXBW12N68vRWX96vgNSw9uXI4T0zFaZBWoVRtOtyU7RA57A82k4vUYq2jApo52pucxIx8kSmLViQy1Bl1WQU4EkCxuNU5gujlPaf9rYahDQo22TeWe6E4JiWnMY0kSeFzMAZHKrRLSX3qCwILtvPYRAS5pVcUxQJDWY_QdBL3UKLVJjfnau5schY9QORE19x90Gd45EnTNxlkrWGVql3fLzpzeFIG5-H_zuHyoNSeV-rvR7lqWXiX9YhHEMH8Q8_7X-6ZFNLZvEjq-YEpfRbbtuBK-By0]--></g></svg>