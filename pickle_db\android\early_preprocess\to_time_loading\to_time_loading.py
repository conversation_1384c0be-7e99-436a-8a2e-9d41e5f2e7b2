import sys
import os
import time
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit

# Create db object using PickleDatabaseSplit (will also load pickle file)
script_directory_path = os.path.dirname(
    os.path.abspath(sys.argv[0]),
)

time_start = time.time()

db = PickleDatabaseSplit(
    db_fpath=script_directory_path,
    db_name="apps",
    part_data_count=100,
)

time_end = time.time()
print("time used:" + "{:.2f}".format((time_end - time_start) * 1000) + " ms")

# # Split
# db.re_split()

# # Dump into splited parts
# db.dump_all_parts_to_pickles()

# g = 1
