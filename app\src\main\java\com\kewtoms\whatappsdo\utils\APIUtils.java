package com.kewtoms.whatappsdo.utils;


import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public class APIUtils {

 /**
  * Fetches the current time in the specified time zone.
  *
  * @param timeZone the time zone to fetch the current time for (e.g.,
  * "Europe/London").
  * @return a JSONObject containing the current time and time zone
  * information.
  * @throws Exception if an error occurs during the API request.
  * @example <pre>{@code
  * JSONObject londonTime = APIUtils.apiGetCurrentTimeInZone("Europe/London");
  * JSONObject newYorkTime = APIUtils.apiGetCurrentTimeInZone("America/New_York");
  * }</pre>
  */
 public static JSONObject apiGetCurrentTimeInZone(String timeZone)
  throws
  Exception {
  // Construct the URL for the API request
  String urlString =
   "http://worldtimeapi.org/api/timezone/" + timeZone;
  URL url = new URL(urlString);

  // Open a connection to the URL
  HttpURLConnection conn = (HttpURLConnection) url.openConnection();
  conn.setRequestMethod("GET");

  // Read the response from the input stream
  BufferedReader in =
   new BufferedReader(new InputStreamReader(conn.getInputStream()));
  String inputLine;
  StringBuilder content = new StringBuilder();

  // Append each line of the response to the content
  while ((inputLine = in.readLine()) != null) {
   content.append(inputLine);
  }

  // Close the input stream and disconnect the connection
  in.close();
  conn.disconnect();

  // Return the response as a JSONObject
  return new JSONObject(content.toString());
 }


}
