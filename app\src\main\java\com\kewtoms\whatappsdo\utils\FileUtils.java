package com.kewtoms.whatappsdo.utils;

public class FileUtils {

 /**
  * Removes the specified file extension from the given file name.
  *
  * @param fileName The name of the file from which to remove the
  * extension.
  * @param ext The extension to be removed.
  * @return The file name without the specified extension.
  *
  * <pre>{@code
  * String result1 = removeFileExtension("document.txt", "txt"); // result1 will be "document"
  * String result2 = removeFileExtension("image.jpeg", "jpeg"); // result2 will be "image"
  * }</pre>
  * @args fileName: String, ext: String
  */
 public static String removeFileExtension(
  String fileName,
  String ext) {
  return fileName.replace(
   "." + ext,
   ""
  );
 }

 /**
  * Checks if the specified file name has the given extension.
  *
  * @param fileName The name of the file to check.
  * @param ext The extension to check against.
  * @return True if the file has the specified extension, false
  * otherwise.
  *
  * <pre>{@code
  * Boolean result1 = isFileExtension(fileName = "document.txt", ext = "txt"); // result1 will be true
  * Boolean result2 = isFileExtension(fileName = "image.jpeg", ext = "png"); // result2 will be false
  * }</pre>
  * @args fileName: String, ext: String
  */
 public static Boolean isFileExtension(
  String fileName,
  String ext) {
  int lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex != -1) {
   String extensionFile = fileName.substring(lastDotIndex + 1);
   return extensionFile.equals(ext);
  }
  return false;
 }
}
