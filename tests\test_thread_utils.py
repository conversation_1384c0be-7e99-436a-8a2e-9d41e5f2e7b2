import unittest
from tomsze_utils.threads_utils import calculate_needed_qualified_workers

class TestCalculateNeededQualifiedWorkers(unittest.TestCase):

    def test_normal(self):
        work_needed = 160
        worker_abilities = {
            'tom': 100,
            'david': 80, 
            'mary': 50,
            'john': 20
        }
        expected_workers = ['tom', 'david']
        expected_ability = 180
        expected_enough = True
        
        workers, ability, enough = calculate_needed_qualified_workers(
            work_needed, worker_abilities)
            
        self.assertEqual(workers, expected_workers)
        self.assertEqual(ability, expected_ability) 
        self.assertEqual(enough, expected_enough)

    def test_empty_worker_dict(self):
        work_needed = 160 
        worker_abilities = {}
        
        expected_workers = []
        expected_ability = 0
        expected_enough = False
        
        workers, ability, enough = calculate_needed_qualified_workers(
            work_needed, worker_abilities)
            
        self.assertEqual(workers, expected_workers)
        self.assertEqual(ability, expected_ability)
        self.assertEqual(enough, expected_enough)

    def test_edge_threshold(self):
        work_needed = 160
        worker_abilities = {
            'tom': 100,
            'david': 55, 
            'mary': 50,
            'john': 20
        }
        
        expected_workers = []
        expected_ability = 0
        expected_enough = False
        
        workers, ability, enough = calculate_needed_qualified_workers(
            work_needed, worker_abilities)
            
        self.assertEqual(workers, expected_workers)
        self.assertEqual(ability, expected_ability)
        self.assertEqual(enough, expected_enough)

if __name__ == '__main__':
    unittest.main()
