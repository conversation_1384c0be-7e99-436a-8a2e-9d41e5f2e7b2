package com.kewtoms.whatappsdo.utils;


import android.util.Log;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScrapeData;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

public class ScrapeApkgkUtils {
 public final String TAG = "APP:ScrapeApkgkUtils";
 public final String descriptionSelector = "[itemprop=description]";
 public final String scriptSelector = "head > script";

 public static Callable<ScrapeData> createScrapeApkgkCallable(String packageName) {
  return new Callable<ScrapeData>() {
   @Override
   public ScrapeData call() {
    String appName = "";
    List<HashMap<String, Object>> categories = new ArrayList<>();
    String description = "";
    String summary = "";
    String genre = "";
    String genreId = "";
    String source = Constants.scrape_source_apkgk;
    ScrapeApkgkUtils scrapeUtils = new ScrapeApkgkUtils();

    String url = scrapeUtils.buildUrl(packageName);
    try {
     Document doc = Jsoup.connect(url).get();

     // Get description from doc.
     description = scrapeUtils.getDescriptionFromDoc(doc);

     // Get other data from script tag.
     HashMap<String, Object> dataMap =
      scrapeUtils.getDatasetMapFromScriptTag(doc);
     appName = (String) dataMap.get("name");
     genre = (String) dataMap.get("applicationSubCategory");
     //                  in python
     //                    scrape_data["genreId"] = (
     //                            f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}'
     //                    )
     genreId =
      (String) dataMap.get("applicationCategory") + '_' + genre;
     summary =
      (String) dataMap.get("description");  // yes, correct for this site

     //                    in python
     //                    scrape_data["categories"] = [
     //                    {
     //                        "name": data_dict["applicationCategory"],
     //                            "id": f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}',
     //                    },
     //                    ]
     HashMap<String, Object> categoryItemMap = new HashMap<>();
     categoryItemMap.put(
      "name",
      genre
     );
     categoryItemMap.put(
      "id",
      genreId
     );
     categories.add(categoryItemMap);

     int g = 1;

    } catch (IOException e) {
     return new ScrapeData(
      packageName,
      appName,
      genre,
      genreId,
      categories,
      description,
      summary,
      source,
      false,
      e.toString()
     );
    }

    return new ScrapeData(
     packageName,
     appName,
     genre,
     genreId,
     categories,
     description,
     summary,
     source,
     true,
     ""
    );
   }

  };
 }

 public String buildUrl(String packageName) {
  return "https://apkgk.com/" + packageName;
 }

 public HashMap<String, Object> getDatasetMapFromScriptTag(Document doc) {
  HashMap<String, Object> datasetMap = new HashMap<>();

  try {
   Elements scriptElements = doc.select(scriptSelector);

   if (!scriptElements.isEmpty()) {
    for (Element element : scriptElements) {
     String scriptData = element.data();

     if (scriptData.contains("\"applicationCategory\":")) {
      ObjectMapper objectMapper = new ObjectMapper();
      try {
       JsonNode rootNode = objectMapper.readTree(scriptData);

       // Convert to hashmap
       datasetMap =
        (HashMap<String, Object>) objectMapper.convertValue(
         rootNode,
         new TypeReference<Map<String, Object>>() {
         }
        );

      } catch (Exception e) {
       Log.e(
        TAG,
        "getDatasetMapFromScriptTag: " + e
       );
      }

      break;

     }
    }
   }

  } catch (Exception e) {
   Log.e(
    TAG,
    "getDatasetMap: " + e
   );
  }

  return datasetMap;
 }

 public String getDescriptionFromDoc(Document doc) {
  StringBuilder description = new StringBuilder();

  //        in python
  //        for content in desc_element.contents:
  //        if content.name not in [
  //                "p",
  //                "div",
  //                "br",
  //                ]:  # some texts that are not description are in p and div.
  //                description += content.strip()

  Element descriptionElement =
   doc.select(descriptionSelector).first();

  if (descriptionElement != null) {
   for (Node node : descriptionElement.childNodes()) {
    if (node instanceof Element) {
     Element childElement = (Element) node;
     String tagName = childElement.tagName();
     String text = childElement.text();
     if (!tagName.equals("p") && !tagName.equals("div") && !tagName.equals("br")) {
      description.append(text);
     }

    } else {
     String text = node.toString().strip();
     description.append(text);
    }
   }
  }

  return description.toString();
 }
}
