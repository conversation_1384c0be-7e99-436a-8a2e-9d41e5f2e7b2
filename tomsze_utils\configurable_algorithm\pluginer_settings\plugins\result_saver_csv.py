"""App plugin"""

import csv
from dataclasses import dataclass
import os
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginResultSaverCsv:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        save_path = parse_data_and_store(
            logger,
            "save_path",
            data_obj,
            step_config,
            config,
        )

        use_manual_input = parse_data_and_store(
            logger,
            "use_manual_input",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        use_dict_input = parse_data_and_store(
            logger,
            "use_dict_input",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        input_data_dict = parse_data_and_store(
            logger,
            "input_data_dict",
            data_obj,
            step_config,
            config,
        )

        assert (use_manual_input or use_dict_input) == True

        if use_manual_input:
            # Get a dict of items to save from step_config.
            dict_save_items = step_config.copy()
            del dict_save_items["step_name"]
            del dict_save_items["type"]
            del dict_save_items["use"]
            del dict_save_items["save_path"]
            del dict_save_items["use_manual_input"]

            if "use_dict_input" in dict_save_items.keys():
                del dict_save_items["use_dict_input"]

            if "input_data_dict" in dict_save_items.keys():
                del dict_save_items["input_data_dict"]

            # Update dict_save_items with parsed data.
            for key in dict_save_items.keys():
                parsed_data = parse_data_and_store(
                    logger,
                    key,
                    data_obj,
                    step_config,
                    config,
                )
                dict_save_items[key] = parsed_data

        if use_dict_input:
            dict_save_items = {}
            for key in input_data_dict.keys():
                dict_save_items[key] = input_data_dict[key]

        # Create save folder if needed.
        save_folder_path = save_path.replace(save_path.split("/")[-1], "")
        if not os.path.exists(save_folder_path):
            os.makedirs(save_folder_path, exist_ok=True)

        write_header = False
        if not os.path.exists(save_path):
            write_header = True

        # Create the csv file.
        f = open(
            save_path,
            "a",
            newline="",
        )
        list_header = list(dict_save_items.keys())

        # Write data to csv file.
        writer = csv.DictWriter(f, fieldnames=list_header)
        with f:
            if write_header:
                writer.writeheader()
            writer.writerow(dict_save_items)

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
