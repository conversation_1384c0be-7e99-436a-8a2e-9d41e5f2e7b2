import shutil
import pytest
import tempfile
import os
from tomsze_utils.script_analyse_utils import (
    extract_function_meta_dict_from_script_in_dir,
    extract_function_names_from_script,
    extract_function_names_from_script_in_dir,
    get_astFuncData_dict_in_script,
    get_astFuncData_list_in_dir,
    get_astFuncData_list_in_script,
    get_function_call_list_from_source_code,
    get_function_name_to_path_dict_in_dir,
    get_refactored_astFuncData_dict_in_dir,
    get_refactored_astFuncData_dict_in_dir_list,
    get_refactored_astFuncData_list_in_dir,
    read_script_file_to_string,
)


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


class TestExtractFunctionNamesFromScript:

    def test_extract_function_names_from_script_valid(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a temporary Python file with some functions
            script_content = """
def function_one():
    '''docstring one'''
    pass

def function_two():
    '''docstring two'''
    pass
"""
            test_filename = "test_script.py"
            script_path = os.path.join(tmpdir, test_filename)
            with open(script_path, "w") as f:
                f.write(script_content)

            # Extract function names from the temporary script
            function_meta_list = extract_function_names_from_script(script_path)
            filenames = [meta.filename for meta in function_meta_list]
            file_paths = [meta.file_path for meta in function_meta_list]
            function_names = [meta.function_name for meta in function_meta_list]
            function_docstrings = [
                meta.function_docstring for meta in function_meta_list
            ]
            function_descriptions = [
                meta.function_description for meta in function_meta_list
            ]
            assert filenames == [test_filename, test_filename]
            assert file_paths == [script_path, script_path]
            assert function_names == ["function_one", "function_two"]
            assert function_descriptions == ["docstring one", "docstring two"]
            assert function_docstrings == ["docstring one", "docstring two"]

    def test_extract_function_names_from_script_ignore_class(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a temporary Python file with some functions
            script_content = """
class ClassA:
    def method_a(self):
        pass
        
def function_one():
    pass

def function_two():
    pass
"""
            script_path = os.path.join(tmpdir, "test_script.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Extract function names from the temporary script
            ignore_class_functions = True
            function_meta_list = extract_function_names_from_script(
                script_path, ignore_class_functions=ignore_class_functions
            )
            function_names = [meta.function_name for meta in function_meta_list]
            assert function_names == ["function_one", "function_two"]

    def test_extract_function_names_from_script_NOT_ignore_class(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a temporary Python file with some functions
            script_content = """
class ClassA:
    def method_a(self):
        pass
        
def function_one():
    pass

def function_two():
    pass
"""
            script_path = os.path.join(tmpdir, "test_script.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Extract function names from the temporary script
            ignore_class_functions = False
            function_meta_list = extract_function_names_from_script(
                script_path, ignore_class_functions=ignore_class_functions
            )
            function_names = [meta.function_name for meta in function_meta_list]
            assert function_names == [
                "method_a",
                "function_one",
                "function_two",
            ]

    def test_extract_function_names_from_script_empty_file(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a temporary empty Python file
            script_path = os.path.join(tmpdir, "empty_script.py")
            open(script_path, "w").close()  # Create an empty file

            # Extract function names from the empty script
            function_meta_list = extract_function_names_from_script(script_path)
            function_names = [meta.function_name for meta in function_meta_list]
            assert function_names == []  # Expecting an empty list for an empty file

    def test_extract_function_names_from_script_ignore_test_functions(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a temporary Python file with some functions
            script_content = """
def test_function_one():
    pass

def function_two():
    pass

def test_function_three():
    pass
    """
            script_path = os.path.join(tmpdir, "test_script.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Extract function names from the temporary script, ignoring test functions
            ignore_class_functions = True
            ignore_test_functions = True
            function_meta_list = extract_function_names_from_script(
                script_path,
                ignore_class_functions=ignore_class_functions,
                ignore_test_functions=ignore_test_functions,
            )
            function_names = [meta.function_name for meta in function_meta_list]
            assert function_names == [
                "function_two"
            ]  # Expecting only function_two to be included

    def test_extract_function_names_from_script_ignore_function_names(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a temporary Python file with some functions
            script_content = """
def main():
    pass

def function_one():
    pass

def function_two():
    pass
            """
            script_path = os.path.join(tmpdir, "script.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Extract function names from the temporary script, ignoring specific function names
            ignore_function_names = ["main"]
            function_meta_list = extract_function_names_from_script(
                script_path,
                ignore_function_names=ignore_function_names,
            )
            function_names = [meta.function_name for meta in function_meta_list]
            assert function_names == [
                "function_one",
                "function_two",
            ]  # Expecting function_one and function_two to be included, but not main

    def test_extract_function_names_from_script_not_ignore_function_names(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create a temporary Python file with some functions
            script_content = """
def main():
    pass

def function_one():
    pass

def function_two():
    pass
                """
            script_path = os.path.join(tmpdir, "script.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Extract function names from the temporary script, ignoring specific function names
            ignore_function_names = []
            function_meta_list = extract_function_names_from_script(
                script_path,
                ignore_function_names=ignore_function_names,
            )
            function_names = [meta.function_name for meta in function_meta_list]
            assert function_names == [
                "main",
                "function_one",
                "function_two",
            ]  # Expecting function_one and function_two to be included, but not main


class TestExtractFunctionNamesFromScriptInDir:

    def test_extract_function_names_from_script_in_dir(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create temporary Python files in the temporary directory
            script_content_1 = """
def function_one():
    pass

def function_two():
    pass
            """
            script_content_2 = """
def function_three():
    pass
            """
            script_path_1 = os.path.join(tmpdir, "script_one.py")
            script_path_2 = os.path.join(tmpdir, "script_two.py")

            with open(script_path_1, "w") as f:
                f.write(script_content_1)
            with open(script_path_2, "w") as f:
                f.write(script_content_2)

            # Extract function names from the temporary directory
            function_meta_list = extract_function_names_from_script_in_dir(tmpdir)
            function_names = [meta.function_name for meta in function_meta_list]
            assert function_names == ["function_one", "function_two", "function_three"]

    def test_extract_function_names_from_script_in_dir_empty_directory(self):
        with tempfile.TemporaryDirectory() as tmpdir:
            # Extract function names from an empty directory
            function_meta_list = extract_function_names_from_script_in_dir(tmpdir)
            function_names = [meta.function_name for meta in function_meta_list]
            assert (
                function_names == []
            )  # Expecting an empty list for an empty directory


class TestExtractFunctionMetaDictFromScriptInDir:

    def test_extract_function_meta_dict_from_script_in_dir(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create temporary Python files in the temporary directory
            script_content_1 = """
def function_one():
    \"\"\"Function one docstring\"\"\"
    pass

def function_two():
    \"\"\"Function two docstring\"\"\"
    pass
            """
            script_content_2 = """
def function_three():
    \"\"\"Function three docstring\"\"\"
    pass
            """
            script_path_1 = os.path.join(temp_dir, "script_one.py")
            script_path_2 = os.path.join(temp_dir, "script_two.py")

            with open(script_path_1, "w") as f:
                f.write(script_content_1)
            with open(script_path_2, "w") as f:
                f.write(script_content_2)

            # Extract function meta dict from the temporary directory
            function_meta_dict = extract_function_meta_dict_from_script_in_dir(temp_dir)
            assert set(function_meta_dict.keys()) == {
                "function_one",
                "function_two",
                "function_three",
            }
            assert (
                function_meta_dict["function_one"].function_description
                == "Function one docstring"
            )
            assert (
                function_meta_dict["function_two"].function_description
                == "Function two docstring"
            )
            assert (
                function_meta_dict["function_three"].function_description
                == "Function three docstring"
            )

    def test_extract_function_meta_dict_from_script_in_dir_empty_directory(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Extract function meta dict from an empty directory
            function_meta_dict = extract_function_meta_dict_from_script_in_dir(temp_dir)
            assert (
                function_meta_dict == {}
            )  # Expecting an empty dict for an empty directory


class TestReadScriptFileToString:

    def test_read_script_file_to_string_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            script_content = "print('Hello, World!')"
            script_path = os.path.join(temp_dir, "test_script.py")
            with open(script_path, "w") as script_file:
                script_file.write(script_content)

            content = read_script_file_to_string(script_path)
            assert content.strip() == script_content.strip()

    def test_read_script_file_to_string_file_not_found(self):
        with pytest.raises(FileNotFoundError):
            read_script_file_to_string("non_existent_file.py")


class TestGetFunctionNameToPathDictInDir:

    def test_get_function_name_to_path_dict_in_dir_with_valid_directory(
        self,
        temp_dir_path,
    ):
        script_content = """
def function_one():
    \"\"\"Function one docstring\"\"\"
    pass

def function_two():
    \"\"\"Function two docstring\"\"\"
    pass
"""
        script_path = os.path.join(temp_dir_path, "test_script.py")
        with open(script_path, "w") as script_file:
            script_file.write(script_content)

        function_name_to_path_dict = get_function_name_to_path_dict_in_dir(
            temp_dir_path
        )
        assert function_name_to_path_dict["function_one"] == script_path
        assert function_name_to_path_dict["function_two"] == script_path

    def test_get_function_name_to_path_dict_in_dir_empty_directory(self, temp_dir_path):
        function_name_to_path_dict = get_function_name_to_path_dict_in_dir(
            temp_dir_path
        )
        assert function_name_to_path_dict == {}


class TestGetFunctionCallsFromSourceCode:

    def test_get_function_call_list_from_source_code_success(self):
        source_code = """
def example():
    my_function()
    another_function()
"""
        expected_calls = ["my_function", "another_function"]
        result = get_function_call_list_from_source_code(source_code)
        assert result == expected_calls

    def test_get_function_call_list_from_source_code_duplicated_call(self):
        source_code = """
def example():
    my_function()
    my_function()
"""
        expected_calls = ["my_function", "my_function"]
        result = get_function_call_list_from_source_code(source_code)
        assert result == expected_calls

    def test_get_function_call_list_from_source_code_no_duplicated_call(self):
        source_code = """
def example():
    my_function()
    my_function()
"""
        expected_calls = ["my_function"]
        result = get_function_call_list_from_source_code(
            source_code,
            is_no_duplicated=True,
        )
        assert result == expected_calls

    def test_get_function_calls_from_source_code_no_calls(self):
        source_code = """
def example():
    pass
"""
        expected_calls = []
        result = get_function_call_list_from_source_code(source_code)
        assert result == expected_calls


class TestGetAstFuncDataListInScript:

    def test_get_astFuncData_list_in_script_valid(self, temp_dir_path):
        script_content = """
def example():
    return "Hello, World!"
"""
        script_path = os.path.join(temp_dir_path, "example_script.py")
        with open(script_path, "w") as script_file:
            script_file.write(script_content)

        ast_func_data_list = get_astFuncData_list_in_script(script_path)
        assert len(ast_func_data_list) == 1
        assert ast_func_data_list[0].function_name == "example"
        assert ast_func_data_list[0].script_path == script_path
        assert ast_func_data_list[0].func_call_list == []

    def test_get_astFuncData_list_in_script_no_functions(self, temp_dir_path):
        script_content = """
# This script has no functions
print("Hello, World!")
"""
        script_path = os.path.join(temp_dir_path, "no_functions_script.py")
        with open(script_path, "w") as script_file:
            script_file.write(script_content)

        ast_func_data_list = get_astFuncData_list_in_script(script_path)
        assert len(ast_func_data_list) == 0


class TestGetAstFuncDataDictInScript:

    def test_get_astFuncData_dict_in_script_valid(self, temp_dir_path):
        script_content = """
def example_function():
    return "Hello, World!"
"""
        script_path = os.path.join(temp_dir_path, "example_script.py")
        with open(script_path, "w") as script_file:
            script_file.write(script_content)

        ast_func_data_dict = get_astFuncData_dict_in_script(script_path)
        assert len(ast_func_data_dict) == 1
        assert "example_function" in ast_func_data_dict
        assert ast_func_data_dict["example_function"].script_path == script_path
        assert ast_func_data_dict["example_function"].func_call_list == []

    def test_get_astFuncData_dict_in_script_no_functions(self, temp_dir_path):
        script_content = """
# This script has no functions
print("Hello, World!")
"""
        script_path = os.path.join(temp_dir_path, "no_functions_script.py")
        with open(script_path, "w") as script_file:
            script_file.write(script_content)

        ast_func_data_dict = get_astFuncData_dict_in_script(script_path)
        assert len(ast_func_data_dict) == 0


class TestGetAstFuncDataListInDir:

    def test_get_astFuncData_list_in_dir_valid(self, temp_dir_path):
        script_content_a = """
def a():
    '''docstring one'''
    return "a"
"""
        script_path_a = os.path.join(temp_dir_path, "fun_a.py")
        with open(script_path_a, "w") as script_file:
            script_file.write(script_content_a)

        script_content_b = """
def b():
    '''docstring two'''
    return "b"
"""
        script_path_b = os.path.join(temp_dir_path, "fun_b.py")
        with open(script_path_b, "w") as script_file:
            script_file.write(script_content_b)

        script_content_c = """
from fun_a import a
from fun_b import b

def c():
    '''docstring two'''
    a()
    b()
"""
        script_path_c = os.path.join(temp_dir_path, "fun_c.py")
        with open(script_path_c, "w") as script_file:
            script_file.write(script_content_c)

        ast_func_data_list = get_astFuncData_list_in_dir(temp_dir_path)
        assert len(ast_func_data_list) == 3
        assert ast_func_data_list[0].function_name == "a"
        assert ast_func_data_list[0].script_path == script_path_a
        assert ast_func_data_list[0].func_call_list == []

        assert ast_func_data_list[1].function_name == "b"
        assert ast_func_data_list[1].script_path == script_path_b
        assert ast_func_data_list[1].func_call_list == []

        assert ast_func_data_list[2].function_name == "c"
        assert ast_func_data_list[2].script_path == script_path_c
        assert ast_func_data_list[2].func_call_list == ["a", "b"]

    def test_get_astFuncData_list_in_dir_empty_directory(self, temp_dir_path):
        ast_func_data_list = get_astFuncData_list_in_dir(temp_dir_path)
        assert ast_func_data_list == []


class TestGetRefactoredAstFuncDataListInDir:

    def test_get_refactored_astFuncData_list_in_dir(self, temp_dir_path):
        script_content_a = """
from fun_b import b
def a():
    '''docstring one'''
    b()
        """
        script_path_a = os.path.join(temp_dir_path, "fun_a.py")
        with open(script_path_a, "w") as script_file:
            script_file.write(script_content_a)

        script_content_b = """
from fun_c import c
def b():
    '''docstring two'''
    c()
        """
        script_path_b = os.path.join(temp_dir_path, "fun_b.py")
        with open(script_path_b, "w") as script_file:
            script_file.write(script_content_b)

        script_content_c = """
from fun_e_f import e, f
def c():
    '''docstring three'''
    e()
    f()
        """
        script_path_c = os.path.join(temp_dir_path, "fun_c.py")
        with open(script_path_c, "w") as script_file:
            script_file.write(script_content_c)

        script_content_e_f = """
def e():
    '''docstring three'''
    return "e"
    
def f():
    '''docstring three'''
    return "f"
        """
        script_path_e_f = os.path.join(temp_dir_path, "fun_e_f.py")
        with open(script_path_e_f, "w") as script_file:
            script_file.write(script_content_e_f)

        ast_func_data_list = get_refactored_astFuncData_list_in_dir(temp_dir_path)
        assert len(ast_func_data_list) == 5
        assert ast_func_data_list[0].func_call_list == ["b", "c", "e", "f"]
        assert ast_func_data_list[1].func_call_list == ["c", "e", "f"]
        assert ast_func_data_list[2].func_call_list == ["e", "f"]
        assert ast_func_data_list[3].func_call_list == []
        assert ast_func_data_list[4].func_call_list == []

        assert ast_func_data_list[0].called_by_func_list == []
        assert ast_func_data_list[1].called_by_func_list == ["a"]
        assert ast_func_data_list[2].called_by_func_list == ["a", "b"]
        assert ast_func_data_list[3].called_by_func_list == ["a", "b", "c"]
        assert ast_func_data_list[4].called_by_func_list == ["a", "b", "c"]

    def test_get_refactored_astFuncData_list_in_dir_empty_directory(
        self, temp_dir_path
    ):
        ast_func_data_list = get_refactored_astFuncData_list_in_dir(temp_dir_path)
        assert ast_func_data_list == []


class TestGetRefactoredAstFuncDataDict:
    def test_get_refactored_astFuncData_dict_in_dir(self, temp_dir_path):
        script_content_a = """
def a():
    '''docstring one'''
    return "a"
        """
        script_path_a = os.path.join(temp_dir_path, "fun_a.py")
        with open(script_path_a, "w") as script_file:
            script_file.write(script_content_a)

        script_content_b = """
from fun_a import a
def b():
    '''docstring two'''
    a()
        """
        script_path_b = os.path.join(temp_dir_path, "fun_b.py")
        with open(script_path_b, "w") as script_file:
            script_file.write(script_content_b)

        ast_func_data_dict = get_refactored_astFuncData_dict_in_dir(temp_dir_path)
        assert len(ast_func_data_dict) == 2
        assert ast_func_data_dict["a"].func_call_list == []
        assert ast_func_data_dict["b"].func_call_list == ["a"]

    def test_get_refactored_astFuncData_dict_in_dir_empty_directory(
        self, temp_dir_path
    ):
        ast_func_data_dict = get_refactored_astFuncData_dict_in_dir(temp_dir_path)
        assert ast_func_data_dict == {}


class TestGetRefactoredAstFuncDataDictInDirList:

    def test_get_refactored_astFuncData_dict_in_dir_list_valid(self, temp_dir_path):
        script_content_a = """
def a():
    return "a"
"""
        temp_dir_path_a = os.path.join(temp_dir_path, "temp_dir_a")
        os.makedirs(temp_dir_path_a, exist_ok=True)
        script_path_a = os.path.join(temp_dir_path_a, "fun_a.py")
        with open(script_path_a, "w") as script_file:
            script_file.write(script_content_a)

        script_content_b = """
def b():
    a()
"""
        temp_dir_path_b = os.path.join(temp_dir_path, "temp_dir_b")
        os.makedirs(temp_dir_path_b, exist_ok=True)
        script_path_b = os.path.join(temp_dir_path_b, "fun_b.py")
        with open(script_path_b, "w") as script_file:
            script_file.write(script_content_b)

        ast_func_data_dict = get_refactored_astFuncData_dict_in_dir_list(
            [temp_dir_path_a, temp_dir_path_b]
        )
        assert len(ast_func_data_dict) == 2
        assert ast_func_data_dict["a"].func_call_list == []
        assert ast_func_data_dict["b"].func_call_list == ["a"]

    def test_get_refactored_astFuncData_dict_in_dir_list_empty(self, temp_dir_path):
        ast_func_data_dict = get_refactored_astFuncData_dict_in_dir_list(
            [temp_dir_path]
        )
        assert ast_func_data_dict == {}
