package com.kewtoms.whatappsdo.model.AppSearcher;

import static com.kewtoms.whatappsdo.data.Constants.post_req_url_search_user_app_link;
import static com.kewtoms.whatappsdo.utils.RequestUtils.getMockUrlFromTestIntentOrProdUrl;
import static com.kewtoms.whatappsdo.utils.RequestUtils.sendPostEncryptedJsonVolley;
import static com.kewtoms.whatappsdo.utils.RequestUtils.sendPostJsonVolley;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScanAppData;
import com.kewtoms.whatappsdo.interfaces.AppSearcherCallback;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class AppSearcher {
  public static final String TAG = "APP:AppSearcher";
  private final Context context;
  private final AppSearcherCallback callback;

  /**
   * Constructor: AppSearcher Initializes the AppSearcher with the
   * given context and callback.
   *
   * @param context The context object, used within AppSearcher to
   * access or manipulate resources and system services of the Android
   * application.
   * @param callback A callback interface object. When AppSearcher
   * finds an app that meets certain criteria, it will call this
   * callback to notify the caller.
   */
  public AppSearcher(
    Context context,
    AppSearcherCallback callback) {
    this.context = context;
    this.callback = callback;
  }


  /**
   * Searches for applications based on the provided search text.
   * <p>
   * This method constructs a JSON object containing the data required
   * for searching applications and sends it to the server. The
   * response is then used to update the RecyclerView with the search
   * results.
   *
   * @param search_text The search text entered by the user.
   * @param scanAppData An object containing application data,
   * including app names, package names, and image paths.
   */
  public void searchApp(
    String search_text,
    ScanAppData scanAppData)
    throws
    InterruptedException {
    // Create a JSON object that fits the server-side Pydantic model
    // Server-side code example:
    // class ClientSearchData(BaseModel):
    //     allowSearch: bool = False
    //     appIds: List[str] = []
    //     downloadSource: List[str] = []
    //     query: str = ""

    String methodName = "searchApp";

    // Log if search_text is empty
    if (search_text.isEmpty()) {
      Log.i(
        TAG,
        "Search text is empty. End of search."
      );
      return;
    } else
      Log.i(
        TAG,
        "Searching for app: " + search_text
      );

    JSONObject jsonBodyJSONObject = new JSONObject();
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "allowSearch",
        true
      );
      dataJSONObject.put(
        "appIds",
        new JSONArray(scanAppData.getPackageNames())
      );
      //      jsonBody.put(f
      dataJSONObject.put(
        "query",
        search_text
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;
    // Put dataJSONObject into jsonBodyJSONObject
    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      Log.i(
        TAG,
        methodName + ": " + e
      );
      throw new RuntimeException(e);
    }

    Log.i(
      TAG,
      "jsonBody:" + jsonBodyJSONObject.toString()
    );

    // Send a POST request
    String url = getMockUrlFromTestIntentOrProdUrl(
      context,
      Constants.mockSearchUserAppUrlKey,
      post_req_url_search_user_app_link
    );
    Log.i(
      TAG,
      methodName + ": " + "Sending request to:" + url
    );
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      true,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          // Handle the successful response
          Log.i(
            TAG,
            "onPostSuccess: responseJsonObj:" + responseJsonObj.toString()
          );

          if (responseJsonObj.has(Constants.response_json_error_key)) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_SHORT
            );
            toast.show();
            return;
          }

          // Extract the result from the response
          JSONObject resultJSONObj =
            (JSONObject) responseJsonObj.opt(Constants.response_json_result_key);
          if (resultJSONObj == null) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_LONG
            );
            toast.show();
            return;
          }

          JSONArray packageNamesJsonArray =
            (JSONArray) resultJSONObj.opt("packageNames");
          JSONArray scoresJsonArray =
            (JSONArray) resultJSONObj.opt("scores");

          if (packageNamesJsonArray == null || scoresJsonArray == null) {
            return;
          }

          // Update the RecyclerView with the search results
          List<String> newAppNames = new ArrayList<>();
          List<String> newPackageNames = new ArrayList<>();
          List<String> newImagePaths = new ArrayList<>();
          for (int i = 0; i < packageNamesJsonArray.length(); i++) {
            try {
              String packageName = packageNamesJsonArray.getString(i);
              newPackageNames.add(packageName);

              int index =
                scanAppData.getPackageNames().indexOf(packageName);
              newAppNames.add(scanAppData.getAppNames().get(index));
              newImagePaths.add(scanAppData.getImagePaths()
                .get(index));

            } catch (JSONException e) {
              throw new RuntimeException(e);
            }
          }

          // Notify the callback that the search is complete
          callback.onSearchDone(
            newAppNames,
            newPackageNames,
            newImagePaths
          );
        }

        @Override
        public void onPostError(String errorMessage) {
          // Handle the error
          Log.e(
            TAG,
            errorMessage
          );
        }
      }

    );
  }


}
