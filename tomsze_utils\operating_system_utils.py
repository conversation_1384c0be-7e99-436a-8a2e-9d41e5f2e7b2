def get_active_window_title() -> str:
    import pywinctl as pwc

    """
    Retrieve the title of the currently active window.

    Returns:
        str: The title of the active window.

    Args:
        None

    Examples:
        ```python
        title = get_active_window_title()  # returns the title of the active window
        ```

        ```python
        active_title = get_active_window_title()  # active_title will hold the title of the current active window
        ```
    """
    return pwc.getActiveWindowTitle()


import ctypes
from ctypes import wintypes
from collections import namedtuple

# Define a structure to hold window information
WindowInfo = namedtuple("WindowInfo", "hwnd title pid")


def get_opened_windows():
    """
    Retrieve a list of all opened windows and their process IDs.

    Returns:
        List[WindowInfo]: A list of WindowInfo namedtuples containing window handle, title, and process ID.

    Note:
        This function is for Windows only.
    """
    windows = []

    # Callback function to be called for each window
    def foreach_window(hwnd, lParam):
        # Check if the window is visible
        if ctypes.windll.user32.IsWindowVisible(hwnd):
            # Get the length of the window title
            length = ctypes.windll.user32.GetWindowTextLengthW(hwnd)
            buff = ctypes.create_unicode_buffer(length + 1)
            # Get the window title
            ctypes.windll.user32.GetWindowTextW(hwnd, buff, length + 1)
            title = buff.value

            # Get the process ID associated with the window
            pid = wintypes.DWORD()
            ctypes.windll.user32.GetWindowThreadProcessId(hwnd, ctypes.byref(pid))

            # Append window info to the list
            windows.append(WindowInfo(hwnd, title, pid.value))
        return True

    # Enumerate all windows
    EnumWindows = ctypes.windll.user32.EnumWindows
    EnumWindows(
        ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)(
            foreach_window
        ),
        0,
    )

    return windows


import psutil


def get_system_usage():
    """
    Return the current system CPU and RAM usage.

    Returns:
        tuple: (cpu_percent, ram_usage_gb, total_ram_gb, ram_percent)
            - cpu_percent (float): CPU usage as a percentage.
            - ram_usage_gb (float): RAM usage in GB.
            - total_ram_gb (float): Total RAM in GB.
            - ram_percent (float): RAM usage as a percentage.
    """
    cpu_percent = psutil.cpu_percent(interval=0.1)
    ram = psutil.virtual_memory()
    ram_usage_gb = round(ram.used / (1024 * 1024 * 1024), 2)
    total_ram_gb = round(ram.total / (1024 * 1024 * 1024), 2)
    ram_percent = ram.percent
    return cpu_percent, ram_usage_gb, total_ram_gb, ram_percent


# Example usage
if __name__ == "__main__":
    opened_windows = get_opened_windows()
    for window in opened_windows:
        print(f"HWND: {window.hwnd}, Title: {window.title}, PID: {window.pid}")

    usage = get_system_usage()
    print(usage)
