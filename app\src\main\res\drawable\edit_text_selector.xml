<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape>
            <!--            commented background color-->
            <!--            <solid android:color="#FFFFFF"/>-->
            <stroke android:width="2dp" android:color="#6200EE" /> <!-- Border color when focused -->
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape>
            <!--            commented background color-->
            <!--            <solid android:color="#FFFFFF"/>-->
            <stroke android:width="2dp" android:color="#9E9E9E" /> <!-- Default border color -->
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>