"""App plugin"""

from dataclasses import dataclass
from tomsze_utils.configurable_algorithm.configurable_algorithm import CA
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginSettingRunner:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        config_folder_path = parse_data_and_store(
            logger,
            "config_folder_path",
            data_obj,
            step_config,
            config,
        )

        algo_setting_filename = parse_data_and_store(
            logger,
            "algo_setting_filename",
            data_obj,
            step_config,
            config,
        )

        ca = CA(
            config_folder_path=config_folder_path,
            algo_setting_filename=algo_setting_filename,
        )
        ca.run()

        # buffer_to_which = parse_data_and_store(
        #     logger,
        #     "buffer_to_which",
        #     data_obj,
        #     step_config,
        #     config,
        #     type="int",
        #     default=0,
        # )

        # Add results to data_obj. (The key will be concatenated with .)
        for key in ca.data_obj.dict_var.keys():
            result = ca.data_obj.dict_var[key]
            data_obj.dict_var[f"{current_step}.{key}"] = result

        print(f"{self.type} ends")
        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
