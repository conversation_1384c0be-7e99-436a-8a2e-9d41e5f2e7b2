import logging
import subprocess
from tomsze_utils.byte_utils import try_decode_byte
from tomsze_utils.string_utils import split_command


def subprocess_run(
    command: str,
    shell: bool = True,
    capture_output: bool = False,
):  # do not return subprocess.CompletedProcess[bytes], or else import error, not sure why yet
    """
    Executes a shell command, logs it, and returns the subprocess.run result.

    Args:
        command (str): Shell command to execute.
        shell (bool, optional): Execute through shell if True. Defaults to True.
        capture_output (bool, optional): Capture stdout and stderr if True. Defaults to False.

    Returns:
        subprocess.CompletedProcess: Result of subprocess.run execution.
    """
    list_split = split_command(command)
    logging.info(command)

    return subprocess.run(list_split, shell=shell, capture_output=capture_output)


def subprocess_run_with_str_output(
    command: str,
    shell: bool = True,
    cwd: str = "./",
) -> tuple:
    """
    Executes a shell command, logs it, captures output as a string, and returns the subprocess.run result along with the output string.

    Args:
        command (str): Shell command to execute.
        shell (bool, optional): Execute through shell if True. Defaults to True.
        cwd (str, optional): The working directory to execute the command in. Defaults to "./".

    Returns:
        tuple: A tuple containing the subprocess.CompletedProcess object and the output string.

    Examples:
        >>> out, output_str = subprocess_run_with_str_output("echo Hello, World!")
        >>> print(output_str)
        Hello, World!
    """
    output_str = ""
    list_split = split_command(command)
    logging.info(command)
    out = subprocess.run(
        list_split,
        shell=shell,
        capture_output=True,
        cwd=cwd,
    )

    if out.stdout:
        output_str = try_decode_byte(out.stdout)

    if out.stderr:
        output_str = try_decode_byte(out.stderr)

    return out, output_str


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.DEBUG,  # if logging.INFO, logger.debug('xx') will not be logged
        format="(%(asctime)s.%(msecs)03d, %(levelname)s, %(module)s, %(funcName)s) %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    result = subprocess_run(
        "dir", capture_output=False
    )  # use ls to show hidden file only

    result = subprocess_run("echo kk")
    logging.info(result)
    # CompletedProcess(args=['echo', 'kk'], returncode=0, stdout=b'kk\r\n', stderr=b'')
