from prettytable import PrettyTable
import platform
import time
import datetime
from cpuinfo import get_cpu_info
from typing import Any, Callable, List, Tu<PERSON>
from functools import partial
from tqdm import tqdm
from dataclasses import dataclass


@dataclass
class BenchFunc:
    partial_func: Callable
    note: str = ""
    num_times_individual: int = -1


def partial_funcA() -> Callable:
    """
    Return a partial function (with argument) to be
    benchmarked in benchmark_func.

    Examples:
        >>> partial_function = partial_funcA()
        >>> partial_function()  # This will print "aa" after a delay of 1 second.

        >>> another_partial_function = partial_funcA()
        >>> another_partial_function()  # This will also print "aa" after a delay of 1 second.
    """
    return partial(funcA, "aa")


def funcA(arg: Any) -> str:
    """
    The function to be bencharmarked final.
    """
    time.sleep(1)
    print(arg)
    return "done"


def benchmark_func(
    num_times: int = 100,
    func: Callable = None,
) -> Tuple[str, float]:
    """
    Benchmark a function by measuring the average time taken to execute it a specified number of times.

    Args:
        num_times (int): The number of times to execute the function. Default is 100.
        func (Callable): The function to benchmark. It can be a normal function or a partial function.

    Returns:
        Tuple[str, float]: A Tuple containing the function name and the average time used in milliseconds.

    Examples:
    ```python
    # Example 1: Benchmark a simple function
    def simple_function():
        time.sleep(0.1)

    func_name, avg_time = benchmark_func(num_times=10, func=simple_function)
    print(func_name, avg_time)  # Outputs the function name and average time

    # Example 2: Benchmark a partial function
    from functools import partial

    partial_function = partial(funcA, "example")
    func_name, avg_time = benchmark_func(num_times=5, func=partial_function)
    print(func_name, avg_time)  # Outputs the function name and average time
    ```
    """
    avg_time_used = 0
    func_name = None
    time0 = time.time()
    if func:
        # func_name = func.__name__ # for normal function
        func_name = func.func.__name__  # for partial function
        print(f"Benchmarking function: {func_name}")
        for n in tqdm(range(num_times)):
            # for n in range(num_times):
            func()
    time1 = time.time()
    avg_time_used = (time1 - time0) * 1000 / num_times
    return func_name, avg_time_used


def benchmark_funcs(
    num_times: int = 100,
    list_benchfunc: List[BenchFunc] = [],
) -> str:
    """
    Output a benchmark string including the OS, CPU, number of tests, and date time.

    Args:
        num_times (int): The number of times to execute each benchmark function. Default is 100.
        list_benchfunc (List[BenchFunc]): A list of benchmark functions to test.

    Returns:
        str: A formatted string containing the benchmark results.

    Examples:
    ```python
    # Example 1: Benchmarking with default number of times
    benchmark_result = benchmark_funcs(list_benchfunc=[BenchFunc(partial_funcA())])
    print(benchmark_result)

    # Example 2: Benchmarking with a specified number of times for each function
    benchmark_result = benchmark_funcs(num_times=50, list_benchfunc=[BenchFunc(partial_funcA(), num_times_individual=10)])
    print(benchmark_result)
    ```
    """
    os = platform.system()
    cpu_info = get_cpu_info()
    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H:%M:%S")

    # Create table
    table = PrettyTable()

    # Add column field name.
    table.field_names = ["func", "time_used(ms)", "note"]

    # Run tests
    for benchfunc in list_benchfunc:
        # Run test for a function.
        func = benchfunc.partial_func
        num_times_individual = benchfunc.num_times_individual
        num_times_new = num_times
        if num_times_individual > 0:
            num_times_new = num_times_individual
        func_name, avg_time_used = benchmark_func(
            num_times=num_times_new,
            func=func,
        )
        note = benchfunc.note

        # Add row.
        table.add_row([func_name, "{:.4f}".format(avg_time_used), note])

    table_string = table.get_string()
    benchmark_result = f"""os: {os}
cpu: {cpu_info.get('brand_raw')}, {cpu_info.get('hz_actual_raw')}, {cpu_info.get('count') / 2} cores
cpu arch: {cpu_info.get('arch')}
host_name: {platform.node()}
num_times_per_function: {num_times}
time_used: The average time used (in ms)
test_datetime: {time_str}

{table_string}"""
    return benchmark_result


if __name__ == "__main__":

    list_func = [
        BenchFunc(partial_funcA()),
        BenchFunc(partial_funcA(), "a note"),
    ]
    benchmark_string = benchmark_funcs(
        num_times=3,
        list_benchfunc=list_func,
    )
    print(benchmark_string)

"""
os: Windows
cpu: Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz, None, 6.0 cores
cpu arch: X86_64
host_name: xx
num_times_per_function: 3
time_used: The average time used (in ms)
test_datetime: 2024-05-17 11:04:02

+-------+---------------+--------+
|  func | time_used(ms) |  note  |
+-------+---------------+--------+
| funcA |   1016.3095   |        |
| funcA |   1007.4077   | a note |
+-------+---------------+--------+
"""
