from typing import Dict, List
from tomsze_utils.variable_dump_read_utils import (
    dump_variable_to_pickle,
    load_pickle_file,
)


class PickleDatabase:
    """
    This database wraps a pickle file and
    provides some methods.

    The pickle file is just a dict.
    ```
    {
        'xx':
            {
                'aa': any,
                'cc': any,
            },
        'yy':
            {
                'aa': any,
                'cc': any,
            },
    }
    ```
    where aa and cc must be consistent for each data.

    """

    def __init__(
        self,
        pickle_db_path,
    ):
        self.pickle_db_path = pickle_db_path
        self.loaded = load_pickle_file(
            self.pickle_db_path,
            {},
        )
        self.data_inconsistency = {}

    def update_data(
        self,
        data: Dict,
    ):
        """
        Update (also insert) to the loaded data
        by ** operator.
        """
        self.loaded = {**self.loaded, **data}

        print(f"self.loaded: {self.loaded}")

    def dump_to_pickle(self):
        """
        Simple wrap around the dump_to_pickle
        function from tomsze_utils.
        """
        dump_variable_to_pickle(
            variable=self.loaded,
            pickle_path=self.pickle_db_path,
        )

    def check_data_consistency(self):
        """
        Check data consistency by comparing
        the first data's datatype and the
        rest of the data.

        Store and return the data that is not consistent.
        """
        # data_type = None
        # if self.loaded and len(self.loaded) > 1:
        #     for ind, data_key in enumerate(self.loaded):
        #         if ind == 0:
        #             data_type = type(self.loaded[data_key])
        #             continue

        #         if data_type != type(self.loaded[data_key]):
        #             # Store and return the data that is not consistent.
        #             self.data_inconsistency = {
        #                 data_key: self.loaded[data_key],
        #             }
        #             return self.data_inconsistency

        # self.data_inconsistency = {}
        # return self.data_inconsistency

        if not self.loaded or len(self.loaded) <= 1:
            self.data_inconsistency = {}
            return self.data_inconsistency

        data_type = None
        for data_key in self.loaded:
            if data_type is None:
                data_type = type(self.loaded[data_key])
            elif data_type != type(self.loaded[data_key]):
                self.data_inconsistency = {
                    data_key: self.loaded[data_key],
                }
                return self.data_inconsistency

        self.data_inconsistency = {}
        return self.data_inconsistency

    def query_as_list(
        self,
        keys: List[str],
        column: str,
    ):
        """
        Query on certain data (certain key) on one "column" and return as a list.
        """
        query_result = [self.loaded[key][column] for key in keys if key in self.loaded]

        return query_result
