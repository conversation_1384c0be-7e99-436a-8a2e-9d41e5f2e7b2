from tomsze_utils.whls_folder_file_parser_utils import parse_whls_in_folder_to_dict


def test_parse_whls_in_folder_to_dict():
    whls_folder_path = r"./tests/fake_whls"

    package_dict = parse_whls_in_folder_to_dict(folder_path=whls_folder_path)

    assert package_dict["package1"] == {
        "front_filename": "package1-0.11.0-py3-none-any",
        "version": "0.11.0",
        "file_name": "package1-0.11.0-py3-none-any.whl",
        "file_path": "./tests/fake_whls/package1-0.11.0-py3-none-any.whl",
    }

    assert package_dict["package2"] == {
        "front_filename": "package2-0.11.1-py3-none-any",
        "version": "0.11.1",
        "file_name": "package2-0.11.1-py3-none-any.whl",
        "file_path": "./tests/fake_whls/package2-0.11.1-py3-none-any.whl",
    }


if __name__ == "__main__":
    test_parse_whls_in_folder_to_dict()
