# https://stackoverflow.com/questions/37944111/python-rolling-log-to-a-variable
# This is the selected answer
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler
import os
import shutil
import sys
import logging
import collections
from typing import Literal
import json

from tomsze_utils.text_file_modify_utils import append_string_to_file
from tomsze_utils.randome_utils import generate_random_string

import inspect  # Import inspect to get the current frame information

GREEN = "\033[32m"
YELLOW = "\033[33m"
RED = "\033[31m"
GRAY = "\033[90m"
RESET = "\033[0m"


class SimpleLogger:
    _instance = None
    _config = None

    def __new__(cls, *args, **kwargs):
        """Create a singleton instance of SimpleLogger before running test_hash_password_is_different_for_same_input."""
        get_existing = kwargs.get("get_existing_instance", True)
        if get_existing:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance

        cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(
        self,
        logs_folder_path: str = "./logs",
        log_base_name: str = "log",
        new_file_for_each: Literal["hour", "day", "month"] = "month",
        format: str = "time: %(asctime)s lv: %(lv)s message: %(message)s fileName: %(filename)s lineNo: %(lineno)d",
        datefmt: str = "%m/%d/%Y %I:%M:%S %p",
        get_existing_instance: bool = True,
        config_path: str = "./logger_config.json",
        id: str = None,
    ) -> None:
        """
        Initializes the SimpleLogger instance.

        Note: For the script that is main entry, use get_existing_instance=False and before
        import any other modules that might use the logger.

        Args:
            logs_folder_path (str): The folder path where logs will be stored.
            log_base_name (str): The base name for the log files.
            new_file_for_each (Literal["hour", "day", "month"]): Frequency for creating new log files.
            format (str): The format of the log messages.
            datefmt (str): The format for the date in log messages.
            get_existing_instance (bool): Flag to get an existing instance of the logger.
            get_existing_instance(bool): Flag to get an existing instance of the logger.
            config_path (str): Path to the logger configuration file.
            id (str): Unique identifier for the logger instance.

        Examples:
            ```python
            logger = SimpleLogger(logs_folder_path="./my_logs", log_base_name="app_log")
            ```

            ```python
            logger = SimpleLogger(new_file_for_each="day", get_existing_instance=False)
            ```
        """
        if not hasattr(self, "initialized"):
            # load or memoize config
            if SimpleLogger._config is None:
                try:
                    with open(config_path, "r") as cf:
                        SimpleLogger._config = json.load(cf).get("loggers", {})
                except FileNotFoundError:
                    SimpleLogger._config = {}

            logs_folder_path = os.path.realpath(logs_folder_path)
            self.logs_folder_path = logs_folder_path
            self.log_base_name = log_base_name
            self.new_file_for_each = new_file_for_each
            self.format = format
            self.datefmt = datefmt
            self.get_existing_instance = get_existing_instance
            self.id = id or self._generate_random_id()

            self.log_file_path = self._generate_log_file_path()
            self._create_log_folder_when_not_exist()
            self._initialize_log_file_when_empty()

            self.initialized = True

    def _generate_random_id(self, length=5):
        import random, string

        return "".join(random.choices(string.ascii_letters + string.digits, k=length))

    def _generate_log_file_path(self) -> str:
        """Generate the log file path based on the current time and specified frequency."""
        current_time = datetime.now()
        time_format_dict = {"hour": "%Y-%m-%d_%H", "day": "%Y-%m-%d", "month": "%Y-%m"}
        time_format = time_format_dict.get(self.new_file_for_each)

        if time_format is None:
            raise ValueError(
                "new_file_for_each must be either 'hour', 'day', or 'month'"
            )

        return os.path.join(
            self.logs_folder_path,
            f"{self.log_base_name}_{current_time:{time_format}}.log",
        )

    def _create_log_folder_when_not_exist(self):
        """Create the log folder if it does not exist."""
        if not os.path.exists(self.logs_folder_path):
            print(f"creating: {self.logs_folder_path}")
            os.makedirs(self.logs_folder_path)

    def _initialize_log_file_when_empty(self):
        """Initialize the log file if it does not already exist."""
        if not os.path.exists(self.log_file_path):
            with open(self.log_file_path, "w") as file:
                file.write("")  # Initialize the log file as empty

    def _log(self, level: str, text: str):
        # determine caller filename
        caller_frame = inspect.stack()[2]
        script_file = os.path.basename(caller_frame.filename)

        if SimpleLogger._config:
            self.enabled = SimpleLogger._config.get(script_file, True)

            if isinstance(self.enabled, dict):
                self.enabled = self.enabled.get("enabled", None)

            if not self.enabled:
                return

        if not isinstance(text, str) or not text.strip():
            raise ValueError("The log message must be a non-empty string.")

        # regenerate path and folder in case time rolled over
        self.log_file_path = self._generate_log_file_path()
        self._create_log_folder_when_not_exist()
        self._initialize_log_file_when_empty()

        # Get the current frame and extract the line number
        lineno = caller_frame.lineno

        formatted_message = self.format % {
            "lv": level,
            "message": text,
            "asctime": datetime.now().strftime(self.datefmt),
            "lineno": lineno,
            "filename": script_file,
        }

        # Print the log message to the console with color coding
        if level == "INFO":
            color = GREEN
        elif level == "WARNING":
            color = YELLOW
        elif level == "ERROR":
            color = RED
        elif level == "DEBUG":
            color = GRAY
        else:
            color = RESET

        formatted_message_to_print = self.format % {
            "lv": f"{color}{level}{RESET}",
            "message": f"{color}{text}{RESET}",
            "asctime": datetime.now().strftime(self.datefmt),
            "lineno": lineno,
            "filename": f"{color}{script_file}{RESET}",
        }

        print(f"{formatted_message_to_print}")

        # Append the log message to the log file
        append_string_to_file(
            file_path=self.log_file_path,
            string_to_append=formatted_message,
        )

    def info(self, text: str):
        """Log an informational message to the log file."""
        self._log("INFO", text)

    def warning(self, text: str):
        """Log a warning message to the log file."""
        self._log("WARNING", text)

    def error(self, text: str):
        """Log an error message to the log file."""
        print(f"{RED}=================================================={RESET}")
        self._log("ERROR", text)
        print(f"{RED}=================================================={RESET}")

    def debug(self, text: str):
        """Log a debug message to the log file."""
        self._log("DEBUG", text)

    def get_log_file_path(self) -> str:
        """Returns the current log file path."""
        return self.log_file_path

    def get_id(self) -> str:
        return self.id


class TailLogHandler(logging.Handler):

    def __init__(self, log_queue):
        logging.Handler.__init__(self)
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.append(self.format(record))

        "record contains variables used for format like name, levelname..."


class TailLogger(object):

    def __init__(self, maxlen):
        self._log_queue = collections.deque(maxlen=maxlen)
        self._log_handler = TailLogHandler(self._log_queue)

    def contents(self):
        return "\n".join(self._log_queue)

    @property
    def log_handler(self):
        return self._log_handler


def remove_all_logging_handlers(logger: logging.Logger) -> logging.Logger:
    """
    Remove all logging handlers from the specified logger.

    Args:
        logger (logging.Logger): The logger from which to remove handlers.

    Examples:
        ```python
        remove_all_logging_handlers(logger=my_logger)
        ```

        ```python
        remove_all_logging_handlers(logger=logging.getLogger("my_custom_logger"))
        ```
    """
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    return logger


class MonthlyRotatingFileHandler(TimedRotatingFileHandler):
    def __init__(self, filename, **kwargs):
        super().__init__(filename, when="midnight", interval=1, **kwargs)
        self.current_month = datetime.now().strftime("%Y_%m")

    def shouldRollover(self, record):
        # Check if the current month has changed
        current_month = datetime.now().strftime("%Y_%m")
        if current_month != self.current_month:
            self.current_month = current_month
            return True
        return super().shouldRollover(record)

    def namer(self, default_name):
        # Create a timestamp in the desired format (e.g., YYYY-MM)
        timestamp = datetime.now().strftime("%Y-%m")
        # Rename the log file with the timestamp
        base_name = default_name[:-11]  # Remove date after *.log.
        return f"{base_name}.{timestamp}"


def setup_logger(
    logger_name: str = "root_logger",
    format: str = "time: %(asctime)s || lv: %(levelname)s || msg: %(message)s || fileName: %(filename)s || moduleName: %(name)s || funcName: %(funcName)s() || lineNo: %(lineno)d",
    datefmt: str = "%m/%d/%Y %I:%M:%S %p",
    loglevel: str = "logging.DEBUG",
    is_clear_log_folder: bool = False,  # Clear the log folder
    is_log_to_file: bool = True,
    log_to_folder_path: str = "./logs",
    log_filename: str = "log.log",
    print_to_screen: bool = True,
    use_rotating_log: bool = False,
    maxKB: int = 10,  # Default value updated to 10KB
    backupCount: int = 10,  # Default backup count
    unlimited_backup: bool = False,
    is_use_time_rotating: bool = False,
    rotate_at_every: str = "Month",  # Rotate at every month by default
) -> logging.Logger:
    """
    Setup the root logger with specified configurations.

    Args:
        logger_name (str): The name of the logger.
        format (str): The format of the log messages, including optional prefixes.
        datefmt (str): The format of the date in log messages.
        loglevel (str): The logging level (e.g., 'logging.DEBUG').
        is_clear_log_folder (bool): Flag to clear the log folder before logging.
        is_log_to_file (bool): Flag to log to a file.
        log_to_folder_path (str): The folder path to save log files.
        log_filename (str): The name of the log file.
        print_to_screen (bool): Flag to print logs to the console.
        use_rotating_log (bool): Flag to use rotating log files.
        maxKB (int): Maximum size of the log file before rotation in KB. (not effective when is_use_time_rotating is True)
        backupCount (int): Number of backup files to keep.
        unlimited_backup (bool): Flag to allow unlimited backup files.
        is_use_time_rotating (bool): Flag to use time-based rotation (only effective when use_rotating_log is True).
        rotate_at_every (str): The interval for rotating logs (default is "M" for monthly).

    Returns:
        logging.Logger: The configured logger instance.
    """
    # Setup the root logger
    logger = logging.getLogger(logger_name)  # Use logger_name for the logger

    # Call this function to remove handlers
    logger = remove_all_logging_handlers(logger=logger)

    if is_clear_log_folder and os.path.exists(log_to_folder_path):
        shutil.rmtree(log_to_folder_path)  # Clear the log folder
        os.makedirs(log_to_folder_path, exist_ok=True)  # Recreate the folder

    log_path = os.path.join(log_to_folder_path, log_filename)
    if not os.path.exists(log_to_folder_path):
        os.makedirs(log_to_folder_path, exist_ok=True)

    if is_log_to_file:
        if use_rotating_log:
            if is_use_time_rotating and rotate_at_every == "Month":
                file_handler = MonthlyRotatingFileHandler(
                    filename=log_path,
                    backupCount=(
                        0 if unlimited_backup else backupCount
                    ),  # Set to 0 for unlimited backup
                    encoding="utf-8",
                )
            elif is_use_time_rotating:

                file_handler = TimedRotatingFileHandler(
                    filename=log_path,
                    when=rotate_at_every,  # Rotate at the specified interval
                    backupCount=(
                        0 if unlimited_backup else backupCount
                    ),  # Set to 0 for unlimited backup
                    encoding="utf-8",
                )
            else:
                from logging.handlers import RotatingFileHandler

                file_handler = RotatingFileHandler(
                    filename=log_path,
                    maxBytes=maxKB * 1024,  # Convert KB to bytes
                    backupCount=(
                        0 if unlimited_backup else backupCount
                    ),  # Set to 0 for unlimited backup
                    encoding="utf-8",
                )
            file_handler.setFormatter(logging.Formatter(format, datefmt))
            logger.addHandler(file_handler)
        else:
            file_handler = logging.FileHandler(log_path, encoding="utf-8")
            file_handler.setFormatter(logging.Formatter(format, datefmt))
            logger.addHandler(file_handler)
    else:
        stream_handler = logging.StreamHandler()
        stream_handler.setFormatter(logging.Formatter(format, datefmt))
        logger.addHandler(stream_handler)

    if print_to_screen:
        logger.addHandler(logging.StreamHandler(sys.stdout))  # also print to screen.

    logger.setLevel(eval(loglevel))

    return logger


def main():
    tail = TailLogger(3)
    logger = logging.getLogger(__name__)

    log_handler = tail.log_handler
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s: %(message)s")
    log_handler.setFormatter(formatter)
    logger.addHandler(log_handler)

    # levels = [logging.INFO, logging.ERROR, logging.WARN, logging.DEBUG, logging.CRITICAL]
    logger.setLevel(logging.INFO)

    for i in range(10):
        logger.log(logging.INFO, "Message {}".format(i))

    print("---------------------")
    print(tail.contents())


if __name__ == "__main__":
    sys.exit(main())
