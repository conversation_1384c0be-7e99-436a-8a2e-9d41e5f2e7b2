import json
import sys
import os
from const_path import proj_ser_api_db_early_path
from server.api.utils.descriptions.get_description import scrape_play_store


def main():
    source_txt_file_path = os.path.join(
        proj_ser_api_db_early_path,
        "check_android_system_apps",
        "android_system_apps.txt",
    )
    result_txt_file_path = os.path.join(
        proj_ser_api_db_early_path,
        "check_android_system_apps",
        "result.txt",
    )

    # Read txt line by line and get the app id
    file = open(source_txt_file_path)

    lines = file.readlines()

    dict_appId_isApp = {}
    for i, line in enumerate(lines):
        app_id = line.replace("\n", "")

        # Scrap from playstore to check.
        scrap_success, result = scrape_play_store(
            device_app_id=app_id,
            lang="en",
        )
        dict_appId_isApp[app_id] = scrap_success

    # print(dict_appId_isApp)

    # Write result to txt file.
    with open(result_txt_file_path, "w") as file:
        file.write(json.dumps(dict_appId_isApp, indent=4))


if __name__ == "__main__":
    sys.exit(main())
