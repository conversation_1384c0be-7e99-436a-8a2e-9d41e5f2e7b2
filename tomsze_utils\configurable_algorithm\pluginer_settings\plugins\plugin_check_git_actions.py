"""App plugin"""

from dataclasses import dataclass
import os
from typing import List
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.dict_utils import extract_data_from_dict_contains
from tomsze_utils.subprocess_utils import subprocess_run_with_str_output
import logging
import re


@dataclass
class PluginCheckGitActions:
    type: str
    plugin_can_change_files: bool

    def will_file_changes_trigger(self, list_collected_changed_files, dict_app_data):
        trigger_plugin = False
        changed_file_list = []

        trigger_plugin = False
        for changed_file in list_collected_changed_files:

            # Check monitor file.
            for monitor_file in [
                ".*/logs/HEAD",
                ".*/index$",
            ]:
                if re.match(f"{monitor_file}", changed_file):
                    logging.info(
                        f'File {monitor_file} has changed, will trigger plugin "{self.type}".'
                    )

                    trigger_plugin = True
                    changed_file_list.append(changed_file)

        logging.info(f'File changes does not trigger plugin "{self.type}".')

        return trigger_plugin, changed_file_list

    def find_git_action(self, changed_file_list, dict_app_data):
        git_action = None
        git_message = None

        logging.info("----------find_git_action----------")
        for changed_file in changed_file_list:
            if re.match(f".*/logs/HEAD", changed_file):
                # Read commit message.
                with open(changed_file, "r") as f:
                    list_lines = f.readlines()
                    logging.info(list_lines[-1])

                # Get current git action.
                if list_lines:
                    current_line = list_lines[-1]
                    git_message = current_line.split("+0800\t")[-1]
                    git_message = git_message[:-1]  # To remove last \n
                    if "+0800\tcommit" in current_line:
                        git_action = "commit"
                        break
                    if "+0800\treset" in current_line:
                        git_action = "reset"
                        break
                    if "+0800\tcheckout" in current_line:
                        git_action = "checkout"
                        break

        if not git_action:
            for changed_file in changed_file_list:
                if re.match(f".*/index$", changed_file):
                    os.chdir(dict_app_data["monitoring_directory_path"])
                    out, output_str = subprocess_run_with_str_output(
                        command=f"git diff --staged"
                    )

                    if output_str:
                        git_action = "stage/unstage"
                        git_message = output_str

                    break

        logging.info(f"git action: {git_action}")
        logging.info(f"git message: \n{git_message}")
        logging.info("----------End find_git_action----------")
        return git_action, git_message

    def do_something(self, dict_app_data, plugin_unique_key) -> None:
        format = "(%(red)s%(asctime)s.%(msecs)03d, %(levelname)s, %(blue)s%(filename)s, %(funcName)s) %(white)s%(message)s"

        logging.basicConfig(
            level=logging.INFO,
            format=format,
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        list_collected_changed_files = []

        will_trigger_plugin, changed_file_list = self.will_file_changes_trigger(
            list_collected_changed_files, dict_app_data
        )
        if will_trigger_plugin:
            logging.info(f"{self.type} detected git action has made.")

            git_action, git_message = self.find_git_action(
                changed_file_list, dict_app_data
            )

            dict_app_data["git_action"] = git_action
            dict_app_data["git_message"] = git_message
            # dict_app_data["commit_logs"] = changed_file
        else:
            dict_app_data["git_action"] = None
            dict_app_data["git_message"] = None
            # dict_app_data["commit_logs"] = None


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
