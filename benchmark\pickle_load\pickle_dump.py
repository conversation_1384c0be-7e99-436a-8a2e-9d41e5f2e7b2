import sys
import os
from tomsze_utils.variable_dump_read_utils import (
    dump_variable_to_pickle,
    load_pickle_file,
)
from dataclasses import dataclass, field
from tqdm import tqdm
from tomsze_utils.time_code_utils import timeit


@dataclass
class DBColEnum:
    IND: str = "ind"
    DESC: str = "desc"
    EMBD: str = "embd"
    KEYW: str = "keyw"


@timeit
def main():
    script_directory_path = (
        f"D:/code/my_projects/search_phone_app/server/api/pickle_db/android/to_merge"
    )

    # --------------------
    embd_pickle_path = os.path.join(
        script_directory_path,
        "004_embeddings_all-MiniLM-L6-v2.pickle",
    )
    dict_embd = load_pickle_file(
        pickle_path=embd_pickle_path,
        default={},
    )

    print(f"len embedding: {len(dict_embd)}")
    # --------------------
    desc_pickle_path = os.path.join(
        script_directory_path,
        "004_descriptions_all-MiniLM-L6-v2.pickle",
    )
    dict_desc = load_pickle_file(
        pickle_path=desc_pickle_path,
        default={},
    )

    print(f"len description: {len(dict_desc)}")
    # --------------------
    ind_pickle_path = os.path.join(
        script_directory_path,
        "004_dict_ids_all-MiniLM-L6-v2.pickle",
    )
    dict_ind = load_pickle_file(
        pickle_path=ind_pickle_path,
        default={},
    )
    print(f"len index: {len(dict_ind)}")
    # --------------------
    keyw_pickle_path = os.path.join(
        script_directory_path,
        "004_keywords.pickle",
    )
    dict_keyw = load_pickle_file(
        pickle_path=keyw_pickle_path,
        default={},
    )
    print(f"len dict_keyword: {len(dict_keyw)}")
    # --------------------

    script_directory_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )
    merged_pickle_path = os.path.join(
        script_directory_path,
        r"dump.pickle",
    )
    dict_merge = {}
    first_key = list(dict_ind.keys())[0]
    for ind in tqdm(range(1000000)):
        item = {
            DBColEnum.IND: dict_ind[first_key],
            DBColEnum.DESC: dict_desc[first_key],
            DBColEnum.EMBD: dict_embd[first_key],
            DBColEnum.KEYW: dict_keyw[first_key],
        }
        dict_merge[str(ind)] = item

    dump_variable_to_pickle(
        dict_merge,
        merged_pickle_path,
    )


if __name__ == "__main__":
    sys.exit(main())
