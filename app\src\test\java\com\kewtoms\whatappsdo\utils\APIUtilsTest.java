package com.kewtoms.whatappsdo.utils;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import org.json.JSONObject;
import org.junit.Test;

public class APIUtilsTest {
    @Test
    public void testApiGetCurrentTimeInZone_ValidTimeZone_ReturnsJSONObject() throws Exception {
        String timeZone = "Europe/London";
        JSONObject resultJSONObject = APIUtils.apiGetCurrentTimeInZone(timeZone);

        // Check that the result is not null and contains expected keys
        assertNotNull(resultJSONObject);
        assertTrue(resultJSONObject.has("datetime"));
        assertTrue(resultJSONObject.has("timezone"));
    }

}
