import os

from tomsze_utils.string_utils import count_space_in_front


def add_simple_function_def(
    script_path: str,
    function_name: str,
    function_body: str,
    overwrite: bool = False,
):
    """
    Add a def function_name before def main or before if __name__ == "__main__": in a script.
    If no def main or if __name__ == "__main__": exists, add the function to the end.
    """
    update_file = False

    assert os.path.exists(script_path)

    # Read script.
    data = open(script_path).read()
    new_data = data

    # Function text to add.
    function_str = f"""def {function_name}():
    {function_body}\n\n\n"""

    def_main_str = "def main():\n"
    if_name_str = 'if __name__ == "__main__":\n'
    if def_main_str in data:
        update_file = True
        split_list = data.split(def_main_str)
        split_list.insert(1, function_str)
        split_list.insert(2, def_main_str)
    elif if_name_str in data:
        update_file = True
        split_list = data.split(if_name_str)
        split_list.insert(1, function_str)
        split_list.insert(2, if_name_str)
    else:
        update_file = True
        split_list = []
        split_list.append(data)
        split_list.append("\n\n")
        split_list.append(function_str)

    write_to_file_path = script_path
    if not overwrite:
        write_to_file_path = script_path.replace(".py", "") + "_new.py"

    if update_file:
        with open(write_to_file_path, "w") as file:
            for text in split_list:
                file.write(text)

    return update_file, write_to_file_path


def append_simple_function_call(
    script_path: str,
    function_name: str,
    overwrite: bool = False,
):
    """
    Append a "def function_name()" to the end.
    """
    update_file = False

    assert os.path.exists(script_path)

    # Read script.
    data = open(script_path).read()

    # Function text to add.
    function_str = f"""    {function_name}()\n"""

    split_list = []
    split_list.append(data)
    split_list.append(function_str)
    update_file = True

    write_to_file_path = script_path
    if not overwrite:
        write_to_file_path = script_path.replace(".py", "") + "_new.py"

    if update_file:
        with open(write_to_file_path, "w") as file:
            for text in split_list:
                file.write(text)

    return update_file, write_to_file_path


def comment_last_line(
    script_path: str,
    overwrite: bool = False,
):
    """
    Comment the last line in a script
    """
    update_file = False

    assert os.path.exists(script_path)

    # Read script.
    data = open(script_path).read()

    split_list = data.splitlines()
    update_file = True

    write_to_file_path = script_path
    if not overwrite:
        write_to_file_path = script_path.replace(".py", "") + "_new.py"

    if update_file:
        with open(write_to_file_path, "w") as file:
            for ind, text in enumerate(split_list):
                if ind != len(split_list) - 1:
                    file.write(text + "\n")
                else:
                    num_spaces = count_space_in_front(text=text)
                    space_str = "".join([" "] * num_spaces)
                    plain_code = text.strip()
                    file.write(f"{space_str}# {plain_code}\n")

    return update_file, write_to_file_path
