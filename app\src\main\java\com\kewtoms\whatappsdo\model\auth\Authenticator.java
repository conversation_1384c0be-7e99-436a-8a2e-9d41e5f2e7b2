package com.kewtoms.whatappsdo.model.auth;

import static com.kewtoms.whatappsdo.utils.RequestUtils.getMockUrlFromTestIntentOrProdUrl;
import static com.kewtoms.whatappsdo.utils.RequestUtils.sendPostEncryptedJsonVolley;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ResponseKeys;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;
import com.kewtoms.whatappsdo.utils.RequestUtils;
import com.kewtoms.whatappsdo.utils.SecurePrefsManager;

import org.json.JSONException;
import org.json.JSONObject;

public class Authenticator {
  public static final String TAG = "APP:Authenticator";
  private final Context context;

  public Authenticator(
    Context context) {
    this.context = context;
  }


  /**
   * Sends a POST request to the server to remove a user. Uses the
   * user's email to identify the user to be removed, and calls a
   * callback function to handle the response. Can choose to wait for
   * the response in the current thread or asynchronously.
   *
   * @param email The email address of the user to be removed.
   * @param callback The callback function for handling the response.
   * @param isWaitThread Whether to wait for the response in the
   * current thread, true for waiting, false for asynchronous.
   * @throws InterruptedException If the operation is interrupted
   *                              while waiting for the response in
   *                              the current thread.
   */
  public void removeUser(
    String email,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {

    JSONObject jsonBodyJSONObject = new JSONObject();
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "email_address",
        email
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key; // use key
    // in strings.xml

    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    // Send a POST request to the server to search for app
    String url = Constants.post_req_url_remove_user_link;
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          // Handle the successful response
          Log.i(
            TAG,
            responseJsonObj.toString()
          );

          // --- Toast the result from the response ---
          if (responseJsonObj.has(Constants.response_json_error_key)) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_SHORT
            );
            toast.show();
            return;
          }

          JSONObject resultJSONObj =
            (JSONObject) responseJsonObj.opt(Constants.response_json_result_key);
          if (resultJSONObj == null) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_SHORT
            );
            toast.show();
          }

          // Notify the callback that the search is complete
          callback.onPostSuccess(responseJsonObj);
        }

        @Override
        public void onPostError(String errorMessage) {
          // Handle the error
          Log.e(
            TAG,
            errorMessage
          );
        }

      }

    );
  }

  public void signIn(
    String email,
    String password,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {

    String methodName = "signIn";

    Log.i(
      TAG,
      methodName + ": " + "run"
    );

    JSONObject jsonBodyJSONObject = new JSONObject();

    // Put email and password into JSONObject
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "email_address",
        email
      );
      dataJSONObject.put(
        "password",
        password
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;

    // Put dataJSONObject into jsonBodyJSONObject
    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      Log.i(
        TAG,
        methodName + ": " + e
      );
      throw new RuntimeException(e);
    }

    // Send a POST request to the server to sign in using credentials
    String url = getMockUrlFromTestIntentOrProdUrl(
      context,
      Constants.mockSignInUrlKey,
      Constants.post_req_url_sign_in_link
    );
    Log.i(
      TAG,
      methodName + ": " + "Sending login request: " + url
    );
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          // Handle the successful response
          Log.i(
            TAG,
            methodName + ": " + "Post request success"
          );

          Log.i(
            TAG,
            methodName + ": " + "responseJsonObj: " + responseJsonObj
          );

          boolean isSuccess;
          try {
            isSuccess =
              RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                context,
                responseJsonObj
              ).getBoolean(ResponseKeys.IS_SUCCESS);

            // Save sign in data to secure prefs
            if (isSuccess) {
              SecurePrefsManager.saveSignInDataFromResponseJSONObject(
                context,
                responseJsonObj
              );
            } else {
              Log.e(
                TAG,
                methodName + ": Login failed"
              );
              Log.e(
                TAG,
                methodName + ": responseJsonObj: " + responseJsonObj
              );

              // Extract error message from response and call error callback
              String errorMessage = "Login failed";
              try {
                errorMessage =
                  RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                    context,
                    responseJsonObj
                  ).getString(ResponseKeys.MESSAGE);
              } catch (JSONException e) {
                Log.w(
                  TAG,
                  methodName + ": Could not extract error message from response, using default"
                );
              }

              callback.onPostError(errorMessage);
              Log.d(
                TAG,
                methodName + ": end"
              );
              return;
            }

          } catch (JSONException e) {
            Log.e(
              TAG,
              methodName + ": " + "JSONException while parsing response: " + e
            );
            callback.onPostError("Failed to parse server response");
            return;
          }


          // Notify the callback that the search is complete
          callback.onPostSuccess(responseJsonObj);
        }

        @Override
        public void onPostError(String errorMessage) {
          // Handle the error
          Log.i(
            TAG,
            methodName + ": " + "Login failed"
          );

          callback.onPostError(errorMessage);
        }

      }

    ); // end of sendPostEncryptedJsonVolley


    Log.d(
      TAG,
      methodName + ": " + "end"
    );

  } // end of signin


  public void signInUsingAccessToken(
    String accessToken,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {
    String methodName = "signInUsingAccessToken:";

    Log.d(
      TAG,
      methodName + "run"
    );

    JSONObject jsonBodyJSONObject = new JSONObject();

    // Put email and password into JSONObject
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "access_token",
        accessToken
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;

    // Put dataJSONObject into jsonBodyJSONObject
    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      Log.i(
        TAG,
        methodName + ": " + e
      );
      throw new RuntimeException(e);
    }

    // Send a POST request to the server to sign in using access token
    String url = getMockUrlFromTestIntentOrProdUrl(
      context,
      Constants.mockSignInAccessTokenUrlKey,
      Constants.post_req_url_sign_in_access_token_link
    );

    Log.i(
      TAG,
      methodName + ": " + "Sending login request:" + url
    );
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          // Handle the successful response
          Log.i(
            TAG,
            methodName + ": " + "Login success using access token"
          );

          try {
            boolean isSuccess =
              RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                context,
                responseJsonObj
              ).getBoolean(ResponseKeys.IS_SUCCESS);

            if (isSuccess) {
              SecurePrefsManager.saveSignInDataFromResponseJSONObject(
                context,
                responseJsonObj
              );
            } else {
              Log.e(
                TAG,
                methodName + ": " + "Login using access token failed"
              );

              // Extract error message from response and call error callback
              String errorMessage = "Login using access token failed";
              try {
                errorMessage =
                  RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                    context,
                    responseJsonObj
                  ).getString(ResponseKeys.MESSAGE);
              } catch (JSONException msgException) {
                Log.w(
                  TAG,
                  methodName + ": Could not extract error message from response, using default"
                );
              }

              callback.onPostError(errorMessage);
              return;
            }
          } catch (JSONException e) {
            Log.e(
              TAG,
              methodName + ": " + "JSONException while parsing response: " + e
            );
            callback.onPostError("Failed to parse server response");
            return;
          }


          // Notify the callback that the post request is complete
          callback.onPostSuccess(responseJsonObj);
        }

        @Override
        public void onPostError(String errorMessage) {
          // Handle the error
          Log.i(
            TAG,
            methodName + ": " + "Login using access token failed"
          );

          callback.onPostError(errorMessage);
        }

      }

    ); // end of sendPostEncryptedJsonVolley


    Log.d(
      TAG,
      methodName + "end"
    );
  }


  // TODO refactor (also removeUser) as they are very similar
  public void signup(
    String email,
    String password,
    String verificationCode,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {

    String methodName = "signup";

    JSONObject jsonBodyJSONObject = new JSONObject();
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "email_address",
        email
      );
      dataJSONObject.put(
        "password",
        password
      );
      dataJSONObject.put(
        "verification_code",
        verificationCode
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;
    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    // Send a POST request to the server
    String url = Constants.post_req_url_sign_up_link;
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          // Handle the successful response


          boolean isSuccess;
          try {
            isSuccess =
              RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                context,
                responseJsonObj
              ).getBoolean(ResponseKeys.IS_SUCCESS);
            if (!isSuccess) {
              Log.e(
                TAG,
                methodName + ": " + "Sign up failed"
              );

              Log.e(
                TAG,
                methodName + ": " + "responseJsonObj: " + responseJsonObj
              );


              Log.d(
                TAG,
                methodName + ": " + "end"
              );

              return;
            }
          } catch (JSONException e) {
            Log.e(
              TAG,
              methodName + ": " + "JSONException: " + e

            );
            throw new RuntimeException(e);
          }


          // --- Toast the result from the response ---
          if (responseJsonObj.has(Constants.response_json_error_key)) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_SHORT
            );
            toast.show();
            return;
          }

          JSONObject resultJSONObj =
            (JSONObject) responseJsonObj.opt(Constants.response_json_result_key);
          if (resultJSONObj == null) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_SHORT
            );
            toast.show();
          }

          // Notify the callback that the search is complete
          callback.onPostSuccess(responseJsonObj);
        }

        @Override
        public void onPostError(String errorMessage) {
          // Handle the error
          Log.e(
            TAG,
            errorMessage
          );
        }

      }

    ); // end of sendPostEncryptedJsonVolley
  } // end of signup


  // TODO refactor (also removeUser) as they are very similar
  public void isUserRegistered(
    String email,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {

    String methodName = "isUserRegistered:";

    JSONObject jsonBodyJSONObject = new JSONObject();
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "email_address",
        email
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key; // use key
    // in strings.xml

    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    // Send a POST request to the server to search for app
    String url = Constants.post_req_url_is_user_signed_up_link;
    Log.i(
      TAG,
      "sending request to: " + url
    );
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          String callMethodName = "sendPostEncryptedJsonVolley";
          Log.d(
            TAG,
            methodName + " calls" + callMethodName + ": " + "outer callback message: " + "onPostSuccess run"
          );

          // Handle the successful response
          Log.i(
            TAG,
            responseJsonObj.toString()
          );

          // --- Toast the result from the response ---
          if (responseJsonObj.has(Constants.response_json_error_key)) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_SHORT
            );
            toast.show();
            return;
          }

          JSONObject resultJSONObj =
            (JSONObject) responseJsonObj.opt(Constants.response_json_result_key);
          if (resultJSONObj == null) {
            Toast toast = Toast.makeText(
              context,
              responseJsonObj.toString(),
              Toast.LENGTH_SHORT
            );
            toast.show();
          }

          // Notify the callback that the search is complete
          callback.onPostSuccess(responseJsonObj);

          Log.d(
            TAG,
            methodName + " calls" + callMethodName + ": " + "outer " + "callback message: " + "onPostSuccess done"
          );
        }

        @Override
        public void onPostError(String errorMessage) {
          // Handle the error
          Log.e(
            TAG,
            errorMessage
          );
        }

      }

    ); // end of sendPostEncryptedJsonVolley
  } // end of isUserRegistered

  /**
   * Checks if account information is stored in secure preferences.
   *
   * <p>This method checks whether the user has previously saved
   * account details, including email, access token, refresh token,
   * and login status. It uses the {@link SecurePrefsManager} to
   * retrieve this data from encrypted shared preferences.
   *
   * @return boolean: True if all required account details are present
   * and valid, false otherwise.
   * @example Usage:
   * <pre>{@code
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.model.auth.Authenticator;
   *
   * public class ExampleClass {
   *     public void exampleMethod(Context context) {
   *         Authenticator authenticator = new Authenticator(context);
   *         boolean hasStored = authenticator.hasAccountStored();
   *         if (hasStored) {
   *             System.out.println("Account details are stored.");
   *         } else {
   *             System.out.println("No account details found.");
   *         }
   *     }
   * }
   * }</pre>
   * @example Handling Null Values:
   * <pre>{@code
   * import android.content.Context;
   * import com.kewtoms.whatappsdo.model.auth.Authenticator;
   *
   * public class ExampleClass {
   *     public void exampleMethodWithNullCheck(Context context) {
   *         if (context == null) {
   *             System.err.println("Context is null, cannot check stored account.");
   *             return;
   *         }
   *
   *         Authenticator authenticator = new Authenticator(context);
   *         boolean hasStored = authenticator.hasAccountStored();
   *         System.out.println("Has account stored: " + hasStored);
   *     }
   * }
   * }</pre>
   */
  public boolean hasAccountStored() {
    String methodName = "hasAccountStored: ";
    Log.d(
      TAG,
      methodName + "run"
    );

    // Get account email saved in shared preferences
    String accountEmail = SecurePrefsManager.getAccountEmail(context);

    // Get token saved in shared preferences
    String accessToken = SecurePrefsManager.getAccessToken(context);
    String refreshToken = SecurePrefsManager.getRefreshToken(context);


    // Get hasSuccessLoggedIn saved in shared preferences
    boolean hasSuccessLoggedIn =
      SecurePrefsManager.getHasSuccessLoggedIn(context);

    if (accountEmail != null && accessToken != null && refreshToken != null && hasSuccessLoggedIn) {
      Log.d(
        TAG,
        methodName + "end: true"
      );
      return true;
    } else {
      Log.d(
        TAG,
        methodName + "end: false"
      );
      return false;
    }

  } // end of hasAccountStored


  public void validateAccessToken(
    String access_token,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {
    String methodName = "validateAccessToken";
    Log.d(
      TAG,
      methodName + ": run"
    );

    JSONObject jsonBodyJSONObject = new JSONObject();

    // Put email and password into JSONObject
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        ResponseKeys.ACCESS_TOKEN,
        access_token
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;
    // Put dataJSONObject into jsonBodyJSONObject
    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      Log.i(
        TAG,
        methodName + ": " + e
      );
      throw new RuntimeException(e);
    }

    // Send a POST request to the server to validate access token
    String url = getMockUrlFromTestIntentOrProdUrl(
      context,
      Constants.mockValidateAccessTokenUrlKey,
      Constants.post_req_url_validate_user_access_token_link
    );
    Log.i(
      TAG,
      methodName + ": " + "Sending request to:" + url
    );
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          // Handle the successful response
          Log.d(
            TAG,
            methodName + ": onPostSuccess:" + "run"
          );

          callback.onPostSuccess(responseJsonObj);

          Log.d(
            TAG,
            methodName + ": onPostSuccess:" + "done"
          );
        }

        @Override
        public void onPostError(String errorMessage) {
          Log.d(
            TAG,
            methodName + ": onPostError:" + "run"
          );

          // Handle the error
          Log.e(
            TAG,
            methodName + ": " + "Validate access token failed"
          );
          Log.e(
            TAG,
            methodName + ": " + errorMessage
          );

          callback.onPostError(errorMessage);

          Log.d(
            TAG,
            methodName + ": onPostError:" + "done"
          );
        }

      }

    ); // end of sendPostEncryptedJsonVolley


    Log.d(
      TAG,
      methodName + ": end"
    );
  }

  public void silentSignIn(
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {

    String methodName = "silentSignIn: ";

    Log.d(
      TAG,
      methodName + "run"
    );

    // Check if account stored
    // If no, do nothing
    if (!hasAccountStored()) {
      Log.i(
        TAG,
        "No account stored"
      );
      Log.d(
        TAG,
        methodName + "end: false"
      );
      return;
    }

    // Get access and refresh token
    String email = SecurePrefsManager.getAccountEmail(context);
    String accessToken = SecurePrefsManager.getAccessToken(context);
    String refreshToken = SecurePrefsManager.getRefreshToken(context);

    if (accessToken == null || refreshToken == null) {
      Log.e(
        TAG,
        methodName + "No access or refresh token stored"
      );
      Log.d(
        TAG,
        methodName + "end: false"
      );
      callback.onPostError("");
      return;
    }

    // Check if access token expired
    // If yes, send  refresh token to get new access token
    validateAccessToken(
      accessToken,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          Log.i(
            TAG,
            methodName + "run validateAccessToken outer callback:" + "Post Success"
          );

          // If success, login
          boolean isSuccess;
          try {
            isSuccess =
              RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                context,
                responseJsonObj
              ).getBoolean(ResponseKeys.IS_SUCCESS);

            if (isSuccess) {
              Log.i(
                TAG,
                methodName + "Validate success. Signing in.."
              );
              signInUsingAccessToken(
                accessToken,
                new PostResponseCallback() {
                  @Override
                  public void onPostSuccess(JSONObject responseJsonObj) {

                  }

                  @Override
                  public void onPostError(String errorMessage) {

                  }
                },
                isWaitThread
              );

            } else {
              // If not success, send refresh token to get new access
              // token and refresh token and login
              // And save new access token and refresh token to shared preferences
              Log.i(
                TAG,
                methodName + "Validate fail. Obtaining new access " + "token and new refresh token"
              );
              obtainNewAccessTokenAndRefreshToken(
                email,
                refreshToken,
                new PostResponseCallback() {
                  @Override
                  public void onPostSuccess(JSONObject responseJsonObj) {
                    String callMethodName =
                      "obtainNewAccessTokenAndRefreshToken";

                    Log.d(
                      TAG,
                      methodName + " calls" + callMethodName + ": " + "outer callback message: " + "onPostSuccess run"
                    );

                    Log.d(
                      TAG,
                      methodName + " calls" + callMethodName + ": " + "outer callback message: responseJsonObj: " + responseJsonObj.toString()
                    );

                    callback.onPostSuccess(responseJsonObj);

                    Log.d(
                      TAG,
                      methodName + " calls" + callMethodName + ": " + "outer callback message: " + "onPostSuccess done"
                    );
                  }

                  @Override
                  public void onPostError(String errorMessage) {
                    Log.i(
                      TAG,
                      methodName + "Post Error+" + errorMessage
                    );


                  }
                },
                isWaitThread
              );
            }

          } catch (JSONException e) {
            Log.e(
              TAG,
              methodName + ": " + "JSONException: " + e

            );
            throw new RuntimeException(e);
          } catch (InterruptedException e) {
            Log.d(
              TAG,
              methodName + ": " + "InterruptedException: " + e
            );
            throw new RuntimeException(e);
          }


        }

        @Override
        public void onPostError(String errorMessage) {
          Log.i(
            TAG,
            methodName + "Post Error:" + errorMessage
          );
        }
      },
      true
    );


    Log.d(
      TAG,
      methodName + "done"
    );
  } // end of silentSignIn

  private void obtainNewAccessTokenAndRefreshToken(
    String email,
    String refreshToken,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {
    String methodName = "obtainNewAccessTokenAndRefreshToken: ";
    Log.d(
      TAG,
      methodName + "run"
    );

    JSONObject jsonBodyJSONObject = new JSONObject();

    // Put email and refresh token into JSONObject
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "email_address",
        email
      );
      dataJSONObject.put(
        ResponseKeys.REFRESH_TOKEN,
        refreshToken
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;
    // Put dataJSONObject into jsonBodyJSONObject
    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      Log.i(
        TAG,
        methodName + ": " + e
      );
      throw new RuntimeException(e);
    }

    // Send a POST request to the server
    String url = getMockUrlFromTestIntentOrProdUrl(
      context,
      Constants.mockObtainNewAccessTokenUrlKey,
      Constants.post_req_url_obtain_new_access_token_by_refresh_token_link
    );

    Log.i(
      TAG,
      methodName + ": " + "Sending request:" + url
    );
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          // Handle the successful response
          Log.d(
            TAG,
            methodName + ": " + "onPostSuccess run"
          );

          // Get is success
          boolean isSuccess;
          String message;
          // Updated to use new API response parsing methods
          isSuccess = RequestUtils.isApiResponseSuccessful(
            context,
            responseJsonObj
          );
          message = RequestUtils.getApiResponseMessage(
            context,
            responseJsonObj
          );

          if (!isSuccess) {
            Log.i(
              TAG,
              methodName + ": error message: " + message
            );
          }

          if (isSuccess) {
            // Save access token to shared preferences
            String accessToken;
            try {
              accessToken =
                RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                  context,
                  responseJsonObj
                ).getString(ResponseKeys.ACCESS_TOKEN);
            } catch (JSONException e) {
              Log.e(
                TAG,
                methodName + ": " + "JSONException: " + e

              );
              throw new RuntimeException(e);
            }
            SecurePrefsManager.saveAccessToken(
              context,
              accessToken
            );
            Log.i(
              TAG,
              methodName + ": " + "Saved new access token"
            );

            // Save refresh token to shared preferences
            String refreshToken;
            try {
              refreshToken =
                RequestUtils.getDbResultJsonObjectFromResponseJSONObject(
                  context,
                  responseJsonObj
                ).getString(ResponseKeys.REFRESH_TOKEN);
            } catch (JSONException e) {
              Log.e(
                TAG,
                methodName + ": " + "JSONException: " + e
              );
              throw new RuntimeException(e);
            }
            SecurePrefsManager.saveRefreshToken(
              context,
              refreshToken
            );
            Log.i(
              TAG,
              methodName + ": " + "Saved new refresh token"
            );


          }

          // Run the callback
          callback.onPostSuccess(responseJsonObj);

          Log.d(
            TAG,
            methodName + "onPostSuccess done"
          );

        }

        @Override
        public void onPostError(String errorMessage) {
          // Handle the error
          Log.d(
            TAG,
            methodName + ": " + "onPostError run"
          );

          Log.e(
            TAG,
            methodName + ": " + errorMessage
          );

          callback.onPostError(errorMessage);

          Log.d(
            TAG,
            methodName + ": " + "onPostError done"
          );
        }

      }

    ); // end of sendPostEncryptedJsonVolley


    Log.d(
      TAG,
      methodName + "done"
    );
  }

  public void obtainVerificationCode(
    String email,
    PostResponseCallback callback,
    boolean isWaitThread)
    throws
    InterruptedException {
    String methodName = "obtainVerificationCode: ";
    Log.d(
      TAG,
      methodName + "run"
    );

    JSONObject jsonBodyJSONObject = new JSONObject();

    // Put email into JSONObject
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "email_address",
        email
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key;
    // Put dataJSONObject into jsonBodyJSONObject
    try {
      jsonBodyJSONObject.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      Log.i(
        TAG,
        methodName + ": " + e
      );
      throw new RuntimeException(e);
    }

    // Send a POST request to obtain verification code
    String url = Constants.post_req_url_obtain_verification_code_link;
    Log.i(
      TAG,
      methodName + ": " + "Sending request:" + url
    );
    sendPostEncryptedJsonVolley(
      context,
      url,
      jsonBodyJSONObject,
      -1,
      isWaitThread,
      new PostResponseCallback() {
        @Override
        public void onPostSuccess(JSONObject responseJsonObj) {
          Log.d(
            TAG,
            methodName + ": " + "onPostSuccess run"
          );

          // Notify the callback that the request is complete
          callback.onPostSuccess(responseJsonObj);

          Log.d(
            TAG,
            methodName + "onPostSuccess done"
          );
        }

        @Override
        public void onPostError(String errorMessage) {
          Log.d(
            TAG,
            methodName + ": " + "onPostError run"
          );
          Log.e(
            TAG,
            methodName + ": " + errorMessage
          );
          callback.onPostError(errorMessage);
          Log.d(
            TAG,
            methodName + ": " + "onPostError done"
          );
        }
      }
    );

    Log.d(
      TAG,
      methodName + "done"
    );
  }
}
