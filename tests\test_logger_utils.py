from datetime import datetime
import time
from freezegun import freeze_time
import json
import pytest
import logging
import os
import tempfile
import shutil
from tomsze_utils.logger_utils import (
    SimpleLogger,
    setup_logger,
    remove_all_logging_handlers,
)
from logging.handlers import RotatingFileHandler


@pytest.fixture(scope="function")
def temp_dir():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


def test_setup_logger_default(temp_dir):
    logger = setup_logger(log_to_folder_path=temp_dir, is_log_to_file=True)
    assert isinstance(logger, logging.Logger)
    assert logger.level == logging.DEBUG
    assert len(logger.handlers) == 2  # File handler and stream handler
    # Close and remove handlers to ensure file is not kept open
    for handler in logger.handlers:
        handler.close()
        logger.removeHandler(handler)


def test_setup_logger_custom(temp_dir):
    custom_logger = setup_logger(
        format="%(levelname)s: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",  # Added date format
        loglevel="logging.INFO",
        is_log_to_file=True,
        log_to_folder_path=temp_dir,
        log_filename="custom_log.log",
        print_to_screen=False,
        use_rotating_log=True,
        maxKB=1,  # Updated to maxKB
        backupCount=5,
    )
    assert isinstance(custom_logger, logging.Logger)
    assert custom_logger.level == logging.INFO
    assert len(custom_logger.handlers) == 1  # Only file handler

    # Check if log file is created
    log_file_path = os.path.join(temp_dir, "custom_log.log")
    assert os.path.exists(log_file_path)

    # Close and remove handlers to ensure file is not kept open
    for handler in custom_logger.handlers:
        handler.close()
        custom_logger.removeHandler(handler)


def test_setup_logger_rotating_file_handler(temp_dir):
    rotating_logger = setup_logger(
        log_to_folder_path=temp_dir,
        log_filename="rotating_log.log",
        use_rotating_log=True,
        maxKB=1,  # Updated to maxKB
        backupCount=3,
    )
    assert isinstance(rotating_logger, logging.Logger)
    assert (
        len(rotating_logger.handlers) == 2
    )  # Rotating file handler and stream handler

    # Check if the file handler is a RotatingFileHandler
    file_handler = next(
        handler
        for handler in rotating_logger.handlers
        if isinstance(handler, RotatingFileHandler)
    )
    assert isinstance(file_handler, RotatingFileHandler)
    assert file_handler.maxBytes == 1024
    assert file_handler.backupCount == 3

    # Check if log file is created
    log_file_path = os.path.join(temp_dir, "rotating_log.log")
    assert os.path.exists(log_file_path)

    # Write enough data to trigger rotation
    for i in range(100):
        rotating_logger.debug("Test log message " * 10)

    # Check if backup files are created
    assert os.path.exists(log_file_path + ".1")
    assert os.path.exists(log_file_path + ".2")
    assert os.path.exists(log_file_path + ".3")
    assert not os.path.exists(
        log_file_path + ".4"
    )  # Should not exist due to backupCount=3

    # Close and remove handlers to ensure files are not kept open
    for handler in rotating_logger.handlers:
        handler.close()
        rotating_logger.removeHandler(handler)


def test_setup_logger_clear_log_folder(temp_dir):
    # Create a log folder and add a log file
    log_folder_path = os.path.join(temp_dir, "log_folder")
    os.makedirs(log_folder_path, exist_ok=True)
    abc_file_path = os.path.join(
        log_folder_path, "abc.txt"
    )  # Changed log.log to abc.txt

    # Create a dummy log file
    with open(abc_file_path, "w") as f:
        f.write("This is a test file.\n")

    # Ensure the log file exists before clearing
    assert os.path.exists(abc_file_path)

    # Setup logger with is_clear_log_folder option
    logger = setup_logger(log_to_folder_path=log_folder_path, is_clear_log_folder=True)

    # Check if the log folder is cleared
    assert not os.path.exists(abc_file_path)  # The log file should be removed
    assert len(logger.handlers) > 0  # Ensure that the logger has handlers

    # Close and remove handlers to ensure files are not kept open
    for handler in logger.handlers:
        handler.close()
        logger.removeHandler(handler)


def test_remove_all_logging_handlers(temp_dir):
    # Setup a logger first
    logger = setup_logger(log_to_folder_path=temp_dir)

    # Now remove all handlers
    logger = remove_all_logging_handlers(logger)
    assert len(logger.handlers) == 0

    # Close and remove handlers to ensure file is not kept open
    for handler in logger.handlers:
        handler.close()
        logger.removeHandler(handler)


class TestLoggerRotation:

    def test_setup_logger_time_rotating(self, temp_dir):
        log_folder_path = os.path.join(temp_dir, "time_rotating_log_folder")
        os.makedirs(log_folder_path, exist_ok=True)

        logger = setup_logger(
            log_to_folder_path=log_folder_path,
            use_rotating_log=True,
            is_use_time_rotating=True,
            rotate_at_every="S",  # Rotate every second for testing purposes
            backupCount=5,
        )

        # Log some messages
        logger.info(f"Test message 1")

        time.sleep(2)

        logger.info(f"Test message 2")

        # Check if the logger has handlers
        assert len(logger.handlers) > 0

        # Close and remove handlers to ensure files are not kept open
        for handler in logger.handlers:
            handler.close()
            logger.removeHandler(handler)

        # Check if log files are created (this may need to be adjusted based on timing)
        log_files = os.listdir(log_folder_path)
        assert len(log_files) == 2  # Ensure that at least one log file is created

    def test_setup_logger_monthly_rotating(self, temp_dir):
        log_folder_path = os.path.join(temp_dir, "monthly_rotating_log_folder")
        os.makedirs(log_folder_path, exist_ok=True)

        with freeze_time("2012-01-14"):
            logger = setup_logger(
                log_to_folder_path=log_folder_path,
                use_rotating_log=True,
                is_use_time_rotating=True,
                rotate_at_every="Month",  # Rotate every month for testing purposes
                backupCount=5,
            )

        with freeze_time("2012-01-14"):
            # Log some messages
            logger.info(f"Test message for monthly rotation")

        with freeze_time("2012-01-15"):
            # Log some messages
            logger.info(f"Test message for monthly rotation")

        # Check if log files are created
        log_files = os.listdir(log_folder_path)
        assert len(log_files) == 1

        with freeze_time("2012-02-14"):
            # Log some messages
            logger.info(f"Test message for monthly rotation")

        with freeze_time("2012-03-14"):
            # Log some messages
            logger.info(f"Test message for monthly rotation")

        # Check if the logger has handlers
        assert len(logger.handlers) > 0

        # Close and remove handlers to ensure files are not kept open
        for handler in logger.handlers:
            handler.close()
            logger.removeHandler(handler)

        # Check if log files are created
        log_files = os.listdir(log_folder_path)

        # Verify that each log file exists in the specified log folder
        for file in log_files:
            log_path = os.path.join(log_folder_path, file)
            assert os.path.exists(log_path)

        assert len(log_files) == 3  # 3 months of logs


class TestSimpleLogger:

    # ------------simple test--------------------
    def test_simple_logger_info(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            logger = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                get_existing_instance=False,
            )
            log_file_path = logger.get_log_file_path()
            logger.info("Test log message.")

            assert os.path.exists(log_file_path)

            with open(log_file_path, "r") as log_file:
                content = log_file.read()
                assert "Test log message." in content

    def test_simple_logger_empty_message(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            logger = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                get_existing_instance=False,
            )
            with pytest.raises(
                ValueError, match="The log message must be a non-empty string."
            ):
                logger.info("")

    # ------------test existing instance--------------------
    def test_simple_logger_get_existing_instance(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            logger_root = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                id="1",
            )
            logger2 = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                get_existing_instance=True,
            )

            assert logger_root.get_id() == logger2.get_id()

            logger3 = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                get_existing_instance=False,
                id="3",
            )
            assert logger_root.get_id() != logger3.get_id()

    # ------------test format--------------------
    def test_simple_logger_format_message(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            logger = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                format="msg: %(message)s",
                get_existing_instance=False,
            )
            log_file_path = logger.get_log_file_path()
            logger.info("Test log message with custom format.")

            assert os.path.exists(log_file_path)

            with open(log_file_path, "r") as log_file:
                content = log_file.read()
                assert "msg: Test log message with custom format." in content

    def test_simple_logger_custom_datefmt(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            with freeze_time("2012-11-15 2:59:59", tz_offset=+8):
                logger = SimpleLogger(
                    logs_folder_path=temp_dir,
                    log_base_name="test_log",
                    format="time: %(asctime)s",
                    datefmt="%m/%d/%Y %I:%M:%S %p",
                    get_existing_instance=False,
                )
                log_file_path = logger.get_log_file_path()
                logger.info("Test log message with custom datefmt.")

                assert os.path.exists(log_file_path)

                with open(log_file_path, "r") as log_file:
                    content = log_file.read()
                    assert "time: " in content
                    assert "11/15/2012 10:59:59 AM" in content

    def test_simple_logger_format_with_line_number(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            logger = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                format="time: %(asctime)s message: %(message)s lineNo: %(lineno)d",
                get_existing_instance=False,
            )
            log_file_path = logger.get_log_file_path()
            logger.info("Test log message with line number.")

            assert os.path.exists(log_file_path)

            with open(log_file_path, "r") as log_file:
                content = log_file.read()
                assert "time: " in content
                assert "message: Test log message with line number." in content
                assert "lineNo: " in content  # Check if line number is included

    # TODO fix filename
    # def test_simple_logger_format_with_filename(self):
    #     with tempfile.TemporaryDirectory() as temp_dir:
    #         with freeze_time("2012-10-15 2:59:59", tz_offset=+8):
    #             logger = SimpleLogger(
    #                 logs_folder_path=temp_dir,
    #                 log_base_name="test_log",
    #                 format="message: %(message)s fileName: %(filename)s",
    #                 get_existing_instance=False,
    #             )
    #             log_file_path = logger.get_log_file_path()
    #             logger.info("Test log message with filename.")

    #             assert os.path.exists(log_file_path)

    #             with open(log_file_path, "r") as log_file:
    #                 content = log_file.read()
    #                 assert "message: Test log message with filename." in content
    #                 assert (
    #                     f"fileName: test_log_{datetime.now().strftime('%Y-%m')}.log"
    #                     in content
    #                 )  # Check if filename is included
    #                 assert (
    #                     "fileName: test_log_2012-10.log" in content
    #                 )  # Check for specific filename format

    # ------------test level--------------------
    def test_simple_logger_warning_method(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            logger = SimpleLogger(
                logs_folder_path=temp_dir,
                log_base_name="test_log",
                format="time: %(asctime)s lv: %(lv)s message: %(message)s",
                get_existing_instance=False,
            )
            log_file_path = logger.get_log_file_path()
            logger.warning("Test warning message.")

            assert os.path.exists(log_file_path)

            with open(log_file_path, "r") as log_file:
                content = log_file.read()
                assert "WARNING" in content
                assert "message: Test warning message." in content

    # ------------test month--------------------
    def test_simple_logger_monthly_file_creation(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            with freeze_time("2012-10-15 2:59:59", tz_offset=+8):
                logger = SimpleLogger(
                    logs_folder_path=temp_dir,
                    log_base_name="test_log",
                    new_file_for_each="month",
                    get_existing_instance=False,
                )

                log_file_path = logger.get_log_file_path()
                logger.info("Log message for October.")

            assert os.path.exists(log_file_path)

            with open(log_file_path, "r") as log_file:
                content = log_file.read()
                assert "Log message for October." in content

            # Simulate moving to the next month
            with freeze_time("2012-11-15 2:59:59", tz_offset=+8):
                logger.info("Log message for November.")
            new_log_file_path = logger.get_log_file_path()

            assert (
                new_log_file_path != log_file_path
            )  # Ensure a new file was created for the new month
            assert os.path.exists(new_log_file_path)

            with open(new_log_file_path, "r") as log_file:
                content = log_file.read()
                assert "Log message for November." in content

    def test_simple_logger_with_config_file(self, monkeypatch):
        from pathlib import Path

        with tempfile.TemporaryDirectory() as temp_dir:
            # Setup mock config
            config = {
                "loggers": {"mock_script.py": {"enabled": False, "singleton": True}}
            }
            config_path = os.path.join(temp_dir, "logger_config.json")
            Path(config_path).write_text(json.dumps(config))

            # Mock inspect to simulate being in "mock_script.py"
            import inspect

            monkeypatch.setattr(
                inspect,
                "stack",
                lambda: [type("Frame", (), {"filename": "mock_script.py"})()] * 3,
            )

            # Setup logger
            logger = SimpleLogger(
                config_path=str(config_path),
                logs_folder_path=temp_dir,
                log_base_name="test_log",
            )

            # Should not print or log anything
            logger.info("This should not be logged.")

            assert logger.enabled is False

            # Check the log is empty
            log_file_path = logger.get_log_file_path()
            assert os.path.exists(log_file_path)

            with open(log_file_path, "r") as log_file:
                content = log_file.read()
                assert content == ""


if __name__ == "__main__":
    pytest.main([__file__])
