import pytest
from sentence_transformers import SentenceTransformer
import torch
from server.api.utils.generate_vectors.utils import convert_to_embedding


@pytest.fixture(scope="module")
def embedder():
    yield SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")


@pytest.mark.slow
def test_convert_to_embedding(embedder):
    list_description = [
        "hi there",
        "hello there",
    ]
    list_embeddings = convert_to_embedding(
        embedder,
        list_description,
        device="cpu",
        batch_size=128,
    )
    assert list_embeddings.shape == torch.Size([2, 384])
