import logging
from tomsze_utils.configurable_algorithm.configurable_algorithm import CA


def run():

    config_folder_path = r"./CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_tasks_start_container.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var
    logging.info(
        "if db has not deployed, the container will fail to start! Run deploy_with_db.py first."
    )


if __name__ == "__main__":
    run()
