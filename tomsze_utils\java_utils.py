from importlib import resources
import os
import subprocess
from tomsze_utils.subprocess_utils import subprocess_run_with_str_output


def run_java_file(java_file_path: str) -> str:
    """
    Compiles and runs a Java file using the Java Runtime Environment.

    Args:
        java_file_path (str): The path to the .java file to be executed.

    Returns:
        str: The output from the Java program execution.

    Raises:
        FileNotFoundError: If the specified Java file does not exist.
        subprocess.CalledProcessError: If the compilation or execution fails.
    """
    if not os.path.isfile(java_file_path):
        raise FileNotFoundError(
            f"The specified Java file does not exist: {java_file_path}"
        )

    # Compile the Java file
    compile_command = f"javac {java_file_path}"
    compile_process = subprocess_run_with_str_output(
        compile_command,
        shell=False,
        cwd=os.path.dirname(java_file_path),
    )

    if compile_process[0].returncode != 0:
        raise subprocess.CalledProcessError(
            compile_process[0].returncode,
            compile_command,
            output=compile_process[0].stdout,
            stderr=compile_process[0].stderr,
        )

    # Get the class name from the file name
    class_name = os.path.splitext(os.path.basename(java_file_path))[0]

    # Run the compiled Java class
    run_command = f"java {class_name}"
    run_process = subprocess_run_with_str_output(
        run_command,
        shell=False,
        cwd=os.path.dirname(java_file_path),
    )

    return run_process[1] if run_process[0].returncode == 0 else run_process[0].stderr


def create_java_file(file_path: str, code: str) -> str:
    """
    Creates a Java file with the specified code.

    Args:
        file_path (str): The path where the .java file will be created.
        code (str): The Java code to write to the file.

    Raises:
        IOError: If there is an error writing to the file.

    Examples:
        ```python
        result = create_java_file(file_path="TestProgram.java", code="public class TestProgram { public static void main(String[] args) { System.out.println('Hello, World!'); } }")
        ```

        ```python
        result = create_java_file(java_fifile_pathle_path="AnotherProgram.java", code="public class AnotherProgram { public static void main(String[] args) { System.out.println('Another program!'); } }")
        ```
    """
    try:
        with open(file_path, "w") as java_file:
            java_file.write(code)
    except IOError as e:
        raise IOError(f"An error occurred while writing to the file: {e}")

    return "done"


junit_jar_path = str(
    resources.files("tomsze_utils").joinpath(
        "./jars",
        "junit-4.13.2.jar",
    )
)
assert os.path.exists(junit_jar_path) == True

hamcrest_jar_path = str(
    resources.files("tomsze_utils").joinpath(
        "./jars",
        "hamcrest-core-1.3.jar",
    )
)
assert os.path.exists(hamcrest_jar_path) == True


def run_java_test_file(
    test_file_path: str,
    junit_jar_path: str = junit_jar_path,
    hamcrest_jar_path: str = hamcrest_jar_path,
) -> str:
    """
    Compiles and runs a Java test file using JUnit.

    Args:
        test_file_path (str): The path to the Java test file.
        junit_jar_path (str): The path to the JUnit jar file. Defaults to the predefined path.
        hamcrest_jar_path (str): The path to the Hamcrest jar file. Defaults to the predefined path.

    Raises:
        FileNotFoundError: If the specified test file does not exist.
        ValueError: If the specified JUnit or Hamcrest jar files do not exist.
        subprocess.CalledProcessError: If the compilation or execution of the test fails.

    Examples:
        ```python
        result = run_java_test_file(test_file_path="TestProgramTest.java")
        ```

        ```python
        result = run_java_test_file(test_file_path="AnotherTest.java", junit_jar_path="path/to/junit.jar", hamcrest_jar_path="path/to/hamcrest.jar")
        ```
    """

    test_file_path = os.path.realpath(test_file_path)

    # Validate input paths
    if not os.path.isfile(test_file_path):
        raise FileNotFoundError(
            f"The specified test file does not exist: {test_file_path}"
        )

    if not os.path.isfile(junit_jar_path):
        raise ValueError(
            f"The specified JUnit jar file does not exist: {junit_jar_path}"
        )

    if not os.path.isfile(hamcrest_jar_path):
        raise ValueError(
            f"The specified Hamcrest jar file does not exist: {hamcrest_jar_path}"
        )

    # Compile the Java test file with JUnit
    compile_command = f"javac -cp .;{junit_jar_path} {test_file_path}"
    compile_process = subprocess_run_with_str_output(
        compile_command,
        shell=False,
        cwd=os.path.dirname(test_file_path),
    )

    if compile_process[0].returncode != 0:
        raise subprocess.CalledProcessError(
            compile_process[0].returncode,
            compile_command,
            output=compile_process[0].stdout,
            stderr=compile_process[0].stderr,
        )

    # Get the class name from the test file name
    class_name = os.path.splitext(os.path.basename(test_file_path))[0]

    # Run the compiled Java test class with JUnit
    run_command = f"java -cp .;{junit_jar_path};{hamcrest_jar_path} org.junit.runner.JUnitCore {class_name}"
    run_process = subprocess_run_with_str_output(
        run_command,
        shell=False,
        cwd=os.path.dirname(test_file_path),
    )

    if run_process[0].returncode != 0:
        raise subprocess.CalledProcessError(
            run_process[0].returncode,
            run_command,
            output=run_process[0].stdout,
            stderr=run_process[0].stderr,
        )

    return run_process[1]


def main():
    print("")


if __name__ == "__main__":
    main()
