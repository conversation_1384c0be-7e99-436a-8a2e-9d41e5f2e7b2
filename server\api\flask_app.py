# A very simple Flask Hello World app for you to get started with...
import sys
import os
import threading
from bs4 import BeautifulSoup
from dotenv import load_dotenv, find_dotenv
import numpy as np
import google_play_scraper

# from google_play_scraper import app
from nordvpn_switcher import initialize_VPN, rotate_VPN
from sentence_transformers import SentenceTransformer
import pandas as pd
import torch
import yake
import urllib.request
from urllib.error import URLError, HTTPError
import re2
from flask import Flask, request

# Load utils path from .env file
env_path = find_dotenv()
load_dotenv(env_path)
utils_path = os.getenv("UTILS_PATH")
sys.path.insert(1, utils_path)

from utils import *
from powerthresaurus import *

# Create Flask app.
app = Flask(__name__)

# Some setups.
model_id = "multi-qa-MiniLM-L6-cos-v1"
# model_id = 'all-mpnet-base-v2'  # best but not multi language
# model_id = 'all-MiniLM-L6-v2'
# model_id = 'paraphrase-multilingual-MiniLM-L12-v2'
embedder = SentenceTransformer(model_id)
api_dataset_path = r"./api/pickle_db/android"
project_root_path = os.getenv("PROJ_ROOT_PATH")

# kw_extractor = yake.KeywordExtractor()
language = "en"
max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20

synonym_site = "powerthesaurus"
max_synonyms = 999
use_powerthesaurus_api = True
pt = PowerThesaurus()
synonyms_pickle_filename = f"synonyms_{synonym_site}.pickle"
no_synonyms_pickle_filename = f"no_synonyms_{synonym_site}.pickle"

disable_fetch_synonym = False  # Set to True when deploy.
use_vpn_to_fetch = True
rotate_vpn_for_n_fetch = 15

deploy_mode = True

if deploy_mode:
    disable_fetch_synonym = True
    use_vpn_to_fetch = False

if use_vpn_to_fetch:
    initialize_VPN(save=1, area_input=["complete rotation"])

# user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'  # this is faster
# headers = {'User-Agent': user_agent}


@app.route("/")
def hello_world():
    return "Hello from Flask!"


def is_id_in_dataset():
    pass


def update_and_reload_dataset():
    pass


@app.route("/vs2", methods=["POST"])
def vs2():
    # Get data from user device.
    data = request.get_json(force=True)
    query_text = data["query"]
    device_platform = data["device_platform"]
    list_device_apps = data["device_apps"]

    # Read app description and embeddings from 004 pickle files.
    dict_ids_pickle_filename = f"004_dict_ids_{model_id}.pickle"
    ids_pickle_filename = f"004_ids_{model_id}.pickle"
    descriptions_pickle_filename = f"004_descriptions_{model_id}.pickle"
    descriptions_zh_pickle_filename = f"004_descriptions_zh_{model_id}.pickle"
    embeddings_pickle_filename = f"004_embeddings_{model_id}.pickle"
    keywords_pickle_filename = f"004_keywords.pickle"

    api_dict_ids_pickle_path = os.path.join(
        project_root_path, api_dataset_path, dict_ids_pickle_filename
    )
    api_description_pickle_path = os.path.join(
        project_root_path, api_dataset_path, descriptions_pickle_filename
    )
    api_embedding_pickle_path = os.path.join(
        project_root_path, api_dataset_path, embeddings_pickle_filename
    )
    api_keyword_pickle_path = os.path.join(
        project_root_path, api_dataset_path, keywords_pickle_filename
    )
    api_synonym_pickle_path = os.path.join(
        project_root_path, api_dataset_path, synonyms_pickle_filename
    )
    api_no_synonym_pickle_path = os.path.join(
        project_root_path, api_dataset_path, no_synonyms_pickle_filename
    )

    dict_id = {}
    dict_description = {}
    dict_embedding_tensor = {}
    dict_keywords = {}
    dict_synonyms = {}
    dict_no_synonyms = {}

    dict_id = read_variable_from_pickle(api_dict_ids_pickle_path, dict_id)
    dict_description = read_variable_from_pickle(
        api_description_pickle_path, dict_description
    )
    dict_embedding_tensor = read_variable_from_pickle(
        api_embedding_pickle_path, dict_embedding_tensor
    )
    dict_keywords = read_variable_from_pickle(api_keyword_pickle_path, dict_keywords)
    dict_synonyms = read_variable_from_pickle(api_synonym_pickle_path, dict_synonyms)
    dict_no_synonyms = read_variable_from_pickle(
        api_no_synonym_pickle_path, dict_no_synonyms
    )

    count_synonym_fetch = 0

    for device_app in list_device_apps:
        print(f"device_app: {device_app}")
        device_app_id = device_app["id"]
        list_custom_description = device_app["custom_description"]
        # Add new data to pickle if not in dict_id.
        if not device_app_id in dict_id:
            scrap_success_en, result_en = scrap_play_store(
                device_app_id=device_app_id,
                lang="en",
            )

            if not scrap_success_en:
                continue

            # Post-process app description.
            app_description_en_original, app_description_en = (
                post_process_app_description(result_en)
            )

            # Convert app descryption to embedding.
            output_embedding_tensor = convert_to_embedding(
                embedder, app_description_en, device="cpu", batch_size=128
            )

            # Extract keywords.
            list_kw = extract_keyword_using_yake(
                lang=language,
                max_ngram_size=max_ngram_size,
                deduplication_threshold=deduplication_threshold,
                max_num_keywords=max_num_keywords,
                app_description_en_original=app_description_en_original,
            )

            list_kw = post_process_keywords(list_kw)

            # Get synonyms for each keyword.
            if not disable_fetch_synonym:
                for keyword_underscore in list_kw:
                    has_fetch = fetch_and_dump_synonyms(
                        keyword_underscore=keyword_underscore,
                        api_synonym_pickle_path=api_synonym_pickle_path,
                        api_no_synonym_pickle_path=api_no_synonym_pickle_path,
                        dict_no_synonyms=dict_no_synonyms,
                        dict_synonyms=dict_synonyms,
                        site=synonym_site,
                        use_powerthesaurus_api=use_powerthesaurus_api,
                        pt_obj=pt,
                        max_num=max_synonyms,
                    )

                    if use_vpn_to_fetch:
                        if has_fetch:
                            count_synonym_fetch += 1
                            if count_synonym_fetch == rotate_vpn_for_n_fetch:
                                count_synonym_fetch = 0
                                rotate_VPN()

            dict_id[device_app_id] = len(dict_id)
            dict_description[device_app_id] = app_description_en
            dict_embedding_tensor[device_app_id] = output_embedding_tensor
            dict_keywords[device_app_id] = list_kw

            # Save to pickle.
            dump_variable_to_pickle(api_dict_ids_pickle_path, dict_id)
            dump_variable_to_pickle(api_description_pickle_path, dict_description)
            dump_variable_to_pickle(api_embedding_pickle_path, dict_embedding_tensor)
            dump_variable_to_pickle(api_keyword_pickle_path, dict_keywords)
        else:
            # Dump only when pickle file does not exist.
            dump_variable_to_pickle(api_dict_ids_pickle_path, dict_id, True)
            dump_variable_to_pickle(api_description_pickle_path, dict_description, True)
            dump_variable_to_pickle(
                api_embedding_pickle_path, dict_embedding_tensor, True
            )
            dump_variable_to_pickle(api_keyword_pickle_path, dict_keywords, True)
            dump_variable_to_pickle(api_synonym_pickle_path, dict_synonyms, True)
            dump_variable_to_pickle(api_no_synonym_pickle_path, dict_no_synonyms, True)

            # Fill synonyms for keyword that might not be successfully fetched before.
            if not disable_fetch_synonym:
                list_kw = dict_keywords[device_app_id]
                for keyword_underscore in list_kw:
                    has_fetch = fetch_and_dump_synonyms(
                        keyword_underscore=keyword_underscore,
                        api_synonym_pickle_path=api_synonym_pickle_path,
                        api_no_synonym_pickle_path=api_no_synonym_pickle_path,
                        dict_no_synonyms=dict_no_synonyms,
                        dict_synonyms=dict_synonyms,
                        site=synonym_site,
                        use_powerthesaurus_api=use_powerthesaurus_api,
                        pt_obj=pt,
                        max_num=max_synonyms,
                    )

                    if use_vpn_to_fetch:
                        if has_fetch:
                            count_synonym_fetch += 1
                            if count_synonym_fetch == rotate_vpn_for_n_fetch:
                                count_synonym_fetch = 0
                                rotate_VPN()

    # Semantic search (similarity search).
    query = [query_text]
    # output = convert_to_embedding_using_huggingface_model(query, f"sentence-transformers/{model_id}")
    # query_embedding = torch.FloatTensor(output)

    query_embedding = convert_to_embedding(
        embedder,
        query,
        device="cpu",
        batch_size=128,
    )

    # query_embeddings = query_embeddings.to('cuda')
    # description_embedding = torch.from_numpy(np.array(list_embedding)).to(torch.float)
    # description_embedding = list_embedding_tensor.copy()
    # description_embedding = description_embedding.to('cuda')
    list_device_embedding_tensor = []
    list_device_description = []
    dict_device_id = {}
    dict_lang_listDescription = {}
    for device_app in list_device_apps:
        device_app_id = device_app["id"]
        if device_app_id in dict_id:
            list_device_embedding_tensor.append(dict_embedding_tensor[device_app_id])
            list_device_description.append(dict_description[device_app_id])

            dict_device_id[device_app_id] = ""
    dict_lang_listDescription["en"] = list_device_description

    # Similarity search.
    hits = similarity_search(query_embedding, list_device_embedding_tensor, top_k=30)

    # Word search.
    list_query_words = split_text(query_text)
    list_num_matches = search_list_text_en_zh(
        dict_lang_listDescription, list_query_words
    )
    list_normalized_word_score = normalize_list(list_num_matches)
    list_normalized_word_score_reorder = []
    hits = hits[0]  # Get the hits for the first query (Use this for app is correct)
    for hit in hits:  # Reorder by similarity search result
        list_normalized_word_score_reorder.append(
            list_normalized_word_score[hit["corpus_id"]]
        )

    # Word search on synonyms.
    dict_lang_listSynonym = {}
    # list_device_synonyms = []
    # for device_app in list_device_apps:
    #     device_app_id = device_app['id']
    #     if device_app_id in dict_id:
    #         list_device_keywords = dict_keywords[device_app_id]

    #         all_synonyms_string = ''
    #         for keyword in list_device_keywords:
    #             if keyword in dict_synonyms:
    #                 list_synonyms = dict_synonyms[keyword]

    #                 for synonyms in list_synonyms:
    #                     all_synonyms_string = all_synonyms_string + synonyms + ', '
    #         list_device_synonyms.append(all_synonyms_string)

    list_device_synonyms = extract_device_app_synonyms(
        list_device_apps=list_device_apps,
        dict_keywords=dict_keywords,
        dict_synonyms=dict_synonyms,
    )

    dict_lang_listSynonym["en"] = list_device_synonyms
    list_num_matches_syn = search_list_text_en_zh(
        dict_lang_listSynonym, list_query_words
    )
    list_normalized_word_score_syn = normalize_list(list_num_matches_syn)
    list_normalized_word_score_reorder_syn = []
    for hit in hits:  # Reorder by similarity search result
        list_normalized_word_score_reorder_syn.append(
            list_normalized_word_score_syn[hit["corpus_id"]]
        )

    # Scoring and ranking.
    list_id_from_dict = list(dict_device_id)
    list_appid_score_sim_only = []
    list_appid_score_word_search = []
    list_appid_score_syn_search = []
    list_appScore = []
    list_id_hits = []
    for hit in hits:
        list_appid_score_sim_only.append(
            [list_id_from_dict[hit["corpus_id"]], hit["score"]]
        )
        list_id_hits.append(list_id_from_dict[hit["corpus_id"]])
        list_appScore.append(hit["score"])

    list_normalized_similarity_score = normalize_list(list_appScore)

    list_final_app_score = [
        x + y
        for x, y in zip(
            list_normalized_word_score_reorder, list_normalized_similarity_score
        )
    ]
    list_appid_score_word_search = [
        [id, score] for score, id in sorted(zip(list_final_app_score, list_id_hits))
    ]
    list_appid_score_word_search.reverse()

    list_final_app_score = [
        x + y + z
        for x, y, z in zip(
            list_normalized_word_score_reorder_syn,
            list_normalized_word_score_reorder,
            list_normalized_similarity_score,
        )
    ]
    list_appid_score_syn_search = [
        [id, score] for score, id in sorted(zip(list_final_app_score, list_id_hits))
    ]
    list_appid_score_syn_search.reverse()

    return {
        "app_score_sim_only": list_appid_score_sim_only,
        "app_score_with_word_search": list_appid_score_word_search,
        "app_score_with_syn_search": list_appid_score_syn_search,
    }


if __name__ == "__main__":
    app.run(debug=True, threaded=False)


# Core
# ok: remove use of 'xx' in dictx.keys()

# Database read write
# TODO: LATER: HARD race condition when multiple user access the same database => can use threading's lock

# Search Scope
# OK: api search only in device apps embeddings (not all apps)

# App Description
# ok: custome description * factor should not be hard coded
# ok: investigate why some description in dict is strange that cannot be opened by PickleViewer=> has emoji or other icon
# ok: use descriptionHTML? NOT YET
# ok: remove emoji from HTML
# ok: if app have custom description, it should not be save to pickle! (at this moment)
# TODO: fetch from google play store or others

# Word search
# TODO: for word search, use part or complete search? part contribute less like 80%？

# Final Scoring
# TODO: wrap matching and scoring into functions

# API return
# TODO: return also the name of app

# App icon
# TODO: pytorch model that can interpret app icon
# TODO: test extract keywords, then convert to embedding

# Keyword extraction
# ok: how to extract keywords from sentences using python? Use YAKE! for now
# ok: extract keyword using the original description (to avoid xxx.xxx extracted)
# ok: if keyword is combination of two or more words, add it ,then split it and add to list of keyword
# TODO: there is dot in extracted keyword
# ok: wrap into function
# TODO: keep looking for better keyword extractor


# Synonym fetching.
# TODO: use powerthesaurus api to get synonyms
# TODO: synonym matches weight less than word search
# LATER: generate single/plural forms for keyword (
# https://www.ef.com/wwen/english-resources/english-grammar/singular-and-plural-nouns/
# go and went like: no need
# bird and birds, cat and catd like: need
# box and boxed, pitch and pitches, wish and wishes like: need
# penny and pennies, spy and spies, baby and babies like: need
# woman and women like: need
# child and children, person and people, leaf and leaves, life and lives like: need
# tooth and teeth llike: need?
#
# LATER: verb forms on he she it
# )
# ok: add dict for saving word that has no synonyms such as metamask
# ok: use powerthesaurus, it has more words than wordhippo
# ok: test paid proxy (mainland) => need identity verify
# ok: use nordvpn-switcher to fetch synonyms from sites => Not stable to use
# ok: test when i was banned from site-> check status code of response
# ok: for deployment, disable fetching synonyms from site beacuse one fetch takes ~1.5s
# ok: if app_id exist, check each keyword for synonym, if not exist, fetch and add it
