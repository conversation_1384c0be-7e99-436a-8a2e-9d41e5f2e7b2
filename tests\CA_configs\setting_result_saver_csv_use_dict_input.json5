{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "save_csv_result",
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "save_csv_result",
            "type": "PluginResultSaverCsv",
            "use": true,
            "save_path":"./tests/temp/test.csv",
            "use_manual_input":false,
            "num1":123,
            "str1":"strxx",
            "use_dict_input":true,
            "input_data_dict":
            {
                "num2":234, 
                "str2":"stryy",
            }
        }
    ]
   

}
