"""App plugin"""

from dataclasses import dataclass
import os
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginFilesRemover:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        file_path_list = parse_data_and_store(
            logger,
            "file_path_list",
            data_obj,
            step_config,
            config,
        )

        is_removed = True
        exception = None
        for file_path in file_path_list:
            if not os.path.exists(file_path):
                exception = f'"{file_path}" does not exist'
                is_removed = False
                break

            os.remove(file_path)
            if os.path.exists(file_path):
                is_removed = False

        data_obj.dict_var[f"{current_step}.is_removed"] = is_removed
        data_obj.dict_var[f"{current_step}.exception"] = exception

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
