from collections import defaultdict
import sys
from typing import Any, List, Dict


def split_a_dict_to_list_dict(
    dictx: Dict[str, Any],
    split_count: int,
) -> List[Dict[str, Any]]:
    """
    Splits a dictionary into a list of dictionaries based on the specified split count.

    Args:
        dictx (Dict[str, Any]): The dictionary to be split.
        split_count (int): The number of items in each split dictionary.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries, each containing up to `split_count` items.

    Example:
        >>> result = split_a_dict_to_list_dict({'a': 1, 'b': 2, 'c': 3}, 2)
        >>> print(result)
        [{'a': 1, 'b': 2}, {'c': 3}]
    """
    results = []
    items = {}
    for key in dictx.keys():
        items[key] = dictx[key]

        if len(items) % split_count == 0:
            results.append(items.copy())
            items.clear()

    if len(items) != 0:
        results.append(items.copy())

    return results


def init_nested_dict(keys: List[str], value: Any) -> Any:
    """
    Recursively initializes a nested dictionary based on the provided keys.

    Args:
        keys (List[str]): A list of keys representing the nested structure.
        value (Any): The value to be assigned to the innermost key.

    Returns:
        Any: The nested dictionary structure with the specified value at the innermost key.

    Example:
        >>> result = init_nested_dict(['layer1', 'layer2', 'layer3'], 'value')
        >>> print(result)
        {'layer1': {'layer2': {'layer3': 'value'}}}
    """
    if not keys:
        return value
    return {keys[0]: init_nested_dict(keys[1:], value)}


def set_nested_dict_value_by_key_list(
    base_dict: Dict,
    keys: List[str],
    value: Any,
    keys_must_exist: bool = False,
) -> Dict:
    """
    Updates a base dictionary by creating a nested structure based on the given keys
    and assigns the value to the innermost key.

    Args:
        base_dict (dict): The base dictionary to be updated.
        keys (List[str]): A list of keys representing the nested structure.
        value (Any): The value to be assigned to the innermost key.
        keys_must_exist (bool): If True, returns the original dict if the keys do not exist.

    Returns:
        dict: The updated base dictionary with the nested structure and assigned value.

    Example:
        >>> base = {}
        >>> set_nested_dict_value_by_key_list(base, ['layer1', 'layer2', 'layer3'], 'value')
        >>> print(base)
        {'layer1': {'layer2': {'layer3': 'value'}}}
        >>> base = {'existing': {'key': 'old_value'}}
        >>> set_nested_dict_value_by_key_list(base, ['existing', 'key'], 'new_value')
        >>> print(base)
        {'existing': {'key': 'new_value'}}
        >>> base = {'existing': {}}
        >>> set_nested_dict_value_by_key_list(base, ['existing', 'key'], 'new_value', keys_must_exist=True)
        >>> print(base)
        {'existing': {}}
    """
    current_dict = base_dict
    for key in keys[:-1]:
        if key not in current_dict:
            if keys_must_exist:
                return base_dict
            current_dict[key] = {}
        current_dict = current_dict[key]  # current_dict points to the inner dict.
    if keys_must_exist and keys[-1] not in current_dict:
        return base_dict
    current_dict[keys[-1]] = value
    return base_dict


def get_nested_dict_value_by_key_list(
    base_dict: Dict,
    keys: List[str],
    default: Any = None,
) -> Any:
    """
    Retrieves a value from a nested dictionary structure based on the given keys.

    Args:
        base_dict (dict): The base dictionary to retrieve the value from.
        keys (List[str]): A list of keys representing the nested structure.
        default (Any): The value to return if the specified keys do not exist.

    Returns:
        Any: The value at the innermost key if found, otherwise the default value.

    Example:
        >>> base = {'layer1': {'layer2': {'layer3': 'value'}}}
        >>> get_nested_dict_value_by_key_list(base, ['layer1', 'layer2', 'layer3'])
        'value'

        >>> base = {'layer1': {'layer2': {'layer3': 'value'}}}
        >>> get_nested_dict_value_by_key_list(base, ['layer1', 'layer2', 'layer4'], 'default_value')
        'default_value'
    """
    current_dict = base_dict
    for key in keys:
        if key in current_dict:
            current_dict = current_dict[key]
        else:
            return default
    return current_dict


def is_dict_contain_keys(dict: dict, contain_keys_list: list) -> bool:
    """
    Check if a dict contains all keys in contain_keys_list.

    Note:
    The dict must not be a nested dict.

    Args:
        dict (dict): The dictionary to check.
        contain_keys_list (list): A list of keys to check for in the dictionary.

    Returns:
        bool: True if all keys are present, False otherwise.

    Example:
        >>> is_dict_contain_keys({'a': 1, 'b': 2, 'c': 3}, ['a', 'c'])
        True

        >>> is_dict_contain_keys({'a': 1, 'b': 2, 'c': 3}, ['a', 'd'])
        False
    """
    is_contain = True
    for key in contain_keys_list:
        if not key in dict.keys():
            is_contain = False
            break

    return is_contain


def is_dict_contain_dict_items(dict: dict, contain_dict: dict) -> bool:
    """
    Check if a dict contains all keys and values in contain_dict.

    Note:
    The dict and contain_dict must not be nested dict.

    Args:
        dict (dict): The dictionary to check.
        contain_dict (dict): The dictionary containing keys and values to check for.

    Returns:
        bool: True if all keys and values are present, False otherwise.

    Example:
        >>> is_dict_contain_dict_items({'a': 1, 'b': 2}, {'a': 1})
        True

        >>> is_dict_contain_dict_items({'a': 1, 'b': 2}, {'a': 2})
        False
    """
    is_contain = True
    for key in contain_dict.keys():
        contain_value = contain_dict[key]

        if not key in dict.keys():
            is_contain = False
            break
        if not dict[key] == contain_value:
            is_contain = False
            break

    return is_contain


def extract_data_from_dict_contains(
    data_dict: dict,
    contain_dict: dict,
    extract_key: str,
    default_return: Any = None,  # Added default return value
) -> Any:
    """
    Extracts data from a nested dictionary based on a given condition.

    This function recursively searches through a nested dictionary to find a key-value pair that matches the given condition.
    The condition is defined by the `contain_dict` parameter, which specifies the keys and values to match.
    Once a match is found, the function extracts the value associated with the `extract_key` and returns it.

    Args:
        data_dict (dict): The dictionary to search through.
        contain_dict (dict): The dictionary that specifies the keys and values to match.
        extract_key (str): The key to extract the value for.
        default_return (Any): The value to return if no match is found (default is None).

    Returns:
        Any: The extracted value if a match is found, otherwise default_return.

    Examples:
        >>> data = {'a': [{'b': 12, 'c': 13}, {'b': 22, 'c': 23}]}
        >>> condition = {'b': 12}
        >>> extract_data_from_dict_contains(data, condition, 'c')
        13

        >>> data = {'x': {'y': 30, 'z': 40}, 'a': [{'b': 12}]}
        >>> condition = {'y': 30}
        >>> extract_data_from_dict_contains(data, condition, 'z')
        40

        >>> data = {'a': [{'b': 12, 'c': 13}, {'b': 22, 'c': 23}]}
        >>> condition = {'b': 99}
        >>> extract_data_from_dict_contains(data, condition, 'c', default_return='Not Found')
        'Not Found'
    """
    data = None
    for key in data_dict:
        value = data_dict[key]

        if isinstance(value, dict):
            if is_dict_contain_dict_items(value, contain_dict):
                if extract_key in value.keys():
                    return value[extract_key]
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, dict):
                    if is_dict_contain_dict_items(item, contain_dict):
                        if extract_key in item.keys():
                            return item[extract_key]

    return default_return  # Return default if no match is found


def extract_data_from_nested_dict_by_key_list(data_dict: dict, keys: list) -> Any:
    """
    Extracts data from a nested dictionary based on a list of keys.

    This function recursively searches through a nested dictionary to find the value associated with the last key in the provided list of keys.
    If any of the keys in the list do not exist in the dictionary, it returns None.

    Args:
        data_dict (dict): The dictionary to search through.
        keys (list): A list of keys to traverse the nested dictionary.

    Returns:
        Any: The extracted value if the keys are found, otherwise None.

    Examples:
        >>> data = {'a': {'b': {'c': 42}}}
        >>> extract_data_from_nested_dict_by_key_list(data, ['a', 'b', 'c'])
        42

        >>> data = {'x': {'y': 30, 'z': 40}, 'a': [{'b': 12}]}
        >>> extract_data_from_nested_dict_by_key_list(data, ['x', 'y'])
        30

        >>> extract_data_from_nested_dict_by_key_list(data, ['a', 'b', 'c'])
        None
    """
    current_dict = data_dict
    for key in keys:
        if isinstance(current_dict, dict) and key in current_dict:
            current_dict = current_dict[key]
        else:
            return None
    return current_dict


def extract_keys_from_dict_that_partly_contains(
    dictx: Dict[str, Any],
    text: str,
) -> List[str]:
    """
    Extracts keys from a dictionary where the key partly contains the given text.

    Args:
        dictx (Dict[str, Any]): The dictionary to search.
        text (str): The substring to look for in the keys.

    Returns:
        List[str]: A list of keys that contain the given text.

    Example:
        >>> extract_keys_from_dict_that_partly_contains({'apple': 1, 'banana': 2, 'grape': 3}, 'ap')
        ['apple', 'grape']
    """
    return [key for key in dictx.keys() if text in key]


def transform_by_relate_dict_keys(
    original_dict: Dict[str, List[str]]
) -> Dict[str, List[str]]:
    """
    Transforms a dictionary by finding all keys that are reachable from each key.

    This function performs a depth-first search (DFS) to find all related keys for each key in the original dictionary.
    The result is a new dictionary where each key maps to a sorted list of its related keys.

    Args:
        original_dict (Dict[str, List[str]]): The original dictionary where keys map to lists of related keys.

    Returns:
        Dict[str, List[str]]: A transformed dictionary with each key mapping to a list of related keys.

    Examples:
        ```python
        original = {
            "a": ["b"],
            "b": ["c"],
            "c": ["e", "f"],
        }
        result = transform_by_relate_dict_keys(original)
        # result will be {'a': ['b', 'c', 'e', 'f'], 'b': ['c', 'e', 'f'], 'c': ['e', 'f']}
        ```

        ```python
        original = {"x": [], "y": ["z"], "z": ["x"]}
        result = transform_by_relate_dict_keys(original)
        # result will be {'x': [], 'y': ['x', 'z'], 'z': ['x']}
        ```
    """
    transformed_dict = {}

    def dfs(node, visited):
        if node not in visited:
            visited.add(node)
            for neighbor in original_dict.get(node, []):
                dfs(neighbor, visited)
        return visited

    for key in original_dict:
        visited = set()
        all_reachable = dfs(key, visited)
        # Exclude the key itself
        related = list(all_reachable - {key})
        related.sort()
        transformed_dict[key] = related

    return transformed_dict
