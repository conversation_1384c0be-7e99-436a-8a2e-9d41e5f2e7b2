import sys
import datetime
import json
import os
import time
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
import base64
import logging

from freezegun import freeze_time
from tomsze_utils.api_utils import api_get_current_time_in_zone
from tomsze_utils.datetime_utils import get_current_system_datetime
from tomsze_utils.env_parser_utils import parse_env
from tomsze_utils.randome_utils import generate_date_string_dict

logger = logging.getLogger(__name__)


def string_to_bytes(input_string: str) -> bytes:
    """
    Converts a string to bytes using UTF-8 encoding.

    Parameters:
    ----------
    input_string: str
        The string to be converted to bytes.

    Returns:
    -------
    bytes
        The converted bytes representation of the string.

    Examples:
    ---------
    >>> string_to_bytes("Hello, World!")
    b'Hello, World!'

    >>> string_to_bytes("Python")
    b'Python'
    """
    return input_string.encode("utf-8")


def encrypt_data(data: bytes, key: bytes, return_as_string: bool = True) -> bytes | str:
    """
    Encrypts the given data using the provided key.

    Parameters:
    ----------
    data: bytes
        The data to be encrypted.
    key: bytes
        The key used for encryption. Must be of length 44.
    return_as_string: bool
        If True, return the encrypted data as a string instead of bytes. Defaults to True.

    Returns:
    -------
    bytes | str
        The encrypted data, either as bytes or as a string based on the return_as_string parameter.

    Examples:
    ---------
    >>> key = Fernet.generate_key()
    >>> encrypted = encrypt_data(b"Secret Data", key)
    >>> isinstance(encrypted, bytes)
    True

    >>> encrypted_str = encrypt_data(b"Secret Data", key, return_as_string=True)
    >>> isinstance(encrypted_str, str)
    True

    >>> encrypted = encrypt_data(b"Another Secret", key)
    >>> encrypted != b"Another Secret"
    True

    Note:
    -----
    The encrypted data will differ from the original data, which is expected and normal.
    """
    cipher_suite = Fernet(key)
    encrypted_data = cipher_suite.encrypt(data)
    return encrypted_data.decode("utf-8") if return_as_string else encrypted_data


def decrypt_data(
    encrypted_data: bytes | str, key: bytes, return_as_string: bool = True
) -> bytes | str:
    """
    Decrypts the given encrypted data using the provided key.

    Parameters:
    ----------
    encrypted_data: bytes | str
        The data to be decrypted. It can be either bytes or a string.
    key: bytes
        The key used for decryption.
    return_as_string: bool
        If True, return the decrypted data as a string instead of bytes. Defaults to True.

    Returns:
    -------
    bytes | str
        The decrypted data, either as bytes or as a string based on the return_as_string parameter.

    Raises:
    ------
    ValueError:
        If the decryption fails due to an invalid key or corrupted data.

    Examples:
    ---------
    >>> key = Fernet.generate_key()
    >>> encrypted = encrypt_data(b"Secret Data", key)
    >>> decrypted = decrypt_data(encrypted, key)
    >>> decrypted == b"Secret Data"
    True

    >>> decrypted_str = decrypt_data(encrypted, key, return_as_string=True)
    >>> decrypted_str == "Secret Data"
    True

    >>> encrypted = encrypt_data(b"Another Secret", key)
    >>> decrypted = decrypt_data(encrypted, key)
    >>> decrypted == b"Another Secret"
    True

    Handling Errors:
    -----------------
    To handle errors during decryption, wrap the call to this function in a try-except block:

    >>> try:
    ...     decrypted = decrypt_data(encrypted_data, wrong_key)
    ... except ValueError as e:
    ...     print(e)  # This will print the error message if decryption fails.
    """
    from cryptography.fernet import InvalidToken

    cipher_suite = Fernet(key)
    try:
        decrypted_data = cipher_suite.decrypt(encrypted_data)
    except InvalidToken:
        raise ValueError(
            "Decryption failed. The key may be invalid or the data may be corrupted."
        )

    return decrypted_data.decode("utf-8") if return_as_string else decrypted_data


def generate_key_from_string(
    string: str,
    salt: bytes = b"default_salt",  # Default value for salt (not random)
    iterations: int = 100000,
    length: int = 32,
) -> bytes:
    """
    Generate a key from a given string using PBKDF2HMAC.

    Parameters:
    ----------
    string: str
        The input string from which to derive the key.
    salt: bytes
        The salt to be used for key derivation.
    iterations: int
        The number of iterations for the key derivation function (default is 100000).
    length: int
        The length of the derived key (default is 32).

    Returns:
    -------
    bytes
        The derived key (length will be 44 bytes).

    Examples:
    ---------
    >>> key = generate_key_from_string("my_password")  # Using default salt
    >>> isinstance(key, bytes)
    True

    Usage with Fernet:
    -------------------
    After generating the key, you can use it with Fernet for encryption and decryption:

    >>> from cryptography.fernet import Fernet
    >>> key = generate_key_from_string("my_secret_password")  # Using default salt
    >>> f = Fernet(key)
    >>> data = b"Secret message!"
    >>> token = f.encrypt(data)  # Encrypting data
    >>> decrypted_data = f.decrypt(token)  # Decrypting data
    >>> decrypted_data == data
    True
    """
    # Derive the key using PBKDF2HMAC
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=length,  # Use the length from the function argument
        salt=salt,
        iterations=iterations,
    )

    # Derive the key and encode it
    key = base64.urlsafe_b64encode(kdf.derive(string.encode()))

    return key


def decrypt_and_parse_json(encrypted_data: bytes, key: bytes) -> dict | str:
    """
    Decrypts the given encrypted data and parses it into a dictionary.

    This function first decrypts the encrypted data using the provided key,
    then attempts to parse the decrypted string into a dictionary. If parsing fails,
    it returns the decrypted string.

    Args:
        encrypted_data (bytes): The encrypted data to be decrypted.
        key (bytes): The key used for decryption.

    Returns:
        dict | str: The decrypted data as a dictionary, or the decrypted string if parsing fails.

    Note: The decrypted string is modified to replace single quotes with double quotes.

    Examples:
    ---------
    ```python
    encrypted_data = encrypt_data(b'{"key": "value"}', key)
    result = decrypt_and_parse_json(encrypted_data, key)
    print(result)  # Outputs: {'key': 'value'}
    ```

    ```python
    encrypted_data = encrypt_data(b'Invalid JSON', key)
    result = decrypt_and_parse_json(encrypted_data, key)
    print(result)  # Outputs: 'Invalid JSON'
    ```
    """
    try:
        decrypted_data_str: str = decrypt_data(encrypted_data, key)
    except Exception:
        decrypted_data_str = encrypted_data.decode(
            "utf-8"
        )  # Set to encrypted data if decryption fails

    decrypted_data_str = decrypted_data_str.replace("'", '"')  # Needed, do not remove

    try:
        decrypted_data_dict = json.loads(decrypted_data_str)
        return decrypted_data_dict
    except json.JSONDecodeError as e:
        logger.error(e)
        return decrypted_data_str  # Return the decrypted string if JSON parsing fails


def generate_key_from_keys_and_api_timezone(
    key_1: str,
    key_2: str,
    time_zone: str = "Asia/Hong_Kong",
    time_format: str = "##%Y~~%m--%d**",
) -> bytes:
    """
    Generates a key based on two input strings and the current time from an API.

    Note: This function should be used on the client side. The time_zone should match the server side.

    Args:
        key_1 (str): The first key component.
        key_2 (str): The second key component.
        time_zone (str): The time zone to use for fetching the current time. Default is "Asia/Hong_Kong".
        time_format (str): The format for the current time string. Default is "##%Y~~%m--%d**".

    Returns:
        bytes: The generated key based on the input strings and the current time.

    Raises:
        ValueError: If there is an error getting the date from the API or if the date is not found in the response.

    Examples:
        ```python
        key = generate_key_from_keys_and_api_timezone("firstPart", "secondPart")
        print(key)  # Outputs a bytes object representing the generated key.
        ```

        ```python
        key = generate_key_from_keys_and_api_timezone("abc", "xyz")
        print(key)  # Outputs a different bytes object based on the current time from the API.
        ```
    """
    sys_datetime_dict = api_get_current_time_in_zone(time_zone)
    if sys_datetime_dict is None:
        raise ValueError("Error getting date from API.")
    sys_datetime_str = sys_datetime_dict.get("dateTime", None)
    if sys_datetime_str is None:
        raise ValueError("Date not found in the response.")

    # Format the datetime string according to the provided time_format
    # Convert to datetime object
    sys_datetime_str = sys_datetime_str.split(".")[
        0
    ]  # Remove the last part that causes problem.
    dt_object = datetime.datetime.fromisoformat(sys_datetime_str)

    # Desired output format (example: YYYY-MM-DD HH:MM:SS)
    formatted_datetime_str = dt_object.strftime(time_format)

    base_key_string = f"{key_1}{formatted_datetime_str}{key_2}"
    key = generate_key_from_string(base_key_string)
    return key


def generate_key_from_keys_and_sys_time(
    key_1: str, key_2: str, time_format: str = "##%Y~~%m--%d**"
) -> bytes:
    """
    Generates a key based on two input strings and the current system date.

    Note: This function should be used on the server side with the default time_format.

    Args:
        key_1 (str): The first key component.
        key_2 (str): The second key component.
        time_format (str): The format for the current system date. Default is "##%Y~~%m--%d**".

    Returns:
        bytes: The generated key based on the input strings and the current system date.

    Raises:
        ValueError: If the key generation fails due to invalid input or if the current system date cannot be retrieved.

    Examples:
        ```python
        key = generate_key_from_keys_and_sys_time("firstPart", "secondPart")
        print(key)  # Outputs a bytes object representing the generated key.
        ```

        ```python
        key = generate_key_from_keys_and_sys_time("abc", "xyz")
        print(key)  # Outputs a different bytes object based on the current date.
        ```
    """
    sys_datetime_str = get_current_system_datetime(time_format)
    if not sys_datetime_str:  # Check if the datetime string is valid
        raise ValueError("Failed to retrieve the current system date.")
    base_key_string = (
        f"{key_1}{sys_datetime_str}{key_2}"  # Updated length consideration
    )
    key = generate_key_from_string(base_key_string)
    return key


def generate_key_from_key_dict_and_sys_time(
    date_key_dict: dict,
    time_format: str = "##%Y~~%m--%d**",
) -> bytes:
    """
    Generates a key based on two input strings from a dictionary and the current system date.

    Note: This function should be used on the server side with the default time_format.

    Args:
        date_key_dict (dict): A dictionary containing key components, where the key is a string representing
                              the month and day (formatted as MMDD) and the value is a tuple of two strings
                              to be used as key components.
        time_format (str): The format for the current system date. Default is "##%Y~~%m--%d**".

    Returns:
        bytes: The generated key based on the input strings and the current system date.

    Raises:
        ValueError: If the key generation fails due to invalid input or if the current system date cannot be retrieved.

    Examples:
        ```python
        key = generate_key_from_key_dict_and_sys_time({"1225": ("firstPart", "secondPart")})
        print(key)  # Outputs a bytes object representing the generated key.
        ```

        ```python
        key = generate_key_from_key_dict_and_sys_time({"0101": ("abc", "xyz")})
        print(key)  # Outputs a different bytes object based on the current date.
        ```
    """
    sys_datetime_str = get_current_system_datetime(time_format)
    if not sys_datetime_str:  # Check if the datetime string is valid
        raise ValueError("Failed to retrieve the current system date.")

    dt_object = datetime.datetime.strptime(sys_datetime_str, time_format)

    month = dt_object.month
    day = dt_object.day
    formatted_month_day = (
        f"{month:02d}{day:02d}"  # Format month and day as two-digit numbers
    )

    # Extract key components from the dictionary
    key_1, key_2 = date_key_dict.get(formatted_month_day, ("Error", "Error"))

    assert key_1 != "Error"
    assert key_2 != "Error"

    base_key_string = f"{key_1}{sys_datetime_str}{key_2}"
    key = generate_key_from_string(base_key_string)
    return key


def generate_key_from_key_dict_and_api_timezone(
    date_key_dict: dict,
    time_zone: str = "Asia/Hong_Kong",
    time_format: str = "##%Y~~%m--%d**",
) -> bytes:
    """
    Generates a key based on two input strings from a dictionary and the current time from an API.

    This function is intended for client-side use, where the time_zone should match the server side.
    It retrieves the current time from the specified time zone and formats it according to the provided
    time_format. The key components are extracted from the date_key_dict based on the current month and day.

    Args:
        date_key_dict (dict): A dictionary containing key components, where the key is a string representing
                         the month and day (formatted as MMDD) and the value is a tuple of two strings
                         to be used as key components.
        time_zone (str): The time zone to use for fetching the current time. Default is "Asia/Hong_Kong".
        time_format (str): The format for the current time string. Default is "##%Y~~%m--%d**".

    Returns:
        bytes: The generated key based on the input strings and the current time.

    Raises:
        ValueError: If there is an error getting the date from the API or if the date is not found in the response.

    Examples:
        ```python
        key = generate_key_from_key_dict_and_api_timezone({"1225": ("firstPart", "secondPart")})
        print(key)  # Outputs a bytes object representing the generated key.
        ```

        ```python
        key = generate_key_from_key_dict_and_api_timezone({"0101": ("abc", "xyz")})
        print(key)  # Outputs a different bytes object based on the current time from the API.
        ```
    """

    sys_datetime_dict = api_get_current_time_in_zone(time_zone)
    if sys_datetime_dict is None:
        raise ValueError("Error getting date from API.")
    sys_datetime_str = sys_datetime_dict.get("dateTime", None)
    if sys_datetime_str is None:
        raise ValueError("Date not found in the response.")

    # Format the datetime string according to the provided time_format
    # Convert to datetime object
    sys_datetime_str = sys_datetime_str.split(".")[
        0
    ]  # Remove the last part that causes problem.
    dt_object = datetime.datetime.fromisoformat(sys_datetime_str)

    # Desired output format (example: YYYY-MM-DD HH:MM:SS)
    formatted_datetime_str = dt_object.strftime(time_format)

    # Combine month and day as a 4-digit string
    month = dt_object.month
    day = dt_object.day
    formatted_month_day = (
        f"{month:02d}{day:02d}"  # Format month and day as two-digit numbers
    )

    key_1, key_2 = date_key_dict.get(
        formatted_month_day, ("Error", "Error")
    )  # Unpack the tuple

    assert key_1 != "Error"
    assert key_2 != "Error"

    base_key_string = f"{key_1}{formatted_datetime_str}{key_2}"
    key = generate_key_from_string(base_key_string)
    return key


def main():
    # # Example usage of generate_key_from_key_dict_and_api_timezone
    # date_key_dict = generate_date_string_dict(num_strings=2, string_length=10)

    # try:
    #     key = generate_key_from_key_dict_and_api_timezone(date_key_dict)
    #     print(
    #         f"Generated key: {key}"
    #     )  # Outputs a bytes object representing the generated key.
    # except ValueError as e:
    #     print(f"An error occurred: {e}")

    # To generate test key and encrypted data for java function
    key = generate_key_from_string("my_secret_password")
    fernet = Fernet(key)

    with freeze_time("2011-01-11T11:11:11Z"):
        encrypt_data_bytes = fernet.encrypt(b"Hello, World!")

    logger.info(key)
    logger.info(encrypt_data_bytes)


if __name__ == "__main__":
    sys.exit(main())
