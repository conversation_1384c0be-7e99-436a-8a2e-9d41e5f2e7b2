import sys
import requests


def api_get_current_time_in_zone(time_zone: str = "Asia/Hong_Kong") -> dict:
    """
    Retrieve the current time for a specified time zone using timeapi.io.

    Examples:
        Example 1:
        ```python
        current_time = get_current_time_in_zone("Europe/London")
        ```
        returns a dictionary containing the current time in London.

        Example 2:
        ```python
        current_time = get_current_time_in_zone("America/New_York")
        ```
        returns a dictionary containing the current time in New York.

    Note: The default time zone is "Asia/Hong_Kong".
    """
    url = f"https://timeapi.io/api/time/current/zone?timeZone={time_zone}"
    headers = {"accept": "application/json"}
    response = requests.get(url, headers=headers)
    return response.json() if response.status_code == 200 else None


def main():
    time_zone = "Europe/Amsterdam"
    current_time = api_get_current_time_in_zone(time_zone)
    if current_time:
        print(f"Current time in {time_zone}: {current_time}")
    else:
        print("Failed to retrieve the current time.")


if __name__ == "__main__":
    sys.exit(main())
