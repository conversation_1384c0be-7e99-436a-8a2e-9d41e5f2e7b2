{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "read_env",
            "ssh_docker_compose_up",
            "wait_for_5s",
            "log_docker_compose",
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "read_env",
            "type": "PluginEnvReader",
            "use": true,
            "use_env_path": true,
            "env_path": "./.env"
        },
        {
            "step_name": "remove_old_deploy_zip",
            "type": "PluginFilesRemover",
            "use": true,
            "file_path_list": [
                './deploy.zip',
            ],
        },
         {
            "step_name": "zip_dir",
            "type": "PluginDirZipper",
            "use": true,
            "folder_to_zip_path": "./",
            "zip_name": "deploy.zip",
            "exclude_path_list": [
                "./pickle_db",
                "./.git",
                "./.vscode",
                "./server_log",
                "./tmp_CA_logs",
                "./docs",
            ],

            "save_to_folder_path": "./",
        },
        {
            "step_name": "ssh_make_code_dir",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
mkdir /root/code/;",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "ssh_make_proj_dir",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
mkdir /root/code/{read_env.REPO};",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "send_zip_to_server",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
scp \
-P {read_env.SSH_PORT} \
./{zip_dir.zip_name} \
{read_env.SSH_USER}@{read_env.SSH_HOST}:/root/code/{read_env.REPO}",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "ssh_unzip",
            "type": "PluginCommandRunner",
            "use": true,
            // -o to always replace
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
unzip -o /root/code/{read_env.REPO}/{zip_dir.zip_name} -d /root/code/{read_env.REPO};",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "ssh_docker_compose_up",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
cd /root/code/{read_env.REPO}/; \
docker-compose up --build --force-recreate -d; \
exit;",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "wait_for_5s",
            "type": "PluginWaiter",
            "use": true,
            "wait_time": 5
        },
        {
            "step_name": "log_docker_compose",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
cd /root/code/{read_env.REPO}/; \
docker-compose logs --tail 1000 --timestamps; \
exit;",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "delete_zip",
            "type": "PluginFilesRemover",
            "use": true,
            "file_path_list": [
                './deploy.zip',
            ],
        }
    ]
   

    

}
