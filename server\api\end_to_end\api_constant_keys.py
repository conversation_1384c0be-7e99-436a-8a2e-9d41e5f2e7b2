from dataclasses import dataclass


@dataclass
class AccessTokeDataConstant:
    USER: str = "user"
    ADMIN: str = "admin"


@dataclass
class ClientColEnum:
    ALLOWSEARCH: str = "allowSearch"
    APPIDS: str = "appIds"
    SCRAPERESULTS: str = "scrape_results"
    QUERY: str = "query"


@dataclass
class DBEmailVericiationColEnum:
    VERIFICATION_CODE: str = "verification_code"


@dataclass
class DBUserDataColEnum:
    EMAIL_ADDRESS: str = "email_address"
    HASHED_PASSWORD: str = "hashed_password"
    IS_VERIFIED: str = "is_verified"


@dataclass
class DBAppsColEnum:
    IND: str = "ind"
    TITLE: str = "title"
    DESC: str = "desc"
    EMBD: str = "embd"
    KEYW: str = "keyw"
    SOURCE: str = "scrape_source"


@dataclass
class DBSnnsColEnum:
    KEYW: str = "keyw"


@dataclass
class ApiResponseKeys:
    API_RESPONSE: str = "ApiResponse"
    API_RESULT: str = "ApiResult"
    API_ERROR: str = "ApiError"


@dataclass
class ApiResponseErrorMessage:
    API_INTERNAL_SERVER_ERROR: str = "ApiInternalServerError"
    API_MISSING_ACCESS_TOKEN: str = "ApiMissingAccessToken"


@dataclass
class ApiFunResponseKeys:
    DEBUG_SIM: str = "sim"
    DEBUG_SIM_WORD: str = "sim_word"
    DEBUG_SIM_WORD_SYN: str = "sim_word_syn"
    RESULT: str = "ApiFunResult"
    ERROR: str = "ApiFunError"


@dataclass
class DBToAddColEnum:
    CHECK_DATE_TIME: str = "check_datetime"
    NOT_FOUND_PLAYSTORE: str = "not_found_playstore"


class EndPointsConstants:
    APP_NAME = "/__whatappsdo"
    ROOT = "/"
    IS_SERVER_ONLINE = "/__is_server_online"
    REQUEST_DATETIME = "/__request_datetime"
    SEND_VERIFICATION_EMAIL = "/__send_verification_email"
    SIGN_UP = "/__sign_up"
    SIGN_IN = "/__sign_in"
    SIGN_IN_ACCESS_TOKEN = "/__sign_in_access_token"
    SIGN_OUT = "/__sign_out"
    SEARCH_USER_APP = "/__search_user_app"
    REQUEST_FAKE_RESOURCE = "/__request_fake_resource"
    CHECK_CLIENT_APP = "/__check_client_apps"
    IS_USER_SIGNED_UP = "/__is_user_signed_up"
    REMOVE_USER = "/__remove_user"
    REQUEST_RESOURCE_X = "/__request_resource_x"
    GET_DASHBOARD_INFO = "/__get_dashboard_info"
    VALIDATE_USER_ACCESS_TOKEN = "/__validate_user_access_token"
    OBTAIN_VERIFICATION_CODE = "/__obtain_verification_code"
    OBTAIN_NEW_ACCESS_TOKEN_BY_REFRESH_TOKEN = (
        "/__obtain_new_access_token_by_refresh_token"
    )


class PostDataKeyConstants:
    EMAIL_ADDRESS = "email_address"
    PASSWORD = "password"
    REFRESH_TOKEN = "refresh_token"
    VERIFICATION_CODE = "verification_code"
    ACCESS_TOKEN = "access_token"
