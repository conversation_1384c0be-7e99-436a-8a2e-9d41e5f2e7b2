{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "step_name_to_be_replaced",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "step_name_to_be_replaced",
            "type": "PluginXXX",
            "use": true,
            "variable_to_be_replace": 123,
        }
    ]
   

}
