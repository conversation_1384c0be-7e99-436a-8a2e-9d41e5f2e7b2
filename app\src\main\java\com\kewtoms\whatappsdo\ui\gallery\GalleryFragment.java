package com.kewtoms.whatappsdo.ui.gallery;

import static com.kewtoms.whatappsdo.utils.ListUtils.nestedLookupOnListObject;

import android.app.AlertDialog;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.kewtoms.whatappsdo.data.Person;
import com.kewtoms.whatappsdo.databinding.FragmentGalleryBinding;
import com.kewtoms.whatappsdo.utils.InternetUtils;


import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Reader;
import java.io.Writer;
import java.util.Arrays;
import java.util.List;

public class GalleryFragment
  extends Fragment {

  public static final String TAG = "APP:GalleryFragment";
  private FragmentGalleryBinding binding;
  private File cacheDir;
  private String jsonPath;

  public View onCreateView(
    @NonNull LayoutInflater inflater,
    ViewGroup container,
    Bundle savedInstanceState) {

    GalleryViewModel galleryViewModel =
      new ViewModelProvider(this).get(GalleryViewModel.class);

    binding = FragmentGalleryBinding.inflate(
      inflater,
      container,
      false
    );
    View root = binding.getRoot();

    final TextView textView = binding.textGallery;
    galleryViewModel.getText().observe(
      getViewLifecycleOwner(),
      textView::setText
    );

    // Test button
    cacheDir = requireActivity().getCacheDir();
    jsonPath = new File(
      cacheDir,
      "test.json"
    ).toString();

    binding.testButtonWriteJson.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {

        Person person = new Person(
          "John Doe",
          30
        );
        Gson gson = new Gson();
        try (Writer writer = new FileWriter(jsonPath)) {
          gson.toJson(
            person,
            writer
          );
        } catch (IOException e) {
          Log.e(
            TAG,
            "onClick: " + e.toString()
          );
        }
      }
    });

    binding.testButtonReadJson.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        Gson gson = new Gson();

        try (Reader reader = new FileReader(jsonPath)) {
          Person person = gson.fromJson(
            reader,
            Person.class
          );
          System.out.println(person.getName() + ", " + person.getAge());
        } catch (IOException e) {
          Log.e(
            TAG,
            "onClick: " + e.toString()
          );
        }
      }
    });

    binding.testButtonShowDialog.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        new AlertDialog.Builder(requireActivity()).setTitle("Test Dialog")
          .setMessage("This is a test dialog with OK button.")
          .setPositiveButton(
            "OK",
            (dialog, which) -> dialog.dismiss()
          )
          .setCancelable(false)
          .create()
          .show();
      }
    });

    binding.testButtonCheckInternetConnection.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        Boolean hasInternet =
          InternetUtils.isInternetAvailable(requireActivity());
      }
    });

    binding.testButtonParseAFInitData.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {

        String jsonString =
          "{\"data\":[[[1],\"2\"]],\"sideChannel\":{}}";

        ObjectMapper objectMapper = new ObjectMapper();
        try {
          JsonNode rootNode = objectMapper.readTree(jsonString);
          JsonNode dataNode = rootNode.get("data");

          if (dataNode.isArray()) {
            List<Object> dataList = objectMapper.convertValue(
              dataNode,
              new TypeReference<List<Object>>() {
              }
            );

            List<Integer> indexes = Arrays.asList(
              0,
              0,
              0
            );
            Object extracted = nestedLookupOnListObject(
              dataList,
              indexes
            );

            int g = 1;
          }

        } catch (Exception e) {
          Log.e(
            TAG,
            "onClick: " + e.toString()
          );
        }

      }
    });

    return root;
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    binding = null;
  }
}