import os
import tempfile

import pytest

from tomsze_utils.text_file_modify_utils import (
    append_string_to_file,
    replace_file_line_contains,
    replace_file_text,
    write_string_list_to_file,
    write_text_to_file,
)


def test_replace_file_text():
    fake_text_file_path = r"./tests/fake_text_file_to_test/fake1.txt"
    assert os.path.exists(fake_text_file_path)

    update_file, write_to_file_path = replace_file_text(
        file_path=fake_text_file_path,
        replace_text="old",
        replace_as="new",
        overwrite=False,
    )

    assert update_file == True
    file = open(write_to_file_path)
    lines = file.readlines()
    assert "new" in lines[0]
    assert "new" in lines[1]


def test_replace_file_line_contains():
    fake_text_file_path = (
        r"./tests/fake_text_file_to_test/fake_replace_line_contain.txt"
    )
    assert os.path.exists(fake_text_file_path)

    update_file, write_to_file_path = replace_file_line_contains(
        file_path=fake_text_file_path,
        contains="var1=",
        replace_as="var1 = 'new 1'",
        overwrite=False,
    )

    update_file, write_to_file_path = replace_file_line_contains(
        file_path=write_to_file_path,
        contains="var3 =",
        replace_as="var3 = 'new 3'",
        overwrite=False,
    )

    update_file, write_to_file_path = replace_file_line_contains(
        file_path=write_to_file_path,
        contains="var4 =",
        replace_as="var4 = 'new 4'",
        overwrite=False,
    )

    update_file, write_to_file_path = replace_file_line_contains(
        file_path=write_to_file_path,
        contains="var5 =",
        replace_as="var5 = 'new 5'",
        overwrite=False,
    )

    assert update_file == True
    file = open(write_to_file_path)
    lines = file.readlines()
    assert "var1 = 'new 1'\n" == lines[2]
    assert "var3 = 'new 3'\n" == lines[4]
    assert "    var4 = 'new 4'\n" == lines[5]
    assert "        var5 = 'new 5'\n" == lines[6]


class TestWriteStringListToFile:

    def test_write_string_list_to_file(self):
        fake_text_file_path = (
            r"./tests/fake_text_file_to_test/fake_write_string_list.txt"
        )
        assert os.path.exists(fake_text_file_path)

        string_list = [
            "string1",
            "string2",
        ]
        update_file, write_to_file_path = write_string_list_to_file(
            file_path=fake_text_file_path,
            string_list=string_list,
            add_newline=True,
            overwrite=False,
        )

        assert update_file == True
        file = open(write_to_file_path)
        lines = file.readlines()

        assert "string1\n" == lines[0]
        assert "string2\n" == lines[1]

    def test_write_string_list_to_file_with_code(self):
        temp_dir = tempfile.TemporaryDirectory()

        text_file_path = os.path.join(temp_dir.name, "test_file.txt")

        string_list = [
            "def sum_two_numbers(a, b):",
            "    return a + b",
        ]
        with open(text_file_path, "w") as file:
            file.write("")

        update_file, write_to_file_path = write_string_list_to_file(
            file_path=text_file_path,
            string_list=string_list,
            add_newline=True,
            overwrite=True,
        )

        assert update_file == True
        with open(write_to_file_path) as file:
            lines = file.readlines()

        assert "def sum_two_numbers(a, b):\n" == lines[0]
        assert "    return a + b\n" == lines[1]

        temp_dir.cleanup()


class TestWriteTextToFile:

    def test_write_text_to_file_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_file.txt")
            text_content = "Hello, World!"
            result = write_text_to_file(file_path, text_content)
            assert result == f"file: {file_path} created with text: {text_content}"
            with open(file_path, "r") as file:
                content = file.read()
                assert content == text_content

    def test_write_text_to_file_overwrite(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_file.txt")
            initial_content = "Initial content."
            write_text_to_file(file_path, initial_content)

            new_content = "New content."
            result = write_text_to_file(file_path, new_content)
            assert result == f"file: {file_path} created with text: {new_content}"
            with open(file_path, "r") as file:
                content = file.read()
                assert content == new_content


class TestAppendStringToFile:

    def test_append_string_to_file_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_file.txt")
            initial_content = "Initial content."
            write_text_to_file(file_path, initial_content)

            string_to_append = "Appended content."
            result = append_string_to_file(file_path, string_to_append)

            assert (
                result
                == f"Appended to file: {file_path} the string: {string_to_append}"
            )
            with open(file_path, "r") as file:
                content = file.read()
                assert content == f"{initial_content}\n{string_to_append}"

    def test_append_string_to_file_no_newline(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_file.txt")
            initial_content = "Initial content."
            write_text_to_file(file_path, initial_content)

            string_to_append = "Appended content."
            result = append_string_to_file(file_path, string_to_append)

            # Append again without a newline
            result = append_string_to_file(file_path, string_to_append)

            with open(file_path, "r") as file:
                content = file.read()
                assert (
                    content
                    == f"{initial_content}\n{string_to_append}\n{string_to_append}"
                )
