package com.kewtoms.whatappsdo.utils;

import android.content.Context;

import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;
import java.security.GeneralSecurityException;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@RunWith(AndroidJUnit4.class)
public class SecurePrefsManagerTest {

  @Before
  public void setup() {
    Context context =
      InstrumentationRegistry.getInstrumentation().getTargetContext();
    try {
      MasterKey masterKey =
        new MasterKey.Builder(context).setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
          .build();
      EncryptedSharedPreferences sharedPreferences =
        (EncryptedSharedPreferences) EncryptedSharedPreferences.create(
          context,
          "secure_prefs",
          masterKey,
          EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
          EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        );
      sharedPreferences.edit().clear().apply();
    } catch (GeneralSecurityException | IOException e) {
      e.printStackTrace();
    }
  }

  @Test
  public void testSaveAndGetAccessToken() {
    Context context =
      InstrumentationRegistry.getInstrumentation().getTargetContext();
    String token = "sample_token_123";
    SecurePrefsManager.saveAccessToken(
      context,
      token
    );
    String retrievedToken =
      SecurePrefsManager.getAccessToken(context);
    assertEquals(
      token,
      retrievedToken
    );
  }

  @Test
  public void testGetAccessTokenWhenNotSaved() {
    Context context =
      InstrumentationRegistry.getInstrumentation().getTargetContext();
    String retrievedToken =
      SecurePrefsManager.getAccessToken(context);
    assertNull(retrievedToken);
  }

  @Test
  public void testSaveNullToken() {
    Context context =
      InstrumentationRegistry.getInstrumentation().getTargetContext();
    SecurePrefsManager.saveAccessToken(
      context,
      null
    );
    String retrievedToken =
      SecurePrefsManager.getAccessToken(context);
    assertNull(retrievedToken);
  }
}