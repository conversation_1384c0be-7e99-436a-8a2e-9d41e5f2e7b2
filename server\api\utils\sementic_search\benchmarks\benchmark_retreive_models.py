from server.api.utils.sementic_search.sementic_search_methods import (
    create_bi_encoder,
    retrieve,
)
from server.api.utils.sementic_search.benchmarks.id_descriptions import id_descriptions
from server.api.utils.sementic_search.benchmarks.models import models
from server.api.utils.sementic_search.benchmarks.languages import languages
from server.api.utils.sementic_search.benchmarks.query_answer import (
    query_answers_eng,
    query_answers_chn,
)

topk = 32
rerank = False

for model in models:
    print("Using model:", model)
    bi_encoder_model = model
    # cross_encoder_model = 'cross-encoder/ms-marco-MiniLM-L-6-v2'

    bi_encoder = create_bi_encoder(bi_encoder_model)
    # cross_encoder = create_cross_encoder(cross_encoder_model)

    app_descriptions = list(id_descriptions.values())
    corpus_embeddings = bi_encoder.encode(
        app_descriptions, convert_to_tensor=True, show_progress_bar=True
    )
    corpus_embeddings = corpus_embeddings.cuda()

    num_correct = 0

    for language in languages:
        if language == "eng":
            query_answers = query_answers_eng
        if language == "chn":
            query_answers = query_answers_chn

        for query in query_answers.keys():
            answer = query_answers[query]
            print("Input question:", query)

            top_id, ids, hits = retrieve(
                bi_encoder,
                query,
                corpus_embeddings,
                topk,
            )

            # result = list(data.keys())[top_id]
            results = [list(id_descriptions.keys())[id] for id in ids]
            results = results[:2]

            print(f"search result: {results}")
            print(f"answer: {query_answers[query]}")
            # if results != answer:
            if answer not in results:
                print(f"X")
            else:
                print(f"V")
                num_correct += 1
            print()

    print(f"model:{model}, num_correct {num_correct} / {len(query_answers)}")

    # avg_score = np.mean([hit['score'] for hit in hits])
    # print(f'avg score: {avg_score}')
