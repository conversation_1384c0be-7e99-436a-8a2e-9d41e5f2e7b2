import os
from pathlib import Path
import sys
import pandas as pd
from sentence_transformers import SentenceTransformer, util

from utils import convert_to_embedding_using_huggingface_model, convert_to_embedding, load_app_data
import pickle

embedder = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')

   
def main():
    # Load app descriptions (here only appstore, android needs TODO).
    num_total = 1000
    num_rows_per_batch = 80
    # model_id = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    model_id = "paraphrase-multilingual-MiniLM-L12-v2"
    list_id, list_list_description = load_app_data(
                                        num_total=num_total,
                                        num_rows_per_batch=num_rows_per_batch,
                                        platform='android',
                                    )
    
    # Convert the descriptions to embeddings.
    final_list_description = []
    final_list_embeddings = []
    for list_description in list_list_description:
        # list_embeddings = convert_to_embedding_using_huggingface_model(
        #                     model_id=model_id,
        #                     list_text=list_description, 
        #                 )
        
        # list_embeddings = embedder.encode(embedder, list_description, convert_to_tensor=True)     # <<<<<<<<<<<<<<<<<<<<<
        list_embeddings = convert_to_embedding(embedder, 
                                                                   list_description,
                                                                   device='cpu' ,
                                                                   batch_size=128
                                                                   )

        final_list_embeddings += list_embeddings
        final_list_description += list_description
    
    # Save the variables to pickle files.
    script_directory_path = os.path.dirname(os.path.abspath(sys.argv[0]))
    id_pickle_path = os.path.join(script_directory_path, f'004_ids_{model_id}.pickle')
    description_pickle_path = os.path.join(script_directory_path, f'004_descriptions_{model_id}.pickle')
    embedding_pickle_path = os.path.join(script_directory_path, f'004_embeddings_{model_id}.pickle')
    
    with open(id_pickle_path, 'wb') as f:
        pickle.dump(list_id, f)
    
    with open(description_pickle_path, 'wb') as f:
        pickle.dump(final_list_description, f)
        
    with open(embedding_pickle_path, 'wb') as f:
        pickle.dump(final_list_embeddings, f)
    
    
if __name__ == "__main__":
    sys.exit(main())