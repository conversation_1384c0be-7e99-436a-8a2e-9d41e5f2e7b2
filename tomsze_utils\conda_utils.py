from typing import List, Tuple

from tomsze_utils.subprocess_utils import subprocess_run_with_str_output


def create_conda_environment(
    env_name: str,
    python_version: str,
) -> Tuple:
    """
    Creates a conda environment with the specified name and packages.

    Args:
        env_name (str): The name of the conda environment to create.
        packages (list, optional): A list of packages to install in the environment. Defaults to None.

    Returns:
        tuple: A tuple containing the subprocess.CompletedProcess object and the output string.

    Examples:
        >>> result, output_str = create_conda_environment("myenv", ["numpy", "pandas"])
        >>> print(output_str)
    """
    command = f"conda create --name {env_name} python={python_version}"
    command += " -y"  # Automatically confirm the installation

    return subprocess_run_with_str_output(command, shell=False)


def pip_install_packages_in_conda_env(
    env_name: str,
    packages: List[str],
) -> Tuple:
    """
    Installs specified packages in a given conda environment.

    Args:
        env_name (str): The name of the conda environment where packages will be installed.
        packages (List[str]): A list of packages to install in the environment, with optional versions (e.g., ["numpy", "pandas==1.3.0"]).

    Returns:
        Tuple: A tuple containing the subprocess.CompletedProcess object and the output string.

    Examples:
        ```python
        result, output_str = pip_install_packages_in_conda_env(env_name="myenv", packages=["numpy==1.21.0", "pandas==1.3.0"])
        print(output_str)
        ```

        ```python
        result, output_str = pip_install_packages_in_conda_env(env_name="test_env", packages=["scipy", "matplotlib==3.4.3"])
        print(output_str)
        ```
    """
    package_str = " ".join(packages)
    command = f"conda run -n {env_name} pip install {package_str}"

    return subprocess_run_with_str_output(command, shell=False)
