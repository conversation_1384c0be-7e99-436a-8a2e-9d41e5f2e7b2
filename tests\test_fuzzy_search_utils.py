import pytest


from tomsze_utils.fuzzy_search_utils.fuzzy_search_utils import (
    fuzzy_match_has_space,
)


class TestFuzzyMatchHasSpace:

    def test_fuzzy_match_has_space_basic(self):
        text = "apple pie pies"
        typed = "pie"
        expected_result = [(6, 9)]
        result = fuzzy_match_has_space(text, typed)
        assert result == expected_result

    def test_fuzzy_match_has_space_basic_with_capital(self):
        text = "Apple pie pies"
        typed = "apple"
        expected_result = [(0, 5)]
        result = fuzzy_match_has_space(text, typed)
        assert result == expected_result

    def test_fuzzy_match_has_space_multiple_matches(self):
        text = "apple pie pies"
        typed = "pie pie"
        expected_result = [(6, 9), (10, 13)]
        result = fuzzy_match_has_space(text, typed)
        assert result == expected_result

    def test_fuzzy_match_has_space_no_match(self):
        text = "hello world"
        typed = "abc"
        expected_result = []
        result = fuzzy_match_has_space(text, typed)
        assert result == expected_result

    def test_fuzzy_match_has_space_empty_typed(self):
        text = "hello world"
        typed = ""
        expected_result = []
        result = fuzzy_match_has_space(text, typed)
        assert result == expected_result
