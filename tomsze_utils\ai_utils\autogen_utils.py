import logging
import os
import sys
import tempfile
from tomsze_utils.import_utils import (
    get_callable_from_module_by_function_name,
    import_module_by_path,
)
from tomsze_utils.script_analyse_utils import extract_function_names_from_script_in_dir
from autogen import ConversableAgent

logger = logging.getLogger(__name__)


def register_tool_functions_in_dir(
    dir: str,
    ai_assistant: ConversableAgent,
    user_proxy: ConversableAgent,
    num_functions: int = None,  # New argument to set the number of functions to register
    function_names: list = None,  # New argument to specify a list of function names to register
) -> None:
    """Register tool functions found in the specified directory with the AI assistant and user proxy.

    This function extracts function metadata from Python scripts in the given directory and registers
    the functions with the provided AI assistant and user proxy.

    Args:
        dir (str): The directory containing Python scripts with functions to register.
        ai_assistant (ConversableAgent): The AI assistant to register functions with.
        user_proxy (ConversableAgent): The user proxy to register functions for execution.
        num_functions (int, optional): The maximum number of functions to register. If None, all functions will be registered.
                                       Must be a positive integer if specified.
        function_names (list, optional): A list of specific function names to register. If None, all functions will be registered.

    Examples:
        ```python
        register_tool_functions_in_dir(
            dir="/path/to/scripts",
            ai_assistant=my_ai_assistant,
            user_proxy=my_user_proxy,
            num_functions=5,
            function_names=["add", "subtract"]
        )
        ```

        ```python
        register_tool_functions_in_dir(
            dir="/another/path/to/scripts",
            ai_assistant=another_ai_assistant,
            user_proxy=another_user_proxy,
            num_functions=3,
            function_names=["multiply"]
        )
        ```

    Note: Ensure that the functions in the scripts have appropriate decorators for registration.
    """
    if num_functions is not None:
        if not isinstance(num_functions, int) or num_functions <= 0:
            raise ValueError("num_functions must be a positive integer or None.")

    function_meta_list = extract_function_names_from_script_in_dir(dir)

    if function_names is not None:
        function_meta_list = [
            meta for meta in function_meta_list if meta.function_name in function_names
        ]

    if num_functions is not None:
        if num_functions > len(function_meta_list):
            logger.warning(
                f"Requested number of functions ({num_functions}) exceeds available functions ({len(function_meta_list)}). Registering all available functions instead."
            )
            num_functions = len(function_meta_list)
        function_meta_list = function_meta_list[
            :num_functions
        ]  # Limit the number of functions to register

    for function_meta in function_meta_list:
        logger.info(
            f"Registering function: {function_meta.function_name} in script: {function_meta.file_path}"
        )
        function_scrip_path = function_meta.file_path
        function_name = function_meta.function_name
        error, module = import_module_by_path(function_scrip_path)

        if error:
            logger.error(error)
            continue

        function_callable = get_callable_from_module_by_function_name(
            module=module,
            function_name=function_name,
        )
        ai_assistant.register_for_llm(
            name=function_meta.function_name,
            description=function_meta.function_description,
        )(function_callable)

        user_proxy.register_for_execution(name=function_name)(function_callable)


def main():

    model = "qwen2.5:latestxxx"
    config_list = [
        {
            "model": model,  # ok
            "base_url": "http://localhost:11434/v1",
            "api_key": model,
            "price": [0, 0],
        }
    ]

    assistant = ConversableAgent(
        name="Assistant",
        system_message="You are a helpful AI assistant. "
        "Response '/TERMINATE' when if you think the task is done.",
        llm_config={"config_list": config_list},
    )

    # The user proxy agent is used for interacting with the assistant agent
    # and executes tool calls.
    user_proxy = ConversableAgent(
        name="User",
        llm_config=False,
        is_termination_msg=lambda msg: msg.get("content") is not None
        and "TERMINATE" in msg["content"],
        human_input_mode="NEVER",
        default_auto_reply="Have you finished your task?",
    )

    assert assistant.llm_config.get("tools", None) is None
    assert user_proxy.function_map == {}

    # Create a temporary directory and a Python file with a valid function
    temp_dir = tempfile.TemporaryDirectory()

    # os.chdir(temp_dir)
    script_content = """
def add(x:int, y:int)->int:
    '''add two numbers'''
    return x + y
"""
    script_path = os.path.join(temp_dir.name, "add_function.py")
    with open(script_path, "w") as f:
        f.write(script_content)

    # Call the function to register tool functions
    register_tool_functions_in_dir(temp_dir.name, assistant, user_proxy)

    # Check if the function was registered correctly
    assert assistant.llm_config.get("tools") is not None
    assert user_proxy.function_map is not None

    temp_dir.cleanup()


if __name__ == "__main__":
    sys.exit(main())
