from tomsze_utils.dict_utils import (
    extract_data_from_dict_contains,
    extract_data_from_nested_dict_by_key_list,
    extract_keys_from_dict_that_partly_contains,
    get_nested_dict_value_by_key_list,
    init_nested_dict,
    is_dict_contain_dict_items,
    is_dict_contain_keys,
    set_nested_dict_value_by_key_list,
    split_a_dict_to_list_dict,
    transform_by_relate_dict_keys,
)


def test_split_dict():
    results = split_a_dict_to_list_dict(
        {
            "k1": "d1",
            "k2": "d2",
            "k3": "d3",
        },
        split_count=2,
    )
    expect = [
        {
            "k1": "d1",
            "k2": "d2",
        },
        {
            "k3": "d3",
        },
    ]

    assert expect == results

    results = split_a_dict_to_list_dict(
        {
            "k1": "d1",
            "k2": "d2",
        },
        split_count=2,
    )
    expect = [
        {
            "k1": "d1",
            "k2": "d2",
        }
    ]

    assert expect == results


def test_init_nested_dict():
    nested_dict = init_nested_dict(
        ["layer1", "layer2", "layer3"],
        "value",
    )
    expect = {"layer1": {"layer2": {"layer3": "value"}}}
    assert nested_dict == expect


def test_set_nested_dict():
    dict_x = {}
    nested_dict = set_nested_dict_value_by_key_list(
        dict_x,
        ["layer1", "layer2", "layer3"],
        "value0",
    )
    expect = {"layer1": {"layer2": {"layer3": "value0"}}}

    assert nested_dict == expect

    nested_dict = set_nested_dict_value_by_key_list(
        dict_x,
        ["layer1", "layer2", "layer3"],
        "value",
    )
    expect = {"layer1": {"layer2": {"layer3": "value"}}}

    assert nested_dict == expect

    nested_dict = set_nested_dict_value_by_key_list(
        dict_x,
        ["layer1", "layer2", "layer3b"],
        "value3b",
    )
    expect = {
        "layer1": {
            "layer2": {
                "layer3": "value",
                "layer3b": "value3b",
            }
        },
    }

    assert nested_dict == expect

    nested_dict = set_nested_dict_value_by_key_list(
        dict_x,
        ["layer1", "layer2b", "layer3c"],
        "value3c",
    )

    expect = {
        "layer1": {
            "layer2": {
                "layer3": "value",
                "layer3b": "value3b",
            },
            "layer2b": {
                "layer3c": "value3c",
            },
        },
    }

    assert nested_dict == expect

    nested_dict = set_nested_dict_value_by_key_list(
        dict_x,
        ["layer1b", "layer2x", "layer3y"],
        "value3y",
    )

    expect = {
        "layer1": {
            "layer2": {
                "layer3": "value",
                "layer3b": "value3b",
            },
            "layer2b": {
                "layer3c": "value3c",
            },
        },
        "layer1b": {
            "layer2x": {
                "layer3y": "value3y",
            },
        },
    }

    assert nested_dict == expect


def test_set_nested_dict_value_by_key_list_keys_must_exist():
    dict_x = {
        "layer1": {
            "layer2": {
                "layer3": "value",
                "layer3b": "value3b",
            }
        }
    }

    # Attempt to set a value with keys that do not all exist
    nested_dict = set_nested_dict_value_by_key_list(
        dict_x,
        ["layer1", "layer2", "nonexistent_layer"],
        "new_value",
        keys_must_exist=True,
    )

    # Expect the original dictionary to remain unchanged
    expect = {
        "layer1": {
            "layer2": {
                "layer3": "value",
                "layer3b": "value3b",
            }
        }
    }
    assert nested_dict == expect

    # Now set a value with all existing keys
    nested_dict = set_nested_dict_value_by_key_list(
        dict_x,
        ["layer1", "layer2", "layer3"],
        "updated_value",
        keys_must_exist=True,
    )

    # Expect the value to be updated
    expect = {
        "layer1": {
            "layer2": {
                "layer3": "updated_value",
                "layer3b": "value3b",
            }
        }
    }
    assert nested_dict == expect


def test_is_dict_contain_keys():
    dict = {
        "a": 1,
        "b": 2,
        "c": 3,
    }
    contain_keys_list = ["a", "c"]
    is_contain_all = is_dict_contain_keys(
        dict=dict,
        contain_keys_list=contain_keys_list,
    )
    assert is_contain_all == True

    contain_keys_list = ["a", "c", "d"]
    is_contain_all = is_dict_contain_keys(
        dict=dict,
        contain_keys_list=contain_keys_list,
    )
    assert is_contain_all == False


def test_is_dict_contain_dict_items():
    test_dict = {
        "a21": 21,
        "a22": 22,
    }
    contain_dict = {
        "a21": 21,
    }
    is_contain = is_dict_contain_dict_items(dict=test_dict, contain_dict=contain_dict)
    assert is_contain == True

    contain_dict = {
        "a22": 21,
    }
    is_contain = is_dict_contain_dict_items(dict=test_dict, contain_dict=contain_dict)
    assert is_contain == False

    contain_dict = {
        "a22": 22,
    }
    is_contain = is_dict_contain_dict_items(dict=test_dict, contain_dict=contain_dict)
    assert is_contain == True

    contain_dict = {
        "a23": 21,
    }
    is_contain = is_dict_contain_dict_items(dict=test_dict, contain_dict=contain_dict)
    assert is_contain == False


def test_extract_data_from_dict_contains():
    data_dict = {
        "a": [
            {
                "a": 11,
                "b": 12,
            },
            {
                "a": 21,
                "b": 22,
            },
        ]
    }

    contain_dict = {
        "a": 11,
    }
    data = extract_data_from_dict_contains(
        data_dict=data_dict,
        contain_dict=contain_dict,
        extract_key="b",
    )

    assert data == 12

    contain_dict = {
        "a": 21,
    }
    data = extract_data_from_dict_contains(
        data_dict=data_dict,
        contain_dict=contain_dict,
        extract_key="b",
    )

    assert data == 22

    contain_dict = {
        "a": 31,
    }
    data = extract_data_from_dict_contains(
        data_dict=data_dict,
        contain_dict=contain_dict,
        extract_key="c",
    )

    assert data is None  # Updated to use 'is None' for clarity

    contain_dict = {
        "a": 11,
    }
    data = extract_data_from_dict_contains(
        data_dict=data_dict,
        contain_dict=contain_dict,
        extract_key="c",
    )

    assert data is None  # Updated to use 'is None' for clarity

    # New test case using function input return_type
    contain_dict = {
        "a": 21,
    }
    data = extract_data_from_dict_contains(
        data_dict=data_dict,
        contain_dict=contain_dict,
        extract_key="b",
        default_return="Not Found",  # Testing with a default return value
    )

    assert data == 22  # Ensure it still returns the correct value

    contain_dict = {
        "a": 99,
    }
    data = extract_data_from_dict_contains(
        data_dict=data_dict,
        contain_dict=contain_dict,
        extract_key="b",
        default_return="Not Found",  # Testing with a default return value
    )

    assert data == "Not Found"  # Ensure it returns the default value when not found


def test_extract_data_from_nested_dict():
    data_dict = {"a": {"b": {"c": 42}}, "x": {"y": 30, "z": 40}, "a_list": [{"b": 12}]}

    # Test case 1: Extracting a value that exists
    result = extract_data_from_nested_dict_by_key_list(data_dict, ["a", "b", "c"])
    assert result == 42

    # Test case 2: Extracting a value from a different path
    result = extract_data_from_nested_dict_by_key_list(data_dict, ["x", "y"])
    assert result == 30

    # Test case 3: Extracting a value that does not exist
    result = extract_data_from_nested_dict_by_key_list(data_dict, ["a", "b", "d"])
    assert result is None

    # Test case 4: Extracting with a key that leads to a list
    result = extract_data_from_nested_dict_by_key_list(data_dict, ["a_list", 0, "b"])
    assert result == None

    # Test case 5: Extracting with a non-existent key
    result = extract_data_from_nested_dict_by_key_list(
        data_dict, ["non_existent", "key"]
    )
    assert result is None


import pytest


def test_get_nested_dict_value_by_key_list():
    # Test case 1: Extracting a value that exists
    base_dict = {"layer1": {"layer2": {"layer3": "value"}}}
    keys = ["layer1", "layer2", "layer3"]
    result = get_nested_dict_value_by_key_list(base_dict, keys)
    assert result == "value"

    # Test case 2: Extracting a value that does not exist with a default return
    base_dict = {"layer1": {"layer2": {"layer3": "value"}}}
    keys = ["layer1", "layer2", "non_existent"]
    result = get_nested_dict_value_by_key_list(base_dict, keys, default="default_value")
    assert result == "default_value"


class TestTransformByRelateDictKeys:

    def test_transform_by_relate_dict_keys_basic(self):
        original_dict = {
            "a": ["b"],
            "b": ["c"],
            "c": ["e", "f"],
        }
        expected_transformed = {
            "a": ["b", "c", "e", "f"],
            "b": ["c", "e", "f"],
            "c": ["e", "f"],
        }
        result = transform_by_relate_dict_keys(original_dict)
        assert result == expected_transformed

    def test_transform_by_relate_dict_keys_with_empty(self):
        original_dict = {"x": [], "y": ["z"], "z": ["x"]}
        expected_transformed = {"x": [], "y": ["x", "z"], "z": ["x"]}
        result = transform_by_relate_dict_keys(original_dict)
        assert result == expected_transformed


class TestExtractKeysFromDictThatPartlyContains:

    def test_extract_keys_from_dict_that_partly_contains_basic(self):
        dictx = {"apple": 1, "banana": 2, "grape": 3, "apricot": 4}
        result = extract_keys_from_dict_that_partly_contains(dictx, "ap")
        expected = ["apple", "grape", "apricot"]
        assert sorted(result) == sorted(expected)

    def test_extract_keys_from_dict_that_partly_contains_no_match(self):
        dictx = {"dog": 1, "cat": 2, "mouse": 3}
        result = extract_keys_from_dict_that_partly_contains(dictx, "zebra")
        expected = []
        assert result == expected


if __name__ == "__main__":
    # test_is_dict_contain_keys()
    # test_is_dict_contain_dict_items()
    test_extract_data_from_dict_contains()
