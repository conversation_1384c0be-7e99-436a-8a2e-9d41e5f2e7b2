import datetime
import shutil
import sys
import os
from const_path import proj_ser_tools_path, proj_ser_api_db_apps_path
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
    create_db_info,
)
from server.api.end_to_end.api_constant_keys import DBAppsColEnum
from server.api.end_to_end.utils import ppd_emb_kw_kwpp_update_using_keys

from server.api.utils.descriptions.get_description import scrape_play_store
from sentence_transformers import SentenceTransformer

"""
To fill in new apps (from a txt) to db_apps
"""
max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20


def fill_in_new_apps_to_db_apps(
    db_apps_folder_path,
    db_apps_output_folder_path,
    db_apps_backup_zip_path,
    new_apps_txt_path,
    model_id: str = "multi-qa-MiniLM-L6-cos-v1",
):
    embedder = SentenceTransformer(model_id)

    # Backup the db_apps folder first.
    shutil.make_archive(db_apps_backup_zip_path, "zip", db_apps_folder_path)

    # Load the db_apps from folder
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        dump_db_fpath=db_apps_output_folder_path,
        load_by_thread=False,
    )

    # Read new apps from txt line by line
    file = open(new_apps_txt_path)
    lines = file.readlines()
    for i, line in enumerate(lines):
        new_app_id = line.replace("\n", "")

        # Scrape from playstore.
        scrap_success, result = scrape_play_store(device_app_id=new_app_id)

        if scrap_success:
            ppd_emb_kw_kwpp_update_using_keys(
                app_id=new_app_id,
                db_apps=db_apps,
                scrape_result=result,
                embedder=embedder,
                deduplication_threshold=deduplication_threshold,
                max_ngram_size=max_ngram_size,
                max_num_keywords=max_num_keywords,
            )

            g = 1
    # End of for loop.

    # Dump db_apps (all parts) to folder.
    db_apps.dump_all_parts_to_pickles()


def test_fill_in_new_apps_to_db_apps():
    db_apps_folder_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_db_folder",
    )

    db_apps_output_folder_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_db_output_folder",
    )

    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    db_apps_backup_zip_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_db_backup_zip_folder",
        f"db_apps_{time_str}",
    )

    new_apps_txt_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_txt_folder",
        "new_apps.txt",
    )

    db_apps_old = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )
    assert db_apps_old.query_key("com.opera.gx") == None
    assert db_apps_old.query_key("io.loudplay.android.app") == None

    fill_in_new_apps_to_db_apps(
        db_apps_folder_path=db_apps_folder_path,
        db_apps_backup_zip_path=db_apps_backup_zip_path,
        db_apps_output_folder_path=db_apps_output_folder_path,
        new_apps_txt_path=new_apps_txt_path,
    )

    db_apps_new = PickleDatabaseSplit(
        db_fpath=db_apps_output_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )

    assert db_apps_new.get_db_num_keys() == 4
    assert db_apps_new.query_key("com.opera.gx") != None
    assert db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.DESC] != ""
    assert (
        db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.EMBD] != None
        or db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.EMBD] != []
    )
    assert len(db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.KEYW]) > 0
    assert db_apps_new.query_key("io.loudplay.android.app") != None

    g = 1


def run_fill_in_new_apps_to_db_apps():
    db_apps_folder_path = proj_ser_api_db_apps_path
    db_apps_output_folder_path = proj_ser_api_db_apps_path

    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    db_apps_backup_zip_path = os.path.join(
        proj_ser_tools_path,
        "run_fill_new_apps_db_backup_zip_folder",
        f"db_apps_{time_str}",
    )

    new_apps_txt_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_txt_folder",
        "new_apps.txt",
    )

    fill_in_new_apps_to_db_apps(
        db_apps_folder_path=db_apps_folder_path,
        db_apps_backup_zip_path=db_apps_backup_zip_path,
        db_apps_output_folder_path=db_apps_output_folder_path,
        new_apps_txt_path=new_apps_txt_path,
    )


def main():
    # Test function and check two more new app
    test_fill_in_new_apps_to_db_apps()

    # Run function
    # run_fill_in_new_apps_to_db_apps()


if __name__ == "__main__":
    sys.exit(main())
