import sys
import http
from multiprocessing.pool import ThreadPool
from tomsze_utils.scraping_utils import get_scrapingant_api_credit_status
from api_keys import dict_account_apikey


def main():
    # ------- Settings ------------------
    list_api_key = list(dict_account_apikey.values())
    # ------------------------------------

    # Create thread arguments.
    num_thread = len(list_api_key)
    list_conn = [
        http.client.HTTPSConnection("api.scrapingant.com")
        for i in range(len(list_api_key))
    ]
    list_args = list(zip(list_conn, list_api_key))

    # Send request to ask for api key status.
    list_credit_status = ThreadPool(num_thread).starmap(
        get_scrapingant_api_credit_status, list_args
    )
    for conn in list_conn:
        conn.close()

    print(list_credit_status)


if __name__ == "__main__":
    sys.exit(main())
