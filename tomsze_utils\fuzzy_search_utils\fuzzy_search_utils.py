from typing import List, Tuple
from tomsze_utils.string_utils import find_best_match_index


def simple_match(self, item: str, search_text: str, pos: int) -> int:
    """Find the next match of `search_text` in `item` starting from `pos`.

    Args:
        item (str): The string to search within.
        search_text (str): The string to search for.
        pos (int): The starting position for the search.

    Returns:
        int: The index of the next match, or -1 if no match is found.

    Examples:
        ```python
        simple_match(item="hello world", search_text="world", pos=0)
        # Expected output: 6
        ```

        ```python
        simple_match(item="hello world", search_text="o", pos=5)
        # Expected output: 7
        ```
    """
    item_lower = item.lower()
    search_lower = search_text.lower()
    return item_lower.find(search_lower, pos)


def fuzzy_match_has_space(text: str, typed: str) -> List[Tuple[int, int]]:
    """
    Finds index ranges of fuzzy matched text segments, separated by spaces, within a given text.
    It uses find_best_match_index to find the best match for each space-separated term.

    Args:
        text (str): The text to search within.
        typed (str): The space-separated search terms.

    Returns:
        list[tuple[int, int]]: A list of tuples, where each tuple contains the start and end index of a matched segment.
        Returns an empty list if no match is found.

    Examples:
        ```python
        fuzzy_match_has_space(text="apple pie pies", typed="pie")
        # Expected output: [(6, 9)]
        ```

        ```python
        fuzzy_match_has_space(text="hello world abc", typed="world abc")
        # Expected output: [(6, 11), (12, 15)]
        ```
    """
    # If the sequence is empty, return the original text
    if not typed:
        return []

    # Convert both text and typed to lowercase for case-insensitive matching
    text_lower = text.lower()
    typed_lower = typed.lower()

    # Split the typed text
    typed_parts = typed_lower.split()

    # Get matched index range
    ignore_ranges = []
    matched_index_range = []
    for typed_part in typed_parts:
        index = find_best_match_index(
            text=text_lower, query=typed_part, ignore_ranges=ignore_ranges
        )
        if index != -1:
            ignore_ranges.append((index, index + len(typed_part)))
            matched_index_range.append((index, index + len(typed_part)))

    return matched_index_range


def main():
    # ----try fuzzy_match_has_space ------
    text = "apple pie pies"
    typed = "pie"
    result = fuzzy_match_has_space(text, typed)
    print(result)
    # [(6, 9)]

    text = "apple pie pies"
    typed = "pie pie"
    result = fuzzy_match_has_space(text, typed)
    print(result)
    # [(6, 9), (10, 13)]

    text = "apple pie pies"
    typed = "pies"
    result = fuzzy_match_has_space(text, typed)
    print(result)
    # [(10, 14)]

    text = "he she them"
    typed = "them he"
    result = fuzzy_match_has_space(text, typed)
    print(result)
    # [(7, 11), (0, 2)]

    # # ----try highligh_text_by_index_range------

    # text = "hello world"
    # index_range_list = [(0, 5)]
    # result = highligh_text_by_index_range(text, index_range_list)
    # print(result)
    # # result will be "\033[1;34mhello\033[0m world"

    # text = "hello world abc"
    # index_range_list = [(0, 5), (6, 11)]
    # result = highligh_text_by_index_range(text, index_range_list)
    # print(result)
    # # result will be "\033[1;34mhello\033[0m \033[1;34mworld\033[0m abc"


if __name__ == "__main__":
    main()
