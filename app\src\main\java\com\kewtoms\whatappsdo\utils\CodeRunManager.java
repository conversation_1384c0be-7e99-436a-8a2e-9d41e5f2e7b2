package com.kewtoms.whatappsdo.utils;

import android.content.Context;
import android.util.Log;

import java.io.InputStream;
import java.util.Properties;

/**
 * Manages runtime configuration and feature toggles using property
 * files.
 *
 * <p>Usage:</p>
 * <ol>
 *   <li>Initialize with application context in your Application class:
 *       {@code CodeRunManager.initialize(context);}</li>
 *   <li>Load a config file from assets (e.g., "config.properties"):
 *       {@code CodeRunManager.getInstance().loadConfig("config.properties");}</li>
 *   <li>Check if a feature is enabled:
 *       {@code boolean featureEnabled = CodeRunManager.getInstance().isEnabled("featureName");}</li>
 * </ol>
 *
 * <p>Example config.properties:</p>
 * <pre>
 * # Feature toggles
 * newFeature=true
 * experimentalMode=false
 * </pre>
 */
public class CodeRunManager {
  private static CodeRunManager instance;
  private final Properties properties;
  private final Context context;

  // Private constructor to prevent direct instantiation
  private CodeRunManager(Context context) {
    this.context = context.getApplicationContext();
    this.properties = new Properties();
  }

  // Initialize the singleton instance
  public static synchronized void initialize(Context context) {
    if (instance == null) {
      instance = new CodeRunManager(context);
    }
  }

  // Get the singleton instance (throws if not initialized)
  public static CodeRunManager getInstance() {
    if (instance == null) {
      throw new IllegalStateException("CodeRunManager not initialized. Call initialize() first.");
    }
    return instance;
  }

  // Load a config file from assets (e.g., "config.properties")
  public void loadConfig(String filename) {
    try (InputStream input = context.getAssets().open(filename)) {
      properties.load(input);
    } catch (Exception e) {
      throw new RuntimeException(
        "Failed to load config: " + filename,
        e
      );
    }
  }

  // Instance method (not static!) to check feature status
  public boolean isEnabled(String featureName) {
    String isEnabledStr = properties.getProperty(
      featureName,
      null
    );

    if (isEnabledStr == null) {
      Log.i(
        "CodeRunManager",
        "Feature " + featureName + " not found in config. Using default value: true"
      );
      isEnabledStr = "true";
    }
    return Boolean.parseBoolean(isEnabledStr);
  }
}
