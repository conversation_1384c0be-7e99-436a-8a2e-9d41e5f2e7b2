package com.kewtoms.whatappsdo.jar;

import com.tomsze.TimeUtils;

import junit.framework.TestCase;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;

// Just to test if the tomsze utils jar has imported correctly

public class JarUtilsTest
  extends TestCase {


  public void testTimeUtilsGetClock() {
    Clock clock = TimeUtils.getClock(
      null,
      null
    );

    Instant expectedInstant = Instant.parse("2024-07-11T12:00:00Z");
    assertEquals(
      expectedInstant,
      clock.instant()
    );
    assertEquals(
      ZoneId.of("UTC"),
      clock.getZone()
    );
  }
}