import collections
import os
from pathlib import Path
import pickle
import sys
import time
from typing import List

from line_profiler import profile
from server.api.end_to_end.api_constant_keys import DBAppsColEnum

from server.api.utils.keywords.get_keywords import lemmatization

import re2

# import urllib3
import urllib.request
from urllib.error import URLError, HTTPError
import google_play_scraper
import yake

from server.api.utils.generate_vectors.powerthresaurus import PowerThesaurus
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from server.api.end_to_end.api_constant_keys import (
    DBSnnsColEnum,
)

# from powerthresaurus import PowerThesaurus


def timeit(method):
    def timed(*args, **kw):
        ts = time.time()
        result = method(*args, **kw)
        te = time.time()
        if "log_time" in kw:
            name = kw.get("log_name", method.__name__.upper())
            kw["log_time"][name] = int((te - ts) * 1000)
        else:
            print("%r  %2.2f ms" % (method.__name__, (te - ts) * 1000))
        return result

    return timed


def check_word_lang_en_zh(word):
    """
    Example1:
    word = 'hi'
    return 'en'

    Example1:
    word = '你好'
    return 'zh'
    """
    lang = ""

    if re2.search("[\u4e00-\u9fff]", word):
        return "zh"
    else:
        return "en"


def search_list_text_en_zh(dict_lang_listText, list_words):
    """
    For each row of text, sum all matched word count (include all lang).

    Example1:
    dict_lang_listText = {
        'en':[
            'one one two four',
            'two two two three four ',
            'one three two three three four',
        ],
        'zh':[
            '一 一 二 四',
            '二 二 二 三 四',
            '一 三 二 三 三 四',
        ]
    }
    list_words = [
        'one',
        'two',
        'three',
        '一',
        '二',
        '三',
    ]
    returns [6, 8, 10]

    Example2:
    dict_lang_listText = {
        'en':[
            'one two four',
            'two two four ',
            'one two three three four',
        ],
        'zh':[
            '一 一 二 四',
            '二 二 三 四',
            '一 二 三 三 四 五',
        ]
    }
    list_words = [
        'one',
        'two',
        'three',
        '一',
        '二',
        '三',
        '四',
        '五',
    ]
    returns [6, 6, 10]
    """
    list_num_matches = []
    num_text = len(dict_lang_listText["en"])

    for text_ind in range(num_text):

        num_total_match = 0
        for word in list_words:
            word_lang = check_word_lang_en_zh(word)

            text = dict_lang_listText[word_lang][text_ind]
            text = text.lower()
            search_word = word.lower()
            if word_lang == "en":
                list_matches = re2.findall(f"\\b{search_word}\\b", text)
            elif word_lang == "zh":
                list_matches = re2.findall(f"{search_word}", text)

            # list_matches = re2.findall(r'\bone\b', text)
            num_match = len(list_matches)
            num_total_match += num_match
        list_num_matches.append(num_total_match)

    return list_num_matches


def split_text(text):
    """
    Split text into words using re2.sub method.

    Example1:
    text = ' one two  three '
    return ['one', 'two', 'three']

    Example2:
    text = ' one two  three 一 二   三 '
    return ['one', 'two', 'three', '一', '二', '三']
    """
    list_words = re2.sub(r"\s+", " ", text).split()
    return list_words


def load_app_data(
    num_total,
    num_rows_per_batch=None,
    platform="android",
    #   num_batches=1,
):

    import pandas as pd

    # assert num_rows_per_batch >=1 or num_rows_per_batch is None, 'num_rows_per_batch must be >= 1 or None(for reading all data)'
    # assert num_batches >=1, 'num_batches must be >= 1'

    script_directory_path = os.path.dirname(os.path.abspath(sys.argv[0]))

    parent_path = str(Path(script_directory_path).parent)

    list_id = []
    list_description = []
    list_list_description = []
    if platform == "ios":
        num_rows = num_total
        description_data_path = os.path.join(
            parent_path,
            "web_scrap_appstore",
            "proj_web_scrap_appstore",
            "spiders",
            "07_scrap_from_api_using_id_ok_bak.csv",
        )
        df_descriptions = pd.read_csv(
            description_data_path, sep=",", nrows=num_rows
        )  # 78W rows takes 8s on i7-8700K CPU @ 3.70GHz

        count = 0
        for index, row in df_descriptions.iterrows():
            app_id = row["app_id"]
            description = row["description"]

            list_id.append(app_id)
            list_description.append(description)
            count += 1

            if count == num_rows_per_batch:
                list_list_description.append(list_description)
                list_description = []
                count = 0

            if count != num_rows_per_batch and index == num_rows - 1:
                list_list_description.append(list_description)

        return list_id, list_list_description  # TODO: change to dict_id later

    elif platform == "android":
        id_data_path = os.path.join(
            parent_path, "web_scrap_google_play", "011_app_dict_id.pickle"
        )

        description_data_path = os.path.join(
            parent_path, "web_scrap_google_play", "011_app_list_description.pickle"
        )

        if not os.path.exists(id_data_path):
            print(f"{id_data_path} does not exist")
            return

        if not os.path.exists(description_data_path):
            print(f"{description_data_path} does not exist")
            return

        # Load the data from the file.
        with open(id_data_path, "rb") as f:
            dict_id = pickle.load(f)
        with open(description_data_path, "rb") as f:
            list_description_file = pickle.load(f)

        if num_total > len(list_description_file):
            num_rows = len(list_description_file)

        count = 0
        for index, id in enumerate(dict_id.keys()):
            app_id = id
            description = list_description_file[index]

            list_id.append(app_id)
            list_description.append(description)
            count += 1

            if count == num_rows_per_batch:
                list_list_description.append(list_description)
                list_description = []
                count = 0

            if count != num_rows_per_batch and index == num_rows - 1:
                list_list_description.append(list_description)

    return dict_id, list_list_description


def convert_to_embedding_using_huggingface_model(
    list_text,
    model_id,
):
    """
    model_id example: "sentence-transformers/all-MiniLM-L6-v2"

    Time measure:
    all-MiniLM-L6-v2
    ~2s for 20 texts
    ~4s for 40 texts
    ~5.4s - 7.3s for 80 texts
    ~8.6s for 160 texts
    ~63s for 256 texts  ???
    ~3.6s - 14.6s for 320 texts
    ~46s / 4s for 640 texts ???
    ~6s-61.0s-80.1s for 1000 texts ???
    ~41s-63.0s for 1280 texts ???

    """
    import requests

    hf_token = "*************************************"  # TODO factor out
    api_url = (
        f"https://api-inference.huggingface.co/pipeline/feature-extraction/{model_id}"
    )
    headers = {"Authorization": f"Bearer {hf_token}"}

    response = requests.post(
        api_url,
        headers=headers,
        json={"inputs": list_text, "options": {"wait_for_model": True}},
    )
    return response.json()


def convert_to_embedding(
    embedder,  # SentenceTransformer
    list_text,
    device="cuda",
    batch_size=64,
):
    """
    Simply wrap the call of embedder.encode() method.

    Return a list of embeddings from the method.
    """
    list_embeddings = embedder.encode(
        list_text,
        convert_to_tensor=True,
        device=device,
        batch_size=batch_size,
    )
    return list_embeddings


def similarity_search(
    query_embeddings,
    list_text_embedding,
    top_k=5,
    score_function=None,
):
    from sentence_transformers.util import (
        semantic_search,
        cos_sim,
    )  # pip install -U sentence-transformers

    if score_function is None:
        score_function = cos_sim

    hits = [[]]
    if list_text_embedding:
        hits = semantic_search(
            query_embeddings=query_embeddings,
            corpus_embeddings=list_text_embedding,
            top_k=top_k,
            score_function=score_function,
        )
    return hits


def test_load_app_data():
    num_total = 10
    num_rows_per_batch = 4  # set None to read all
    # num_batches = 2
    list_id, list_description = load_app_data(
        num_total=num_total,
        num_rows_per_batch=num_rows_per_batch,
        # num_batches=num_batches,
    )


def fetch_synonyms_from_powerthesaurus(keyword_underscore) -> dict:
    from bs4 import BeautifulSoup

    print(f"getting synoymn of {keyword_underscore}")
    url = f"https://www.powerthesaurus.org/{keyword_underscore}/synonyms/"

    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"  # this is faster
    headers = {"User-Agent": user_agent}
    req = urllib.request.Request(url, headers=headers)

    fetch_synonym_success = False
    try:
        with urllib.request.urlopen(req) as response:
            dom = response.read().decode("utf-8")
        fetch_synonym_success = True

    except HTTPError as e:
        print("The server couldn't fulfill the request.")
        print("Error code: ", e.code)
    except URLError as e:
        print("We failed to reach a server.")
        print("Reason: ", e.reason)
    except UnicodeEncodeError as e:
        print("Error: ", e)
        print("Reason: ", e.reason)

    list_synonym = []
    if fetch_synonym_success:
        soup = BeautifulSoup(dom, "html.parser")

        # <div><div><a class="cg_ay cg_ch y9_ay" href="https://www.powerthesaurus.org/fondness/synonyms" title="fondness synonym">fondness</a></div></div>
        list_match = soup.select("div#primary-area div a")
        for match in list_match:
            list_synonym.append(match.text)

    return fetch_synonym_success, list_synonym

    # Add to dict.
    # dict_synonyms[keyword_underscore] = list_synonym


def fetch_synonyms(
    keyword_underscore,
    site="powerthesaurus",
    use_powerthesaurus_api=True,
    pt_obj=PowerThesaurus(),
    max_num=10,
):
    import requests
    from bs4 import BeautifulSoup

    print(f'getting synoymn of "{keyword_underscore}"')
    fetch_synonym_success = False
    list_synonym = []

    if use_powerthesaurus_api:
        assert (
            site == "powerthesaurus"
        ), "site must be powerthesaurus for using powerthesaurus api"

        # This gets me banned!!!
        request_success, term_found, robot_detected, list_synonym = pt_obj.thesaurus(
            keyword_underscore
        )
        if request_success and robot_detected == False:
            fetch_synonym_success = True

    else:

        if site == "wordhippo":
            url = f"https://www.wordhippo.com/what-is/another-word-for/{keyword_underscore}.html"
            selector = "div.relatedwords div.wb a"
        elif site == "powerthesaurus":
            url = f"https://www.powerthesaurus.org/{keyword_underscore}/synonyms/"
            selector = "div#primary-area div a"

        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"  # this is faster
        headers = {"User-Agent": user_agent}
        # req = urllib.request.Request(url,
        #                                 headers=headers)

        dom = ""
        if site == "wordhippo":
            response = requests.get(url)  # fast then using urllib about 500ms
        if site == "powerthesaurus":
            response = requests.get(
                url, headers=headers
            )  # fast then using urllib about 500ms
        dom = response.content.decode("utf-8")

        if response.status_code == 200:
            fetch_synonym_success = True
        else:
            print(response.status_code)
            print(response.reason)

        if fetch_synonym_success:
            soup = BeautifulSoup(dom, "html.parser")

            list_match = soup.select(selector)
            for match in list_match:
                list_synonym.append(match.text)
                if len(list_synonym) == max_num:
                    break

    return fetch_synonym_success, list_synonym


def fetch_and_dump_synonyms(
    keyword_underscore,
    api_synonym_pickle_path,
    api_no_synonym_pickle_path,
    dict_no_synonyms,
    dict_synonyms,
    site="powerthesaurus",
    use_powerthesaurus_api=True,
    pt_obj=PowerThesaurus(),
    max_num=10,
):
    has_fetch = False

    if keyword_underscore in dict_no_synonyms:
        print(f'"{keyword_underscore}" has no synonym, skip fetching from site {site}')
        return

    if keyword_underscore not in dict_synonyms:
        fetch_synonym_success, list_synonym = fetch_synonyms(
            keyword_underscore=keyword_underscore,
            site=site,
            use_powerthesaurus_api=use_powerthesaurus_api,
            pt_obj=pt_obj,
            max_num=max_num,
        )

        if fetch_synonym_success:
            has_fetch = True

        if fetch_synonym_success:
            if len(list_synonym) > 0:
                print(
                    f'Fecthed {len(list_synonym)} result. Save the keyword "{keyword_underscore}" to synonym dict pickfle file'
                )
                dict_synonyms[keyword_underscore] = list_synonym
                dump_variable_to_pickle(api_synonym_pickle_path, dict_synonyms)
            else:
                print(
                    f'Fecthed 0 result. Save the keyword "{keyword_underscore}" to no synonym dict pickfle file'
                )
                dict_no_synonyms[keyword_underscore] = 1
                dump_variable_to_pickle(api_no_synonym_pickle_path, dict_no_synonyms)

    elif len(dict_synonyms[keyword_underscore]) == 0:
        print(f"Found keyword {keyword_underscore} has empty synonyms, fetch again.")
        # Fetch again if that key has empty value (due to time reason/accident).
        fetch_synonym_success, list_synonym = fetch_synonyms(
            keyword_underscore=keyword_underscore,
            site=site,
        )

        if fetch_synonym_success:
            has_fetch = True

        if fetch_synonym_success:
            if len(list_synonym) > 0:
                print(
                    f'Fecthed {len(list_synonym)} result. Save the keyword "{keyword_underscore}" to synonym dict pickfle file'
                )
                dict_synonyms[keyword_underscore] = list_synonym
                dump_variable_to_pickle(api_synonym_pickle_path, dict_synonyms)
            else:
                print(
                    f'Fecthed 0 result. Save the keyword "{keyword_underscore}" to no synonym dict pickfle file'
                )
                del dict_synonyms[keyword_underscore]
                dict_no_synonyms[keyword_underscore] = 1
                dump_variable_to_pickle(api_no_synonym_pickle_path, dict_no_synonyms)
    else:
        print(f"keyword {keyword_underscore} is in dict synonym, skip saving.")

    return has_fetch


def test_convert_to_embedding_using_huggingface_model():
    list_text = [
        "What is the monthly premium for Medicare Part B?",
        "How do I terminate my Medicare Part B (medical insurance)?",
        "How do I sign up for Medicare?",
    ]
    model_id = "sentence-transformers/all-MiniLM-L6-v2"
    list_embeddings = convert_to_embedding_using_huggingface_model(
        list_text=list_text,
        model_id=model_id,
    )


def dump_variable_to_pickle(pickle_path, variable, dump_only_when_file_not_exist=False):
    dumped = False
    if dump_only_when_file_not_exist:
        if not os.path.exists(pickle_path):
            with open(pickle_path, "wb") as f:
                pickle.dump(variable, f)
                dumped = True
    else:
        with open(pickle_path, "wb") as f:
            pickle.dump(variable, f)
            dumped = True
    return dumped


def read_variable_from_pickle(pickle_path, var):
    if os.path.exists(pickle_path):
        with open(pickle_path, "rb") as f:
            var = pickle.load(f)

    return var


def test_dump_variable_to_pickle():
    pickle_path = r"./test_pickle_dump.pickle"
    variable = "hi"
    if os.path.exists(pickle_path):
        os.remove(pickle_path)

    dumped = dump_variable_to_pickle(pickle_path, variable)

    assert (
        os.path.exists(pickle_path) == True
    ), "The test pickle file should be created."

    dumped = dump_variable_to_pickle(
        pickle_path, variable, dump_only_when_file_not_exist=True
    )
    assert dumped == False


def test_read_variable_from_pickle():
    pickle_path = r"./test_pickle_dump.pickle"
    variable = None
    variable = read_variable_from_pickle(pickle_path, variable)
    assert variable is not None, "The variable read should not be None anymore"

    pickle_path = r"./test_pickle_dumpxxxxx.pickle"
    variable = []
    variable = read_variable_from_pickle(pickle_path, variable)
    assert len(variable) == 0, "The variable should be []"


def test_fetch_synonyms():
    keyword_underscore = "hi"
    site = "powerthesaurus"
    fetch_synonym_success, list_synonym = fetch_synonyms(
        keyword_underscore=keyword_underscore,
        site=site,
    )
    assert fetch_synonym_success == True
    assert len(list_synonym) > 0

    # keyword_underscore = 'how_are_you'
    keyword_underscore = "howdy"
    # keyword_underscore = 'key_features'
    # keyword_underscore = 'enjoy'
    site = "wordhippo"
    fetch_synonym_success, list_synonym = fetch_synonyms(
        keyword_underscore=keyword_underscore, site=site, max_num=100
    )
    assert fetch_synonym_success == True
    assert len(list_synonym) > 0


def loop1(num_loop):
    list_num_1 = []
    list_num_2 = []
    for i in range(num_loop):
        list_num_1.append(i)

    for i in range(num_loop):
        list_num_2.append(i)


def loop2(num_loop):
    list_num_1 = []
    list_num_2 = []
    for i in range(num_loop):
        list_num_1.append(i)
        list_num_2.append(i)


def scrap_play_store(device_app_id, lang):
    """
    Using google_play_scraper to scrap.

    param lang: 'en' or 'zh' ...
    """
    scrap_success = False
    result = {}
    try:
        result = google_play_scraper.app(device_app_id, lang=lang)  # defaults to 'en'
        scrap_success = True
    except google_play_scraper.exceptions.NotFoundError:
        print(f"{device_app_id} is not found")

    return scrap_success, result


def test_for_loops():
    num_loop = 100000
    loop1(num_loop)
    loop2(num_loop)

    # Result: loop1 takes longer time then loop2 but not twice longer.


def hi():
    print("hi from utils")


# def save_to_pickle(pickle_path, var):
#     if not os.path.exists(pickle_path):
#         with open(pickle_path, 'wb') as f:
#             pickle.dump(var, f)


def function(var):
    # var = [999]     # this cannot change var
    var[0] = 999  # this can change var


def test_pass_to_function():
    var = [1]
    function(var)

    assert var == [999]


def post_process_app_description(result):
    app_description = (
        result["description"].replace("\n", "").replace("\r", "").replace("\u2022", "")
    )  # \u2022 is bullet point
    app_description_original = app_description

    app_description = result["summary"] + ". " + app_description
    app_description = result["genre"] + ". " + app_description
    app_description = result["genreId"] + ". " + app_description

    app_categories = result["categories"]
    for category in app_categories:
        name = category["name"]
        id = category.get("id", None)
        if id is not None:
            app_description = id + ". " + app_description
        else:
            app_description = name + ". " + app_description

    return app_description_original, app_description


def extract_keyword_using_yake(
    lang,
    max_ngram_size,
    deduplication_threshold,
    max_num_keywords,
    app_description_en_original,
):

    custom_kw_extractor = yake.KeywordExtractor(
        lan=lang,
        n=max_ngram_size,
        dedupLim=deduplication_threshold,
        top=max_num_keywords,
        features=None,
    )
    list_keyword_score = custom_kw_extractor.extract_keywords(
        app_description_en_original
    )

    list_kw = []
    for keywords_score in list_keyword_score:
        keyword_underscore = keywords_score[0]
        # keyword_underscore = keywords_score[0].replace('.', ' ').strip().replace(' ', '_').lower()
        list_kw.append(keyword_underscore)

    return list_kw


def check_two_lists_same(list1, list2):
    return collections.Counter(list1) == collections.Counter(list2)


def test_check_two_lists_same():
    list1 = [
        "a",
        "a",
        "b",
        "c",
        "c",
        "c",
    ]
    list2 = [
        "a",
        "a",
        "b",
        "c",
        "c",
        "c",
    ]
    is_same = check_two_lists_same(list1=list1, list2=list2)
    assert is_same == True

    list1 = [
        "a",
        "a",
        "b",
        "c",
        "c",
        "c",
    ]
    list2 = [
        "a",
        "a",
        "b",
        "c",
        "c",
        "h",
    ]
    is_same = check_two_lists_same(list1=list1, list2=list2)
    assert is_same == False


def extract_device_app_synonyms(list_device_apps, dict_keywords, dict_synonyms):
    """
    Given
    list_device_apps = [
        {'id':'com.whatsapp'},
        {'id':'com.rovio.BadPiggies'},
    ]

    dict_keywords = {
        'com.whatsapp':['family','calling','chats'],
        'com.rovio.BadPiggies':['rovio','game'],
        'de.stocard.stocard':['wallet','cards','passbook'],

    }

    dict_synonyms = {
        'family':['household', 'lineage'],
        'calling':['vocation', 'job'],
        'chats':['talks', 'converses'],
        'game':['play', 'match'],

        'wallet':['pocketbook', 'purse'],
        'cards':['card game', 'jokers'],
        'passbook':['bankbook', 'register'],
    }

    Return
    [
        'household, lineage, vocation, job, talks, converses, ',
        'play, match, '
    ]
    """
    list_device_synonyms = []
    for device_app in list_device_apps:
        device_app_id = device_app["id"]
        if device_app_id in dict_keywords:
            list_device_keywords = dict_keywords[device_app_id]

            all_synonyms_string = ""
            for keyword in list_device_keywords:
                if keyword in dict_synonyms:
                    list_synonyms = dict_synonyms[keyword]

                    for synonyms in list_synonyms:
                        all_synonyms_string = all_synonyms_string + synonyms + ", "
            list_device_synonyms.append(all_synonyms_string)

    return list_device_synonyms


@profile
def extract_device_app_synonyms_v2(
    list_device_apps: List[str],
    db_apps: PickleDatabaseSplit,
    db_synonyms: PickleDatabaseSplit,
    lemmatization_model,
    extract_num_per_keyword=5,
) -> List[str]:
    """
    Extract each device app id's keywords' synonyms into a string.

    Return a list of device's id 's string concated with synonyms.

    Given
    list_device_apps = [
        "com.whatsapp",
        "com.rovio.BadPiggies",
    ]

    dict_keywords = {
        'com.whatsapp':['family','calling','chats'],
        'com.rovio.BadPiggies':['rovio','game'],
        'de.stocard.stocard':['wallet','cards','passbook'],

    }

    dict_synonyms = {
        'family':['household', 'lineage'],
        'calling':['vocation', 'job'],
        'chats':['talks', 'converses'],
        'game':['play', 'match'],

        'wallet':['pocketbook', 'purse'],
        'cards':['card game', 'jokers'],
        'passbook':['bankbook', 'register'],
    }

    Return
    ['household, lineage, vocation, job, talks, converses, ', 'play, match, ']
    """
    list_device_synonyms = []
    # For each device app id.
    for device_app_id in list_device_apps:
        if db_apps.is_key_in(device_app_id):
            # Query app id keywords.
            list_device_keywords = db_apps.query_key_col(
                key=device_app_id,
                col=DBAppsColEnum.KEYW,
            )

            all_synonyms_string = ""
            # For each keyword.
            for device_keyword in list_device_keywords:
                # Lemmatize keyword.
                keyw_lemmatized = lemmatization(
                    nlp_model=lemmatization_model,
                    text=device_keyword,
                )
                if db_synonyms.is_key_in(keyw_lemmatized):
                    # Query synonyms by keyword and col.
                    list_synonyms = db_synonyms.query_key_col(
                        key=keyw_lemmatized,
                        col="pt",
                        default=[],
                    )
                    list_synonyms = list_synonyms[:extract_num_per_keyword]

                    # Concate synonyms to a string.
                    for synonyms in list_synonyms:
                        all_synonyms_string = all_synonyms_string + synonyms + ", "

            list_device_synonyms.append(all_synonyms_string)

    return list_device_synonyms


def test_extract_device_app_synonyms():
    list_device_apps = [
        {"id": "com.whatsapp"},
        {"id": "com.rovio.BadPiggies"},
    ]
    # dict_id = {
    #     'com.whatsapp':'',
    #     'com.rovio.BadPiggies':'',
    #     'de.stocard.stocard':'',

    # }
    dict_keywords = {
        "com.whatsapp": ["family", "calling", "chats"],
        "com.rovio.BadPiggies": ["rovio", "game"],
        "de.stocard.stocard": ["wallet", "cards", "passbook"],
    }
    dict_synonyms = {
        "family": ["household", "lineage"],
        "calling": ["vocation", "job"],
        "chats": ["talks", "converses"],
        "game": ["play", "match"],
        "wallet": ["pocketbook", "purse"],
        "cards": ["card game", "jokers"],
        "passbook": ["bankbook", "register"],
    }
    list_device_synonyms = extract_device_app_synonyms(
        list_device_apps=list_device_apps,
        dict_keywords=dict_keywords,
        dict_synonyms=dict_synonyms,
    )

    assert list_device_synonyms == [
        "household, lineage, vocation, job, talks, converses, ",
        "play, match, ",
    ]


def test_fetch_synonyms_from_powerthesaurus_using_api():
    keyword_underscore = "hi"
    site = "powerthesaurus"
    use_powerthesaurus_api = True
    pt_obj = PowerThesaurus()
    fetch_synonym_success, list_synonym = fetch_synonyms(
        keyword_underscore=keyword_underscore,
        site=site,
        use_powerthesaurus_api=use_powerthesaurus_api,
        pt_obj=pt_obj,
    )
    assert fetch_synonym_success == True
    assert len(list_synonym) > 0


def test_post_process_app_description():
    app_description = ""
    app_description_original = ""
    device_app_id = "com.rovio.BadPiggies"
    lang = "en"
    scrape_success, result = scrap_play_store(
        device_app_id=device_app_id,
        lang=lang,
    )

    app_description_original, app_description = post_process_app_description(result)

    assert scrape_success == True
    assert app_description_original != ""
    assert app_description != ""


def main():
    # test_load_app_data()
    # test_convert_to_embedding_using_huggingface_model()
    # test_fetch_synonyms_from_powerthesaurus()
    # test_fetch_synonyms()
    # test_fetch_synonyms_from_powerthesaurus_using_api()
    # test_dump_variable_to_pickle()
    # test_read_variable_from_pickle()
    # test_for_loops()
    # test_pass_to_function()

    # test_post_process_app_description()
    # test_extract_keyword_using_yake_and_post_process()
    # test_post_process_keywords()
    # test_remove_emoji()
    # test_check_two_lists_same()
    # test_extract_device_app_synonyms()
    pass


if __name__ == "__main__":
    sys.exit(main())
