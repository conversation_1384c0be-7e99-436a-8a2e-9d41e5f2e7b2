from typing import List, Any, Tuple
import re2


def duplicate_list_limited(
    list_to_duplicate: List[Any],
    limit: int,
) -> List[Any]:
    """
    This function duplicates a list to a limit in length.
    If the limit is less than the length of the list,
    the function returns the first limit number of elements from the list.

    Parameters:
    ----------
    list_to_duplicate: list
        The list to be duplicated.

    limit: int
        The number of times to duplicate the list.

    Returns:
    --------
    list_duplicated: list
        The duplicated list.

    Examples:
    ---------
    Given:
    list_to_duplicate = ['a', 'b']
    limit = 11
    return:
    [
        'a', 'b',
        'a', 'b',
        'a', 'b',
        'a', 'b',
        'a', 'b',
        'a',
    ]

    Given
    list_to_duplicate = ['a', 'b', 'c', 'd']
    limit = 2
    return:
    [
        'a', 'b',
    ]

    """
    list_duplicated = list_to_duplicate.copy()
    num_list = len(list_to_duplicate)

    if num_list == 0:
        return list_duplicated

    if len(list_to_duplicate) > limit:
        list_duplicated = list_duplicated[0:limit]
        return list_duplicated

    times = limit // num_list
    more = limit - num_list * times

    list_duplicated = list_duplicated * times
    for i in range(more):
        list_duplicated.append(list_to_duplicate[i])

    return list_duplicated


def search_list_text(
    list_text: List[str],
    list_words: List[str],
    match_whole_word: bool = True,
) -> List[int]:
    """
    Counts the occurrences of specified words in each row of text.

    Args:
        list_text (List[str]): A list of text rows to search through.
        list_words (List[str]): A list of words to count in the text rows.
        match_whole_word (bool): If True, counts only whole word matches. Defaults to True.

    Returns:
        List[int]: A list of counts of matched words for each text row.

    Examples:
    ```python
    result = search_list_text(
        list_text=['one one two four', 'two two two three four ', 'one three two three three four'],
        list_words=['one', 'two', 'three']
    )
    # returns [3, 4, 5]

    result = search_list_text(
        list_text=['one two four', 'two two four ', 'one two three three four'],
        list_words=['one', 'two', 'three'],
        match_whole_word=False
    )
    # returns [2, 2, 4]
    ```
    """
    list_num_matches = []
    num_text = len(list_text)

    for text_ind in range(num_text):

        num_total_match = 0
        for word in list_words:

            text = list_text[text_ind]
            text = text.lower()
            search_word = word.lower()
            if match_whole_word:
                list_matches = re2.findall(f"\\b{search_word}\\b", text)
            else:
                list_matches = re2.findall(f"{search_word}", text)

            num_match = len(list_matches)
            num_total_match += num_match
        list_num_matches.append(num_total_match)

    return list_num_matches


def generate_number_list(
    length: int,
    gap: int,
) -> List[int]:
    """
    Generate a list of numbers starting from 0, incremented by a specified gap.

    Examples:
        Example 1:
        ```python
        generate_number_list(5, 2)
        ```
        returns `[0, 2, 4, 6, 8]`

        Example 2:
        ```python
        generate_number_list(3, 3)
        ```
        returns `[0, 3, 6]`
    """
    return [i * gap for i in range(length)]


def append_and_squeeze_list(
    data_list: List[Any], new_data: Any, list_fixed_size: int
) -> List[Any]:
    """
    Update a list by appending new data. If the list has reached a fixed size,
    the earliest element will be removed before appending the new data.

    Args:
        data_list (List[Any]): The list to be updated.
        new_data (Any): The new data to append to the list.
        list_fixed_size (int): The maximum size of the list.

    Returns:
        List[Any]: The updated list.

    Example 1:
    ```python
    data = [1, 2, 3]
    updated_data = append_and_squeeze_list(data, 4, 3)
    print(updated_data)  # Output: [2, 3, 4]
    ```

    Example 2:
    ```python
    data = [1, 2]
    updated_data = append_and_squeeze_list(data, 3, 3)
    print(updated_data)  # Output: [1, 2, 3]
    ```
    """
    if len(data_list) >= list_fixed_size:
        data_list.pop(0)  # Remove the earliest element
    data_list.append(new_data)  # Append the new data
    return data_list


def calculate_total_len_of_index_range_list(
    index_range_list: List[Tuple[int, int]]
) -> int:
    """
    Calculate the total length of a list of index ranges.

    Args:
        index_range_list (List[Tuple[int, int]]): A list of tuples, where each tuple contains the start and end index of a range.

    Returns:
        int: The total length of the index ranges.

    Example:
    ```python
    result = calculate_total_len_of_index_range_list(index_range_list=[(0, 5), (10, 15)])
    # returns 10
    ```
    """
    total_length = 0
    for start, end in index_range_list:
        total_length += end - start
    return total_length
