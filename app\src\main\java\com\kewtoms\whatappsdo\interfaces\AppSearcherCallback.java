package com.kewtoms.whatappsdo.interfaces;

import java.util.List;

/**
 * Callback interface for app search operations. This interface is
 * used to notify when the app search is completed.
 *
 * <p>Example usage:</p>
 * <pre>{@code
 *     AppSearcherCallback callback = new AppSearcherCallback() {
 *         @Override
 *         public void onSearchDone(List<String> newAppNames, List<String> newPackageNames, List<String> newImagePaths) {
 *             // Handle the search results here
 *         }
 *     };
 * }
 * </pre>
 */
public interface AppSearcherCallback {
 /**
  * Called when the app search operation is completed.
  *
  * @param newAppNames A list of names of the newly found
  * applications.
  * @param newPackageNames A list of package names corresponding to
  * the newly found applications.
  * @param newImagePaths A list of image paths for the icons of the
  * newly found applications.
  *
  * <p>Example usage:</p>
  * <pre>{@code
  *     AppSearcherCallback callback = new AppSearcherCallback() {
  *         @Override
  *         public void onSearchDone(List<String> newAppNames, List<String> newPackageNames, List<String> newImagePaths) {
  *             // Handle the search results here
  *             for (String appName : newAppNames) {
  *                 System.out.println("Found app: " + appName);
  *             }
  *         }
  *     };
  * }
  * </pre>
  */
 void onSearchDone(
  List<String> newAppNames,
  List<String> newPackageNames,
  List<String> newImagePaths);
}