package com.kewtoms.whatappsdo.model;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScrapeData;
import com.kewtoms.whatappsdo.model.scrapeApps.AppScraper;

import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Arrays;
import java.util.List;


@RunWith(AndroidJUnit4.class)

public class AppScraperTest {

  @Test
  public void testScrapeGooglePlay() {
    List<String> packageNamesListString = Arrays.asList(
      "com.tencent.mmz",
      // <<<< fake package name
      "com.tencent.mm",
      "com.HoYoverse.hkrpgoversea",
      "com.mihoyo.desktopportal",
      "com.duolingo",
      "com.HoYoverse.Nap",
      "com.immutable.guildofguardians",
      "com.heliogames.amikin.survival",
      "com.colossi.survival.gladiators",
      "valhalla.survival.craft.z",
      "com.pgstudio.exile.survival"
    );
    AppScraper appScraper = new AppScraper();
    List<ScrapeData> listScrapeData =
      appScraper.scrapeGooglePlay(packageNamesListString);

    assertFalse(listScrapeData.get(0).getSuccess());
    assertEquals(
      "",
      listScrapeData.get(0).getAppName()
    );

    assertTrue(listScrapeData.get(1).getSuccess());
    assertEquals(
      Constants.scrape_source_playstore,
      listScrapeData.get(0).getSource()
    );
  }
}
