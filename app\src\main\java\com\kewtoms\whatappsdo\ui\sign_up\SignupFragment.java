package com.kewtoms.whatappsdo.ui.sign_up;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.ConstantsPrivate;
import com.kewtoms.whatappsdo.databinding.FragmentSignUpBinding;

import com.google.firebase.auth.FirebaseAuth;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;
import com.kewtoms.whatappsdo.model.auth.Authenticator;
import com.kewtoms.whatappsdo.utils.Authentication.FirebaseAuthenticationUtils;

import org.json.JSONObject;

import java.util.Objects;


public class SignupFragment
  extends Fragment {

  private SignupViewModel signupViewModel;
  private FragmentSignUpBinding binding;
  private FirebaseAuth mAuth;
  public static final String TAG = "APP:SignupFragment";

  public View onCreateView(
    @NonNull LayoutInflater inflater,
    ViewGroup container,
    Bundle savedInstanceState) {

    signupViewModel =
      new ViewModelProvider(requireActivity()).get(SignupViewModel.class);

    mAuth = FirebaseAuth.getInstance();

    binding = FragmentSignUpBinding.inflate(
      inflater,
      container,
      false
    );
    View root = binding.getRoot();

    // Register (observe) LiveData with view's methods
    signupViewModel.getEmailAddress().observe(
      getViewLifecycleOwner(),
      binding.editTextEmailAddress::setText
    );
    signupViewModel.getPassword().observe(
      getViewLifecycleOwner(),
      binding.textInputEditTextPassword::setText
    );
    signupViewModel.getConfirmPassword().observe(
      getViewLifecycleOwner(),
      binding.textInputEditTextConfirmPassword::setText
    );
    signupViewModel.getStatus().observe(
      getViewLifecycleOwner(),
      binding.textViewSigninStatus::setText
    );
    signupViewModel.getVerificationCode().observe(
      getViewLifecycleOwner(),
      binding.editTextVerificationCode::setText
    );

    // Set up Sign In button on click listener
    Button loginButton = binding.buttonLoginSmall;
    loginButton.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        // Navigate to LoginFragment
        NavController navController = Navigation.findNavController(v);
        navController.navigate(R.id.action_nav_sign_up_to_nav_login);
      }
    });

    // Set up Sign up button on click listener
    Button signupButtonLarge = binding.buttonSignUpLarge;
    signupButtonLarge.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        // Register new user
        String email =
          binding.editTextEmailAddress.getText().toString();
        String password =
          Objects.requireNonNull(binding.textInputEditTextPassword.getText())
            .toString();
        String confirmPassword =
          Objects.requireNonNull(binding.textInputEditTextConfirmPassword.getText())
            .toString();
        String verificationCode =
          binding.editTextVerificationCode.getText().toString();

        // Sign up user ..
        PostResponseCallback callback = new PostResponseCallback() {
          @Override
          public void onPostSuccess(JSONObject response) {

          }

          @Override
          public void onPostError(String errorMessage) {

          }
        };

        Authenticator authenticator =
          new Authenticator(requireActivity());

        try {
          authenticator.signup(
            email,
            password,
            verificationCode,
            callback,
            false
          );
        } catch (InterruptedException e) {
          throw new RuntimeException(e);
        }


      }
    });

    // Set up obtain verification code button (the GET button) on
    // click listener
    Button getVerificationCodeButton =
      binding.buttonGetVerificationCode;
    getVerificationCodeButton.setOnClickListener(new View.OnClickListener() {
      final String methodName = "getVerificationCodeButton onCLick";

      @Override
      public void onClick(View v) {
        // Immediately disable the button to prevent multiple clicks
        getVerificationCodeButton.setEnabled(false);

        // Create and start the CountDownTimer for 30 seconds with 1-second intervals
        new CountDownTimer(
          30000,
          1000
        ) {

          @SuppressLint("SetTextI18n")
          @Override
          public void onTick(long millisUntilFinished) {
            getVerificationCodeButton.setText(millisUntilFinished / 1000 + " s");
          }

          @Override
          public void onFinish() {
            // Reset button text and enable it once the countdown is over
            getVerificationCodeButton.setText(getString(R.string.get_verification_code_button_text));
            getVerificationCodeButton.setEnabled(true);
          }
        }.start();

        String email =
          binding.editTextEmailAddress.getText().toString();

        Authenticator authenticator =
          new Authenticator(requireActivity());

        PostResponseCallback callback = new PostResponseCallback() {
          final String callMethodName =
            "authenticator.obtainVerificationCode";

          @Override
          public void onPostSuccess(JSONObject response) {
            Log.d(
              TAG,
              methodName + " calls" + callMethodName + ": " + "outer callback message: " + "onPostSuccess run"
            );

            Toast.makeText(
              getActivity(),
              "Please check email for verification code",
              Toast.LENGTH_SHORT
            ).show();

            Log.d(
              TAG,
              methodName + " calls" + callMethodName + ": " + "outer callback message: " + "onPostSuccess done"
            );
          }

          @Override
          public void onPostError(String errorMessage) {

          }
        };

        try {
          authenticator.obtainVerificationCode(
            email,
            callback,
            false
          );
        } catch (InterruptedException e) {
          throw new RuntimeException(e);
        }
      }
    });

    // Set up editTexts' listeners
    binding.editTextEmailAddress.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(
        CharSequence s,
        int start,
        int count,
        int after) {

      }

      @Override
      public void onTextChanged(
        CharSequence s,
        int start,
        int before,
        int count) {

      }

      @Override
      public void afterTextChanged(Editable s) {
        if (binding.editTextEmailAddress.getText()
          .toString()
          .equals(ConstantsPrivate.private_test_email_address)) {
          return;
        }
        // Validate email address
        boolean isValidEmail =
          FirebaseAuthenticationUtils.isValidEmail(binding.editTextEmailAddress.getText()
            .toString());
        if (!isValidEmail) {
          signupViewModel.setStatus("Invalid email address");
        }
        if (isValidEmail) {
          signupViewModel.setStatus("");
        }

      }
    });

    binding.textInputEditTextConfirmPassword.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(
        CharSequence s,
        int start,
        int count,
        int after) {

      }

      @Override
      public void onTextChanged(
        CharSequence s,
        int start,
        int before,
        int count) {

      }

      @Override
      public void afterTextChanged(Editable s) {
        // Check if password match
        if (!FirebaseAuthenticationUtils.isPasswordMatch(
          Objects.requireNonNull(binding.textInputEditTextPassword.getText())
            .toString(),
          Objects.requireNonNull(binding.textInputEditTextConfirmPassword.getText())
            .toString()
        )) {
          signupViewModel.setStatus("Passwords do not match");
        } else {
          signupViewModel.setStatus("");
        }
      }
    });

    return root;
  }


  @Override
  public void onDestroyView() {
    super.onDestroyView();
    binding = null;
  }
}
