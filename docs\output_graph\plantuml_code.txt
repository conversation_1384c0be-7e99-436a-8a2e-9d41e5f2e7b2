@startuml test_verify_client_apps_trace
'left to right direction
skinparam ranksep 0
allowmixing

' --------------------------


struct fastapi_app {
  {method} sign_in
  {method} sign_out
  {method} sign_up
  {method} search_user_app
}

note left of fastapi_app
  Each endpoint has input data of type Data
  ---
  class Data(BaseModel):
    """
    A Pydantic model representing the data structure for incoming requests.

    Attributes:
        data_str (str): A string containing the data to be processed.
                         Defaults to an empty string.
    """

    data_str: str = ""
  ---
    Each endpoint has response format
    {"Response": data_dict}
    If there is error, 
    {"Response": {"Error": error_message}}
  ---
end note

' --------------------------
class UserDatabase {
{method} sign_in
{method} sign_out
{method} sign_up
{method} delete
}

note top of UserDatabase
  Each method has response format
  {"db_result": data_dict}
end note

note right of UserDatabase::sign_in
  doc string: Sign in
  in: email_address:str, password:str
  out: suitable dict
  is_regenerate: False
end note

note right of UserDatabase::sign_out
  doc string: Sign out
  in: email_address:str
  out: suitable dict
  is_regenerate: True
end note


' --------------------------
class AppDatabase {
{method} search_user_app
}

note left of AppDatabase::search_user_app
  doc string: Search app from database using vector search
  in: client_search_data: Dict, 
    debug_mode: bool,
  out: suitable dict
  is_regenerate: True
end note
' --------------------------
class PickleDatabaseSplit {
}

note right of PickleDatabaseSplit
  to import: from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
  is_regenerate: False
end note
' --------------------------


fastapi_app::sign_in -right-> UserDatabase::sign_in
fastapi_app::sign_out -right-> UserDatabase::sign_out
fastapi_app::sign_up -right-> UserDatabase::sign_up

fastapi_app::search_user_app --> AppDatabase::search_user_app

UserDatabase --> PickleDatabaseSplit : inherits
AppDatabase --> PickleDatabaseSplit : inherits



@enduml


