import os
from dataclasses import dataclass
import shutil
from sys import settrace
import tempfile
from typing import Dict, List
from tomsze_utils.byte_utils import try_decode_byte
from tomsze_utils.config_parser_utils import load_json_config
from tomsze_utils.import_utils import import_module_by_path
from tomsze_utils.dict_utils import extract_data_from_dict_contains
from tomsze_utils.logger_utils import SimpleLogger
from tomsze_utils.script_analyse_utils import (
    get_astFuncData_dict_in_script,
    get_refactored_astFuncData_dict_in_dir,
    get_refactored_astFuncData_dict_in_dir_list,
)
from tomsze_utils.path_utils import find_project_root_using_git_folder
from tomsze_utils.string_utils import wrap_text_by_indices
from tomsze_utils.subprocess_utils import subprocess_run
from tomsze_utils.text_file_modify_utils import write_text_to_file
from tomsze_utils.xml_utils import (
    find_next_tag_text,
    find_previous_tag_indices,
    find_text_tag_indices,
)

logger = SimpleLogger(
    get_existing_instance=False,
)

EVENT = "event"
EVENT_CALLS = "call"
EVENT_LINES = "line"
EVENT_RETURN = "return"

# PARENT_CODE = 'parent_code'
PARENT_FUNCTION_NAME = "parent_function"
PARENT_FILENAME = "parent_filename"
PARENT_CLASS_NAME = "parent_class_name"
PARENT_LINE_NUMBER = "parent_line_number"

# CHILD_CODE = 'child_code'
CHILD_FUNCTION_NAME = "child_function"
CHILD_FILENAME = "child_filename"
CHILD_CLASS_NAME = "child_class_name"
CHILD_LINE_NUMBER = "child_line_number"


class PlantUmlGenerator:

    def __init__(
        self,
    ) -> None:
        pass

    def my_tracer(self, frame, event, arg=None):
        try:
            if not frame:
                logger.info("No frame found.")
                return self.my_tracer

            parent_frame = frame.f_back  # not string
            if not parent_frame:
                logger.info("No parent frame found.")
                return self.my_tracer
            parent_code = parent_frame.f_code  # not string
            if not parent_code:
                logger.info("No parent code found.")
                return self.my_tracer

            parent_code_func_name = parent_code.co_name
            parent_filename = parent_code.co_filename
            parent_class_name = parent_frame.f_locals.get(
                "self", None
            ).__class__.__name__
            # parent_line_number = parent_frame.f_lineno

            child_code = frame.f_code
            if not child_code:
                logger.info("No child code found.")
                return self.my_tracer

            child_code_func_name = child_code.co_name
            # child_filename = child_code.co_filename
            child_class_name = frame.f_locals.get("self", None).__class__.__name__
            # child_line_number = frame.f_lineno

            # parent_co_varnames = parent_code.co_varnames
            # child_co_varnames = child_code.co_varnames

            parent_filename = os.path.realpath(parent_filename)
            parent_filename = parent_filename.replace("\\", "/")
            if not parent_filename in self.list_only_scripts:
                return
            logger.info(f'parent_filename: "{parent_filename}"')

            if event == EVENT_CALLS:
                if parent_class_name == "NoneType":
                    parent_class_name = ""

                if child_class_name == "NoneType":
                    child_class_name = ""

                plantuml_str = f'{parent_filename}" {parent_class_name} {parent_code_func_name}()" -> "{child_class_name} {child_code_func_name}()" : {event}'
                self.list_trace_str.append(plantuml_str)

            if event == EVENT_RETURN:
                if parent_class_name == "NoneType":
                    parent_class_name = ""

                if child_class_name == "NoneType":
                    child_class_name = ""

                local_vars = frame.f_locals
                return_var = [var for var in local_vars.keys()]  # Get variable names
                return_var_str = ", ".join(return_var)

                if not return_var_str:
                    return_var_str = f'"{arg}"'

                    plantuml_str = f'{parent_filename} "{parent_class_name} {parent_code_func_name}()" <- "{child_class_name} {child_code_func_name}()" : {event} {return_var_str}'
                    self.list_trace_str.append(plantuml_str)

        except Exception as e:
            # logger.error(f"error: {str(e)}")
            # logger.error(f"event: {str(event)}")
            # return
            print(f"error: {str(e)}")

        return self.my_tracer

    # Define a function to run the test and generate the graph
    def run_test_function_with_tracer(
        self,
        test_script_path,
        test_function_name,
        output_dir,
    ):

        module_working_directory = find_project_root_using_git_folder(test_script_path)
        error, loaded_module = import_module_by_path(
            test_script_path,
            working_directory=module_working_directory,
        )

        if error != "":
            logger.error(
                f'There is error when importing test script "{test_script_path}"'
            )
            logger.error(f"{error}")
            return

        self.list_trace_str.clear()
        settrace(self.my_tracer)

        # Run the test function
        test_function = getattr(loaded_module, test_function_name)
        test_function()

        output_puml_path = os.path.join(output_dir, f"{test_function_name}_trace.puml")
        output_puml_path = output_puml_path.replace("\\", "/")

        # Ensure the output directory exists
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # Write the trace data to a PlantUML file
        with open(output_puml_path, "w", encoding="utf-8") as file:
            file.write(f"@startuml {test_function_name}_trace\n")
            for trace_json in self.list_trace_str:
                file.write(f"{trace_json}\n")
            file.write(f"@enduml\n")

    def generate(
        self,
        test_script_path,
        test_function_name,
        output_dir,
        exclude_path_list=[],
    ) -> None:

        if not os.path.exists(test_script_path):
            raise FileNotFoundError(f"{test_script_path} does not exist.")

        # Find the root path of the project using the provided test script path
        test_script_root_path = find_project_root_using_git_folder(test_script_path)

        # Collect all Python script files in the specified directory
        list_file_type = [
            "py",
        ]
        self.list_only_scripts = []
        for dirpath, dirnames, filenames in os.walk(test_script_root_path):
            for filename in filenames:
                filetype = filename.split(".")[-1]
                front_filename = filename.replace("." + filetype, "")
                if filetype in list_file_type:
                    file_path = os.path.join(dirpath, filename).replace("\\", "/")
                    self.list_only_scripts.append(file_path)

        # Remove exclude_path_list paths from list_only_scripts
        self.list_only_scripts = [
            file_path
            for file_path in self.list_only_scripts
            if not any(exclude_path in file_path for exclude_path in exclude_path_list)
        ]

        self.list_trace_str = []

        if test_function_name[0:5] == "test_":
            # Execute the test function and create the associated puml file
            self.run_test_function_with_tracer(
                test_script_path,
                test_function_name,
                output_dir,
            )


def puml_to_interactive_svg(
    jar_path: str,
    puml_path: str,
    output_dir: str,
    method_mapping_json_path: str,
    is_remove_intermediate_svg: bool = True,
) -> None:
    """
    Converts a PlantUML (.puml) file to an interactive SVG with clickable links based on a method mapping JSON.

    This function runs the PlantUML JAR to generate an SVG, modifies it to add hyperlinks for methods defined in
    the method_mapping_json, and writes the final SVG back to disk. Optionally removes the intermediate SVG.

    Args:
        jar_path (str): Path to the PlantUML JAR file.
        puml_path (str): Path to the input .puml file.
        output_dir (str): Directory where the resulting SVG will be saved.
        method_mapping_json_path (str): Path to the JSON file mapping method names to file paths.
        is_remove_intermediate_svg (bool): Whether to remove the intermediate SVG file after processing. Defaults to True.

    Notes:
        - The SVG file is initially generated with the same name as the `.puml` file but with a `.svg` extension.
        - An intermediate version of the SVG may be created during processing with `_interm.svg` suffix.
        - If class name detection fails, warnings may be logged and execution stopped.

    Examples:
        ```python
        puml_to_interactive_svg(
            jar_path="plantuml.jar",
            puml_path="diagram.puml",
            output_dir="./output",
            method_mapping_json_path="method_map.json"
        )
        ```

        ```python
        puml_to_interactive_svg(
            jar_path="/opt/plantuml/plantuml.jar",
            puml_path="/project/diagrams/flow.puml",
            output_dir="/project/output",
            method_mapping_json_path="/project/mappings/methods.json",
            is_remove_intermediate_svg=False
        )
        ```
    """

    # Run command to convert to svg and save to output_dir
    command = f"java -jar {jar_path} -tsvg {puml_path}"
    out = subprocess_run(
        command=command,
        capture_output=True,
        shell=False,
    )
    if out.stdout:
        output_str = try_decode_byte(out.stdout)
        logger.info(output_str)

    if out.stderr:
        output_str = try_decode_byte(out.stderr)
        logger.error(f"{out.stderr}")
        return

    # Rename the svg file
    svg_interm_old_path = os.path.join(
        output_dir, os.path.basename(puml_path).replace(".puml", ".svg")
    )
    svg_path = svg_interm_old_path
    svg_interm_new_path = os.path.join(
        output_dir, os.path.basename(puml_path).replace(".puml", "_interm.svg")
    )
    if not os.path.exists(svg_interm_new_path):
        logger.info(f"Renaming {svg_interm_old_path} to {svg_interm_new_path}")
        os.rename(svg_interm_old_path, svg_interm_new_path)

    # Read the method mapping json file
    file_content_dict = load_json_config(method_mapping_json_path)

    # Read the svg
    if not os.path.exists(svg_interm_new_path):
        logger.error(f"SVG file {svg_interm_new_path} does not exist.")
        return

    with open(svg_interm_new_path, "r") as file:
        svg_content = file.read()

    # Get the class name
    class_name = ""
    class_match_indices_list = find_text_tag_indices(
        svg_content,
        "Class",
    )
    if not class_match_indices_list:
        logger.error(f"Class name not found in {svg_interm_new_path}")
        return

    next_tag_text_list = find_next_tag_text(svg_content, class_match_indices_list)
    if next_tag_text_list:
        class_name = next_tag_text_list[0]

    # Replace any text that is "self " to class name + "."
    self_replaced_svg_content = svg_content.replace("self ", class_name + ".")
    # Edit the svg by wrapping the text with method names
    # and prev element with <a> tags
    for method_name, method_file_path in file_content_dict.items():
        match_indices_list = find_text_tag_indices(
            self_replaced_svg_content,
            method_name,
        )

        if match_indices_list:

            # Get the previous tag indices of the method name text tag
            prev_tag_indices_list = find_previous_tag_indices(
                self_replaced_svg_content, match_indices_list
            )

            # Wrap the method name text with <a> tags
            for match_indices, start_index in zip(
                match_indices_list, prev_tag_indices_list
            ):
                end_index = match_indices[1]

                self_replaced_svg_content = wrap_text_by_indices(
                    self_replaced_svg_content,
                    start_index,
                    end_index - 1,
                    f"<a href='{method_file_path}'>",
                    "</a>",
                )

    final_svg_content = self_replaced_svg_content.replace(class_name + ".", "self ")

    # Write new svg content to the file
    write_text_to_file(
        file_path=svg_path,
        text=final_svg_content,
    )

    # Remove the intermediate svg file
    if is_remove_intermediate_svg:
        if os.path.exists(svg_interm_new_path):
            os.remove(svg_interm_new_path)


def puml_to_interactive_svg_in_dir(
    jar_path,
    puml_dir,
    output_dir,
    method_mapping_json_path,
    is_remove_intermediate_svg=True,
):
    for puml_file in os.listdir(puml_dir):
        if puml_file.endswith(".puml"):
            puml_path = os.path.join(puml_dir, puml_file)
            puml_to_interactive_svg(
                jar_path,
                puml_path,
                output_dir,
                method_mapping_json_path,
                is_remove_intermediate_svg,
            )


def main():
    # # Get current script path
    # current_script_path = os.path.realpath(__file__).replace("\\", "/")

    # plantUmlGenerator = PlantUmlGenerator()
    # plantUmlGenerator.generate(
    #     test_script_path="./tests/test_plantuml_utils.py",
    #     test_function_name="test_a",
    #     output_dir="./tests/tmp_output_puml",
    #     exclude_path_list=[current_script_path],
    # )

    # plantUmlGenerator.generate(
    #     test_script_path="./tests/test_dict_utils.py",
    #     test_function_name="test_split_dict",
    #     output_dir="./tests/tmp_output_puml",
    # )

    # Try puml_to_interactive_svg
    jar_path = "./tomsze_utils/jars/plantuml_version_has_interactive_svg.jar"
    puml_path = "./tomsze_utils/plantuml_utils_trial_dir/a.puml"
    output_dir = "./tomsze_utils/plantuml_utils_trial_dir"
    method_mapping_json_path = (
        "./tomsze_utils/plantuml_utils_trial_dir/method_mapping.json5"
    )
    puml_to_interactive_svg(
        jar_path,
        puml_path,
        output_dir,
        method_mapping_json_path,
    )

    jar_path = "./tomsze_utils/jars/plantuml_version_has_interactive_svg.jar"
    puml_path = "./tomsze_utils/plantuml_utils_trial_dir/b.puml"
    output_dir = "./tomsze_utils/plantuml_utils_trial_dir"
    method_mapping_json_path = (
        "./tomsze_utils/plantuml_utils_trial_dir/method_mapping.json5"
    )
    puml_to_interactive_svg(
        jar_path,
        puml_path,
        output_dir,
        method_mapping_json_path,
    )

    jar_path = "./tomsze_utils/jars/plantuml_version_has_interactive_svg.jar"
    puml_path = "./tomsze_utils/plantuml_utils_trial_dir/c.x.puml"
    output_dir = "./tomsze_utils/plantuml_utils_trial_dir"
    method_mapping_json_path = (
        "./tomsze_utils/plantuml_utils_trial_dir/method_mapping.json5"
    )
    puml_to_interactive_svg(
        jar_path,
        puml_path,
        output_dir,
        method_mapping_json_path,
    )

    # # Try puml_to_interactive_svg_in_dir
    # jar_path = "./tomsze_utils/jars/plantuml_version_has_interactive_svg.jar"
    # puml_dir = "./tomsze_utils/plantuml_utils_trial_dir"
    # output_dir = "./tomsze_utils/plantuml_utils_trial_dir"
    # method_mapping_json_path = (
    #     "./tomsze_utils/plantuml_utils_trial_dir/method_mapping.json5"
    # )
    # puml_to_interactive_svg_in_dir(
    #     jar_path,
    #     puml_dir,
    #     output_dir,
    #     method_mapping_json_path,
    # )


if __name__ == "__main__":
    main()
