from datetime import datetime
import json
from typing import Dict, List
from server.api.utils.generate_vectors.utils import (
    post_process_app_description,
)
from server.api.utils.generate_vectors.utils import (
    convert_to_embedding,
)
from server.api.utils.generate_vectors.utils import (
    extract_keyword_using_yake,
)
from server.api.utils.keywords.get_keywords import post_process_keywords, lemmatization
from server.api.end_to_end.api_constant_keys import (
    DBAppsColEnum,
)
from tomsze_utils.time_code_utils import timeit
from tomsze_utils.email_utils import send_email
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.fastapi_utils import check_request_violation
from mode import is_production


@timeit
def send_email_after_update(
    db_apps: PickleDatabaseSplit,
    db_to_add: PickleDatabaseSplit,
    env_path: str,
    client_apps_to_update: Dict,
):
    """
    To be run in update_server_apps method.
    """

    num_data = db_apps.get_db_num_keys()
    num_db_to_add = db_to_add.get_db_num_keys()
    is_production = is_production()
    subject = "Seach phone app: usa"
    body = f"""
    database production  mode: {is_production}
    
    client_apps_to_update: 
    {json.dumps(client_apps_to_update, indent=4)}
    
    num db_apps item: {str(num_data)}
    num db_to_add item: {str(num_db_to_add)}
    """
    send_email(
        env_path=env_path,
        subject=subject,
        body=body,
    )


def update_db_apps(
    db_apps: PickleDatabaseSplit,
    app_id: str,
    app_desc: str,
    app_desc_embedding,
    list_kw: List[str],
):
    """
    Update to database var using update_data_v2

    """
    db_apps.update_data_with_key_and_col(
        key=app_id,
        col=DBAppsColEnum.IND,
        data=db_apps.get_db_num_keys()
        + 1,  # TODO add member var self.num_keys and update for PickleDatabaseSplit
    )
    db_apps.update_data_with_key_and_col(
        key=app_id,
        col=DBAppsColEnum.DESC,
        data=app_desc,
    )
    db_apps.update_data_with_key_and_col(
        key=app_id,
        col=DBAppsColEnum.EMBD,
        data=app_desc_embedding,
    )
    db_apps.update_data_with_key_and_col(
        key=app_id,
        col=DBAppsColEnum.KEYW,
        data=list_kw,
    )


def update_db_apps_using_keys(
    db_apps: PickleDatabaseSplit,
    app_id: str,
    app_title: str,
    app_desc: str,
    app_desc_embedding,
    list_kw: List[str],
    scrape_source: str,
):
    """
    Update to database var using update_data_with_keys
    """
    db_apps.update_data_with_keys(
        # key=app_id,
        # col=DBAppsColEnum.IND,
        # data=db_apps.get_db_num_keys()
        # + 1,  # TODO add member var self.num_keys and update for PickleDatabaseSplit
        keys=[app_id, DBAppsColEnum.IND],
        data=db_apps.get_db_num_keys() + 1,
    )
    db_apps.update_data_with_keys(
        keys=[app_id, DBAppsColEnum.TITLE],
        data=app_title,
    )
    db_apps.update_data_with_keys(
        # key=app_id,
        # col=DBAppsColEnum.DESC,
        # data=app_desc,
        keys=[app_id, DBAppsColEnum.DESC],
        data=app_desc,
    )
    db_apps.update_data_with_keys(
        # key=app_id,
        # col=DBAppsColEnum.EMBD,
        # data=app_desc_embedding,
        keys=[app_id, DBAppsColEnum.EMBD],
        data=app_desc_embedding,
    )
    db_apps.update_data_with_keys(
        # key=app_id,
        # col=DBAppsColEnum.KEYW,
        # data=list_kw,
        keys=[app_id, DBAppsColEnum.KEYW],
        data=list_kw,
    )
    db_apps.update_data_with_keys(
        keys=[app_id, DBAppsColEnum.SOURCE],
        data=scrape_source,
    )


def ppd_emb_kw_kwpp_update(
    db_apps: PickleDatabaseSplit,
    app_id,
    scrape_result: Dict,
    embedder,
    max_ngram_size,
    deduplication_threshold,
    max_num_keywords,
):
    """
    Post-process app description.
    Convert app desc to embedding.
    Extract keywords.
    Update to database var
    """

    # Post-process app description.
    app_desc_orig, app_desc = post_process_app_description(scrape_result)

    # Convert app desc to embedding.
    output_embedding_tensor = convert_to_embedding(  # TODO change to best embedder
        embedder, app_desc, device="cpu", batch_size=128
    )

    # Extract keywords.
    list_kw = extract_keyword_using_yake(
        lang="en",
        max_ngram_size=max_ngram_size,
        deduplication_threshold=deduplication_threshold,
        max_num_keywords=max_num_keywords,
        app_description_en_original=app_desc_orig,
    )

    list_kw = post_process_keywords(list_kw)

    # Update to database var (But dump to file later)
    update_db_apps(
        db_apps=db_apps,
        app_id=app_id,
        app_desc=app_desc,
        app_desc_embedding=output_embedding_tensor,
        list_kw=list_kw,
    )


def ppd_emb_kw_kwpp_update_using_keys(
    db_apps: PickleDatabaseSplit,
    app_id,
    scrape_result: Dict,
    embedder,
    max_ngram_size,
    deduplication_threshold,
    max_num_keywords,
):
    """
    Post-process app description.
    Convert app desc to embedding.
    Extract keywords.
    Update to database var *using update_data_with_keys
    """
    app_title = scrape_result["title"]
    scrape_source = scrape_result["source"]

    # Post-process app description.
    app_desc_orig, app_desc = post_process_app_description(scrape_result)

    # Convert app desc to embedding.
    output_embedding_tensor = convert_to_embedding(  # TODO change to best embedder
        embedder, app_desc, device="cpu", batch_size=128
    )

    # Extract keywords.
    list_kw = extract_keyword_using_yake(
        lang="en",
        max_ngram_size=max_ngram_size,
        deduplication_threshold=deduplication_threshold,
        max_num_keywords=max_num_keywords,
        app_description_en_original=app_desc_orig,
    )

    list_kw = post_process_keywords(list_kw)

    # Update to database var (But dump to file later)
    update_db_apps_using_keys(
        db_apps=db_apps,
        app_id=app_id,
        app_title=app_title,
        app_desc=app_desc,
        app_desc_embedding=output_embedding_tensor,
        list_kw=list_kw,
        scrape_source=scrape_source,
    )


def add_new_ip_to_db_rate_limit_if_not_exist(
    ip: str,
    db_rate_limit: PickleDatabaseSplit,
    max_requests_per_min: int = 20,
    time_period_in_min: int = 1,
) -> None:
    """
    Check if an IP is in the database rate limit and add it if not.

    This function checks if the provided IP exists in the rate limit database.
    If it does not exist, it initializes the necessary data for that IP.

    Args:
        ip (str): The IP address to check and potentially add.
        db_rate_limit (PickleDatabaseSplit): The database instance to update.
        max_requests_per_min (int, optional): The maximum number of requests allowed per minute. Defaults to 20.
        time_period_in_min (int, optional): The time period in minutes for the rate limit. Defaults to 1.

    Examples:
        ```python
        add_new_ip_to_db_rate_limit_if_not_exist(
            ip="***********",
            db_rate_limit=my_db,
            max_requests_per_min=30,
            time_period_in_min=2
        )
        ```

        ```python
        add_new_ip_to_db_rate_limit_if_not_exist(
            ip="********",
            db_rate_limit=my_db
        )
        ```

    Note: Check if ip in db rate limt
    """
    is_in = db_rate_limit.is_key_in(ip)

    if not is_in:
        # ip request times list
        db_rate_limit.update_data_with_keys(
            keys=[ip, "request_time_list"],
            data=[],
        )

        # ip max requests per min
        db_rate_limit.update_data_with_keys(
            keys=[ip, "max_requests_per_min"],
            data=max_requests_per_min,
        )

        db_rate_limit.update_data_with_keys(
            keys=[ip, "time_period_in_min"],
            data=time_period_in_min,
        )

        db_rate_limit.update_data_with_keys(
            keys=[ip, "request_time_list_fix_size"],
            data=max_requests_per_min * 2,
        )


def add_ip_request_time_to_db_rate_limit(
    db_rate_limit: PickleDatabaseSplit,
    client_ip: str,
    request_time_format: str = "%Y-%m-%dT%H:%M:%S",
) -> None:
    """
    Add the current request time to the rate limit database for the given client IP.

    Args:
        db_rate_limit (PickleDatabaseSplit): The database instance to update.
        client_ip (str): The IP address of the client making the request.
        request_time_format (str, optional): The format for the request time. Defaults to "%Y-%m-%dT%H:%M:%S".

    Examples:
        ```python
        add_ip_request_time_to_db_rate_limit(
            db_rate_limit=my_db,
            client_ip="***********",
            request_time_format="%Y-%m-%d %H:%M:%S"
        )
        ```

        ```python
        add_ip_request_time_to_db_rate_limit(
            db_rate_limit=my_db,
            client_ip="********"
        )
        ```

    Note: Check if ip in db rate limit.
    """
    request_time_list_fix_size, ind = db_rate_limit.query_by_keys_list(
        keys_list=[
            client_ip,
            "request_time_list_fix_size",
        ],
    )
    request_time = datetime.now().strftime(request_time_format)
    db_rate_limit.append_and_squeeze_list_data(
        keys=[client_ip, "request_time_list"],
        data=request_time,
        list_fix_size=request_time_list_fix_size,
    )


def check_ip_request_violation_in_db_rate_limit(
    db_rate_limit: PickleDatabaseSplit,
    client_ip: str,
) -> bool:
    """
    Check if the given client IP has exceeded the request limit in the rate limit database.

    Args:
        db_rate_limit (PickleDatabaseSplit): The database instance to check against.
        client_ip (str): The IP address of the client to check.

    Returns:
        bool: True if the IP has exceeded the request limit, False otherwise.

    Examples:
        ```python
        is_violated = check_ip_request_violation_in_db_rate_limit(
            db_rate_limit=my_db,
            client_ip="***********"
        )
        ```

        ```python
        is_violated = check_ip_request_violation_in_db_rate_limit(
            db_rate_limit=my_db,
            client_ip="********"
        )
        ```

    Note: Check if ip over limit.
    """
    # Get this ip requests from db rate limit.
    max_requests_per_min, index = db_rate_limit.query_by_keys_list(
        keys_list=[client_ip, "max_requests_per_min"],
    )
    request_time_list, index = db_rate_limit.query_by_keys_list(
        keys_list=[client_ip, "request_time_list"],
    )

    time_period_in_min, index = db_rate_limit.query_by_keys_list(
        keys_list=[client_ip, "time_period_in_min"],
    )

    # Check if ip over limit.
    is_violated = check_request_violation(
        request_time_list=request_time_list,
        max_requests=max_requests_per_min,
        time_period_in_min=time_period_in_min,
    )
    return is_violated


def add_new_ip_to_db_block_ip_if_not_exist(
    ip: str,
    db_block_ip: PickleDatabaseSplit,
) -> None:
    """
    Add a new IP to the block IP database if it does not already exist.

    Args:
        ip (str): The IP address to be added to the block list.
        db_block_ip (PickleDatabaseSplit): The database instance to update.

    Examples:
        ```python
        add_new_ip_to_db_block_ip_if_not_exist(
            ip="***********",
            db_block_ip=my_db_block_ip
        )
        ```

        ```python
        add_new_ip_to_db_block_ip_if_not_exist(
            ip="********",
            db_block_ip=my_db_block_ip
        )
        ```
    """
    is_in = db_block_ip.is_key_in(ip)

    if not is_in:
        # ip request times list
        db_block_ip.update_data_with_keys(
            keys=[ip, "is_blocked"],
            data=True,
        )


def check_is_ip_blocked_in_db_block_ip(
    ip: str,
    db_block_ip: PickleDatabaseSplit,
) -> bool:
    """
    Check if a given IP is blocked in the block IP database.

    Args:
        ip (str): The IP address to check.
        db_block_ip (PickleDatabaseSplit): The database instance to query.

    Returns:
        bool: True if the IP is blocked, False otherwise.

    Examples:
        ```python
        is_blocked = check_is_ip_blocked_in_db_block_ip(
            ip="***********",
            db_block_ip=my_db_block_ip
        )
        ```

        ```python
        is_blocked = check_is_ip_blocked_in_db_block_ip(
            ip="********",
            db_block_ip=my_db_block_ip
        )
        ```
    """
    # Get is blocked data of the ip
    is_blocked, ind = db_block_ip.query_by_keys_list(
        keys_list=[ip, "is_blocked"],
        default=False,
    )

    return is_blocked
