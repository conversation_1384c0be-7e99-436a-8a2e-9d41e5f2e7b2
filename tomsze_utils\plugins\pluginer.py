import os
import ast
import random
from typing import Any, Dict, List
import json
from path import Path
import logging
from tomsze_utils.config_parser_utils import load_json_config

from tomsze_utils.plugins.app_plugin_base.app_plugin import AppPlugin
from tomsze_utils.plugins.constant import plugin_constants
from tomsze_utils.plugins.plugin_utils import factory, loader


logger = logging.getLogger(__name__)


# Define a visitor class to traverse the AST and extract class names
class ClassNameVisitor(ast.NodeVisitor):
    def __init__(self):
        self.list_class_name = []

    def visit_ClassDef(self, node):
        classname = node.name
        self.list_class_name.append(classname)

    def clear_list_class_name(self):
        self.list_class_name.clear()


def get_classname_to_script_path_dict_in_dir(
    class_name_visitor: ClassNameVisitor,
    dir_path: str,
) -> Dict[str, str]:
    """
    Traverse a directory and its subdirectories to find Python files,
    extract class names from these files, and return a dictionary mapping
    class names to their corresponding file paths.

    Args:
        class_name_visitor (ClassNameVisitor): A visitor class to extract class names from AST trees.
        dir_path (str): The path to the directory to traverse.

    Returns:
        Dict[str, str]: A dictionary mapping class names to their corresponding file paths.

    Examples:
        >>> visitor = ClassNameVisitor()
        >>> result = get_dir_dict_classname_to_path(visitor, './my_plugins')
        >>> print(result)  # Output might be {'MyPlugin': './my_plugins/my_plugin.py'}

        >>> visitor = ClassNameVisitor()
        >>> result = get_dir_dict_classname_to_path(visitor, './another_directory')
        >>> print(result)  # Output might be {'AnotherPlugin': './another_directory/another_plugin.py'}
    """
    dict_classname_filepath = {}

    for dirpath, dirnames, filenames in os.walk(dir_path):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            front_filename = filename.replace("." + filetype, "")
            if filetype == "py":
                file_path = os.path.join(dirpath, filename)
                file_path = file_path.replace("\\", "/")

                # Parse the Python script into an AST tree
                with open(file_path, "r") as f:
                    ast_tree = ast.parse(f.read())

                # Traverse the AST with the visitor class to extract class names
                class_name_visitor.clear_list_class_name()
                class_name_visitor.visit(ast_tree)
                list_class_name = class_name_visitor.list_class_name

                for classname in list_class_name:
                    if classname not in dict_classname_filepath:
                        if classname[:6] == "Plugin":
                            dict_classname_filepath[classname] = file_path

    return dict_classname_filepath


class Pluginer:
    """
    A class to manage and load plugins from specified settings and directories.

    The Pluginer class is responsible for initializing plugins from a JSON configuration file,
    loading plugins from a specified directory, and managing the lifecycle of these plugins.
    It provides methods to run the plugins and validate their settings.

    Attributes:
        class_name_visitor (ClassNameVisitor): An instance of ClassNameVisitor to extract class names from plugin scripts.
        list_setting (list): A list of plugin settings loaded from the JSON file.
        dict_obj_plugin (Dict[str, AppPlugin]): A dictionary mapping plugin types to their corresponding plugin objects.
        dict_plugin_load_success (Dict[str, bool]): A dictionary indicating whether each plugin was loaded successfully.
        dict_plugin_can_change_file (Dict[str, bool]): A dictionary indicating whether each plugin can change files.
        dict_plugin_use (Dict[str, bool]): A dictionary indicating whether each plugin is set to be used.

    Methods:
        init_from_json(json_path, key, load_unspecified_use=False):
            Initializes the pluginer from a JSON file.
        init_from_json_and_dir(json_path, key, dir_path, load_unspecified_use=False):
            Initializes the pluginer from a JSON file and loads plugins from a directory.
        load_plugins(plugin_script_dir, load_unspecified_use=False):
            Loads plugins from the specified directory.
        run_plugins(dict_app_data):
            Executes the 'do_something' method of each loaded plugin.
        run_plugin(dict_app_data, plugin_type):
            Executes the 'do_something' method of a specific plugin.
        validate_setting_json(dict_json_data, key):
            Validates the JSON settings.
        load_and_register_plugin(plugin_file_path, type):
            Loads and registers a plugin from a file.
        create_plugin_object(arguments):
            Creates a plugin object using the provided arguments.

    Examples:
        >>> pluginer = Pluginer()
        >>> pluginer.init_from_json("path/to/settings.json", "plugin")
        >>> pluginer.run_plugins({"data": "example_data"})

        >>> pluginer = Pluginer()
        >>> pluginer.init_from_json_and_dir("path/to/settings.json", "plugin", "path/to/plugins")
        >>> pluginer.run_plugin({"data": "example_data"}, "MyPlugin")
    """

    def __init__(self):
        self.class_name_visitor = ClassNameVisitor()
        self.list_setting = []
        self.list_setting_with_unique_key = []
        self.dict_obj_plugin: Dict[str, AppPlugin] = {}
        self.dict_plugin_load_success: Dict[str, bool] = {}
        self.dict_plugin_can_change_file: Dict[str, bool] = {}
        self.dict_plugin_use: Dict[str, bool] = {}

    def init_from_json(self, json_path, key, load_unspecified_use=False):
        """
        Initializes the pluginer from a JSON file.

        This method reads the JSON file, validates its contents,
        and loads the plugins specified in the file.
        It also creates plugin objects and stores them in the
        pluginer's internal dictionaries.

        Args:
            json_path (str): The path to the JSON file.
            key (str): The key to use in the JSON file.
                This key should point to a list of plugin settings.

        Returns:
            None

        Notes:
            The JSON file should have the following structure:
            {
                "key": [
                    {
                        "plugin_type": "plugin_name",
                        "use": true/false,
                        "plugin_can_change_files": true/false
                    },
                    ...
                ]
            }
            The "key" should match the key parameter passed to this method.
        """
        self.dict_obj_plugin.clear()
        self.dict_plugin_load_success.clear()
        self.dict_plugin_can_change_file.clear()
        self.dict_plugin_use.clear()

        dict_json_data = load_json_config(json_path)

        self.list_setting = dict_json_data[key]

        # Validate json file for main keys.
        if not self.validate_setting_json(dict_json_data, key):
            print("There is some problem with the setting json. End.")
            return

        # Load each plugins.
        factory.unregister_all()

        plugin_script_dir = os.path.join(
            str(Path(json_path).parent),
            plugin_constants.PLUGIN_SCRIPT_DIR_NAME,
        )

        self.load_plugins(plugin_script_dir, load_unspecified_use)

    def init_from_json_and_dir(
        self,
        json_path: str,
        key: str,
        dir_path: str,
        load_unspecified_use: bool = False,
    ) -> None:
        """
        Initialize the pluginer from a JSON file and load plugins from a directory.

        Args:
            json_path (str): The path to the JSON file containing plugin settings.
            key (str): The key in the JSON file that points to the list of plugin settings.
            dir_path (str): The directory path where plugin scripts are located.
            load_unspecified_use (bool): Flag to load plugins that are not specified in the JSON.

        Examples:
            >>> pluginer = Pluginer()
            >>> pluginer.init_from_json_and_dir("path/to/settings.json", "plugin", "path/to/plugins")
            >>> pluginer.init_from_json_and_dir("path/to/settings.json", "plugin", "path/to/plugins", load_unspecified_use=True)
        """
        self.init_from_json(json_path, key, load_unspecified_use)
        self.load_plugins(dir_path, load_unspecified_use)

    def load_plugins(self, plugin_script_dir, load_unspecified_use=False):
        """
        Load plugins from the specified directory.

        Args:
            plugin_script_dir (str): The directory containing plugin scripts.

        Notes:
            This method loads each plugin, registers it, and creates a plugin object.
            It also updates the pluginer's internal dictionaries with the loaded plugins.
        """
        try:
            # Load each plugins.
            dict_classname_filepath = get_classname_to_script_path_dict_in_dir(
                self.class_name_visitor,
                plugin_script_dir,
            )

            for dict_setting in self.list_setting:
                for key in dict_classname_filepath.keys():
                    type = key
                    create_plugin = False

                    if load_unspecified_use:
                        if (
                            type
                            == dict_setting[plugin_constants.PLUGIN_SETTING_PLUGIN_TYPE]
                        ):
                            # Skip on the setting if the key of 'use' is set to false.
                            if not dict_setting["use"]:
                                create_plugin = False
                            else:
                                create_plugin = True
                        else:
                            create_plugin = True

                    if not load_unspecified_use:
                        if (
                            type
                            == dict_setting[plugin_constants.PLUGIN_SETTING_PLUGIN_TYPE]
                        ):
                            if dict_setting["use"]:
                                create_plugin = True
                            else:
                                create_plugin = False

                    if create_plugin:
                        plugin_unique_key = f"{type}_{random.randint(0, 1000)}"

                        # Load the plugins.
                        plugin_file_path = dict_classname_filepath[type]
                        load_success, error_message = self.load_and_register_plugin(
                            plugin_file_path=plugin_file_path,
                            type=type,
                        )

                        if not load_success:
                            logger.error(f"{error_message} for {type}")

                        self.dict_plugin_load_success[plugin_unique_key] = load_success
                        self.dict_plugin_use[plugin_unique_key] = True

                        # Create the plugin objects.
                        if load_success:
                            dict_setting_will_create = {
                                "type": type,
                                "use": True,
                                "plugin_can_change_files": True,
                            }

                            dict_setting_new = dict_setting.copy()
                            dict_setting_new["plugin_unique_key"] = plugin_unique_key
                            self.list_setting_with_unique_key.append(dict_setting_new)
                            self.dict_obj_plugin[plugin_unique_key] = (
                                self.create_plugin_object(dict_setting_will_create)
                            )

                            can_change_file = dict_setting[
                                plugin_constants.PLUGIN_SETTING_CAN_CHANGE_FILE
                            ]
                            self.dict_plugin_can_change_file[plugin_unique_key] = (
                                can_change_file
                            )
        except Exception as e:
            logger.error(f"Error loading plugins: {e}")

    def run_plugins(self, dict_app_data: dict, plugin_unique_key: str) -> None:
        """
        Executes the 'do_something' method for each loaded plugin.

        Args:
            dict_app_data (dict): A dictionary containing application data to be processed by the plugins.

        Examples:
            >>> app_data = {"key": "value"}
            >>> pluginer.run_plugins(app_data)

            >>> app_data = {"user": "test_user", "action": "test_action"}
            >>> pluginer.run_plugins(app_data)
        """
        for key in self.dict_obj_plugin.keys():
            obj_plugin = self.dict_obj_plugin[key]

            if self.dict_plugin_load_success[key]:
                obj_plugin.do_something(dict_app_data, plugin_unique_key)

    def run_plugin(self, dict_app_data: dict, plugin_unique_key: str) -> None:
        """
        Executes the 'do_something' method of the specified plugin.

        Args:
            dict_app_data (dict): A dictionary containing application data to be processed by the plugin.
            plugin_type (str): The type of the plugin to run.

        Examples:
            >>> app_data = {"key": "value"}
            >>> pluginer.run_plugin(app_data, "PluginSample")

            >>> app_data = {"user": "test_user", "action": "test_action"}
            >>> pluginer.run_plugin(app_data, "PluginVariableAssigner")
        """
        obj_plugin = self.dict_obj_plugin[plugin_unique_key]

        if self.dict_plugin_load_success[plugin_unique_key]:
            obj_plugin.do_something(dict_app_data, plugin_unique_key)

    def validate_setting_json(self, dict_json_data: Dict[str, Any], key: str) -> bool:
        """
        Validates the JSON settings against the specified key.

        Args:
            dict_json_data (Dict[str, Any]): The JSON data to validate.
            key (str): The key in the JSON data that points to the settings to validate.

        Returns:
            bool: True if the settings are valid, False otherwise.

        Examples:
            >>> json_data = {"plugin": [{"type": "PluginSample", "use": True}]}
            >>> validate_setting_json(json_data, "plugin")
            True

            >>> invalid_json_data = {"plugin": [{"type": "PluginSample", "use": "not_a_boolean"}]}
            >>> validate_setting_json(invalid_json_data, "plugin")
            False
        """
        return loader.validate_setting_json(dict_json_data, key)

    def load_and_register_plugin(self, plugin_file_path: str, type: str):
        """
        Loads and registers a plugin from the specified file path.

        Args:
            plugin_file_path (str): The path to the plugin file to be loaded.
            type (str): The type of the plugin being registered.

        Examples:
            >>> success, message = load_and_register_plugin("path/to/plugin.py", "PluginSample")
            >>> print(success)  # Output: True

            >>> success, message = load_and_register_plugin("invalid/path/to/plugin.py", "PluginSample")
            >>> print(message)  # Output: Error message indicating the failure reason
        """
        load_success = True
        error_message = ""
        try:
            assert os.path.exists(plugin_file_path)
            loader.load_and_register_plugin(plugin_file_path, type)
        except Exception as e:
            logging.error(str(e))
            error_message = str(e)
            load_success = False

        return load_success, error_message

    def create_plugin_object(self, arguments: Dict[str, Any]) -> Any:
        """
        Creates a plugin object using the provided arguments.

        Args:
            arguments (Dict[str, Any]): A dictionary containing the necessary arguments to create the plugin object.

        Returns:
            Any: The created plugin object.

        Examples:
            >>> plugin_args = {"type": "PluginSample", "config": {"key": "value"}}
            >>> plugin_object = create_plugin_object(plugin_args)
            >>> print(type(plugin_object))  # Output: <class 'PluginSample'>

            >>> invalid_args = {"type": "InvalidPlugin"}
            >>> plugin_object = create_plugin_object(invalid_args)
            >>> print(plugin_object)  # Output: None or raises an error depending on implementation
        """
        return factory.create_plugin_object(arguments)

    def update_plugin_ui(self, dict_app_data: dict, plugin_unique_key: str) -> None:
        """
        Executes the 'update_ui' method of the specified plugin if it exists.

        Args:
            dict_app_data (dict): A dictionary containing application data to be used for updating the UI.
            plugin_type (str): The type of the plugin whose UI should be updated.

        Notes:
            If the plugin does not have an 'update_ui' method, this function will silently skip the update.

        Examples:
            >>> app_data = {"user": "Alice", "theme": "dark"}
            >>> pluginer.update_plugin_ui(app_data, "PluginSample")

            >>> app_data = {"action": "resize", "dimensions": (1920, 1080)}
            >>> pluginer.update_plugin_ui(app_data, "PluginVariableAssigner")
        """
        obj_plugin = self.dict_obj_plugin[plugin_unique_key]

        if self.dict_plugin_load_success[plugin_unique_key]:
            if hasattr(obj_plugin, "update_ui"):
                obj_plugin.update_ui(dict_app_data, plugin_unique_key)
