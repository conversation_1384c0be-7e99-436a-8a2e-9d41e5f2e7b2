```before importing SentenceTransformer things
```done importing SentenceTransformer
Done importing Translator
Done importing PickleDatabaseSplit
Done importing end_to_end
Done importing get_description
Done importing post_process_keywords
Done importing convert_to_embedding
Done importing extract_device_app_synonyms_v2, extract_keyword_using_yake, normalize_list
Done importing post_process_app_description, similarity_search, split_text
Done importing translate
Done importing sentence_transformers
Done importing spacy
Loading pickle files
22 pickles loaded in:176.00 ms
Loading pickle files
1 pickles loaded in:52.00 ms
Loading pickle files
5 pickles loaded in:0.00 ms
Loading pickle files
1 pickles loaded in:0.00 ms
Done initializing objects
Loading pickle files
22 pickles loaded in:209.05 ms
Loading pickle files
1 pickles loaded in:49.36 ms
Loading pickle files
5 pickles loaded in:0.00 ms
Benchmarking function: timed
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  2.96 ms
'dump_variable_to_pickle'  2.00 ms
'search_user_app'  3944.63 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  2.97 ms
'dump_variable_to_pickle'  1.99 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  3.01 ms
'dump_variable_to_pickle'  2.01 ms
'search_user_app'  3862.39 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  3.01 ms
'dump_variable_to_pickle'  2.02 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  1.99 ms
'search_user_app'  3950.77 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'search_user_app'  3885.82 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  3.98 ms
'dump_variable_to_pickle'  3.01 ms
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  3.02 ms
'dump_variable_to_pickle'  2.01 ms
'search_user_app'  3813.66 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  2.96 ms
'dump_variable_to_pickle'  2.99 ms
'dump_variable_to_pickle'  1.99 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'search_user_app'  3850.42 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  3.02 ms
'dump_variable_to_pickle'  3.01 ms
'dump_variable_to_pickle'  3.01 ms
'dump_variable_to_pickle'  3.01 ms
'dump_variable_to_pickle'  2.00 ms
'search_user_app'  3881.40 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  2.99 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'search_user_app'  3803.24 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  2.98 ms
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  1.99 ms
'dump_variable_to_pickle'  1.99 ms
'search_user_app'  3835.35 ms
------ Client data ---------
search text: 'chat'
client apps: ['com.android.camera2', 'com.android.chrome', 'com.android.contacts', 'com.android.dialer', 'com.android.settings', 'com.android.vending', 'com.google.android.apps.docs', 'com.google.android.apps.maps', 'com.google.android.apps.messaging', 'com.google.android.apps.photos', 'com.google.android.apps.tachyon', 'com.google.android.apps.youtube.music', 'com.google.android.calendar', 'com.google.android.deskclock', 'com.google.android.gm', 'com.google.android.videos', 'com.google.android.youtube', 'host.exp.exponent', 'com.google.android.apps.wallpaper', 'com.google.android.documentsui', 'com.google.android.googlequicksearchbox', 'org.chromium.webview_shell', 'com.aigrind.warspear', 'com.example.a06constraintlayout', 'com.example.android.courtcounter', 'com.example.example', 'com.example.flutter_scrollable_app_icons_example', 'com.example.flutter_scrollable_icons', 'com.example.myapplication', 'com.example.progressbarexample', 'com.example.search_app_by_description', 'com.example.test_scrollable_richtext_widget', 'com.sankuai.sailor.afooddelivery', 'com.sharmadhiraj.installed_apps_example', 'com.whatsapp', 'io.metamask', 'math.puzzle.games.crossmath.number.puzzles.free']
----------------------------
'dump_variable_to_pickle'  3.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'dump_variable_to_pickle'  2.00 ms
'search_user_app'  4014.00 ms
os: Windows
cpu: Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz, None, 6.0 cores
cpu arch: X86_64
host_name: DESKTOP-HK9AKSA
num_times_per_function: 500
time_used: The average time used (in ms)
test_datetime: 2024-07-07 21:06:30

+-------+---------------+-----------------------------+
|  func | time_used(ms) |             note            |
+-------+---------------+-----------------------------+
| timed |   3884.3707   |      num client app: 37     |
|       |               |    num db_apps keys: 2132   |
|       |               | num db_synonyms keys: 12846 |
+-------+---------------+-----------------------------+
Wrote profile results to benchmark_api_functions.py.lprof
Timer unit: 0.001 s

Total time: 38.7945 s
File: D:\code\my_projects\search_phone_app_server\server\api\end_to_end\api_functions.py
Function: search_user_app at line 192

Line #      Hits         Time  Per Hit   % Time  Line Contents
==============================================================
   192                                           @timeit
   193                                           @profile
   194                                           def search_user_app(
   195                                               client_search_data: Dict,
   196                                               debug_mode: bool,
   197                                               db_apps: PickleDatabaseSplit = db_apps,
   198                                               db_synonyms: PickleDatabaseSplit = db_synonyms,
   199                                               db_synonyms_need_powerthesaurus: PickleDatabaseSplit = db_synonyms_need_powerthesaurus,
   200                                           ):
   201                                               """
   202                                               To be ran in enpoint 'sua'
   203                                           
   204                                               Search user apps from server apps.
   205                                           
   206                                               Only update db_synonyms_need_powerthesaurus.
   207                                               """
   208        10          0.1      0.0      0.0      print(f"------ Client data ---------")
   209        10          0.0      0.0      0.0      list_device_apps = client_search_data[ClientColEnum.APPIDS]
   210        10          0.0      0.0      0.0      client_search = client_search_data[ClientColEnum.QUERY]
   211        10          0.0      0.0      0.0      print(f"search text: '{client_search}'")
   212        10          0.2      0.0      0.0      print(f"client apps: {list_device_apps}")
   213        10          0.0      0.0      0.0      print(f"----------------------------")
   214                                           
   215                                               # Return if not allowed search.
   216        10          0.0      0.0      0.0      if not client_search_data[ClientColEnum.ALLOWSEARCH]:
   217                                                   return {ClientColEnum.ALLOWSEARCH: False}
   218                                           
   219                                               # Check if client search is english, else translate to eng
   220        10       1063.2    106.3      2.7      success, is_eng = check_is_eng(translator, client_search)
   221        10          0.0      0.0      0.0      assert success == True  # TODO log error
   222        10          0.0      0.0      0.0      if not is_eng:
   223                                                   success, client_search = translate_to_eng(translator, client_search)
   224                                           
   225        10          0.0      0.0      0.0      assert success == True  # TODO log error
   226                                               # --------------------------------------------------------
   227                                           
   <USER>        <GROUP>          0.0      0.0      0.0      """
   229                                               ====================================================================================
   230                                               Semantic search (similarity search).
   231                                               
   232                                               Lemmatization on client search text.
   233                                               Similarity search.
   234                                               Word search.
   235                                               Word search on synonyms.
   236                                               """
   237                                               # Lemmatization on client search text.
   238        10         37.4      3.7      0.1      query_text = lemmatization(nlp_model=lemmatization_model, text=client_search)
   239        10          0.0      0.0      0.0      query = [query_text]
   240                                           
   241        20        148.6      7.4      0.4      query_embedding = convert_to_embedding(
   242        10          0.0      0.0      0.0          embedder,
   243        10          0.0      0.0      0.0          query,
   244        10          0.0      0.0      0.0          device="cpu",
   245        10          0.0      0.0      0.0          batch_size=128,
   246                                               )
   247                                           
   248        10          0.0      0.0      0.0      list_device_embedding_tensor = []
   249        10          0.0      0.0      0.0      list_device_description = []
   250        10          0.0      0.0      0.0      dict_device_id = {}
   251       380          0.2      0.0      0.0      for device_app_id in list_device_apps:
   252       370          5.1      0.0      0.0          if db_apps.is_key_in(device_app_id):
   253       400          0.2      0.0      0.0              list_device_embedding_tensor.append(
   254       400          3.9      0.0      0.0                  db_apps.query_key_col(
   255       200          0.1      0.0      0.0                      key=device_app_id,
   256       200          0.1      0.0      0.0                      col=DBAppsColEnum.EMBD,
   257                                                           )
   258                                                       )
   259       400          0.2      0.0      0.0              list_device_description.append(
   260       400          3.8      0.0      0.0                  db_apps.query_key_col(
   261       200          0.1      0.0      0.0                      key=device_app_id,
   262       200          0.1      0.0      0.0                      col=DBAppsColEnum.DESC,
   263                                                           )
   264                                                       )
   265                                           
   266       200          0.1      0.0      0.0              dict_device_id[device_app_id] = ""
   267                                           
   268        10          0.0      0.0      0.0      if not list_device_description:
   269                                                   return {ResponseColEnum.Error: "Not even one user app is found in database."}
   270                                           
   271                                               # Similarity search.
   272        20          3.2      0.2      0.0      hits = similarity_search(
   273        10          0.0      0.0      0.0          query_embeddings=query_embedding,
   274        10          0.0      0.0      0.0          list_text_embedding=list_device_embedding_tensor,
   275        10          0.0      0.0      0.0          top_k=30,
   276                                               )
   277                                           
   278                                               # Word search.
   279        10          1.2      0.1      0.0      list_query_words = split_text(query_text)
   280        20         20.5      1.0      0.1      list_num_matches = search_list_text(
   281        10          0.0      0.0      0.0          list_device_description,
   282        10          0.0      0.0      0.0          list_query_words,
   283                                               )
   284        10          0.2      0.0      0.0      list_normalized_word_score = normalize_list(list_num_matches)
   285        10          0.0      0.0      0.0      list_normalized_word_score_reorder = []
   286        10          0.0      0.0      0.0      hits = hits[0]  # Get the hits for the first query (Use this for app is correct)
   287       210          0.1      0.0      0.0      for hit in hits:  # Reorder by similarity search result
   288       400          0.2      0.0      0.0          list_normalized_word_score_reorder.append(
   289       200          0.1      0.0      0.0              list_normalized_word_score[hit["corpus_id"]]
   290                                                   )
   291                                           
   292                                               # Word search on synonyms.
   293        20      19260.9    963.0     49.6      list_device_synonyms = extract_device_app_synonyms_v2(
   294        10          0.0      0.0      0.0          list_device_apps=list_device_apps,
   295        10          0.0      0.0      0.0          db_apps=db_apps,
   296        10          0.0      0.0      0.0          db_synonyms=db_synonyms,
   297        10          0.0      0.0      0.0          lemmatization_model=lemmatization_model,
   298                                               )
   299        20         15.7      0.8      0.0      list_num_matches_syn = search_list_text(
   300        10          0.0      0.0      0.0          list_device_synonyms,
   301        10          0.0      0.0      0.0          list_query_words,
   302                                               )
   303        10          0.1      0.0      0.0      list_normalized_word_score_syn = normalize_list(list_num_matches_syn)
   304        10          0.0      0.0      0.0      list_normalized_word_score_reorder_syn = []
   305       210          0.1      0.0      0.0      for hit in hits:  # Reorder by similarity search result
   306       400          0.1      0.0      0.0          list_normalized_word_score_reorder_syn.append(
   307       200          0.1      0.0      0.0              list_normalized_word_score_syn[hit["corpus_id"]]
   308                                                   )
   309                                           
   310        10          0.0      0.0      0.0      """
   311                                               Mark keywords of device id not that db_synonyms
   312                                               
   313                                               For each device
   314                                                   For each keyword in keyword
   315                                                       Lemmatized the keyword
   316                                                       Query the keyword
   317                                                       If keyword not exist, add to db_synonyms_need_powertheasaurus
   318                                                       
   319                                               (Dump db_synonyms_need_powertheasaurus to file after send response to client)
   320                                               """
   321                                               # For each device
   322       380          0.2      0.0      0.0      for device_app_id in list_device_apps:
   323       370          4.9      0.0      0.0          if db_apps.is_key_in(device_app_id):
   324                                                       # Query app id keywords.
   325       400          3.0      0.0      0.0              list_device_keywords = db_apps.query_key_col(
   326       200          0.0      0.0      0.0                  key=device_app_id,
   327       200          0.2      0.0      0.0                  col=DBAppsColEnum.KEYW,
   328                                                       )
   329                                           
   330                                                       # For each keyword.
   331      8280          3.5      0.0      0.0              for keyword in list_device_keywords:
   332                                                           # Lemmatize keyword.
   333     16160      18000.8      1.1     46.4                  keyw_lemmatized = lemmatization(
   334      8080          2.3      0.0      0.0                      nlp_model=lemmatization_model,
   335      8080          1.8      0.0      0.0                      text=keyword,
   336                                                           )
   337      8080          3.2      0.0      0.0                  dict_synonyms = {}
   338      8080         23.1      0.0      0.1                  if db_synonyms.is_key_in(keyw_lemmatized):
   339                                           
   340                                                               # Query the keyword
   341     10120         16.5      0.0      0.0                      dict_synonyms = db_synonyms.query_key(
   342      5060          1.4      0.0      0.0                          key=keyw_lemmatized,
   343      5060          1.3      0.0      0.0                          default=[],
   344                                                               )
   345                                           
   346                                                           # If keyword not exist, add to db_synonyms_need_powertheasaurus
   347      8080          3.0      0.0      0.0                  if not dict_synonyms:
   348      6040         34.4      0.0      0.1                      db_synonyms_need_powerthesaurus.update_data(
   349      3020          1.2      0.0      0.0                          datas={keyw_lemmatized: 1}
   350                                                               )
   351                                                           # If keyword not exist in pt, add to db_synonyms_need_powertheasaurus
   352      5060          5.9      0.0      0.0                  elif not "pt" in dict_synonyms:
   353     10120         55.5      0.0      0.1                      db_synonyms_need_powerthesaurus.update_data(
   354      5060          1.8      0.0      0.0                          datas={keyw_lemmatized: 1}
   355                                                               )
   356                                           
   357                                               # Dump (remove later)
   358        10          0.0      0.0      0.0      if db_synonyms_need_powerthesaurus.dirty_inds:
   359        10         62.5      6.2      0.2          db_synonyms_need_powerthesaurus.dump_dirty_parts_to_pickles_thread()
   360        10          0.0      0.0      0.0      """
   361                                               ====================================================================================
   362                                               Scoring and ranking.
   363                                               """
   364        10          0.0      0.0      0.0      list_id_from_dict = list(dict_device_id)
   365        10          0.0      0.0      0.0      list_appid_score_sim_only = []
   366        10          0.0      0.0      0.0      list_appid_score_word_search = []
   367        10          0.0      0.0      0.0      list_appid_score_syn_search = []
   368        10          0.0      0.0      0.0      list_appScore = []
   369        10          0.0      0.0      0.0      list_id_hits = []
   370                                           
   371                                               # Sementic search score only.
   372       210          0.1      0.0      0.0      for hit in hits:
   373       400          0.2      0.0      0.0          list_appid_score_sim_only.append(
   374       200          0.2      0.0      0.0              [list_id_from_dict[hit["corpus_id"]], hit["score"]]
   375                                                   )
   376       200          0.1      0.0      0.0          list_id_hits.append(list_id_from_dict[hit["corpus_id"]])
   377       200          0.1      0.0      0.0          list_appScore.append(hit["score"])
   378                                           
   379        10          0.1      0.0      0.0      list_normalized_similarity_score = normalize_list(list_appScore)
   380                                           
   381                                               # Word search score addon.
   382        20          0.2      0.0      0.0      list_final_app_score = [
   383                                                   x + y
   384        20          0.0      0.0      0.0          for x, y in zip(
   385        10          0.0      0.0      0.0              list_normalized_word_score_reorder,
   386        10          0.0      0.0      0.0              list_normalized_similarity_score,
   387                                                   )
   388                                               ]
   389                                               # sort by score.
   390        20          0.2      0.0      0.0      list_appid_score_word_search = [
   391        10          0.1      0.0      0.0          [id, score] for score, id in sorted(zip(list_final_app_score, list_id_hits))
   392                                               ]
   393        10          0.0      0.0      0.0      list_appid_score_word_search.reverse()
   394                                           
   395                                               # Syn search score addon.
   396        20          0.2      0.0      0.0      list_final_app_score = [
   397                                                   x + y + z
   398        20          0.0      0.0      0.0          for x, y, z in zip(
   399        10          0.0      0.0      0.0              list_normalized_word_score_reorder_syn,
   400        10          0.0      0.0      0.0              list_normalized_word_score_reorder,
   401        10          0.0      0.0      0.0              list_normalized_similarity_score,
   402                                                   )
   403                                               ]
   404                                               # sort by score.
   405        20          0.2      0.0      0.0      list_appid_score_syn_search = [
   406        10          0.0      0.0      0.0          [id, score] for score, id in sorted(zip(list_final_app_score, list_id_hits))
   407                                               ]
   408        10          0.0      0.0      0.0      list_appid_score_syn_search.reverse()
   409                                           
   410        20          0.1      0.0      0.0      list_appid_syn_search_sorted = [
   411        10          0.0      0.0      0.0          id for _, id in sorted(zip(list_final_app_score, list_id_hits))
   412                                               ]
   413        10          0.0      0.0      0.0      list_appid_syn_search_sorted.reverse()
   414                                           
   415        20          0.1      0.0      0.0      list_score_syn_search_sorted = [
   416        10          0.0      0.0      0.0          score for score, _ in sorted(zip(list_final_app_score, list_id_hits))
   417                                               ]
   418        10          0.0      0.0      0.0      list_score_syn_search_sorted.reverse()
   419                                           
   420        10          0.0      0.0      0.0      if debug_mode:
   421                                                   result = {
   422                                                       ResponseColEnum.DebugSim: list_appid_score_sim_only,
   423                                                       ResponseColEnum.DebugSimWord: list_appid_score_word_search,
   424                                                       ResponseColEnum.DebugSimWordSyn: list_appid_score_syn_search,
   425                                                   }
   426                                               else:
   427        10          0.0      0.0      0.0          result = {
   428        20          0.0      0.0      0.0              ResponseColEnum.Result: {
   429        10          0.0      0.0      0.0                  "packageNames": list_appid_syn_search_sorted,
   430        10          0.0      0.0      0.0                  "scores": list_score_syn_search_sorted,
   431                                                       }
   432                                                   }
   433                                           
   434        10          0.0      0.0      0.0      return result

Total time: 19.1232 s
File: D:\code\my_projects\search_phone_app_server\server\api\utils\generate_vectors\utils.py
Function: extract_device_app_synonyms_v2 at line 905

Line #      Hits         Time  Per Hit   % Time  Line Contents
==============================================================
   905                                           @profile
   906                                           def extract_device_app_synonyms_v2(
   907                                               list_device_apps: List[str],
   908                                               db_apps: PickleDatabaseSplit,
   909                                               db_synonyms: PickleDatabaseSplit,
   910                                               lemmatization_model,
   911                                           ) -> List[str]:
   912                                               """
   913                                               Extract each device app id's keywords' synonyms into a string.
   914                                           
   915                                               Return a list of device's id 's string concated with synonyms.
   916                                           
   917                                               Given
   918                                               list_device_apps = [
   919                                                   {'id':'com.whatsapp'},
   920                                                   {'id':'com.rovio.BadPiggies'},
   921                                               ]
   922                                           
   923                                               TODO modify
   924                                               dict_keywords = {
   925                                                   'com.whatsapp':['family','calling','chats'],
   926                                                   'com.rovio.BadPiggies':['rovio','game'],
   927                                                   'de.stocard.stocard':['wallet','cards','passbook'],
   928                                           
   929                                               }
   930                                           
   931                                               dict_synonyms = {
   932                                                   'family':['household', 'lineage'],
   933                                                   'calling':['vocation', 'job'],
   934                                                   'chats':['talks', 'converses'],
   935                                                   'game':['play', 'match'],
   936                                           
   937                                                   'wallet':['pocketbook', 'purse'],
   938                                                   'cards':['card game', 'jokers'],
   939                                                   'passbook':['bankbook', 'register'],
   940                                               }
   941                                           
   942                                               Return
   943                                               ['household, lineage, vocation, job, talks, converses, ', 'play, match, ']
   944                                               """
   945        10          0.0      0.0      0.0      list_device_synonyms = []
   946                                               # For each device app id.
   947       380          0.2      0.0      0.0      for device_app_id in list_device_apps:
   948       370          5.2      0.0      0.0          if db_apps.is_key_in(device_app_id):
   949                                                       # Query app id keywords.
   950       400          3.1      0.0      0.0              list_device_keywords = db_apps.query_key_col(
   951       200          0.1      0.0      0.0                  key=device_app_id,
   952       200          0.2      0.0      0.0                  col=DBAppsColEnum.KEYW,
   953                                                       )
   954                                           
   955       200          0.1      0.0      0.0              all_synonyms_string = ""
   956                                                       # For each keyword.
   957      8280          3.4      0.0      0.0              for device_keyword in list_device_keywords:
   958                                                           # Lemmatize keyword.
   959     16160      18796.1      1.2     98.3                  keyw_lemmatized = lemmatization(
   960      8080          2.0      0.0      0.0                      nlp_model=lemmatization_model,
   961      8080          1.7      0.0      0.0                      text=device_keyword,
   962                                                           )
   963      8080         25.1      0.0      0.1                  if db_synonyms.is_key_in(keyw_lemmatized):
   964                                                               # Query synonyms by keyword and col.
   965     10120         17.7      0.0      0.1                      list_synonyms = db_synonyms.query_key(
   966      5060          1.2      0.0      0.0                          key=keyw_lemmatized,
   967      5060          1.3      0.0      0.0                          default=[],
   968                                                               )
   969                                           
   970                                                               # Concate synonyms to a string.
   971    231500         60.2      0.0      0.3                      for synonyms in list_synonyms:
   972    226440        205.5      0.0      1.1                          all_synonyms_string = all_synonyms_string + synonyms + ", "
   973                                           
   974       200          0.1      0.0      0.0              list_device_synonyms.append(all_synonyms_string)
   975                                           
   976        10          0.0      0.0      0.0      return list_device_synonyms

