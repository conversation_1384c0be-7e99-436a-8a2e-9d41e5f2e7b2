package com.kewtoms.whatappsdo.utils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * General functions for HTML manipulation.
 */
public class HtmlUtils {

 private static final Map<Integer, String> INVALID_CHARREFS =
  new HashMap<>();
 private static final Set<Integer> INVALID_CODEPOINTS =
  new HashSet<>();
 private static final Map<String, String> HTML5_ENTITIES =
  new HashMap<>(); // You need to populate this with HTML5 entities
 private static final Pattern CHARREF =
  Pattern.compile("&(#[0-9]+;?|#[xX][0-9a-fA-F]+;?|[^\\t\\n\\f <&#;]{1,32};?)");

 static {
  // Populate INVALID_CHARREFS
  INVALID_CHARREFS.put(
   0x00,
   "\\ufffd"
  );  // REPLACEMENT CHARACTER
  INVALID_CHARREFS.put(
   0x0d,
   "\\r"
  );      // CARRIAGE RETURN
  INVALID_CHARREFS.put(
   0x80,
   "\\u20ac"
  );  // EURO SIGN
  INVALID_CHARREFS.put(
   0x81,
   "\\x81"
  );    // <control>
  INVALID_CHARREFS.put(
   0x82,
   "\\u201a"
  );  // SINGLE LOW-9 QUOTATION MARK
  INVALID_CHARREFS.put(
   0x83,
   "\\u0192"
  );  // LATIN SMALL LETTER F WITH HOOK
  INVALID_CHARREFS.put(
   0x84,
   "\\u201e"
  );  // DOUBLE LOW-9 QUOTATION MARK
  INVALID_CHARREFS.put(
   0x85,
   "\\u2026"
  );  // HORIZONTAL ELLIPSIS
  INVALID_CHARREFS.put(
   0x86,
   "\\u2020"
  );  // DAGGER
  INVALID_CHARREFS.put(
   0x87,
   "\\u2021"
  );  // DOUBLE DAGGER
  INVALID_CHARREFS.put(
   0x88,
   "\\u02c6"
  );  // MODIFIER LETTER CIRCUMFLEX ACCENT
  INVALID_CHARREFS.put(
   0x89,
   "\\u2030"
  );  // PER MILLE SIGN
  INVALID_CHARREFS.put(
   0x8a,
   "\\u0160"
  );  // LATIN CAPITAL LETTER S WITH CARON
  INVALID_CHARREFS.put(
   0x8b,
   "\\u2039"
  );  // SINGLE LEFT-POINTING ANGLE QUOTATION MARK
  INVALID_CHARREFS.put(
   0x8c,
   "\\u0152"
  );  // LATIN CAPITAL LIGATURE OE
  INVALID_CHARREFS.put(
   0x8d,
   "\\x8d"
  );    // <control>
  INVALID_CHARREFS.put(
   0x8e,
   "\\u017d"
  );  // LATIN CAPITAL LETTER Z WITH CARON
  INVALID_CHARREFS.put(
   0x8f,
   "\\x8f"
  );    // <control>
  INVALID_CHARREFS.put(
   0x90,
   "\\x90"
  );    // <control>
  INVALID_CHARREFS.put(
   0x91,
   "\\u2018"
  );  // LEFT SINGLE QUOTATION MARK
  INVALID_CHARREFS.put(
   0x92,
   "\\u2019"
  );  // RIGHT SINGLE QUOTATION MARK
  INVALID_CHARREFS.put(
   0x93,
   "\\u201c"
  );  // LEFT DOUBLE QUOTATION MARK
  INVALID_CHARREFS.put(
   0x94,
   "\\u201d"
  );  // RIGHT DOUBLE QUOTATION MARK
  INVALID_CHARREFS.put(
   0x95,
   "\\u2022"
  );  // BULLET
  INVALID_CHARREFS.put(
   0x96,
   "\\u2013"
  );  // EN DASH
  INVALID_CHARREFS.put(
   0x97,
   "\\u2014"
  );  // EM DASH
  INVALID_CHARREFS.put(
   0x98,
   "\\u02dc"
  );  // SMALL TILDE
  INVALID_CHARREFS.put(
   0x99,
   "\\u2122"
  );  // TRADE MARK SIGN
  INVALID_CHARREFS.put(
   0x9a,
   "\\u0161"
  );  // LATIN SMALL LETTER S WITH CARON
  INVALID_CHARREFS.put(
   0x9b,
   "\\u203a"
  );  // SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
  INVALID_CHARREFS.put(
   0x9c,
   "\\u0153"
  );  // LATIN SMALL LIGATURE OE
  INVALID_CHARREFS.put(
   0x9d,
   "\\x9d"
  );    // <control>
  INVALID_CHARREFS.put(
   0x9e,
   "\\u017e"
  );  // LATIN SMALL LETTER Z WITH CARON
  INVALID_CHARREFS.put(
   0x9f,
   "\\u0178"
  );  // LATIN CAPITAL LETTER Y WITH DIAERESIS

  // Populate INVALID_CODEPOINTS
  // 0x0001 to 0x0008
  INVALID_CODEPOINTS.add(0x1);
  INVALID_CODEPOINTS.add(0x2);
  INVALID_CODEPOINTS.add(0x3);
  INVALID_CODEPOINTS.add(0x4);
  INVALID_CODEPOINTS.add(0x5);
  INVALID_CODEPOINTS.add(0x6);
  INVALID_CODEPOINTS.add(0x7);
  INVALID_CODEPOINTS.add(0x8);

  // 0x000E to 0x001F
  INVALID_CODEPOINTS.add(0xe);
  INVALID_CODEPOINTS.add(0xf);
  INVALID_CODEPOINTS.add(0x10);
  INVALID_CODEPOINTS.add(0x11);
  INVALID_CODEPOINTS.add(0x12);
  INVALID_CODEPOINTS.add(0x13);
  INVALID_CODEPOINTS.add(0x14);
  INVALID_CODEPOINTS.add(0x15);
  INVALID_CODEPOINTS.add(0x16);
  INVALID_CODEPOINTS.add(0x17);
  INVALID_CODEPOINTS.add(0x18);
  INVALID_CODEPOINTS.add(0x19);
  INVALID_CODEPOINTS.add(0x1a);
  INVALID_CODEPOINTS.add(0x1b);
  INVALID_CODEPOINTS.add(0x1c);
  INVALID_CODEPOINTS.add(0x1d);
  INVALID_CODEPOINTS.add(0x1e);
  INVALID_CODEPOINTS.add(0x1f);

  // 0x007F to 0x009F
  INVALID_CODEPOINTS.add(0x7f);
  INVALID_CODEPOINTS.add(0x80);
  INVALID_CODEPOINTS.add(0x81);
  INVALID_CODEPOINTS.add(0x82);
  INVALID_CODEPOINTS.add(0x83);
  INVALID_CODEPOINTS.add(0x84);
  INVALID_CODEPOINTS.add(0x85);
  INVALID_CODEPOINTS.add(0x86);
  INVALID_CODEPOINTS.add(0x87);
  INVALID_CODEPOINTS.add(0x88);
  INVALID_CODEPOINTS.add(0x89);
  INVALID_CODEPOINTS.add(0x8a);
  INVALID_CODEPOINTS.add(0x8b);
  INVALID_CODEPOINTS.add(0x8c);
  INVALID_CODEPOINTS.add(0x8d);
  INVALID_CODEPOINTS.add(0x8e);
  INVALID_CODEPOINTS.add(0x8f);
  INVALID_CODEPOINTS.add(0x90);
  INVALID_CODEPOINTS.add(0x91);
  INVALID_CODEPOINTS.add(0x92);
  INVALID_CODEPOINTS.add(0x93);
  INVALID_CODEPOINTS.add(0x94);
  INVALID_CODEPOINTS.add(0x95);
  INVALID_CODEPOINTS.add(0x96);
  INVALID_CODEPOINTS.add(0x97);
  INVALID_CODEPOINTS.add(0x98);
  INVALID_CODEPOINTS.add(0x99);
  INVALID_CODEPOINTS.add(0x9a);
  INVALID_CODEPOINTS.add(0x9b);
  INVALID_CODEPOINTS.add(0x9c);
  INVALID_CODEPOINTS.add(0x9d);
  INVALID_CODEPOINTS.add(0x9e);
  INVALID_CODEPOINTS.add(0x9f);

  // 0xFDD0 to 0xFDEF
  INVALID_CODEPOINTS.add(0xfdd0);
  INVALID_CODEPOINTS.add(0xfdd1);
  INVALID_CODEPOINTS.add(0xfdd2);
  INVALID_CODEPOINTS.add(0xfdd3);
  INVALID_CODEPOINTS.add(0xfdd4);
  INVALID_CODEPOINTS.add(0xfdd5);
  INVALID_CODEPOINTS.add(0xfdd6);
  INVALID_CODEPOINTS.add(0xfdd7);
  INVALID_CODEPOINTS.add(0xfdd8);
  INVALID_CODEPOINTS.add(0xfdd9);
  INVALID_CODEPOINTS.add(0xfdda);
  INVALID_CODEPOINTS.add(0xfddb);
  INVALID_CODEPOINTS.add(0xfddc);
  INVALID_CODEPOINTS.add(0xfddd);
  INVALID_CODEPOINTS.add(0xfdde);
  INVALID_CODEPOINTS.add(0xfddf);
  INVALID_CODEPOINTS.add(0xfde0);
  INVALID_CODEPOINTS.add(0xfde1);
  INVALID_CODEPOINTS.add(0xfde2);
  INVALID_CODEPOINTS.add(0xfde3);
  INVALID_CODEPOINTS.add(0xfde4);
  INVALID_CODEPOINTS.add(0xfde5);
  INVALID_CODEPOINTS.add(0xfde6);
  INVALID_CODEPOINTS.add(0xfde7);
  INVALID_CODEPOINTS.add(0xfde8);
  INVALID_CODEPOINTS.add(0xfde9);
  INVALID_CODEPOINTS.add(0xfdea);
  INVALID_CODEPOINTS.add(0xfdeb);
  INVALID_CODEPOINTS.add(0xfdec);
  INVALID_CODEPOINTS.add(0xfded);
  INVALID_CODEPOINTS.add(0xfdee);
  INVALID_CODEPOINTS.add(0xfdef);

  // others
  INVALID_CODEPOINTS.add(0xb);
  INVALID_CODEPOINTS.add(0xfffe);
  INVALID_CODEPOINTS.add(0xffff);
  INVALID_CODEPOINTS.add(0x1fffe);
  INVALID_CODEPOINTS.add(0x1ffff);
  INVALID_CODEPOINTS.add(0x2fffe);
  INVALID_CODEPOINTS.add(0x2ffff);
  INVALID_CODEPOINTS.add(0x3fffe);
  INVALID_CODEPOINTS.add(0x3ffff);
  INVALID_CODEPOINTS.add(0x4fffe);
  INVALID_CODEPOINTS.add(0x4ffff);
  INVALID_CODEPOINTS.add(0x5fffe);
  INVALID_CODEPOINTS.add(0x5ffff);
  INVALID_CODEPOINTS.add(0x6fffe);
  INVALID_CODEPOINTS.add(0x6ffff);
  INVALID_CODEPOINTS.add(0x7fffe);
  INVALID_CODEPOINTS.add(0x7ffff);
  INVALID_CODEPOINTS.add(0x8fffe);
  INVALID_CODEPOINTS.add(0x8ffff);
  INVALID_CODEPOINTS.add(0x9fffe);
  INVALID_CODEPOINTS.add(0x9ffff);
  INVALID_CODEPOINTS.add(0xafffe);
  INVALID_CODEPOINTS.add(0xaffff);
  INVALID_CODEPOINTS.add(0xbfffe);
  INVALID_CODEPOINTS.add(0xbffff);
  INVALID_CODEPOINTS.add(0xcfffe);
  INVALID_CODEPOINTS.add(0xcffff);
  INVALID_CODEPOINTS.add(0xdfffe);
  INVALID_CODEPOINTS.add(0xdffff);
  INVALID_CODEPOINTS.add(0xefffe);
  INVALID_CODEPOINTS.add(0xeffff);
  INVALID_CODEPOINTS.add(0xffffe);
  INVALID_CODEPOINTS.add(0xfffff);
  INVALID_CODEPOINTS.add(0x10fffe);
  INVALID_CODEPOINTS.add(0x10ffff);


  // Populate HTML5_ENTITIES (you need to add all HTML5 entities here)
  HTML5_ENTITIES.put(
   "Aacute",
   "\\xc1"
  );
  HTML5_ENTITIES.put(
   "aacute",
   "\\xe1"
  );
  HTML5_ENTITIES.put(
   "Aacute;",
   "\\xc1"
  );
  HTML5_ENTITIES.put(
   "aacute;",
   "\\xe1"
  );
  HTML5_ENTITIES.put(
   "Abreve;",
   "\\u0102"
  );
  HTML5_ENTITIES.put(
   "abreve;",
   "\\u0103"
  );
  HTML5_ENTITIES.put(
   "ac;",
   "\\u223e"
  );
  HTML5_ENTITIES.put(
   "acd;",
   "\\u223f"
  );
  HTML5_ENTITIES.put(
   "acE;",
   "\\u223e\\u0333"
  );
  HTML5_ENTITIES.put(
   "Acirc",
   "\\xc2"
  );
  HTML5_ENTITIES.put(
   "acirc",
   "\\xe2"
  );
  HTML5_ENTITIES.put(
   "Acirc;",
   "\\xc2"
  );
  HTML5_ENTITIES.put(
   "acirc;",
   "\\xe2"
  );
  HTML5_ENTITIES.put(
   "acute",
   "\\xb4"
  );
  HTML5_ENTITIES.put(
   "acute;",
   "\\xb4"
  );
  HTML5_ENTITIES.put(
   "Acy;",
   "\\u0410"
  );
  HTML5_ENTITIES.put(
   "acy;",
   "\\u0430"
  );
  HTML5_ENTITIES.put(
   "AElig",
   "\\xc6"
  );
  HTML5_ENTITIES.put(
   "aelig",
   "\\xe6"
  );
  HTML5_ENTITIES.put(
   "AElig;",
   "\\xc6"
  );
  HTML5_ENTITIES.put(
   "aelig;",
   "\\xe6"
  );
  HTML5_ENTITIES.put(
   "af;",
   "\\u2061"
  );
  HTML5_ENTITIES.put(
   "Afr;",
   "\\U0001d504"
  );
  HTML5_ENTITIES.put(
   "afr;",
   "\\U0001d51e"
  );
  HTML5_ENTITIES.put(
   "Agrave",
   "\\xc0"
  );
  HTML5_ENTITIES.put(
   "agrave",
   "\\xe0"
  );
  HTML5_ENTITIES.put(
   "Agrave;",
   "\\xc0"
  );
  HTML5_ENTITIES.put(
   "agrave;",
   "\\xe0"
  );
  HTML5_ENTITIES.put(
   "alefsym;",
   "\\u2135"
  );
  HTML5_ENTITIES.put(
   "aleph;",
   "\\u2135"
  );
  HTML5_ENTITIES.put(
   "Alpha;",
   "\\u0391"
  );
  HTML5_ENTITIES.put(
   "alpha;",
   "\\u03b1"
  );
  HTML5_ENTITIES.put(
   "Amacr;",
   "\\u0100"
  );
  HTML5_ENTITIES.put(
   "amacr;",
   "\\u0101"
  );
  HTML5_ENTITIES.put(
   "amalg;",
   "\\u2a3f"
  );
  HTML5_ENTITIES.put(
   "AMP",
   "&"
  );
  HTML5_ENTITIES.put(
   "amp",
   "&"
  );
  HTML5_ENTITIES.put(
   "AMP;",
   "&"
  );
  HTML5_ENTITIES.put(
   "amp;",
   "&"
  );
  HTML5_ENTITIES.put(
   "And;",
   "\\u2a53"
  );
  HTML5_ENTITIES.put(
   "and;",
   "\\u2227"
  );
  HTML5_ENTITIES.put(
   "andand;",
   "\\u2a55"
  );
  HTML5_ENTITIES.put(
   "andd;",
   "\\u2a5c"
  );
  HTML5_ENTITIES.put(
   "andslope;",
   "\\u2a58"
  );
  HTML5_ENTITIES.put(
   "andv;",
   "\\u2a5a"
  );
  HTML5_ENTITIES.put(
   "ang;",
   "\\u2220"
  );
  HTML5_ENTITIES.put(
   "ange;",
   "\\u29a4"
  );
  HTML5_ENTITIES.put(
   "angle;",
   "\\u2220"
  );
  HTML5_ENTITIES.put(
   "angmsd;",
   "\\u2221"
  );
  HTML5_ENTITIES.put(
   "angmsdaa;",
   "\\u29a8"
  );
  HTML5_ENTITIES.put(
   "angmsdab;",
   "\\u29a9"
  );
  HTML5_ENTITIES.put(
   "angmsdac;",
   "\\u29aa"
  );
  HTML5_ENTITIES.put(
   "angmsdad;",
   "\\u29ab"
  );
  HTML5_ENTITIES.put(
   "angmsdae;",
   "\\u29ac"
  );
  HTML5_ENTITIES.put(
   "angmsdaf;",
   "\\u29ad"
  );
  HTML5_ENTITIES.put(
   "angmsdag;",
   "\\u29ae"
  );
  HTML5_ENTITIES.put(
   "angmsdah;",
   "\\u29af"
  );
  HTML5_ENTITIES.put(
   "angrt;",
   "\\u221f"
  );
  HTML5_ENTITIES.put(
   "angrtvb;",
   "\\u22be"
  );
  HTML5_ENTITIES.put(
   "angrtvbd;",
   "\\u299d"
  );
  HTML5_ENTITIES.put(
   "angsph;",
   "\\u2222"
  );
  HTML5_ENTITIES.put(
   "angst;",
   "\\xc5"
  );
  HTML5_ENTITIES.put(
   "angzarr;",
   "\\u237c"
  );
  HTML5_ENTITIES.put(
   "Aogon;",
   "\\u0104"
  );
  HTML5_ENTITIES.put(
   "aogon;",
   "\\u0105"
  );
  HTML5_ENTITIES.put(
   "Aopf;",
   "\\U0001d538"
  );
  HTML5_ENTITIES.put(
   "aopf;",
   "\\U0001d552"
  );
  HTML5_ENTITIES.put(
   "ap;",
   "\\u2248"
  );
  HTML5_ENTITIES.put(
   "apacir;",
   "\\u2a6f"
  );
  HTML5_ENTITIES.put(
   "apE;",
   "\\u2a70"
  );
  HTML5_ENTITIES.put(
   "ape;",
   "\\u224a"
  );
  HTML5_ENTITIES.put(
   "apid;",
   "\\u224b"
  );
  HTML5_ENTITIES.put(
   "apos;",
   "'"
  );
  HTML5_ENTITIES.put(
   "ApplyFunction;",
   "\\u2061"
  );
  HTML5_ENTITIES.put(
   "approx;",
   "\\u2248"
  );
  HTML5_ENTITIES.put(
   "approxeq;",
   "\\u224a"
  );
  HTML5_ENTITIES.put(
   "Aring",
   "\\xc5"
  );
  HTML5_ENTITIES.put(
   "aring",
   "\\xe5"
  );
  HTML5_ENTITIES.put(
   "Aring;",
   "\\xc5"
  );
  HTML5_ENTITIES.put(
   "aring;",
   "\\xe5"
  );
  HTML5_ENTITIES.put(
   "Ascr;",
   "\\U0001d49c"
  );
  HTML5_ENTITIES.put(
   "ascr;",
   "\\U0001d4b6"
  );
  HTML5_ENTITIES.put(
   "Assign;",
   "\\u2254"
  );
  HTML5_ENTITIES.put(
   "ast;",
   "*"
  );
  HTML5_ENTITIES.put(
   "asymp;",
   "\\u2248"
  );
  HTML5_ENTITIES.put(
   "asympeq;",
   "\\u224d"
  );
  HTML5_ENTITIES.put(
   "Atilde",
   "\\xc3"
  );
  HTML5_ENTITIES.put(
   "atilde",
   "\\xe3"
  );
  HTML5_ENTITIES.put(
   "Atilde;",
   "\\xc3"
  );
  HTML5_ENTITIES.put(
   "atilde;",
   "\\xe3"
  );
  HTML5_ENTITIES.put(
   "Auml",
   "\\xc4"
  );
  HTML5_ENTITIES.put(
   "auml",
   "\\xe4"
  );
  HTML5_ENTITIES.put(
   "Auml;",
   "\\xc4"
  );
  HTML5_ENTITIES.put(
   "auml;",
   "\\xe4"
  );
  HTML5_ENTITIES.put(
   "awconint;",
   "\\u2233"
  );
  HTML5_ENTITIES.put(
   "awint;",
   "\\u2a11"
  );
  HTML5_ENTITIES.put(
   "backcong;",
   "\\u224c"
  );
  HTML5_ENTITIES.put(
   "backepsilon;",
   "\\u03f6"
  );
  HTML5_ENTITIES.put(
   "backprime;",
   "\\u2035"
  );
  HTML5_ENTITIES.put(
   "backsim;",
   "\\u223d"
  );
  HTML5_ENTITIES.put(
   "backsimeq;",
   "\\u22cd"
  );
  HTML5_ENTITIES.put(
   "Backslash;",
   "\\u2216"
  );
  HTML5_ENTITIES.put(
   "Barv;",
   "\\u2ae7"
  );
  HTML5_ENTITIES.put(
   "barvee;",
   "\\u22bd"
  );
  HTML5_ENTITIES.put(
   "Barwed;",
   "\\u2306"
  );
  HTML5_ENTITIES.put(
   "barwed;",
   "\\u2305"
  );
  HTML5_ENTITIES.put(
   "barwedge;",
   "\\u2305"
  );
  HTML5_ENTITIES.put(
   "bbrk;",
   "\\u23b5"
  );
  HTML5_ENTITIES.put(
   "bbrktbrk;",
   "\\u23b6"
  );
  HTML5_ENTITIES.put(
   "bcong;",
   "\\u224c"
  );
  HTML5_ENTITIES.put(
   "Bcy;",
   "\\u0411"
  );
  HTML5_ENTITIES.put(
   "bcy;",
   "\\u0431"
  );
  HTML5_ENTITIES.put(
   "bdquo;",
   "\\u201e"
  );
  HTML5_ENTITIES.put(
   "becaus;",
   "\\u2235"
  );
  HTML5_ENTITIES.put(
   "Because;",
   "\\u2235"
  );
  HTML5_ENTITIES.put(
   "because;",
   "\\u2235"
  );
  HTML5_ENTITIES.put(
   "bemptyv;",
   "\\u29b0"
  );
  HTML5_ENTITIES.put(
   "bepsi;",
   "\\u03f6"
  );
  HTML5_ENTITIES.put(
   "bernou;",
   "\\u212c"
  );
  HTML5_ENTITIES.put(
   "Bernoullis;",
   "\\u212c"
  );
  HTML5_ENTITIES.put(
   "Beta;",
   "\\u0392"
  );
  HTML5_ENTITIES.put(
   "beta;",
   "\\u03b2"
  );
  HTML5_ENTITIES.put(
   "beth;",
   "\\u2136"
  );
  HTML5_ENTITIES.put(
   "between;",
   "\\u226c"
  );
  HTML5_ENTITIES.put(
   "Bfr;",
   "\\U0001d505"
  );
  HTML5_ENTITIES.put(
   "bfr;",
   "\\U0001d51f"
  );
  HTML5_ENTITIES.put(
   "bigcap;",
   "\\u22c2"
  );
  HTML5_ENTITIES.put(
   "bigcirc;",
   "\\u25ef"
  );
  HTML5_ENTITIES.put(
   "bigcup;",
   "\\u22c3"
  );
  HTML5_ENTITIES.put(
   "bigodot;",
   "\\u2a00"
  );
  HTML5_ENTITIES.put(
   "bigoplus;",
   "\\u2a01"
  );
  HTML5_ENTITIES.put(
   "bigotimes;",
   "\\u2a02"
  );
  HTML5_ENTITIES.put(
   "bigsqcup;",
   "\\u2a06"
  );
  HTML5_ENTITIES.put(
   "bigstar;",
   "\\u2605"
  );
  HTML5_ENTITIES.put(
   "bigtriangledown;",
   "\\u25bd"
  );
  HTML5_ENTITIES.put(
   "bigtriangleup;",
   "\\u25b3"
  );
  HTML5_ENTITIES.put(
   "biguplus;",
   "\\u2a04"
  );
  HTML5_ENTITIES.put(
   "bigvee;",
   "\\u22c1"
  );
  HTML5_ENTITIES.put(
   "bigwedge;",
   "\\u22c0"
  );
  HTML5_ENTITIES.put(
   "bkarow;",
   "\\u290d"
  );
  HTML5_ENTITIES.put(
   "blacklozenge;",
   "\\u29eb"
  );
  HTML5_ENTITIES.put(
   "blacksquare;",
   "\\u25aa"
  );
  HTML5_ENTITIES.put(
   "blacktriangle;",
   "\\u25b4"
  );
  HTML5_ENTITIES.put(
   "blacktriangledown;",
   "\\u25be"
  );
  HTML5_ENTITIES.put(
   "blacktriangleleft;",
   "\\u25c2"
  );
  HTML5_ENTITIES.put(
   "blacktriangleright;",
   "\\u25b8"
  );
  HTML5_ENTITIES.put(
   "blank;",
   "\\u2423"
  );
  HTML5_ENTITIES.put(
   "blk12;",
   "\\u2592"
  );
  HTML5_ENTITIES.put(
   "blk14;",
   "\\u2591"
  );
  HTML5_ENTITIES.put(
   "blk34;",
   "\\u2593"
  );
  HTML5_ENTITIES.put(
   "block;",
   "\\u2588"
  );
  HTML5_ENTITIES.put(
   "bne;",
   "=\\u20e5"
  );
  HTML5_ENTITIES.put(
   "bnequiv;",
   "\\u2261\\u20e5"
  );
  HTML5_ENTITIES.put(
   "bNot;",
   "\\u2aed"
  );
  HTML5_ENTITIES.put(
   "bnot;",
   "\\u2310"
  );
  HTML5_ENTITIES.put(
   "Bopf;",
   "\\U0001d539"
  );
  HTML5_ENTITIES.put(
   "bopf;",
   "\\U0001d553"
  );
  HTML5_ENTITIES.put(
   "bot;",
   "\\u22a5"
  );
  HTML5_ENTITIES.put(
   "bottom;",
   "\\u22a5"
  );
  HTML5_ENTITIES.put(
   "bowtie;",
   "\\u22c8"
  );
  HTML5_ENTITIES.put(
   "boxbox;",
   "\\u29c9"
  );
  HTML5_ENTITIES.put(
   "boxDL;",
   "\\u2557"
  );
  HTML5_ENTITIES.put(
   "boxDl;",
   "\\u2556"
  );
  HTML5_ENTITIES.put(
   "boxdL;",
   "\\u2555"
  );
  HTML5_ENTITIES.put(
   "boxdl;",
   "\\u2510"
  );
  HTML5_ENTITIES.put(
   "boxDR;",
   "\\u2554"
  );
  HTML5_ENTITIES.put(
   "boxDr;",
   "\\u2553"
  );
  HTML5_ENTITIES.put(
   "boxdR;",
   "\\u2552"
  );
  HTML5_ENTITIES.put(
   "boxdr;",
   "\\u250c"
  );
  HTML5_ENTITIES.put(
   "boxH;",
   "\\u2550"
  );
  HTML5_ENTITIES.put(
   "boxh;",
   "\\u2500"
  );
  HTML5_ENTITIES.put(
   "boxHD;",
   "\\u2566"
  );
  HTML5_ENTITIES.put(
   "boxHd;",
   "\\u2564"
  );
  HTML5_ENTITIES.put(
   "boxhD;",
   "\\u2565"
  );
  HTML5_ENTITIES.put(
   "boxhd;",
   "\\u252c"
  );
  HTML5_ENTITIES.put(
   "boxHU;",
   "\\u2569"
  );
  HTML5_ENTITIES.put(
   "boxHu;",
   "\\u2567"
  );
  HTML5_ENTITIES.put(
   "boxhU;",
   "\\u2568"
  );
  HTML5_ENTITIES.put(
   "boxhu;",
   "\\u2534"
  );
  HTML5_ENTITIES.put(
   "boxminus;",
   "\\u229f"
  );
  HTML5_ENTITIES.put(
   "boxplus;",
   "\\u229e"
  );
  HTML5_ENTITIES.put(
   "boxtimes;",
   "\\u22a0"
  );
  HTML5_ENTITIES.put(
   "boxUL;",
   "\\u255d"
  );
  HTML5_ENTITIES.put(
   "boxUl;",
   "\\u255c"
  );
  HTML5_ENTITIES.put(
   "boxuL;",
   "\\u255b"
  );
  HTML5_ENTITIES.put(
   "boxul;",
   "\\u2518"
  );
  HTML5_ENTITIES.put(
   "boxUR;",
   "\\u255a"
  );
  HTML5_ENTITIES.put(
   "boxUr;",
   "\\u2559"
  );
  HTML5_ENTITIES.put(
   "boxuR;",
   "\\u2558"
  );
  HTML5_ENTITIES.put(
   "boxur;",
   "\\u2514"
  );
  HTML5_ENTITIES.put(
   "boxV;",
   "\\u2551"
  );
  HTML5_ENTITIES.put(
   "boxv;",
   "\\u2502"
  );
  HTML5_ENTITIES.put(
   "boxVH;",
   "\\u256c"
  );
  HTML5_ENTITIES.put(
   "boxVh;",
   "\\u256b"
  );
  HTML5_ENTITIES.put(
   "boxvH;",
   "\\u256a"
  );
  HTML5_ENTITIES.put(
   "boxvh;",
   "\\u253c"
  );
  HTML5_ENTITIES.put(
   "boxVL;",
   "\\u2563"
  );
  HTML5_ENTITIES.put(
   "boxVl;",
   "\\u2562"
  );
  HTML5_ENTITIES.put(
   "boxvL;",
   "\\u2561"
  );
  HTML5_ENTITIES.put(
   "boxvl;",
   "\\u2524"
  );
  HTML5_ENTITIES.put(
   "boxVR;",
   "\\u2560"
  );
  HTML5_ENTITIES.put(
   "boxVr;",
   "\\u255f"
  );
  HTML5_ENTITIES.put(
   "boxvR;",
   "\\u255e"
  );
  HTML5_ENTITIES.put(
   "boxvr;",
   "\\u251c"
  );
  HTML5_ENTITIES.put(
   "bprime;",
   "\\u2035"
  );
  HTML5_ENTITIES.put(
   "Breve;",
   "\\u02d8"
  );
  HTML5_ENTITIES.put(
   "breve;",
   "\\u02d8"
  );
  HTML5_ENTITIES.put(
   "brvbar",
   "\\xa6"
  );
  HTML5_ENTITIES.put(
   "brvbar;",
   "\\xa6"
  );
  HTML5_ENTITIES.put(
   "Bscr;",
   "\\u212c"
  );
  HTML5_ENTITIES.put(
   "bscr;",
   "\\U0001d4b7"
  );
  HTML5_ENTITIES.put(
   "bsemi;",
   "\\u204f"
  );
  HTML5_ENTITIES.put(
   "bsim;",
   "\\u223d"
  );
  HTML5_ENTITIES.put(
   "bsime;",
   "\\u22cd"
  );
  HTML5_ENTITIES.put(
   "bsol;",
   "\\\\"
  );
  HTML5_ENTITIES.put(
   "bsolb;",
   "\\u29c5"
  );
  HTML5_ENTITIES.put(
   "bsolhsub;",
   "\\u27c8"
  );
  HTML5_ENTITIES.put(
   "bull;",
   "\\u2022"
  );
  HTML5_ENTITIES.put(
   "bullet;",
   "\\u2022"
  );
  HTML5_ENTITIES.put(
   "bump;",
   "\\u224e"
  );
  HTML5_ENTITIES.put(
   "bumpE;",
   "\\u2aae"
  );
  HTML5_ENTITIES.put(
   "bumpe;",
   "\\u224f"
  );
  HTML5_ENTITIES.put(
   "Bumpeq;",
   "\\u224e"
  );
  HTML5_ENTITIES.put(
   "bumpeq;",
   "\\u224f"
  );
  HTML5_ENTITIES.put(
   "Cacute;",
   "\\u0106"
  );
  HTML5_ENTITIES.put(
   "cacute;",
   "\\u0107"
  );
  HTML5_ENTITIES.put(
   "Cap;",
   "\\u22d2"
  );
  HTML5_ENTITIES.put(
   "cap;",
   "\\u2229"
  );
  HTML5_ENTITIES.put(
   "capand;",
   "\\u2a44"
  );
  HTML5_ENTITIES.put(
   "capbrcup;",
   "\\u2a49"
  );
  HTML5_ENTITIES.put(
   "capcap;",
   "\\u2a4b"
  );
  HTML5_ENTITIES.put(
   "capcup;",
   "\\u2a47"
  );
  HTML5_ENTITIES.put(
   "capdot;",
   "\\u2a40"
  );
  HTML5_ENTITIES.put(
   "CapitalDifferentialD;",
   "\\u2145"
  );
  HTML5_ENTITIES.put(
   "caps;",
   "\\u2229\\ufe00"
  );
  HTML5_ENTITIES.put(
   "caret;",
   "\\u2041"
  );
  HTML5_ENTITIES.put(
   "caron;",
   "\\u02c7"
  );
  HTML5_ENTITIES.put(
   "Cayleys;",
   "\\u212d"
  );
  HTML5_ENTITIES.put(
   "ccaps;",
   "\\u2a4d"
  );
  HTML5_ENTITIES.put(
   "Ccaron;",
   "\\u010c"
  );
  HTML5_ENTITIES.put(
   "ccaron;",
   "\\u010d"
  );
  HTML5_ENTITIES.put(
   "Ccedil",
   "\\xc7"
  );
  HTML5_ENTITIES.put(
   "ccedil",
   "\\xe7"
  );
  HTML5_ENTITIES.put(
   "Ccedil;",
   "\\xc7"
  );
  HTML5_ENTITIES.put(
   "ccedil;",
   "\\xe7"
  );
  HTML5_ENTITIES.put(
   "Ccirc;",
   "\\u0108"
  );
  HTML5_ENTITIES.put(
   "ccirc;",
   "\\u0109"
  );
  HTML5_ENTITIES.put(
   "Cconint;",
   "\\u2230"
  );
  HTML5_ENTITIES.put(
   "ccups;",
   "\\u2a4c"
  );
  HTML5_ENTITIES.put(
   "ccupssm;",
   "\\u2a50"
  );
  HTML5_ENTITIES.put(
   "Cdot;",
   "\\u010a"
  );
  HTML5_ENTITIES.put(
   "cdot;",
   "\\u010b"
  );
  HTML5_ENTITIES.put(
   "cedil",
   "\\xb8"
  );
  HTML5_ENTITIES.put(
   "cedil;",
   "\\xb8"
  );
  HTML5_ENTITIES.put(
   "Cedilla;",
   "\\xb8"
  );
  HTML5_ENTITIES.put(
   "cemptyv;",
   "\\u29b2"
  );
  HTML5_ENTITIES.put(
   "cent",
   "\\xa2"
  );
  HTML5_ENTITIES.put(
   "cent;",
   "\\xa2"
  );
  HTML5_ENTITIES.put(
   "CenterDot;",
   "\\xb7"
  );
  HTML5_ENTITIES.put(
   "centerdot;",
   "\\xb7"
  );
  HTML5_ENTITIES.put(
   "Cfr;",
   "\\u212d"
  );
  HTML5_ENTITIES.put(
   "cfr;",
   "\\U0001d520"
  );
  HTML5_ENTITIES.put(
   "CHcy;",
   "\\u0427"
  );
  HTML5_ENTITIES.put(
   "chcy;",
   "\\u0447"
  );
  HTML5_ENTITIES.put(
   "check;",
   "\\u2713"
  );
  HTML5_ENTITIES.put(
   "checkmark;",
   "\\u2713"
  );
  HTML5_ENTITIES.put(
   "Chi;",
   "\\u03a7"
  );
  HTML5_ENTITIES.put(
   "chi;",
   "\\u03c7"
  );
  HTML5_ENTITIES.put(
   "cir;",
   "\\u25cb"
  );
  HTML5_ENTITIES.put(
   "circ;",
   "\\u02c6"
  );
  HTML5_ENTITIES.put(
   "circeq;",
   "\\u2257"
  );
  HTML5_ENTITIES.put(
   "circlearrowleft;",
   "\\u21ba"
  );
  HTML5_ENTITIES.put(
   "circlearrowright;",
   "\\u21bb"
  );
  HTML5_ENTITIES.put(
   "circledast;",
   "\\u229b"
  );
  HTML5_ENTITIES.put(
   "circledcirc;",
   "\\u229a"
  );
  HTML5_ENTITIES.put(
   "circleddash;",
   "\\u229d"
  );
  HTML5_ENTITIES.put(
   "CircleDot;",
   "\\u2299"
  );
  HTML5_ENTITIES.put(
   "circledR;",
   "\\xae"
  );
  HTML5_ENTITIES.put(
   "circledS;",
   "\\u24c8"
  );
  HTML5_ENTITIES.put(
   "CircleMinus;",
   "\\u2296"
  );
  HTML5_ENTITIES.put(
   "CirclePlus;",
   "\\u2295"
  );
  HTML5_ENTITIES.put(
   "CircleTimes;",
   "\\u2297"
  );
  HTML5_ENTITIES.put(
   "cirE;",
   "\\u29c3"
  );
  HTML5_ENTITIES.put(
   "cire;",
   "\\u2257"
  );
  HTML5_ENTITIES.put(
   "cirfnint;",
   "\\u2a10"
  );
  HTML5_ENTITIES.put(
   "cirmid;",
   "\\u2aef"
  );
  HTML5_ENTITIES.put(
   "cirscir;",
   "\\u29c2"
  );
  HTML5_ENTITIES.put(
   "ClockwiseContourIntegral;",
   "\\u2232"
  );
  HTML5_ENTITIES.put(
   "CloseCurlyDoubleQuote;",
   "\\u201d"
  );
  HTML5_ENTITIES.put(
   "CloseCurlyQuote;",
   "\\u2019"
  );
  HTML5_ENTITIES.put(
   "clubs;",
   "\\u2663"
  );
  HTML5_ENTITIES.put(
   "clubsuit;",
   "\\u2663"
  );
  HTML5_ENTITIES.put(
   "Colon;",
   "\\u2237"
  );
  HTML5_ENTITIES.put(
   "colon;",
   ""
  );
  HTML5_ENTITIES.put(
   "Colone;",
   "\\u2a74"
  );
  HTML5_ENTITIES.put(
   "colone;",
   "\\u2254"
  );
  HTML5_ENTITIES.put(
   "coloneq;",
   "\\u2254"
  );
  HTML5_ENTITIES.put(
   "comma;",
   ","
  );
  HTML5_ENTITIES.put(
   "commat;",
   "@"
  );
  HTML5_ENTITIES.put(
   "comp;",
   "\\u2201"
  );
  HTML5_ENTITIES.put(
   "compfn;",
   "\\u2218"
  );
  HTML5_ENTITIES.put(
   "complement;",
   "\\u2201"
  );
  HTML5_ENTITIES.put(
   "complexes;",
   "\\u2102"
  );
  HTML5_ENTITIES.put(
   "cong;",
   "\\u2245"
  );
  HTML5_ENTITIES.put(
   "congdot;",
   "\\u2a6d"
  );
  HTML5_ENTITIES.put(
   "Congruent;",
   "\\u2261"
  );
  HTML5_ENTITIES.put(
   "Conint;",
   "\\u222f"
  );
  HTML5_ENTITIES.put(
   "conint;",
   "\\u222e"
  );
  HTML5_ENTITIES.put(
   "ContourIntegral;",
   "\\u222e"
  );
  HTML5_ENTITIES.put(
   "Copf;",
   "\\u2102"
  );
  HTML5_ENTITIES.put(
   "copf;",
   "\\U0001d554"
  );
  HTML5_ENTITIES.put(
   "coprod;",
   "\\u2210"
  );
  HTML5_ENTITIES.put(
   "Coproduct;",
   "\\u2210"
  );
  HTML5_ENTITIES.put(
   "COPY",
   "\\xa9"
  );
  HTML5_ENTITIES.put(
   "copy",
   "\\xa9"
  );
  HTML5_ENTITIES.put(
   "COPY;",
   "\\xa9"
  );
  HTML5_ENTITIES.put(
   "copy;",
   "\\xa9"
  );
  HTML5_ENTITIES.put(
   "copysr;",
   "\\u2117"
  );
  HTML5_ENTITIES.put(
   "CounterClockwiseContourIntegral;",
   "\\u2233"
  );
  HTML5_ENTITIES.put(
   "crarr;",
   "\\u21b5"
  );
  HTML5_ENTITIES.put(
   "Cross;",
   "\\u2a2f"
  );
  HTML5_ENTITIES.put(
   "cross;",
   "\\u2717"
  );
  HTML5_ENTITIES.put(
   "Cscr;",
   "\\U0001d49e"
  );
  HTML5_ENTITIES.put(
   "cscr;",
   "\\U0001d4b8"
  );
  HTML5_ENTITIES.put(
   "csub;",
   "\\u2acf"
  );
  HTML5_ENTITIES.put(
   "csube;",
   "\\u2ad1"
  );
  HTML5_ENTITIES.put(
   "csup;",
   "\\u2ad0"
  );
  HTML5_ENTITIES.put(
   "csupe;",
   "\\u2ad2"
  );
  HTML5_ENTITIES.put(
   "ctdot;",
   "\\u22ef"
  );
  HTML5_ENTITIES.put(
   "cudarrl;",
   "\\u2938"
  );
  HTML5_ENTITIES.put(
   "cudarrr;",
   "\\u2935"
  );
  HTML5_ENTITIES.put(
   "cuepr;",
   "\\u22de"
  );
  HTML5_ENTITIES.put(
   "cuesc;",
   "\\u22df"
  );
  HTML5_ENTITIES.put(
   "cularr;",
   "\\u21b6"
  );
  HTML5_ENTITIES.put(
   "cularrp;",
   "\\u293d"
  );
  HTML5_ENTITIES.put(
   "Cup;",
   "\\u22d3"
  );
  HTML5_ENTITIES.put(
   "cup;",
   "\\u222a"
  );
  HTML5_ENTITIES.put(
   "cupbrcap;",
   "\\u2a48"
  );
  HTML5_ENTITIES.put(
   "CupCap;",
   "\\u224d"
  );
  HTML5_ENTITIES.put(
   "cupcap;",
   "\\u2a46"
  );
  HTML5_ENTITIES.put(
   "cupcup;",
   "\\u2a4a"
  );
  HTML5_ENTITIES.put(
   "cupdot;",
   "\\u228d"
  );
  HTML5_ENTITIES.put(
   "cupor;",
   "\\u2a45"
  );
  HTML5_ENTITIES.put(
   "cups;",
   "\\u222a\\ufe00"
  );
  HTML5_ENTITIES.put(
   "curarr;",
   "\\u21b7"
  );
  HTML5_ENTITIES.put(
   "curarrm;",
   "\\u293c"
  );
  HTML5_ENTITIES.put(
   "curlyeqprec;",
   "\\u22de"
  );
  HTML5_ENTITIES.put(
   "curlyeqsucc;",
   "\\u22df"
  );
  HTML5_ENTITIES.put(
   "curlyvee;",
   "\\u22ce"
  );
  HTML5_ENTITIES.put(
   "curlywedge;",
   "\\u22cf"
  );
  HTML5_ENTITIES.put(
   "curren",
   "\\xa4"
  );
  HTML5_ENTITIES.put(
   "curren;",
   "\\xa4"
  );
  HTML5_ENTITIES.put(
   "curvearrowleft;",
   "\\u21b6"
  );
  HTML5_ENTITIES.put(
   "curvearrowright;",
   "\\u21b7"
  );
  HTML5_ENTITIES.put(
   "cuvee;",
   "\\u22ce"
  );
  HTML5_ENTITIES.put(
   "cuwed;",
   "\\u22cf"
  );
  HTML5_ENTITIES.put(
   "cwconint;",
   "\\u2232"
  );
  HTML5_ENTITIES.put(
   "cwint;",
   "\\u2231"
  );
  HTML5_ENTITIES.put(
   "cylcty;",
   "\\u232d"
  );
  HTML5_ENTITIES.put(
   "Dagger;",
   "\\u2021"
  );
  HTML5_ENTITIES.put(
   "dagger;",
   "\\u2020"
  );
  HTML5_ENTITIES.put(
   "daleth;",
   "\\u2138"
  );
  HTML5_ENTITIES.put(
   "Darr;",
   "\\u21a1"
  );
  HTML5_ENTITIES.put(
   "dArr;",
   "\\u21d3"
  );
  HTML5_ENTITIES.put(
   "darr;",
   "\\u2193"
  );
  HTML5_ENTITIES.put(
   "dash;",
   "\\u2010"
  );
  HTML5_ENTITIES.put(
   "Dashv;",
   "\\u2ae4"
  );
  HTML5_ENTITIES.put(
   "dashv;",
   "\\u22a3"
  );
  HTML5_ENTITIES.put(
   "dbkarow;",
   "\\u290f"
  );
  HTML5_ENTITIES.put(
   "dblac;",
   "\\u02dd"
  );
  HTML5_ENTITIES.put(
   "Dcaron;",
   "\\u010e"
  );
  HTML5_ENTITIES.put(
   "dcaron;",
   "\\u010f"
  );
  HTML5_ENTITIES.put(
   "Dcy;",
   "\\u0414"
  );
  HTML5_ENTITIES.put(
   "dcy;",
   "\\u0434"
  );
  HTML5_ENTITIES.put(
   "DD;",
   "\\u2145"
  );
  HTML5_ENTITIES.put(
   "dd;",
   "\\u2146"
  );
  HTML5_ENTITIES.put(
   "ddagger;",
   "\\u2021"
  );
  HTML5_ENTITIES.put(
   "ddarr;",
   "\\u21ca"
  );
  HTML5_ENTITIES.put(
   "DDotrahd;",
   "\\u2911"
  );
  HTML5_ENTITIES.put(
   "ddotseq;",
   "\\u2a77"
  );
  HTML5_ENTITIES.put(
   "deg",
   "\\xb0"
  );
  HTML5_ENTITIES.put(
   "deg;",
   "\\xb0"
  );
  HTML5_ENTITIES.put(
   "Del;",
   "\\u2207"
  );
  HTML5_ENTITIES.put(
   "Delta;",
   "\\u0394"
  );
  HTML5_ENTITIES.put(
   "delta;",
   "\\u03b4"
  );
  HTML5_ENTITIES.put(
   "demptyv;",
   "\\u29b1"
  );
  HTML5_ENTITIES.put(
   "dfisht;",
   "\\u297f"
  );
  HTML5_ENTITIES.put(
   "Dfr;",
   "\\U0001d507"
  );
  HTML5_ENTITIES.put(
   "dfr;",
   "\\U0001d521"
  );
  HTML5_ENTITIES.put(
   "dHar;",
   "\\u2965"
  );
  HTML5_ENTITIES.put(
   "dharl;",
   "\\u21c3"
  );
  HTML5_ENTITIES.put(
   "dharr;",
   "\\u21c2"
  );
  HTML5_ENTITIES.put(
   "DiacriticalAcute;",
   "\\xb4"
  );
  HTML5_ENTITIES.put(
   "DiacriticalDot;",
   "\\u02d9"
  );
  HTML5_ENTITIES.put(
   "DiacriticalDoubleAcute;",
   "\\u02dd"
  );
  HTML5_ENTITIES.put(
   "DiacriticalGrave;",
   "`"
  );
  HTML5_ENTITIES.put(
   "DiacriticalTilde;",
   "\\u02dc"
  );
  HTML5_ENTITIES.put(
   "diam;",
   "\\u22c4"
  );
  HTML5_ENTITIES.put(
   "Diamond;",
   "\\u22c4"
  );
  HTML5_ENTITIES.put(
   "diamond;",
   "\\u22c4"
  );
  HTML5_ENTITIES.put(
   "diamondsuit;",
   "\\u2666"
  );
  HTML5_ENTITIES.put(
   "diams;",
   "\\u2666"
  );
  HTML5_ENTITIES.put(
   "die;",
   "\\xa8"
  );
  HTML5_ENTITIES.put(
   "DifferentialD;",
   "\\u2146"
  );
  HTML5_ENTITIES.put(
   "digamma;",
   "\\u03dd"
  );
  HTML5_ENTITIES.put(
   "disin;",
   "\\u22f2"
  );
  HTML5_ENTITIES.put(
   "div;",
   "\\xf7"
  );
  HTML5_ENTITIES.put(
   "divide",
   "\\xf7"
  );
  HTML5_ENTITIES.put(
   "divide;",
   "\\xf7"
  );
  HTML5_ENTITIES.put(
   "divideontimes;",
   "\\u22c7"
  );
  HTML5_ENTITIES.put(
   "divonx;",
   "\\u22c7"
  );
  HTML5_ENTITIES.put(
   "DJcy;",
   "\\u0402"
  );
  HTML5_ENTITIES.put(
   "djcy;",
   "\\u0452"
  );
  HTML5_ENTITIES.put(
   "dlcorn;",
   "\\u231e"
  );
  HTML5_ENTITIES.put(
   "dlcrop;",
   "\\u230d"
  );
  HTML5_ENTITIES.put(
   "dollar;",
   "$"
  );
  HTML5_ENTITIES.put(
   "Dopf;",
   "\\U0001d53b"
  );
  HTML5_ENTITIES.put(
   "dopf;",
   "\\U0001d555"
  );
  HTML5_ENTITIES.put(
   "Dot;",
   "\\xa8"
  );
  HTML5_ENTITIES.put(
   "dot;",
   "\\u02d9"
  );
  HTML5_ENTITIES.put(
   "DotDot;",
   "\\u20dc"
  );
  HTML5_ENTITIES.put(
   "doteq;",
   "\\u2250"
  );
  HTML5_ENTITIES.put(
   "doteqdot;",
   "\\u2251"
  );
  HTML5_ENTITIES.put(
   "DotEqual;",
   "\\u2250"
  );
  HTML5_ENTITIES.put(
   "dotminus;",
   "\\u2238"
  );
  HTML5_ENTITIES.put(
   "dotplus;",
   "\\u2214"
  );
  HTML5_ENTITIES.put(
   "dotsquare;",
   "\\u22a1"
  );
  HTML5_ENTITIES.put(
   "doublebarwedge;",
   "\\u2306"
  );
  HTML5_ENTITIES.put(
   "DoubleContourIntegral;",
   "\\u222f"
  );
  HTML5_ENTITIES.put(
   "DoubleDot;",
   "\\xa8"
  );
  HTML5_ENTITIES.put(
   "DoubleDownArrow;",
   "\\u21d3"
  );
  HTML5_ENTITIES.put(
   "DoubleLeftArrow;",
   "\\u21d0"
  );
  HTML5_ENTITIES.put(
   "DoubleLeftRightArrow;",
   "\\u21d4"
  );
  HTML5_ENTITIES.put(
   "DoubleLeftTee;",
   "\\u2ae4"
  );
  HTML5_ENTITIES.put(
   "DoubleLongLeftArrow;",
   "\\u27f8"
  );
  HTML5_ENTITIES.put(
   "DoubleLongLeftRightArrow;",
   "\\u27fa"
  );
  HTML5_ENTITIES.put(
   "DoubleLongRightArrow;",
   "\\u27f9"
  );
  HTML5_ENTITIES.put(
   "DoubleRightArrow;",
   "\\u21d2"
  );
  HTML5_ENTITIES.put(
   "DoubleRightTee;",
   "\\u22a8"
  );
  HTML5_ENTITIES.put(
   "DoubleUpArrow;",
   "\\u21d1"
  );
  HTML5_ENTITIES.put(
   "DoubleUpDownArrow;",
   "\\u21d5"
  );
  HTML5_ENTITIES.put(
   "DoubleVerticalBar;",
   "\\u2225"
  );
  HTML5_ENTITIES.put(
   "DownArrow;",
   "\\u2193"
  );
  HTML5_ENTITIES.put(
   "Downarrow;",
   "\\u21d3"
  );
  HTML5_ENTITIES.put(
   "downarrow;",
   "\\u2193"
  );
  HTML5_ENTITIES.put(
   "DownArrowBar;",
   "\\u2913"
  );
  HTML5_ENTITIES.put(
   "DownArrowUpArrow;",
   "\\u21f5"
  );
  HTML5_ENTITIES.put(
   "DownBreve;",
   "\\u0311"
  );
  HTML5_ENTITIES.put(
   "downdownarrows;",
   "\\u21ca"
  );
  HTML5_ENTITIES.put(
   "downharpoonleft;",
   "\\u21c3"
  );
  HTML5_ENTITIES.put(
   "downharpoonright;",
   "\\u21c2"
  );
  HTML5_ENTITIES.put(
   "DownLeftRightVector;",
   "\\u2950"
  );
  HTML5_ENTITIES.put(
   "DownLeftTeeVector;",
   "\\u295e"
  );
  HTML5_ENTITIES.put(
   "DownLeftVector;",
   "\\u21bd"
  );
  HTML5_ENTITIES.put(
   "DownLeftVectorBar;",
   "\\u2956"
  );
  HTML5_ENTITIES.put(
   "DownRightTeeVector;",
   "\\u295f"
  );
  HTML5_ENTITIES.put(
   "DownRightVector;",
   "\\u21c1"
  );
  HTML5_ENTITIES.put(
   "DownRightVectorBar;",
   "\\u2957"
  );
  HTML5_ENTITIES.put(
   "DownTee;",
   "\\u22a4"
  );
  HTML5_ENTITIES.put(
   "DownTeeArrow;",
   "\\u21a7"
  );
  HTML5_ENTITIES.put(
   "drbkarow;",
   "\\u2910"
  );
  HTML5_ENTITIES.put(
   "drcorn;",
   "\\u231f"
  );
  HTML5_ENTITIES.put(
   "drcrop;",
   "\\u230c"
  );
  HTML5_ENTITIES.put(
   "Dscr;",
   "\\U0001d49f"
  );
  HTML5_ENTITIES.put(
   "dscr;",
   "\\U0001d4b9"
  );
  HTML5_ENTITIES.put(
   "DScy;",
   "\\u0405"
  );
  HTML5_ENTITIES.put(
   "dscy;",
   "\\u0455"
  );
  HTML5_ENTITIES.put(
   "dsol;",
   "\\u29f6"
  );
  HTML5_ENTITIES.put(
   "Dstrok;",
   "\\u0110"
  );
  HTML5_ENTITIES.put(
   "dstrok;",
   "\\u0111"
  );
  HTML5_ENTITIES.put(
   "dtdot;",
   "\\u22f1"
  );
  HTML5_ENTITIES.put(
   "dtri;",
   "\\u25bf"
  );
  HTML5_ENTITIES.put(
   "dtrif;",
   "\\u25be"
  );
  HTML5_ENTITIES.put(
   "duarr;",
   "\\u21f5"
  );
  HTML5_ENTITIES.put(
   "duhar;",
   "\\u296f"
  );
  HTML5_ENTITIES.put(
   "dwangle;",
   "\\u29a6"
  );
  HTML5_ENTITIES.put(
   "DZcy;",
   "\\u040f"
  );
  HTML5_ENTITIES.put(
   "dzcy;",
   "\\u045f"
  );
  HTML5_ENTITIES.put(
   "dzigrarr;",
   "\\u27ff"
  );
  HTML5_ENTITIES.put(
   "Eacute",
   "\\xc9"
  );
  HTML5_ENTITIES.put(
   "eacute",
   "\\xe9"
  );
  HTML5_ENTITIES.put(
   "Eacute;",
   "\\xc9"
  );
  HTML5_ENTITIES.put(
   "eacute;",
   "\\xe9"
  );
  HTML5_ENTITIES.put(
   "easter;",
   "\\u2a6e"
  );
  HTML5_ENTITIES.put(
   "Ecaron;",
   "\\u011a"
  );
  HTML5_ENTITIES.put(
   "ecaron;",
   "\\u011b"
  );
  HTML5_ENTITIES.put(
   "ecir;",
   "\\u2256"
  );
  HTML5_ENTITIES.put(
   "Ecirc",
   "\\xca"
  );
  HTML5_ENTITIES.put(
   "ecirc",
   "\\xea"
  );
  HTML5_ENTITIES.put(
   "Ecirc;",
   "\\xca"
  );
  HTML5_ENTITIES.put(
   "ecirc;",
   "\\xea"
  );
  HTML5_ENTITIES.put(
   "ecolon;",
   "\\u2255"
  );
  HTML5_ENTITIES.put(
   "Ecy;",
   "\\u042d"
  );
  HTML5_ENTITIES.put(
   "ecy;",
   "\\u044d"
  );
  HTML5_ENTITIES.put(
   "eDDot;",
   "\\u2a77"
  );
  HTML5_ENTITIES.put(
   "Edot;",
   "\\u0116"
  );
  HTML5_ENTITIES.put(
   "eDot;",
   "\\u2251"
  );
  HTML5_ENTITIES.put(
   "edot;",
   "\\u0117"
  );
  HTML5_ENTITIES.put(
   "ee;",
   "\\u2147"
  );
  HTML5_ENTITIES.put(
   "efDot;",
   "\\u2252"
  );
  HTML5_ENTITIES.put(
   "Efr;",
   "\\U0001d508"
  );
  HTML5_ENTITIES.put(
   "efr;",
   "\\U0001d522"
  );
  HTML5_ENTITIES.put(
   "eg;",
   "\\u2a9a"
  );
  HTML5_ENTITIES.put(
   "Egrave",
   "\\xc8"
  );
  HTML5_ENTITIES.put(
   "egrave",
   "\\xe8"
  );
  HTML5_ENTITIES.put(
   "Egrave;",
   "\\xc8"
  );
  HTML5_ENTITIES.put(
   "egrave;",
   "\\xe8"
  );
  HTML5_ENTITIES.put(
   "egs;",
   "\\u2a96"
  );
  HTML5_ENTITIES.put(
   "egsdot;",
   "\\u2a98"
  );
  HTML5_ENTITIES.put(
   "el;",
   "\\u2a99"
  );
  HTML5_ENTITIES.put(
   "Element;",
   "\\u2208"
  );
  HTML5_ENTITIES.put(
   "elinters;",
   "\\u23e7"
  );
  HTML5_ENTITIES.put(
   "ell;",
   "\\u2113"
  );
  HTML5_ENTITIES.put(
   "els;",
   "\\u2a95"
  );
  HTML5_ENTITIES.put(
   "elsdot;",
   "\\u2a97"
  );
  HTML5_ENTITIES.put(
   "Emacr;",
   "\\u0112"
  );
  HTML5_ENTITIES.put(
   "emacr;",
   "\\u0113"
  );
  HTML5_ENTITIES.put(
   "empty;",
   "\\u2205"
  );
  HTML5_ENTITIES.put(
   "emptyset;",
   "\\u2205"
  );
  HTML5_ENTITIES.put(
   "EmptySmallSquare;",
   "\\u25fb"
  );
  HTML5_ENTITIES.put(
   "emptyv;",
   "\\u2205"
  );
  HTML5_ENTITIES.put(
   "EmptyVerySmallSquare;",
   "\\u25ab"
  );
  HTML5_ENTITIES.put(
   "emsp13;",
   "\\u2004"
  );
  HTML5_ENTITIES.put(
   "emsp14;",
   "\\u2005"
  );
  HTML5_ENTITIES.put(
   "emsp;",
   "\\u2003"
  );
  HTML5_ENTITIES.put(
   "ENG;",
   "\\u014a"
  );
  HTML5_ENTITIES.put(
   "eng;",
   "\\u014b"
  );
  HTML5_ENTITIES.put(
   "ensp;",
   "\\u2002"
  );
  HTML5_ENTITIES.put(
   "Eogon;",
   "\\u0118"
  );
  HTML5_ENTITIES.put(
   "eogon;",
   "\\u0119"
  );
  HTML5_ENTITIES.put(
   "Eopf;",
   "\\U0001d53c"
  );
  HTML5_ENTITIES.put(
   "eopf;",
   "\\U0001d556"
  );
  HTML5_ENTITIES.put(
   "epar;",
   "\\u22d5"
  );
  HTML5_ENTITIES.put(
   "eparsl;",
   "\\u29e3"
  );
  HTML5_ENTITIES.put(
   "eplus;",
   "\\u2a71"
  );
  HTML5_ENTITIES.put(
   "epsi;",
   "\\u03b5"
  );
  HTML5_ENTITIES.put(
   "Epsilon;",
   "\\u0395"
  );
  HTML5_ENTITIES.put(
   "epsilon;",
   "\\u03b5"
  );
  HTML5_ENTITIES.put(
   "epsiv;",
   "\\u03f5"
  );
  HTML5_ENTITIES.put(
   "eqcirc;",
   "\\u2256"
  );
  HTML5_ENTITIES.put(
   "eqcolon;",
   "\\u2255"
  );
  HTML5_ENTITIES.put(
   "eqsim;",
   "\\u2242"
  );
  HTML5_ENTITIES.put(
   "eqslantgtr;",
   "\\u2a96"
  );
  HTML5_ENTITIES.put(
   "eqslantless;",
   "\\u2a95"
  );
  HTML5_ENTITIES.put(
   "Equal;",
   "\\u2a75"
  );
  HTML5_ENTITIES.put(
   "equals;",
   "="
  );
  HTML5_ENTITIES.put(
   "EqualTilde;",
   "\\u2242"
  );
  HTML5_ENTITIES.put(
   "equest;",
   "\\u225f"
  );
  HTML5_ENTITIES.put(
   "Equilibrium;",
   "\\u21cc"
  );
  HTML5_ENTITIES.put(
   "equiv;",
   "\\u2261"
  );
  HTML5_ENTITIES.put(
   "equivDD;",
   "\\u2a78"
  );
  HTML5_ENTITIES.put(
   "eqvparsl;",
   "\\u29e5"
  );
  HTML5_ENTITIES.put(
   "erarr;",
   "\\u2971"
  );
  HTML5_ENTITIES.put(
   "erDot;",
   "\\u2253"
  );
  HTML5_ENTITIES.put(
   "Escr;",
   "\\u2130"
  );
  HTML5_ENTITIES.put(
   "escr;",
   "\\u212f"
  );
  HTML5_ENTITIES.put(
   "esdot;",
   "\\u2250"
  );
  HTML5_ENTITIES.put(
   "Esim;",
   "\\u2a73"
  );
  HTML5_ENTITIES.put(
   "esim;",
   "\\u2242"
  );
  HTML5_ENTITIES.put(
   "Eta;",
   "\\u0397"
  );
  HTML5_ENTITIES.put(
   "eta;",
   "\\u03b7"
  );
  HTML5_ENTITIES.put(
   "ETH",
   "\\xd0"
  );
  HTML5_ENTITIES.put(
   "eth",
   "\\xf0"
  );
  HTML5_ENTITIES.put(
   "ETH;",
   "\\xd0"
  );
  HTML5_ENTITIES.put(
   "eth;",
   "\\xf0"
  );
  HTML5_ENTITIES.put(
   "Euml",
   "\\xcb"
  );
  HTML5_ENTITIES.put(
   "euml",
   "\\xeb"
  );
  HTML5_ENTITIES.put(
   "Euml;",
   "\\xcb"
  );
  HTML5_ENTITIES.put(
   "euml;",
   "\\xeb"
  );
  HTML5_ENTITIES.put(
   "euro;",
   "\\u20ac"
  );
  HTML5_ENTITIES.put(
   "excl;",
   "!"
  );
  HTML5_ENTITIES.put(
   "exist;",
   "\\u2203"
  );
  HTML5_ENTITIES.put(
   "Exists;",
   "\\u2203"
  );
  HTML5_ENTITIES.put(
   "expectation;",
   "\\u2130"
  );
  HTML5_ENTITIES.put(
   "ExponentialE;",
   "\\u2147"
  );
  HTML5_ENTITIES.put(
   "exponentiale;",
   "\\u2147"
  );
  HTML5_ENTITIES.put(
   "fallingdotseq;",
   "\\u2252"
  );
  HTML5_ENTITIES.put(
   "Fcy;",
   "\\u0424"
  );
  HTML5_ENTITIES.put(
   "fcy;",
   "\\u0444"
  );
  HTML5_ENTITIES.put(
   "female;",
   "\\u2640"
  );
  HTML5_ENTITIES.put(
   "ffilig;",
   "\\ufb03"
  );
  HTML5_ENTITIES.put(
   "fflig;",
   "\\ufb00"
  );
  HTML5_ENTITIES.put(
   "ffllig;",
   "\\ufb04"
  );
  HTML5_ENTITIES.put(
   "Ffr;",
   "\\U0001d509"
  );
  HTML5_ENTITIES.put(
   "ffr;",
   "\\U0001d523"
  );
  HTML5_ENTITIES.put(
   "filig;",
   "\\ufb01"
  );
  HTML5_ENTITIES.put(
   "FilledSmallSquare;",
   "\\u25fc"
  );
  HTML5_ENTITIES.put(
   "FilledVerySmallSquare;",
   "\\u25aa"
  );
  HTML5_ENTITIES.put(
   "fjlig;",
   "fj"
  );
  HTML5_ENTITIES.put(
   "flat;",
   "\\u266d"
  );
  HTML5_ENTITIES.put(
   "fllig;",
   "\\ufb02"
  );
  HTML5_ENTITIES.put(
   "fltns;",
   "\\u25b1"
  );
  HTML5_ENTITIES.put(
   "fnof;",
   "\\u0192"
  );
  HTML5_ENTITIES.put(
   "Fopf;",
   "\\U0001d53d"
  );
  HTML5_ENTITIES.put(
   "fopf;",
   "\\U0001d557"
  );
  HTML5_ENTITIES.put(
   "ForAll;",
   "\\u2200"
  );
  HTML5_ENTITIES.put(
   "forall;",
   "\\u2200"
  );
  HTML5_ENTITIES.put(
   "fork;",
   "\\u22d4"
  );
  HTML5_ENTITIES.put(
   "forkv;",
   "\\u2ad9"
  );
  HTML5_ENTITIES.put(
   "Fouriertrf;",
   "\\u2131"
  );
  HTML5_ENTITIES.put(
   "fpartint;",
   "\\u2a0d"
  );
  HTML5_ENTITIES.put(
   "frac12",
   "\\xbd"
  );
  HTML5_ENTITIES.put(
   "frac12;",
   "\\xbd"
  );
  HTML5_ENTITIES.put(
   "frac13;",
   "\\u2153"
  );
  HTML5_ENTITIES.put(
   "frac14",
   "\\xbc"
  );
  HTML5_ENTITIES.put(
   "frac14;",
   "\\xbc"
  );
  HTML5_ENTITIES.put(
   "frac15;",
   "\\u2155"
  );
  HTML5_ENTITIES.put(
   "frac16;",
   "\\u2159"
  );
  HTML5_ENTITIES.put(
   "frac18;",
   "\\u215b"
  );
  HTML5_ENTITIES.put(
   "frac23;",
   "\\u2154"
  );
  HTML5_ENTITIES.put(
   "frac25;",
   "\\u2156"
  );
  HTML5_ENTITIES.put(
   "frac34",
   "\\xbe"
  );
  HTML5_ENTITIES.put(
   "frac34;",
   "\\xbe"
  );
  HTML5_ENTITIES.put(
   "frac35;",
   "\\u2157"
  );
  HTML5_ENTITIES.put(
   "frac38;",
   "\\u215c"
  );
  HTML5_ENTITIES.put(
   "frac45;",
   "\\u2158"
  );
  HTML5_ENTITIES.put(
   "frac56;",
   "\\u215a"
  );
  HTML5_ENTITIES.put(
   "frac58;",
   "\\u215d"
  );
  HTML5_ENTITIES.put(
   "frac78;",
   "\\u215e"
  );
  HTML5_ENTITIES.put(
   "frasl;",
   "\\u2044"
  );
  HTML5_ENTITIES.put(
   "frown;",
   "\\u2322"
  );
  HTML5_ENTITIES.put(
   "Fscr;",
   "\\u2131"
  );
  HTML5_ENTITIES.put(
   "fscr;",
   "\\U0001d4bb"
  );
  HTML5_ENTITIES.put(
   "gacute;",
   "\\u01f5"
  );
  HTML5_ENTITIES.put(
   "Gamma;",
   "\\u0393"
  );
  HTML5_ENTITIES.put(
   "gamma;",
   "\\u03b3"
  );
  HTML5_ENTITIES.put(
   "Gammad;",
   "\\u03dc"
  );
  HTML5_ENTITIES.put(
   "gammad;",
   "\\u03dd"
  );
  HTML5_ENTITIES.put(
   "gap;",
   "\\u2a86"
  );
  HTML5_ENTITIES.put(
   "Gbreve;",
   "\\u011e"
  );
  HTML5_ENTITIES.put(
   "gbreve;",
   "\\u011f"
  );
  HTML5_ENTITIES.put(
   "Gcedil;",
   "\\u0122"
  );
  HTML5_ENTITIES.put(
   "Gcirc;",
   "\\u011c"
  );
  HTML5_ENTITIES.put(
   "gcirc;",
   "\\u011d"
  );
  HTML5_ENTITIES.put(
   "Gcy;",
   "\\u0413"
  );
  HTML5_ENTITIES.put(
   "gcy;",
   "\\u0433"
  );
  HTML5_ENTITIES.put(
   "Gdot;",
   "\\u0120"
  );
  HTML5_ENTITIES.put(
   "gdot;",
   "\\u0121"
  );
  HTML5_ENTITIES.put(
   "gE;",
   "\\u2267"
  );
  HTML5_ENTITIES.put(
   "ge;",
   "\\u2265"
  );
  HTML5_ENTITIES.put(
   "gEl;",
   "\\u2a8c"
  );
  HTML5_ENTITIES.put(
   "gel;",
   "\\u22db"
  );
  HTML5_ENTITIES.put(
   "geq;",
   "\\u2265"
  );
  HTML5_ENTITIES.put(
   "geqq;",
   "\\u2267"
  );
  HTML5_ENTITIES.put(
   "geqslant;",
   "\\u2a7e"
  );
  HTML5_ENTITIES.put(
   "ges;",
   "\\u2a7e"
  );
  HTML5_ENTITIES.put(
   "gescc;",
   "\\u2aa9"
  );
  HTML5_ENTITIES.put(
   "gesdot;",
   "\\u2a80"
  );
  HTML5_ENTITIES.put(
   "gesdoto;",
   "\\u2a82"
  );
  HTML5_ENTITIES.put(
   "gesdotol;",
   "\\u2a84"
  );
  HTML5_ENTITIES.put(
   "gesl;",
   "\\u22db\\ufe00"
  );
  HTML5_ENTITIES.put(
   "gesles;",
   "\\u2a94"
  );
  HTML5_ENTITIES.put(
   "Gfr;",
   "\\U0001d50a"
  );
  HTML5_ENTITIES.put(
   "gfr;",
   "\\U0001d524"
  );
  HTML5_ENTITIES.put(
   "Gg;",
   "\\u22d9"
  );
  HTML5_ENTITIES.put(
   "gg;",
   "\\u226b"
  );
  HTML5_ENTITIES.put(
   "ggg;",
   "\\u22d9"
  );
  HTML5_ENTITIES.put(
   "gimel;",
   "\\u2137"
  );
  HTML5_ENTITIES.put(
   "GJcy;",
   "\\u0403"
  );
  HTML5_ENTITIES.put(
   "gjcy;",
   "\\u0453"
  );
  HTML5_ENTITIES.put(
   "gl;",
   "\\u2277"
  );
  HTML5_ENTITIES.put(
   "gla;",
   "\\u2aa5"
  );
  HTML5_ENTITIES.put(
   "glE;",
   "\\u2a92"
  );
  HTML5_ENTITIES.put(
   "glj;",
   "\\u2aa4"
  );
  HTML5_ENTITIES.put(
   "gnap;",
   "\\u2a8a"
  );
  HTML5_ENTITIES.put(
   "gnapprox;",
   "\\u2a8a"
  );
  HTML5_ENTITIES.put(
   "gnE;",
   "\\u2269"
  );
  HTML5_ENTITIES.put(
   "gne;",
   "\\u2a88"
  );
  HTML5_ENTITIES.put(
   "gneq;",
   "\\u2a88"
  );
  HTML5_ENTITIES.put(
   "gneqq;",
   "\\u2269"
  );
  HTML5_ENTITIES.put(
   "gnsim;",
   "\\u22e7"
  );
  HTML5_ENTITIES.put(
   "Gopf;",
   "\\U0001d53e"
  );
  HTML5_ENTITIES.put(
   "gopf;",
   "\\U0001d558"
  );
  HTML5_ENTITIES.put(
   "grave;",
   "`"
  );
  HTML5_ENTITIES.put(
   "GreaterEqual;",
   "\\u2265"
  );
  HTML5_ENTITIES.put(
   "GreaterEqualLess;",
   "\\u22db"
  );
  HTML5_ENTITIES.put(
   "GreaterFullEqual;",
   "\\u2267"
  );
  HTML5_ENTITIES.put(
   "GreaterGreater;",
   "\\u2aa2"
  );
  HTML5_ENTITIES.put(
   "GreaterLess;",
   "\\u2277"
  );
  HTML5_ENTITIES.put(
   "GreaterSlantEqual;",
   "\\u2a7e"
  );
  HTML5_ENTITIES.put(
   "GreaterTilde;",
   "\\u2273"
  );
  HTML5_ENTITIES.put(
   "Gscr;",
   "\\U0001d4a2"
  );
  HTML5_ENTITIES.put(
   "gscr;",
   "\\u210a"
  );
  HTML5_ENTITIES.put(
   "gsim;",
   "\\u2273"
  );
  HTML5_ENTITIES.put(
   "gsime;",
   "\\u2a8e"
  );
  HTML5_ENTITIES.put(
   "gsiml;",
   "\\u2a90"
  );
  HTML5_ENTITIES.put(
   "GT",
   ">"
  );
  HTML5_ENTITIES.put(
   "gt",
   ">"
  );
  HTML5_ENTITIES.put(
   "GT;",
   ">"
  );
  HTML5_ENTITIES.put(
   "Gt;",
   "\\u226b"
  );
  HTML5_ENTITIES.put(
   "gt;",
   ">"
  );
  HTML5_ENTITIES.put(
   "gtcc;",
   "\\u2aa7"
  );
  HTML5_ENTITIES.put(
   "gtcir;",
   "\\u2a7a"
  );
  HTML5_ENTITIES.put(
   "gtdot;",
   "\\u22d7"
  );
  HTML5_ENTITIES.put(
   "gtlPar;",
   "\\u2995"
  );
  HTML5_ENTITIES.put(
   "gtquest;",
   "\\u2a7c"
  );
  HTML5_ENTITIES.put(
   "gtrapprox;",
   "\\u2a86"
  );
  HTML5_ENTITIES.put(
   "gtrarr;",
   "\\u2978"
  );
  HTML5_ENTITIES.put(
   "gtrdot;",
   "\\u22d7"
  );
  HTML5_ENTITIES.put(
   "gtreqless;",
   "\\u22db"
  );
  HTML5_ENTITIES.put(
   "gtreqqless;",
   "\\u2a8c"
  );
  HTML5_ENTITIES.put(
   "gtrless;",
   "\\u2277"
  );
  HTML5_ENTITIES.put(
   "gtrsim;",
   "\\u2273"
  );
  HTML5_ENTITIES.put(
   "gvertneqq;",
   "\\u2269\\ufe00"
  );
  HTML5_ENTITIES.put(
   "gvnE;",
   "\\u2269\\ufe00"
  );
  HTML5_ENTITIES.put(
   "Hacek;",
   "\\u02c7"
  );
  HTML5_ENTITIES.put(
   "hairsp;",
   "\\u200a"
  );
  HTML5_ENTITIES.put(
   "half;",
   "\\xbd"
  );
  HTML5_ENTITIES.put(
   "hamilt;",
   "\\u210b"
  );
  HTML5_ENTITIES.put(
   "HARDcy;",
   "\\u042a"
  );
  HTML5_ENTITIES.put(
   "hardcy;",
   "\\u044a"
  );
  HTML5_ENTITIES.put(
   "hArr;",
   "\\u21d4"
  );
  HTML5_ENTITIES.put(
   "harr;",
   "\\u2194"
  );
  HTML5_ENTITIES.put(
   "harrcir;",
   "\\u2948"
  );
  HTML5_ENTITIES.put(
   "harrw;",
   "\\u21ad"
  );
  HTML5_ENTITIES.put(
   "Hat;",
   "^"
  );
  HTML5_ENTITIES.put(
   "hbar;",
   "\\u210f"
  );
  HTML5_ENTITIES.put(
   "Hcirc;",
   "\\u0124"
  );
  HTML5_ENTITIES.put(
   "hcirc;",
   "\\u0125"
  );
  HTML5_ENTITIES.put(
   "hearts;",
   "\\u2665"
  );
  HTML5_ENTITIES.put(
   "heartsuit;",
   "\\u2665"
  );
  HTML5_ENTITIES.put(
   "hellip;",
   "\\u2026"
  );
  HTML5_ENTITIES.put(
   "hercon;",
   "\\u22b9"
  );
  HTML5_ENTITIES.put(
   "Hfr;",
   "\\u210c"
  );
  HTML5_ENTITIES.put(
   "hfr;",
   "\\U0001d525"
  );
  HTML5_ENTITIES.put(
   "HilbertSpace;",
   "\\u210b"
  );
  HTML5_ENTITIES.put(
   "hksearow;",
   "\\u2925"
  );
  HTML5_ENTITIES.put(
   "hkswarow;",
   "\\u2926"
  );
  HTML5_ENTITIES.put(
   "hoarr;",
   "\\u21ff"
  );
  HTML5_ENTITIES.put(
   "homtht;",
   "\\u223b"
  );
  HTML5_ENTITIES.put(
   "hookleftarrow;",
   "\\u21a9"
  );
  HTML5_ENTITIES.put(
   "hookrightarrow;",
   "\\u21aa"
  );
  HTML5_ENTITIES.put(
   "Hopf;",
   "\\u210d"
  );
  HTML5_ENTITIES.put(
   "hopf;",
   "\\U0001d559"
  );
  HTML5_ENTITIES.put(
   "horbar;",
   "\\u2015"
  );
  HTML5_ENTITIES.put(
   "HorizontalLine;",
   "\\u2500"
  );
  HTML5_ENTITIES.put(
   "Hscr;",
   "\\u210b"
  );
  HTML5_ENTITIES.put(
   "hscr;",
   "\\U0001d4bd"
  );
  HTML5_ENTITIES.put(
   "hslash;",
   "\\u210f"
  );
  HTML5_ENTITIES.put(
   "Hstrok;",
   "\\u0126"
  );
  HTML5_ENTITIES.put(
   "hstrok;",
   "\\u0127"
  );
  HTML5_ENTITIES.put(
   "HumpDownHump;",
   "\\u224e"
  );
  HTML5_ENTITIES.put(
   "HumpEqual;",
   "\\u224f"
  );
  HTML5_ENTITIES.put(
   "hybull;",
   "\\u2043"
  );
  HTML5_ENTITIES.put(
   "hyphen;",
   "\\u2010"
  );
  HTML5_ENTITIES.put(
   "Iacute",
   "\\xcd"
  );
  HTML5_ENTITIES.put(
   "iacute",
   "\\xed"
  );
  HTML5_ENTITIES.put(
   "Iacute;",
   "\\xcd"
  );
  HTML5_ENTITIES.put(
   "iacute;",
   "\\xed"
  );
  HTML5_ENTITIES.put(
   "ic;",
   "\\u2063"
  );
  HTML5_ENTITIES.put(
   "Icirc",
   "\\xce"
  );
  HTML5_ENTITIES.put(
   "icirc",
   "\\xee"
  );
  HTML5_ENTITIES.put(
   "Icirc;",
   "\\xce"
  );
  HTML5_ENTITIES.put(
   "icirc;",
   "\\xee"
  );
  HTML5_ENTITIES.put(
   "Icy;",
   "\\u0418"
  );
  HTML5_ENTITIES.put(
   "icy;",
   "\\u0438"
  );
  HTML5_ENTITIES.put(
   "Idot;",
   "\\u0130"
  );
  HTML5_ENTITIES.put(
   "IEcy;",
   "\\u0415"
  );
  HTML5_ENTITIES.put(
   "iecy;",
   "\\u0435"
  );
  HTML5_ENTITIES.put(
   "iexcl",
   "\\xa1"
  );
  HTML5_ENTITIES.put(
   "iexcl;",
   "\\xa1"
  );
  HTML5_ENTITIES.put(
   "iff;",
   "\\u21d4"
  );
  HTML5_ENTITIES.put(
   "Ifr;",
   "\\u2111"
  );
  HTML5_ENTITIES.put(
   "ifr;",
   "\\U0001d526"
  );
  HTML5_ENTITIES.put(
   "Igrave",
   "\\xcc"
  );
  HTML5_ENTITIES.put(
   "igrave",
   "\\xec"
  );
  HTML5_ENTITIES.put(
   "Igrave;",
   "\\xcc"
  );
  HTML5_ENTITIES.put(
   "igrave;",
   "\\xec"
  );
  HTML5_ENTITIES.put(
   "ii;",
   "\\u2148"
  );
  HTML5_ENTITIES.put(
   "iiiint;",
   "\\u2a0c"
  );
  HTML5_ENTITIES.put(
   "iiint;",
   "\\u222d"
  );
  HTML5_ENTITIES.put(
   "iinfin;",
   "\\u29dc"
  );
  HTML5_ENTITIES.put(
   "iiota;",
   "\\u2129"
  );
  HTML5_ENTITIES.put(
   "IJlig;",
   "\\u0132"
  );
  HTML5_ENTITIES.put(
   "ijlig;",
   "\\u0133"
  );
  HTML5_ENTITIES.put(
   "Im;",
   "\\u2111"
  );
  HTML5_ENTITIES.put(
   "Imacr;",
   "\\u012a"
  );
  HTML5_ENTITIES.put(
   "imacr;",
   "\\u012b"
  );
  HTML5_ENTITIES.put(
   "image;",
   "\\u2111"
  );
  HTML5_ENTITIES.put(
   "ImaginaryI;",
   "\\u2148"
  );
  HTML5_ENTITIES.put(
   "imagline;",
   "\\u2110"
  );
  HTML5_ENTITIES.put(
   "imagpart;",
   "\\u2111"
  );
  HTML5_ENTITIES.put(
   "imath;",
   "\\u0131"
  );
  HTML5_ENTITIES.put(
   "imof;",
   "\\u22b7"
  );
  HTML5_ENTITIES.put(
   "imped;",
   "\\u01b5"
  );
  HTML5_ENTITIES.put(
   "Implies;",
   "\\u21d2"
  );
  HTML5_ENTITIES.put(
   "in;",
   "\\u2208"
  );
  HTML5_ENTITIES.put(
   "incare;",
   "\\u2105"
  );
  HTML5_ENTITIES.put(
   "infin;",
   "\\u221e"
  );
  HTML5_ENTITIES.put(
   "infintie;",
   "\\u29dd"
  );
  HTML5_ENTITIES.put(
   "inodot;",
   "\\u0131"
  );
  HTML5_ENTITIES.put(
   "Int;",
   "\\u222c"
  );
  HTML5_ENTITIES.put(
   "int;",
   "\\u222b"
  );
  HTML5_ENTITIES.put(
   "intcal;",
   "\\u22ba"
  );
  HTML5_ENTITIES.put(
   "integers;",
   "\\u2124"
  );
  HTML5_ENTITIES.put(
   "Integral;",
   "\\u222b"
  );
  HTML5_ENTITIES.put(
   "intercal;",
   "\\u22ba"
  );
  HTML5_ENTITIES.put(
   "Intersection;",
   "\\u22c2"
  );
  HTML5_ENTITIES.put(
   "intlarhk;",
   "\\u2a17"
  );
  HTML5_ENTITIES.put(
   "intprod;",
   "\\u2a3c"
  );
  HTML5_ENTITIES.put(
   "InvisibleComma;",
   "\\u2063"
  );
  HTML5_ENTITIES.put(
   "InvisibleTimes;",
   "\\u2062"
  );
  HTML5_ENTITIES.put(
   "IOcy;",
   "\\u0401"
  );
  HTML5_ENTITIES.put(
   "iocy;",
   "\\u0451"
  );
  HTML5_ENTITIES.put(
   "Iogon;",
   "\\u012e"
  );
  HTML5_ENTITIES.put(
   "iogon;",
   "\\u012f"
  );
  HTML5_ENTITIES.put(
   "Iopf;",
   "\\U0001d540"
  );
  HTML5_ENTITIES.put(
   "iopf;",
   "\\U0001d55a"
  );
  HTML5_ENTITIES.put(
   "Iota;",
   "\\u0399"
  );
  HTML5_ENTITIES.put(
   "iota;",
   "\\u03b9"
  );
  HTML5_ENTITIES.put(
   "iprod;",
   "\\u2a3c"
  );
  HTML5_ENTITIES.put(
   "iquest",
   "\\xbf"
  );
  HTML5_ENTITIES.put(
   "iquest;",
   "\\xbf"
  );
  HTML5_ENTITIES.put(
   "Iscr;",
   "\\u2110"
  );
  HTML5_ENTITIES.put(
   "iscr;",
   "\\U0001d4be"
  );
  HTML5_ENTITIES.put(
   "isin;",
   "\\u2208"
  );
  HTML5_ENTITIES.put(
   "isindot;",
   "\\u22f5"
  );
  HTML5_ENTITIES.put(
   "isinE;",
   "\\u22f9"
  );
  HTML5_ENTITIES.put(
   "isins;",
   "\\u22f4"
  );
  HTML5_ENTITIES.put(
   "isinsv;",
   "\\u22f3"
  );
  HTML5_ENTITIES.put(
   "isinv;",
   "\\u2208"
  );
  HTML5_ENTITIES.put(
   "it;",
   "\\u2062"
  );
  HTML5_ENTITIES.put(
   "Itilde;",
   "\\u0128"
  );
  HTML5_ENTITIES.put(
   "itilde;",
   "\\u0129"
  );
  HTML5_ENTITIES.put(
   "Iukcy;",
   "\\u0406"
  );
  HTML5_ENTITIES.put(
   "iukcy;",
   "\\u0456"
  );
  HTML5_ENTITIES.put(
   "Iuml",
   "\\xcf"
  );
  HTML5_ENTITIES.put(
   "iuml",
   "\\xef"
  );
  HTML5_ENTITIES.put(
   "Iuml;",
   "\\xcf"
  );
  HTML5_ENTITIES.put(
   "iuml;",
   "\\xef"
  );
  HTML5_ENTITIES.put(
   "Jcirc;",
   "\\u0134"
  );
  HTML5_ENTITIES.put(
   "jcirc;",
   "\\u0135"
  );
  HTML5_ENTITIES.put(
   "Jcy;",
   "\\u0419"
  );
  HTML5_ENTITIES.put(
   "jcy;",
   "\\u0439"
  );
  HTML5_ENTITIES.put(
   "Jfr;",
   "\\U0001d50d"
  );
  HTML5_ENTITIES.put(
   "jfr;",
   "\\U0001d527"
  );
  HTML5_ENTITIES.put(
   "jmath;",
   "\\u0237"
  );
  HTML5_ENTITIES.put(
   "Jopf;",
   "\\U0001d541"
  );
  HTML5_ENTITIES.put(
   "jopf;",
   "\\U0001d55b"
  );
  HTML5_ENTITIES.put(
   "Jscr;",
   "\\U0001d4a5"
  );
  HTML5_ENTITIES.put(
   "jscr;",
   "\\U0001d4bf"
  );
  HTML5_ENTITIES.put(
   "Jsercy;",
   "\\u0408"
  );
  HTML5_ENTITIES.put(
   "jsercy;",
   "\\u0458"
  );
  HTML5_ENTITIES.put(
   "Jukcy;",
   "\\u0404"
  );
  HTML5_ENTITIES.put(
   "jukcy;",
   "\\u0454"
  );
  HTML5_ENTITIES.put(
   "Kappa;",
   "\\u039a"
  );
  HTML5_ENTITIES.put(
   "kappa;",
   "\\u03ba"
  );
  HTML5_ENTITIES.put(
   "kappav;",
   "\\u03f0"
  );
  HTML5_ENTITIES.put(
   "Kcedil;",
   "\\u0136"
  );
  HTML5_ENTITIES.put(
   "kcedil;",
   "\\u0137"
  );
  HTML5_ENTITIES.put(
   "Kcy;",
   "\\u041a"
  );
  HTML5_ENTITIES.put(
   "kcy;",
   "\\u043a"
  );
  HTML5_ENTITIES.put(
   "Kfr;",
   "\\U0001d50e"
  );
  HTML5_ENTITIES.put(
   "kfr;",
   "\\U0001d528"
  );
  HTML5_ENTITIES.put(
   "kgreen;",
   "\\u0138"
  );
  HTML5_ENTITIES.put(
   "KHcy;",
   "\\u0425"
  );
  HTML5_ENTITIES.put(
   "khcy;",
   "\\u0445"
  );
  HTML5_ENTITIES.put(
   "KJcy;",
   "\\u040c"
  );
  HTML5_ENTITIES.put(
   "kjcy;",
   "\\u045c"
  );
  HTML5_ENTITIES.put(
   "Kopf;",
   "\\U0001d542"
  );
  HTML5_ENTITIES.put(
   "kopf;",
   "\\U0001d55c"
  );
  HTML5_ENTITIES.put(
   "Kscr;",
   "\\U0001d4a6"
  );
  HTML5_ENTITIES.put(
   "kscr;",
   "\\U0001d4c0"
  );
  HTML5_ENTITIES.put(
   "lAarr;",
   "\\u21da"
  );
  HTML5_ENTITIES.put(
   "Lacute;",
   "\\u0139"
  );
  HTML5_ENTITIES.put(
   "lacute;",
   "\\u013a"
  );
  HTML5_ENTITIES.put(
   "laemptyv;",
   "\\u29b4"
  );
  HTML5_ENTITIES.put(
   "lagran;",
   "\\u2112"
  );
  HTML5_ENTITIES.put(
   "Lambda;",
   "\\u039b"
  );
  HTML5_ENTITIES.put(
   "lambda;",
   "\\u03bb"
  );
  HTML5_ENTITIES.put(
   "Lang;",
   "\\u27ea"
  );
  HTML5_ENTITIES.put(
   "lang;",
   "\\u27e8"
  );
  HTML5_ENTITIES.put(
   "langd;",
   "\\u2991"
  );
  HTML5_ENTITIES.put(
   "langle;",
   "\\u27e8"
  );
  HTML5_ENTITIES.put(
   "lap;",
   "\\u2a85"
  );
  HTML5_ENTITIES.put(
   "Laplacetrf;",
   "\\u2112"
  );
  HTML5_ENTITIES.put(
   "laquo",
   "\\xab"
  );
  HTML5_ENTITIES.put(
   "laquo;",
   "\\xab"
  );
  HTML5_ENTITIES.put(
   "Larr;",
   "\\u219e"
  );
  HTML5_ENTITIES.put(
   "lArr;",
   "\\u21d0"
  );
  HTML5_ENTITIES.put(
   "larr;",
   "\\u2190"
  );
  HTML5_ENTITIES.put(
   "larrb;",
   "\\u21e4"
  );
  HTML5_ENTITIES.put(
   "larrbfs;",
   "\\u291f"
  );
  HTML5_ENTITIES.put(
   "larrfs;",
   "\\u291d"
  );
  HTML5_ENTITIES.put(
   "larrhk;",
   "\\u21a9"
  );
  HTML5_ENTITIES.put(
   "larrlp;",
   "\\u21ab"
  );
  HTML5_ENTITIES.put(
   "larrpl;",
   "\\u2939"
  );
  HTML5_ENTITIES.put(
   "larrsim;",
   "\\u2973"
  );
  HTML5_ENTITIES.put(
   "larrtl;",
   "\\u21a2"
  );
  HTML5_ENTITIES.put(
   "lat;",
   "\\u2aab"
  );
  HTML5_ENTITIES.put(
   "lAtail;",
   "\\u291b"
  );
  HTML5_ENTITIES.put(
   "latail;",
   "\\u2919"
  );
  HTML5_ENTITIES.put(
   "late;",
   "\\u2aad"
  );
  HTML5_ENTITIES.put(
   "lates;",
   "\\u2aad\\ufe00"
  );
  HTML5_ENTITIES.put(
   "lBarr;",
   "\\u290e"
  );
  HTML5_ENTITIES.put(
   "lbarr;",
   "\\u290c"
  );
  HTML5_ENTITIES.put(
   "lbbrk;",
   "\\u2772"
  );
  HTML5_ENTITIES.put(
   "lbrace;",
   "{"
  );
  HTML5_ENTITIES.put(
   "lbrack;",
   "["
  );
  HTML5_ENTITIES.put(
   "lbrke;",
   "\\u298b"
  );
  HTML5_ENTITIES.put(
   "lbrksld;",
   "\\u298f"
  );
  HTML5_ENTITIES.put(
   "lbrkslu;",
   "\\u298d"
  );
  HTML5_ENTITIES.put(
   "Lcaron;",
   "\\u013d"
  );
  HTML5_ENTITIES.put(
   "lcaron;",
   "\\u013e"
  );
  HTML5_ENTITIES.put(
   "Lcedil;",
   "\\u013b"
  );
  HTML5_ENTITIES.put(
   "lcedil;",
   "\\u013c"
  );
  HTML5_ENTITIES.put(
   "lceil;",
   "\\u2308"
  );
  HTML5_ENTITIES.put(
   "lcub;",
   "{"
  );
  HTML5_ENTITIES.put(
   "Lcy;",
   "\\u041b"
  );
  HTML5_ENTITIES.put(
   "lcy;",
   "\\u043b"
  );
  HTML5_ENTITIES.put(
   "ldca;",
   "\\u2936"
  );
  HTML5_ENTITIES.put(
   "ldquo;",
   "\\u201c"
  );
  HTML5_ENTITIES.put(
   "ldquor;",
   "\\u201e"
  );
  HTML5_ENTITIES.put(
   "ldrdhar;",
   "\\u2967"
  );
  HTML5_ENTITIES.put(
   "ldrushar;",
   "\\u294b"
  );
  HTML5_ENTITIES.put(
   "ldsh;",
   "\\u21b2"
  );
  HTML5_ENTITIES.put(
   "lE;",
   "\\u2266"
  );
  HTML5_ENTITIES.put(
   "le;",
   "\\u2264"
  );
  HTML5_ENTITIES.put(
   "LeftAngleBracket;",
   "\\u27e8"
  );
  HTML5_ENTITIES.put(
   "LeftArrow;",
   "\\u2190"
  );
  HTML5_ENTITIES.put(
   "Leftarrow;",
   "\\u21d0"
  );
  HTML5_ENTITIES.put(
   "leftarrow;",
   "\\u2190"
  );
  HTML5_ENTITIES.put(
   "LeftArrowBar;",
   "\\u21e4"
  );
  HTML5_ENTITIES.put(
   "LeftArrowRightArrow;",
   "\\u21c6"
  );
  HTML5_ENTITIES.put(
   "leftarrowtail;",
   "\\u21a2"
  );
  HTML5_ENTITIES.put(
   "LeftCeiling;",
   "\\u2308"
  );
  HTML5_ENTITIES.put(
   "LeftDoubleBracket;",
   "\\u27e6"
  );
  HTML5_ENTITIES.put(
   "LeftDownTeeVector;",
   "\\u2961"
  );
  HTML5_ENTITIES.put(
   "LeftDownVector;",
   "\\u21c3"
  );
  HTML5_ENTITIES.put(
   "LeftDownVectorBar;",
   "\\u2959"
  );
  HTML5_ENTITIES.put(
   "LeftFloor;",
   "\\u230a"
  );
  HTML5_ENTITIES.put(
   "leftharpoondown;",
   "\\u21bd"
  );
  HTML5_ENTITIES.put(
   "leftharpoonup;",
   "\\u21bc"
  );
  HTML5_ENTITIES.put(
   "leftleftarrows;",
   "\\u21c7"
  );
  HTML5_ENTITIES.put(
   "LeftRightArrow;",
   "\\u2194"
  );
  HTML5_ENTITIES.put(
   "Leftrightarrow;",
   "\\u21d4"
  );
  HTML5_ENTITIES.put(
   "leftrightarrow;",
   "\\u2194"
  );
  HTML5_ENTITIES.put(
   "leftrightarrows;",
   "\\u21c6"
  );
  HTML5_ENTITIES.put(
   "leftrightharpoons;",
   "\\u21cb"
  );
  HTML5_ENTITIES.put(
   "leftrightsquigarrow;",
   "\\u21ad"
  );
  HTML5_ENTITIES.put(
   "LeftRightVector;",
   "\\u294e"
  );
  HTML5_ENTITIES.put(
   "LeftTee;",
   "\\u22a3"
  );
  HTML5_ENTITIES.put(
   "LeftTeeArrow;",
   "\\u21a4"
  );
  HTML5_ENTITIES.put(
   "LeftTeeVector;",
   "\\u295a"
  );
  HTML5_ENTITIES.put(
   "leftthreetimes;",
   "\\u22cb"
  );
  HTML5_ENTITIES.put(
   "LeftTriangle;",
   "\\u22b2"
  );
  HTML5_ENTITIES.put(
   "LeftTriangleBar;",
   "\\u29cf"
  );
  HTML5_ENTITIES.put(
   "LeftTriangleEqual;",
   "\\u22b4"
  );
  HTML5_ENTITIES.put(
   "LeftUpDownVector;",
   "\\u2951"
  );
  HTML5_ENTITIES.put(
   "LeftUpTeeVector;",
   "\\u2960"
  );
  HTML5_ENTITIES.put(
   "LeftUpVector;",
   "\\u21bf"
  );
  HTML5_ENTITIES.put(
   "LeftUpVectorBar;",
   "\\u2958"
  );
  HTML5_ENTITIES.put(
   "LeftVector;",
   "\\u21bc"
  );
  HTML5_ENTITIES.put(
   "LeftVectorBar;",
   "\\u2952"
  );
  HTML5_ENTITIES.put(
   "lEg;",
   "\\u2a8b"
  );
  HTML5_ENTITIES.put(
   "leg;",
   "\\u22da"
  );
  HTML5_ENTITIES.put(
   "leq;",
   "\\u2264"
  );
  HTML5_ENTITIES.put(
   "leqq;",
   "\\u2266"
  );
  HTML5_ENTITIES.put(
   "leqslant;",
   "\\u2a7d"
  );
  HTML5_ENTITIES.put(
   "les;",
   "\\u2a7d"
  );
  HTML5_ENTITIES.put(
   "lescc;",
   "\\u2aa8"
  );
  HTML5_ENTITIES.put(
   "lesdot;",
   "\\u2a7f"
  );
  HTML5_ENTITIES.put(
   "lesdoto;",
   "\\u2a81"
  );
  HTML5_ENTITIES.put(
   "lesdotor;",
   "\\u2a83"
  );
  HTML5_ENTITIES.put(
   "lesg;",
   "\\u22da\\ufe00"
  );
  HTML5_ENTITIES.put(
   "lesges;",
   "\\u2a93"
  );
  HTML5_ENTITIES.put(
   "lessapprox;",
   "\\u2a85"
  );
  HTML5_ENTITIES.put(
   "lessdot;",
   "\\u22d6"
  );
  HTML5_ENTITIES.put(
   "lesseqgtr;",
   "\\u22da"
  );
  HTML5_ENTITIES.put(
   "lesseqqgtr;",
   "\\u2a8b"
  );
  HTML5_ENTITIES.put(
   "LessEqualGreater;",
   "\\u22da"
  );
  HTML5_ENTITIES.put(
   "LessFullEqual;",
   "\\u2266"
  );
  HTML5_ENTITIES.put(
   "LessGreater;",
   "\\u2276"
  );
  HTML5_ENTITIES.put(
   "lessgtr;",
   "\\u2276"
  );
  HTML5_ENTITIES.put(
   "LessLess;",
   "\\u2aa1"
  );
  HTML5_ENTITIES.put(
   "lesssim;",
   "\\u2272"
  );
  HTML5_ENTITIES.put(
   "LessSlantEqual;",
   "\\u2a7d"
  );
  HTML5_ENTITIES.put(
   "LessTilde;",
   "\\u2272"
  );
  HTML5_ENTITIES.put(
   "lfisht;",
   "\\u297c"
  );
  HTML5_ENTITIES.put(
   "lfloor;",
   "\\u230a"
  );
  HTML5_ENTITIES.put(
   "Lfr;",
   "\\U0001d50f"
  );
  HTML5_ENTITIES.put(
   "lfr;",
   "\\U0001d529"
  );
  HTML5_ENTITIES.put(
   "lg;",
   "\\u2276"
  );
  HTML5_ENTITIES.put(
   "lgE;",
   "\\u2a91"
  );
  HTML5_ENTITIES.put(
   "lHar;",
   "\\u2962"
  );
  HTML5_ENTITIES.put(
   "lhard;",
   "\\u21bd"
  );
  HTML5_ENTITIES.put(
   "lharu;",
   "\\u21bc"
  );
  HTML5_ENTITIES.put(
   "lharul;",
   "\\u296a"
  );
  HTML5_ENTITIES.put(
   "lhblk;",
   "\\u2584"
  );
  HTML5_ENTITIES.put(
   "LJcy;",
   "\\u0409"
  );
  HTML5_ENTITIES.put(
   "ljcy;",
   "\\u0459"
  );
  HTML5_ENTITIES.put(
   "Ll;",
   "\\u22d8"
  );
  HTML5_ENTITIES.put(
   "ll;",
   "\\u226a"
  );
  HTML5_ENTITIES.put(
   "llarr;",
   "\\u21c7"
  );
  HTML5_ENTITIES.put(
   "llcorner;",
   "\\u231e"
  );
  HTML5_ENTITIES.put(
   "Lleftarrow;",
   "\\u21da"
  );
  HTML5_ENTITIES.put(
   "llhard;",
   "\\u296b"
  );
  HTML5_ENTITIES.put(
   "lltri;",
   "\\u25fa"
  );
  HTML5_ENTITIES.put(
   "Lmidot;",
   "\\u013f"
  );
  HTML5_ENTITIES.put(
   "lmidot;",
   "\\u0140"
  );
  HTML5_ENTITIES.put(
   "lmoust;",
   "\\u23b0"
  );
  HTML5_ENTITIES.put(
   "lmoustache;",
   "\\u23b0"
  );
  HTML5_ENTITIES.put(
   "lnap;",
   "\\u2a89"
  );
  HTML5_ENTITIES.put(
   "lnapprox;",
   "\\u2a89"
  );
  HTML5_ENTITIES.put(
   "lnE;",
   "\\u2268"
  );
  HTML5_ENTITIES.put(
   "lne;",
   "\\u2a87"
  );
  HTML5_ENTITIES.put(
   "lneq;",
   "\\u2a87"
  );
  HTML5_ENTITIES.put(
   "lneqq;",
   "\\u2268"
  );
  HTML5_ENTITIES.put(
   "lnsim;",
   "\\u22e6"
  );
  HTML5_ENTITIES.put(
   "loang;",
   "\\u27ec"
  );
  HTML5_ENTITIES.put(
   "loarr;",
   "\\u21fd"
  );
  HTML5_ENTITIES.put(
   "lobrk;",
   "\\u27e6"
  );
  HTML5_ENTITIES.put(
   "LongLeftArrow;",
   "\\u27f5"
  );
  HTML5_ENTITIES.put(
   "Longleftarrow;",
   "\\u27f8"
  );
  HTML5_ENTITIES.put(
   "longleftarrow;",
   "\\u27f5"
  );
  HTML5_ENTITIES.put(
   "LongLeftRightArrow;",
   "\\u27f7"
  );
  HTML5_ENTITIES.put(
   "Longleftrightarrow;",
   "\\u27fa"
  );
  HTML5_ENTITIES.put(
   "longleftrightarrow;",
   "\\u27f7"
  );
  HTML5_ENTITIES.put(
   "longmapsto;",
   "\\u27fc"
  );
  HTML5_ENTITIES.put(
   "LongRightArrow;",
   "\\u27f6"
  );
  HTML5_ENTITIES.put(
   "Longrightarrow;",
   "\\u27f9"
  );
  HTML5_ENTITIES.put(
   "longrightarrow;",
   "\\u27f6"
  );
  HTML5_ENTITIES.put(
   "looparrowleft;",
   "\\u21ab"
  );
  HTML5_ENTITIES.put(
   "looparrowright;",
   "\\u21ac"
  );
  HTML5_ENTITIES.put(
   "lopar;",
   "\\u2985"
  );
  HTML5_ENTITIES.put(
   "Lopf;",
   "\\U0001d543"
  );
  HTML5_ENTITIES.put(
   "lopf;",
   "\\U0001d55d"
  );
  HTML5_ENTITIES.put(
   "loplus;",
   "\\u2a2d"
  );
  HTML5_ENTITIES.put(
   "lotimes;",
   "\\u2a34"
  );
  HTML5_ENTITIES.put(
   "lowast;",
   "\\u2217"
  );
  HTML5_ENTITIES.put(
   "lowbar;",
   "_"
  );
  HTML5_ENTITIES.put(
   "LowerLeftArrow;",
   "\\u2199"
  );
  HTML5_ENTITIES.put(
   "LowerRightArrow;",
   "\\u2198"
  );
  HTML5_ENTITIES.put(
   "loz;",
   "\\u25ca"
  );
  HTML5_ENTITIES.put(
   "lozenge;",
   "\\u25ca"
  );
  HTML5_ENTITIES.put(
   "lozf;",
   "\\u29eb"
  );
  HTML5_ENTITIES.put(
   "lpar;",
   "("
  );
  HTML5_ENTITIES.put(
   "lparlt;",
   "\\u2993"
  );
  HTML5_ENTITIES.put(
   "lrarr;",
   "\\u21c6"
  );
  HTML5_ENTITIES.put(
   "lrcorner;",
   "\\u231f"
  );
  HTML5_ENTITIES.put(
   "lrhar;",
   "\\u21cb"
  );
  HTML5_ENTITIES.put(
   "lrhard;",
   "\\u296d"
  );
  HTML5_ENTITIES.put(
   "lrm;",
   "\\u200e"
  );
  HTML5_ENTITIES.put(
   "lrtri;",
   "\\u22bf"
  );
  HTML5_ENTITIES.put(
   "lsaquo;",
   "\\u2039"
  );
  HTML5_ENTITIES.put(
   "Lscr;",
   "\\u2112"
  );
  HTML5_ENTITIES.put(
   "lscr;",
   "\\U0001d4c1"
  );
  HTML5_ENTITIES.put(
   "Lsh;",
   "\\u21b0"
  );
  HTML5_ENTITIES.put(
   "lsh;",
   "\\u21b0"
  );
  HTML5_ENTITIES.put(
   "lsim;",
   "\\u2272"
  );
  HTML5_ENTITIES.put(
   "lsime;",
   "\\u2a8d"
  );
  HTML5_ENTITIES.put(
   "lsimg;",
   "\\u2a8f"
  );
  HTML5_ENTITIES.put(
   "lsqb;",
   "["
  );
  HTML5_ENTITIES.put(
   "lsquo;",
   "\\u2018"
  );
  HTML5_ENTITIES.put(
   "lsquor;",
   "\\u201a"
  );
  HTML5_ENTITIES.put(
   "Lstrok;",
   "\\u0141"
  );
  HTML5_ENTITIES.put(
   "lstrok;",
   "\\u0142"
  );
  HTML5_ENTITIES.put(
   "LT",
   "<"
  );
  HTML5_ENTITIES.put(
   "lt",
   "<"
  );
  HTML5_ENTITIES.put(
   "LT;",
   "<"
  );
  HTML5_ENTITIES.put(
   "Lt;",
   "\\u226a"
  );
  HTML5_ENTITIES.put(
   "lt;",
   "<"
  );
  HTML5_ENTITIES.put(
   "ltcc;",
   "\\u2aa6"
  );
  HTML5_ENTITIES.put(
   "ltcir;",
   "\\u2a79"
  );
  HTML5_ENTITIES.put(
   "ltdot;",
   "\\u22d6"
  );
  HTML5_ENTITIES.put(
   "lthree;",
   "\\u22cb"
  );
  HTML5_ENTITIES.put(
   "ltimes;",
   "\\u22c9"
  );
  HTML5_ENTITIES.put(
   "ltlarr;",
   "\\u2976"
  );
  HTML5_ENTITIES.put(
   "ltquest;",
   "\\u2a7b"
  );
  HTML5_ENTITIES.put(
   "ltri;",
   "\\u25c3"
  );
  HTML5_ENTITIES.put(
   "ltrie;",
   "\\u22b4"
  );
  HTML5_ENTITIES.put(
   "ltrif;",
   "\\u25c2"
  );
  HTML5_ENTITIES.put(
   "ltrPar;",
   "\\u2996"
  );
  HTML5_ENTITIES.put(
   "lurdshar;",
   "\\u294a"
  );
  HTML5_ENTITIES.put(
   "luruhar;",
   "\\u2966"
  );
  HTML5_ENTITIES.put(
   "lvertneqq;",
   "\\u2268\\ufe00"
  );
  HTML5_ENTITIES.put(
   "lvnE;",
   "\\u2268\\ufe00"
  );
  HTML5_ENTITIES.put(
   "macr",
   "\\xaf"
  );
  HTML5_ENTITIES.put(
   "macr;",
   "\\xaf"
  );
  HTML5_ENTITIES.put(
   "male;",
   "\\u2642"
  );
  HTML5_ENTITIES.put(
   "malt;",
   "\\u2720"
  );
  HTML5_ENTITIES.put(
   "maltese;",
   "\\u2720"
  );
  HTML5_ENTITIES.put(
   "Map;",
   "\\u2905"
  );
  HTML5_ENTITIES.put(
   "map;",
   "\\u21a6"
  );
  HTML5_ENTITIES.put(
   "mapsto;",
   "\\u21a6"
  );
  HTML5_ENTITIES.put(
   "mapstodown;",
   "\\u21a7"
  );
  HTML5_ENTITIES.put(
   "mapstoleft;",
   "\\u21a4"
  );
  HTML5_ENTITIES.put(
   "mapstoup;",
   "\\u21a5"
  );
  HTML5_ENTITIES.put(
   "marker;",
   "\\u25ae"
  );
  HTML5_ENTITIES.put(
   "mcomma;",
   "\\u2a29"
  );
  HTML5_ENTITIES.put(
   "Mcy;",
   "\\u041c"
  );
  HTML5_ENTITIES.put(
   "mcy;",
   "\\u043c"
  );
  HTML5_ENTITIES.put(
   "mdash;",
   "\\u2014"
  );
  HTML5_ENTITIES.put(
   "mDDot;",
   "\\u223a"
  );
  HTML5_ENTITIES.put(
   "measuredangle;",
   "\\u2221"
  );
  HTML5_ENTITIES.put(
   "MediumSpace;",
   "\\u205f"
  );
  HTML5_ENTITIES.put(
   "Mellintrf;",
   "\\u2133"
  );
  HTML5_ENTITIES.put(
   "Mfr;",
   "\\U0001d510"
  );
  HTML5_ENTITIES.put(
   "mfr;",
   "\\U0001d52a"
  );
  HTML5_ENTITIES.put(
   "mho;",
   "\\u2127"
  );
  HTML5_ENTITIES.put(
   "micro",
   "\\xb5"
  );
  HTML5_ENTITIES.put(
   "micro;",
   "\\xb5"
  );
  HTML5_ENTITIES.put(
   "mid;",
   "\\u2223"
  );
  HTML5_ENTITIES.put(
   "midast;",
   "*"
  );
  HTML5_ENTITIES.put(
   "midcir;",
   "\\u2af0"
  );
  HTML5_ENTITIES.put(
   "middot",
   "\\xb7"
  );
  HTML5_ENTITIES.put(
   "middot;",
   "\\xb7"
  );
  HTML5_ENTITIES.put(
   "minus;",
   "\\u2212"
  );
  HTML5_ENTITIES.put(
   "minusb;",
   "\\u229f"
  );
  HTML5_ENTITIES.put(
   "minusd;",
   "\\u2238"
  );
  HTML5_ENTITIES.put(
   "minusdu;",
   "\\u2a2a"
  );
  HTML5_ENTITIES.put(
   "MinusPlus;",
   "\\u2213"
  );
  HTML5_ENTITIES.put(
   "mlcp;",
   "\\u2adb"
  );
  HTML5_ENTITIES.put(
   "mldr;",
   "\\u2026"
  );
  HTML5_ENTITIES.put(
   "mnplus;",
   "\\u2213"
  );
  HTML5_ENTITIES.put(
   "models;",
   "\\u22a7"
  );
  HTML5_ENTITIES.put(
   "Mopf;",
   "\\U0001d544"
  );
  HTML5_ENTITIES.put(
   "mopf;",
   "\\U0001d55e"
  );
  HTML5_ENTITIES.put(
   "mp;",
   "\\u2213"
  );
  HTML5_ENTITIES.put(
   "Mscr;",
   "\\u2133"
  );
  HTML5_ENTITIES.put(
   "mscr;",
   "\\U0001d4c2"
  );
  HTML5_ENTITIES.put(
   "mstpos;",
   "\\u223e"
  );
  HTML5_ENTITIES.put(
   "Mu;",
   "\\u039c"
  );
  HTML5_ENTITIES.put(
   "mu;",
   "\\u03bc"
  );
  HTML5_ENTITIES.put(
   "multimap;",
   "\\u22b8"
  );
  HTML5_ENTITIES.put(
   "mumap;",
   "\\u22b8"
  );
  HTML5_ENTITIES.put(
   "nabla;",
   "\\u2207"
  );
  HTML5_ENTITIES.put(
   "Nacute;",
   "\\u0143"
  );
  HTML5_ENTITIES.put(
   "nacute;",
   "\\u0144"
  );
  HTML5_ENTITIES.put(
   "nang;",
   "\\u2220\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nap;",
   "\\u2249"
  );
  HTML5_ENTITIES.put(
   "napE;",
   "\\u2a70\\u0338"
  );
  HTML5_ENTITIES.put(
   "napid;",
   "\\u224b\\u0338"
  );
  HTML5_ENTITIES.put(
   "napos;",
   "\\u0149"
  );
  HTML5_ENTITIES.put(
   "napprox;",
   "\\u2249"
  );
  HTML5_ENTITIES.put(
   "natur;",
   "\\u266e"
  );
  HTML5_ENTITIES.put(
   "natural;",
   "\\u266e"
  );
  HTML5_ENTITIES.put(
   "naturals;",
   "\\u2115"
  );
  HTML5_ENTITIES.put(
   "nbsp",
   "\\xa0"
  );
  HTML5_ENTITIES.put(
   "nbsp;",
   "\\xa0"
  );
  HTML5_ENTITIES.put(
   "nbump;",
   "\\u224e\\u0338"
  );
  HTML5_ENTITIES.put(
   "nbumpe;",
   "\\u224f\\u0338"
  );
  HTML5_ENTITIES.put(
   "ncap;",
   "\\u2a43"
  );
  HTML5_ENTITIES.put(
   "Ncaron;",
   "\\u0147"
  );
  HTML5_ENTITIES.put(
   "ncaron;",
   "\\u0148"
  );
  HTML5_ENTITIES.put(
   "Ncedil;",
   "\\u0145"
  );
  HTML5_ENTITIES.put(
   "ncedil;",
   "\\u0146"
  );
  HTML5_ENTITIES.put(
   "ncong;",
   "\\u2247"
  );
  HTML5_ENTITIES.put(
   "ncongdot;",
   "\\u2a6d\\u0338"
  );
  HTML5_ENTITIES.put(
   "ncup;",
   "\\u2a42"
  );
  HTML5_ENTITIES.put(
   "Ncy;",
   "\\u041d"
  );
  HTML5_ENTITIES.put(
   "ncy;",
   "\\u043d"
  );
  HTML5_ENTITIES.put(
   "ndash;",
   "\\u2013"
  );
  HTML5_ENTITIES.put(
   "ne;",
   "\\u2260"
  );
  HTML5_ENTITIES.put(
   "nearhk;",
   "\\u2924"
  );
  HTML5_ENTITIES.put(
   "neArr;",
   "\\u21d7"
  );
  HTML5_ENTITIES.put(
   "nearr;",
   "\\u2197"
  );
  HTML5_ENTITIES.put(
   "nearrow;",
   "\\u2197"
  );
  HTML5_ENTITIES.put(
   "nedot;",
   "\\u2250\\u0338"
  );
  HTML5_ENTITIES.put(
   "NegativeMediumSpace;",
   "\\u200b"
  );
  HTML5_ENTITIES.put(
   "NegativeThickSpace;",
   "\\u200b"
  );
  HTML5_ENTITIES.put(
   "NegativeThinSpace;",
   "\\u200b"
  );
  HTML5_ENTITIES.put(
   "NegativeVeryThinSpace;",
   "\\u200b"
  );
  HTML5_ENTITIES.put(
   "nequiv;",
   "\\u2262"
  );
  HTML5_ENTITIES.put(
   "nesear;",
   "\\u2928"
  );
  HTML5_ENTITIES.put(
   "nesim;",
   "\\u2242\\u0338"
  );
  HTML5_ENTITIES.put(
   "NestedGreaterGreater;",
   "\\u226b"
  );
  HTML5_ENTITIES.put(
   "NestedLessLess;",
   "\\u226a"
  );
  HTML5_ENTITIES.put(
   "NewLine;",
   "\\n"
  );
  HTML5_ENTITIES.put(
   "nexist;",
   "\\u2204"
  );
  HTML5_ENTITIES.put(
   "nexists;",
   "\\u2204"
  );
  HTML5_ENTITIES.put(
   "Nfr;",
   "\\U0001d511"
  );
  HTML5_ENTITIES.put(
   "nfr;",
   "\\U0001d52b"
  );
  HTML5_ENTITIES.put(
   "ngE;",
   "\\u2267\\u0338"
  );
  HTML5_ENTITIES.put(
   "nge;",
   "\\u2271"
  );
  HTML5_ENTITIES.put(
   "ngeq;",
   "\\u2271"
  );
  HTML5_ENTITIES.put(
   "ngeqq;",
   "\\u2267\\u0338"
  );
  HTML5_ENTITIES.put(
   "ngeqslant;",
   "\\u2a7e\\u0338"
  );
  HTML5_ENTITIES.put(
   "nges;",
   "\\u2a7e\\u0338"
  );
  HTML5_ENTITIES.put(
   "nGg;",
   "\\u22d9\\u0338"
  );
  HTML5_ENTITIES.put(
   "ngsim;",
   "\\u2275"
  );
  HTML5_ENTITIES.put(
   "nGt;",
   "\\u226b\\u20d2"
  );
  HTML5_ENTITIES.put(
   "ngt;",
   "\\u226f"
  );
  HTML5_ENTITIES.put(
   "ngtr;",
   "\\u226f"
  );
  HTML5_ENTITIES.put(
   "nGtv;",
   "\\u226b\\u0338"
  );
  HTML5_ENTITIES.put(
   "nhArr;",
   "\\u21ce"
  );
  HTML5_ENTITIES.put(
   "nharr;",
   "\\u21ae"
  );
  HTML5_ENTITIES.put(
   "nhpar;",
   "\\u2af2"
  );
  HTML5_ENTITIES.put(
   "ni;",
   "\\u220b"
  );
  HTML5_ENTITIES.put(
   "nis;",
   "\\u22fc"
  );
  HTML5_ENTITIES.put(
   "nisd;",
   "\\u22fa"
  );
  HTML5_ENTITIES.put(
   "niv;",
   "\\u220b"
  );
  HTML5_ENTITIES.put(
   "NJcy;",
   "\\u040a"
  );
  HTML5_ENTITIES.put(
   "njcy;",
   "\\u045a"
  );
  HTML5_ENTITIES.put(
   "nlArr;",
   "\\u21cd"
  );
  HTML5_ENTITIES.put(
   "nlarr;",
   "\\u219a"
  );
  HTML5_ENTITIES.put(
   "nldr;",
   "\\u2025"
  );
  HTML5_ENTITIES.put(
   "nlE;",
   "\\u2266\\u0338"
  );
  HTML5_ENTITIES.put(
   "nle;",
   "\\u2270"
  );
  HTML5_ENTITIES.put(
   "nLeftarrow;",
   "\\u21cd"
  );
  HTML5_ENTITIES.put(
   "nleftarrow;",
   "\\u219a"
  );
  HTML5_ENTITIES.put(
   "nLeftrightarrow;",
   "\\u21ce"
  );
  HTML5_ENTITIES.put(
   "nleftrightarrow;",
   "\\u21ae"
  );
  HTML5_ENTITIES.put(
   "nleq;",
   "\\u2270"
  );
  HTML5_ENTITIES.put(
   "nleqq;",
   "\\u2266\\u0338"
  );
  HTML5_ENTITIES.put(
   "nleqslant;",
   "\\u2a7d\\u0338"
  );
  HTML5_ENTITIES.put(
   "nles;",
   "\\u2a7d\\u0338"
  );
  HTML5_ENTITIES.put(
   "nless;",
   "\\u226e"
  );
  HTML5_ENTITIES.put(
   "nLl;",
   "\\u22d8\\u0338"
  );
  HTML5_ENTITIES.put(
   "nlsim;",
   "\\u2274"
  );
  HTML5_ENTITIES.put(
   "nLt;",
   "\\u226a\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nlt;",
   "\\u226e"
  );
  HTML5_ENTITIES.put(
   "nltri;",
   "\\u22ea"
  );
  HTML5_ENTITIES.put(
   "nltrie;",
   "\\u22ec"
  );
  HTML5_ENTITIES.put(
   "nLtv;",
   "\\u226a\\u0338"
  );
  HTML5_ENTITIES.put(
   "nmid;",
   "\\u2224"
  );
  HTML5_ENTITIES.put(
   "NoBreak;",
   "\\u2060"
  );
  HTML5_ENTITIES.put(
   "NonBreakingSpace;",
   "\\xa0"
  );
  HTML5_ENTITIES.put(
   "Nopf;",
   "\\u2115"
  );
  HTML5_ENTITIES.put(
   "nopf;",
   "\\U0001d55f"
  );
  HTML5_ENTITIES.put(
   "not",
   "\\xac"
  );
  HTML5_ENTITIES.put(
   "Not;",
   "\\u2aec"
  );
  HTML5_ENTITIES.put(
   "not;",
   "\\xac"
  );
  HTML5_ENTITIES.put(
   "NotCongruent;",
   "\\u2262"
  );
  HTML5_ENTITIES.put(
   "NotCupCap;",
   "\\u226d"
  );
  HTML5_ENTITIES.put(
   "NotDoubleVerticalBar;",
   "\\u2226"
  );
  HTML5_ENTITIES.put(
   "NotElement;",
   "\\u2209"
  );
  HTML5_ENTITIES.put(
   "NotEqual;",
   "\\u2260"
  );
  HTML5_ENTITIES.put(
   "NotEqualTilde;",
   "\\u2242\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotExists;",
   "\\u2204"
  );
  HTML5_ENTITIES.put(
   "NotGreater;",
   "\\u226f"
  );
  HTML5_ENTITIES.put(
   "NotGreaterEqual;",
   "\\u2271"
  );
  HTML5_ENTITIES.put(
   "NotGreaterFullEqual;",
   "\\u2267\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotGreaterGreater;",
   "\\u226b\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotGreaterLess;",
   "\\u2279"
  );
  HTML5_ENTITIES.put(
   "NotGreaterSlantEqual;",
   "\\u2a7e\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotGreaterTilde;",
   "\\u2275"
  );
  HTML5_ENTITIES.put(
   "NotHumpDownHump;",
   "\\u224e\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotHumpEqual;",
   "\\u224f\\u0338"
  );
  HTML5_ENTITIES.put(
   "notin;",
   "\\u2209"
  );
  HTML5_ENTITIES.put(
   "notindot;",
   "\\u22f5\\u0338"
  );
  HTML5_ENTITIES.put(
   "notinE;",
   "\\u22f9\\u0338"
  );
  HTML5_ENTITIES.put(
   "notinva;",
   "\\u2209"
  );
  HTML5_ENTITIES.put(
   "notinvb;",
   "\\u22f7"
  );
  HTML5_ENTITIES.put(
   "notinvc;",
   "\\u22f6"
  );
  HTML5_ENTITIES.put(
   "NotLeftTriangle;",
   "\\u22ea"
  );
  HTML5_ENTITIES.put(
   "NotLeftTriangleBar;",
   "\\u29cf\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotLeftTriangleEqual;",
   "\\u22ec"
  );
  HTML5_ENTITIES.put(
   "NotLess;",
   "\\u226e"
  );
  HTML5_ENTITIES.put(
   "NotLessEqual;",
   "\\u2270"
  );
  HTML5_ENTITIES.put(
   "NotLessGreater;",
   "\\u2278"
  );
  HTML5_ENTITIES.put(
   "NotLessLess;",
   "\\u226a\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotLessSlantEqual;",
   "\\u2a7d\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotLessTilde;",
   "\\u2274"
  );
  HTML5_ENTITIES.put(
   "NotNestedGreaterGreater;",
   "\\u2aa2\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotNestedLessLess;",
   "\\u2aa1\\u0338"
  );
  HTML5_ENTITIES.put(
   "notni;",
   "\\u220c"
  );
  HTML5_ENTITIES.put(
   "notniva;",
   "\\u220c"
  );
  HTML5_ENTITIES.put(
   "notnivb;",
   "\\u22fe"
  );
  HTML5_ENTITIES.put(
   "notnivc;",
   "\\u22fd"
  );
  HTML5_ENTITIES.put(
   "NotPrecedes;",
   "\\u2280"
  );
  HTML5_ENTITIES.put(
   "NotPrecedesEqual;",
   "\\u2aaf\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotPrecedesSlantEqual;",
   "\\u22e0"
  );
  HTML5_ENTITIES.put(
   "NotReverseElement;",
   "\\u220c"
  );
  HTML5_ENTITIES.put(
   "NotRightTriangle;",
   "\\u22eb"
  );
  HTML5_ENTITIES.put(
   "NotRightTriangleBar;",
   "\\u29d0\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotRightTriangleEqual;",
   "\\u22ed"
  );
  HTML5_ENTITIES.put(
   "NotSquareSubset;",
   "\\u228f\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotSquareSubsetEqual;",
   "\\u22e2"
  );
  HTML5_ENTITIES.put(
   "NotSquareSuperset;",
   "\\u2290\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotSquareSupersetEqual;",
   "\\u22e3"
  );
  HTML5_ENTITIES.put(
   "NotSubset;",
   "\\u2282\\u20d2"
  );
  HTML5_ENTITIES.put(
   "NotSubsetEqual;",
   "\\u2288"
  );
  HTML5_ENTITIES.put(
   "NotSucceeds;",
   "\\u2281"
  );
  HTML5_ENTITIES.put(
   "NotSucceedsEqual;",
   "\\u2ab0\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotSucceedsSlantEqual;",
   "\\u22e1"
  );
  HTML5_ENTITIES.put(
   "NotSucceedsTilde;",
   "\\u227f\\u0338"
  );
  HTML5_ENTITIES.put(
   "NotSuperset;",
   "\\u2283\\u20d2"
  );
  HTML5_ENTITIES.put(
   "NotSupersetEqual;",
   "\\u2289"
  );
  HTML5_ENTITIES.put(
   "NotTilde;",
   "\\u2241"
  );
  HTML5_ENTITIES.put(
   "NotTildeEqual;",
   "\\u2244"
  );
  HTML5_ENTITIES.put(
   "NotTildeFullEqual;",
   "\\u2247"
  );
  HTML5_ENTITIES.put(
   "NotTildeTilde;",
   "\\u2249"
  );
  HTML5_ENTITIES.put(
   "NotVerticalBar;",
   "\\u2224"
  );
  HTML5_ENTITIES.put(
   "npar;",
   "\\u2226"
  );
  HTML5_ENTITIES.put(
   "nparallel;",
   "\\u2226"
  );
  HTML5_ENTITIES.put(
   "nparsl;",
   "\\u2afd\\u20e5"
  );
  HTML5_ENTITIES.put(
   "npart;",
   "\\u2202\\u0338"
  );
  HTML5_ENTITIES.put(
   "npolint;",
   "\\u2a14"
  );
  HTML5_ENTITIES.put(
   "npr;",
   "\\u2280"
  );
  HTML5_ENTITIES.put(
   "nprcue;",
   "\\u22e0"
  );
  HTML5_ENTITIES.put(
   "npre;",
   "\\u2aaf\\u0338"
  );
  HTML5_ENTITIES.put(
   "nprec;",
   "\\u2280"
  );
  HTML5_ENTITIES.put(
   "npreceq;",
   "\\u2aaf\\u0338"
  );
  HTML5_ENTITIES.put(
   "nrArr;",
   "\\u21cf"
  );
  HTML5_ENTITIES.put(
   "nrarr;",
   "\\u219b"
  );
  HTML5_ENTITIES.put(
   "nrarrc;",
   "\\u2933\\u0338"
  );
  HTML5_ENTITIES.put(
   "nrarrw;",
   "\\u219d\\u0338"
  );
  HTML5_ENTITIES.put(
   "nRightarrow;",
   "\\u21cf"
  );
  HTML5_ENTITIES.put(
   "nrightarrow;",
   "\\u219b"
  );
  HTML5_ENTITIES.put(
   "nrtri;",
   "\\u22eb"
  );
  HTML5_ENTITIES.put(
   "nrtrie;",
   "\\u22ed"
  );
  HTML5_ENTITIES.put(
   "nsc;",
   "\\u2281"
  );
  HTML5_ENTITIES.put(
   "nsccue;",
   "\\u22e1"
  );
  HTML5_ENTITIES.put(
   "nsce;",
   "\\u2ab0\\u0338"
  );
  HTML5_ENTITIES.put(
   "Nscr;",
   "\\U0001d4a9"
  );
  HTML5_ENTITIES.put(
   "nscr;",
   "\\U0001d4c3"
  );
  HTML5_ENTITIES.put(
   "nshortmid;",
   "\\u2224"
  );
  HTML5_ENTITIES.put(
   "nshortparallel;",
   "\\u2226"
  );
  HTML5_ENTITIES.put(
   "nsim;",
   "\\u2241"
  );
  HTML5_ENTITIES.put(
   "nsime;",
   "\\u2244"
  );
  HTML5_ENTITIES.put(
   "nsimeq;",
   "\\u2244"
  );
  HTML5_ENTITIES.put(
   "nsmid;",
   "\\u2224"
  );
  HTML5_ENTITIES.put(
   "nspar;",
   "\\u2226"
  );
  HTML5_ENTITIES.put(
   "nsqsube;",
   "\\u22e2"
  );
  HTML5_ENTITIES.put(
   "nsqsupe;",
   "\\u22e3"
  );
  HTML5_ENTITIES.put(
   "nsub;",
   "\\u2284"
  );
  HTML5_ENTITIES.put(
   "nsubE;",
   "\\u2ac5\\u0338"
  );
  HTML5_ENTITIES.put(
   "nsube;",
   "\\u2288"
  );
  HTML5_ENTITIES.put(
   "nsubset;",
   "\\u2282\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nsubseteq;",
   "\\u2288"
  );
  HTML5_ENTITIES.put(
   "nsubseteqq;",
   "\\u2ac5\\u0338"
  );
  HTML5_ENTITIES.put(
   "nsucc;",
   "\\u2281"
  );
  HTML5_ENTITIES.put(
   "nsucceq;",
   "\\u2ab0\\u0338"
  );
  HTML5_ENTITIES.put(
   "nsup;",
   "\\u2285"
  );
  HTML5_ENTITIES.put(
   "nsupE;",
   "\\u2ac6\\u0338"
  );
  HTML5_ENTITIES.put(
   "nsupe;",
   "\\u2289"
  );
  HTML5_ENTITIES.put(
   "nsupset;",
   "\\u2283\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nsupseteq;",
   "\\u2289"
  );
  HTML5_ENTITIES.put(
   "nsupseteqq;",
   "\\u2ac6\\u0338"
  );
  HTML5_ENTITIES.put(
   "ntgl;",
   "\\u2279"
  );
  HTML5_ENTITIES.put(
   "Ntilde",
   "\\xd1"
  );
  HTML5_ENTITIES.put(
   "ntilde",
   "\\xf1"
  );
  HTML5_ENTITIES.put(
   "Ntilde;",
   "\\xd1"
  );
  HTML5_ENTITIES.put(
   "ntilde;",
   "\\xf1"
  );
  HTML5_ENTITIES.put(
   "ntlg;",
   "\\u2278"
  );
  HTML5_ENTITIES.put(
   "ntriangleleft;",
   "\\u22ea"
  );
  HTML5_ENTITIES.put(
   "ntrianglelefteq;",
   "\\u22ec"
  );
  HTML5_ENTITIES.put(
   "ntriangleright;",
   "\\u22eb"
  );
  HTML5_ENTITIES.put(
   "ntrianglerighteq;",
   "\\u22ed"
  );
  HTML5_ENTITIES.put(
   "Nu;",
   "\\u039d"
  );
  HTML5_ENTITIES.put(
   "nu;",
   "\\u03bd"
  );
  HTML5_ENTITIES.put(
   "num;",
   "#"
  );
  HTML5_ENTITIES.put(
   "numero;",
   "\\u2116"
  );
  HTML5_ENTITIES.put(
   "numsp;",
   "\\u2007"
  );
  HTML5_ENTITIES.put(
   "nvap;",
   "\\u224d\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nVDash;",
   "\\u22af"
  );
  HTML5_ENTITIES.put(
   "nVdash;",
   "\\u22ae"
  );
  HTML5_ENTITIES.put(
   "nvDash;",
   "\\u22ad"
  );
  HTML5_ENTITIES.put(
   "nvdash;",
   "\\u22ac"
  );
  HTML5_ENTITIES.put(
   "nvge;",
   "\\u2265\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nvgt;",
   ">\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nvHarr;",
   "\\u2904"
  );
  HTML5_ENTITIES.put(
   "nvinfin;",
   "\\u29de"
  );
  HTML5_ENTITIES.put(
   "nvlArr;",
   "\\u2902"
  );
  HTML5_ENTITIES.put(
   "nvle;",
   "\\u2264\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nvlt;",
   "<\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nvltrie;",
   "\\u22b4\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nvrArr;",
   "\\u2903"
  );
  HTML5_ENTITIES.put(
   "nvrtrie;",
   "\\u22b5\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nvsim;",
   "\\u223c\\u20d2"
  );
  HTML5_ENTITIES.put(
   "nwarhk;",
   "\\u2923"
  );
  HTML5_ENTITIES.put(
   "nwArr;",
   "\\u21d6"
  );
  HTML5_ENTITIES.put(
   "nwarr;",
   "\\u2196"
  );
  HTML5_ENTITIES.put(
   "nwarrow;",
   "\\u2196"
  );
  HTML5_ENTITIES.put(
   "nwnear;",
   "\\u2927"
  );
  HTML5_ENTITIES.put(
   "Oacute",
   "\\xd3"
  );
  HTML5_ENTITIES.put(
   "oacute",
   "\\xf3"
  );
  HTML5_ENTITIES.put(
   "Oacute;",
   "\\xd3"
  );
  HTML5_ENTITIES.put(
   "oacute;",
   "\\xf3"
  );
  HTML5_ENTITIES.put(
   "oast;",
   "\\u229b"
  );
  HTML5_ENTITIES.put(
   "ocir;",
   "\\u229a"
  );
  HTML5_ENTITIES.put(
   "Ocirc",
   "\\xd4"
  );
  HTML5_ENTITIES.put(
   "ocirc",
   "\\xf4"
  );
  HTML5_ENTITIES.put(
   "Ocirc;",
   "\\xd4"
  );
  HTML5_ENTITIES.put(
   "ocirc;",
   "\\xf4"
  );
  HTML5_ENTITIES.put(
   "Ocy;",
   "\\u041e"
  );
  HTML5_ENTITIES.put(
   "ocy;",
   "\\u043e"
  );
  HTML5_ENTITIES.put(
   "odash;",
   "\\u229d"
  );
  HTML5_ENTITIES.put(
   "Odblac;",
   "\\u0150"
  );
  HTML5_ENTITIES.put(
   "odblac;",
   "\\u0151"
  );
  HTML5_ENTITIES.put(
   "odiv;",
   "\\u2a38"
  );
  HTML5_ENTITIES.put(
   "odot;",
   "\\u2299"
  );
  HTML5_ENTITIES.put(
   "odsold;",
   "\\u29bc"
  );
  HTML5_ENTITIES.put(
   "OElig;",
   "\\u0152"
  );
  HTML5_ENTITIES.put(
   "oelig;",
   "\\u0153"
  );
  HTML5_ENTITIES.put(
   "ofcir;",
   "\\u29bf"
  );
  HTML5_ENTITIES.put(
   "Ofr;",
   "\\U0001d512"
  );
  HTML5_ENTITIES.put(
   "ofr;",
   "\\U0001d52c"
  );
  HTML5_ENTITIES.put(
   "ogon;",
   "\\u02db"
  );
  HTML5_ENTITIES.put(
   "Ograve",
   "\\xd2"
  );
  HTML5_ENTITIES.put(
   "ograve",
   "\\xf2"
  );
  HTML5_ENTITIES.put(
   "Ograve;",
   "\\xd2"
  );
  HTML5_ENTITIES.put(
   "ograve;",
   "\\xf2"
  );
  HTML5_ENTITIES.put(
   "ogt;",
   "\\u29c1"
  );
  HTML5_ENTITIES.put(
   "ohbar;",
   "\\u29b5"
  );
  HTML5_ENTITIES.put(
   "ohm;",
   "\\u03a9"
  );
  HTML5_ENTITIES.put(
   "oint;",
   "\\u222e"
  );
  HTML5_ENTITIES.put(
   "olarr;",
   "\\u21ba"
  );
  HTML5_ENTITIES.put(
   "olcir;",
   "\\u29be"
  );
  HTML5_ENTITIES.put(
   "olcross;",
   "\\u29bb"
  );
  HTML5_ENTITIES.put(
   "oline;",
   "\\u203e"
  );
  HTML5_ENTITIES.put(
   "olt;",
   "\\u29c0"
  );
  HTML5_ENTITIES.put(
   "Omacr;",
   "\\u014c"
  );
  HTML5_ENTITIES.put(
   "omacr;",
   "\\u014d"
  );
  HTML5_ENTITIES.put(
   "Omega;",
   "\\u03a9"
  );
  HTML5_ENTITIES.put(
   "omega;",
   "\\u03c9"
  );
  HTML5_ENTITIES.put(
   "Omicron;",
   "\\u039f"
  );
  HTML5_ENTITIES.put(
   "omicron;",
   "\\u03bf"
  );
  HTML5_ENTITIES.put(
   "omid;",
   "\\u29b6"
  );
  HTML5_ENTITIES.put(
   "ominus;",
   "\\u2296"
  );
  HTML5_ENTITIES.put(
   "Oopf;",
   "\\U0001d546"
  );
  HTML5_ENTITIES.put(
   "oopf;",
   "\\U0001d560"
  );
  HTML5_ENTITIES.put(
   "opar;",
   "\\u29b7"
  );
  HTML5_ENTITIES.put(
   "OpenCurlyDoubleQuote;",
   "\\u201c"
  );
  HTML5_ENTITIES.put(
   "OpenCurlyQuote;",
   "\\u2018"
  );
  HTML5_ENTITIES.put(
   "operp;",
   "\\u29b9"
  );
  HTML5_ENTITIES.put(
   "oplus;",
   "\\u2295"
  );
  HTML5_ENTITIES.put(
   "Or;",
   "\\u2a54"
  );
  HTML5_ENTITIES.put(
   "or;",
   "\\u2228"
  );
  HTML5_ENTITIES.put(
   "orarr;",
   "\\u21bb"
  );
  HTML5_ENTITIES.put(
   "ord;",
   "\\u2a5d"
  );
  HTML5_ENTITIES.put(
   "order;",
   "\\u2134"
  );
  HTML5_ENTITIES.put(
   "orderof;",
   "\\u2134"
  );
  HTML5_ENTITIES.put(
   "ordf",
   "\\xaa"
  );
  HTML5_ENTITIES.put(
   "ordf;",
   "\\xaa"
  );
  HTML5_ENTITIES.put(
   "ordm",
   "\\xba"
  );
  HTML5_ENTITIES.put(
   "ordm;",
   "\\xba"
  );
  HTML5_ENTITIES.put(
   "origof;",
   "\\u22b6"
  );
  HTML5_ENTITIES.put(
   "oror;",
   "\\u2a56"
  );
  HTML5_ENTITIES.put(
   "orslope;",
   "\\u2a57"
  );
  HTML5_ENTITIES.put(
   "orv;",
   "\\u2a5b"
  );
  HTML5_ENTITIES.put(
   "oS;",
   "\\u24c8"
  );
  HTML5_ENTITIES.put(
   "Oscr;",
   "\\U0001d4aa"
  );
  HTML5_ENTITIES.put(
   "oscr;",
   "\\u2134"
  );
  HTML5_ENTITIES.put(
   "Oslash",
   "\\xd8"
  );
  HTML5_ENTITIES.put(
   "oslash",
   "\\xf8"
  );
  HTML5_ENTITIES.put(
   "Oslash;",
   "\\xd8"
  );
  HTML5_ENTITIES.put(
   "oslash;",
   "\\xf8"
  );
  HTML5_ENTITIES.put(
   "osol;",
   "\\u2298"
  );
  HTML5_ENTITIES.put(
   "Otilde",
   "\\xd5"
  );
  HTML5_ENTITIES.put(
   "otilde",
   "\\xf5"
  );
  HTML5_ENTITIES.put(
   "Otilde;",
   "\\xd5"
  );
  HTML5_ENTITIES.put(
   "otilde;",
   "\\xf5"
  );
  HTML5_ENTITIES.put(
   "Otimes;",
   "\\u2a37"
  );
  HTML5_ENTITIES.put(
   "otimes;",
   "\\u2297"
  );
  HTML5_ENTITIES.put(
   "otimesas;",
   "\\u2a36"
  );
  HTML5_ENTITIES.put(
   "Ouml",
   "\\xd6"
  );
  HTML5_ENTITIES.put(
   "ouml",
   "\\xf6"
  );
  HTML5_ENTITIES.put(
   "Ouml;",
   "\\xd6"
  );
  HTML5_ENTITIES.put(
   "ouml;",
   "\\xf6"
  );
  HTML5_ENTITIES.put(
   "ovbar;",
   "\\u233d"
  );
  HTML5_ENTITIES.put(
   "OverBar;",
   "\\u203e"
  );
  HTML5_ENTITIES.put(
   "OverBrace;",
   "\\u23de"
  );
  HTML5_ENTITIES.put(
   "OverBracket;",
   "\\u23b4"
  );
  HTML5_ENTITIES.put(
   "OverParenthesis;",
   "\\u23dc"
  );
  HTML5_ENTITIES.put(
   "par;",
   "\\u2225"
  );
  HTML5_ENTITIES.put(
   "para",
   "\\xb6"
  );
  HTML5_ENTITIES.put(
   "para;",
   "\\xb6"
  );
  HTML5_ENTITIES.put(
   "parallel;",
   "\\u2225"
  );
  HTML5_ENTITIES.put(
   "parsim;",
   "\\u2af3"
  );
  HTML5_ENTITIES.put(
   "parsl;",
   "\\u2afd"
  );
  HTML5_ENTITIES.put(
   "part;",
   "\\u2202"
  );
  HTML5_ENTITIES.put(
   "PartialD;",
   "\\u2202"
  );
  HTML5_ENTITIES.put(
   "Pcy;",
   "\\u041f"
  );
  HTML5_ENTITIES.put(
   "pcy;",
   "\\u043f"
  );
  HTML5_ENTITIES.put(
   "percnt;",
   "%"
  );
  HTML5_ENTITIES.put(
   "period;",
   "."
  );
  HTML5_ENTITIES.put(
   "permil;",
   "\\u2030"
  );
  HTML5_ENTITIES.put(
   "perp;",
   "\\u22a5"
  );
  HTML5_ENTITIES.put(
   "pertenk;",
   "\\u2031"
  );
  HTML5_ENTITIES.put(
   "Pfr;",
   "\\U0001d513"
  );
  HTML5_ENTITIES.put(
   "pfr;",
   "\\U0001d52d"
  );
  HTML5_ENTITIES.put(
   "Phi;",
   "\\u03a6"
  );
  HTML5_ENTITIES.put(
   "phi;",
   "\\u03c6"
  );
  HTML5_ENTITIES.put(
   "phiv;",
   "\\u03d5"
  );
  HTML5_ENTITIES.put(
   "phmmat;",
   "\\u2133"
  );
  HTML5_ENTITIES.put(
   "phone;",
   "\\u260e"
  );
  HTML5_ENTITIES.put(
   "Pi;",
   "\\u03a0"
  );
  HTML5_ENTITIES.put(
   "pi;",
   "\\u03c0"
  );
  HTML5_ENTITIES.put(
   "pitchfork;",
   "\\u22d4"
  );
  HTML5_ENTITIES.put(
   "piv;",
   "\\u03d6"
  );
  HTML5_ENTITIES.put(
   "planck;",
   "\\u210f"
  );
  HTML5_ENTITIES.put(
   "planckh;",
   "\\u210e"
  );
  HTML5_ENTITIES.put(
   "plankv;",
   "\\u210f"
  );
  HTML5_ENTITIES.put(
   "plus;",
   "+"
  );
  HTML5_ENTITIES.put(
   "plusacir;",
   "\\u2a23"
  );
  HTML5_ENTITIES.put(
   "plusb;",
   "\\u229e"
  );
  HTML5_ENTITIES.put(
   "pluscir;",
   "\\u2a22"
  );
  HTML5_ENTITIES.put(
   "plusdo;",
   "\\u2214"
  );
  HTML5_ENTITIES.put(
   "plusdu;",
   "\\u2a25"
  );
  HTML5_ENTITIES.put(
   "pluse;",
   "\\u2a72"
  );
  HTML5_ENTITIES.put(
   "PlusMinus;",
   "\\xb1"
  );
  HTML5_ENTITIES.put(
   "plusmn",
   "\\xb1"
  );
  HTML5_ENTITIES.put(
   "plusmn;",
   "\\xb1"
  );
  HTML5_ENTITIES.put(
   "plussim;",
   "\\u2a26"
  );
  HTML5_ENTITIES.put(
   "plustwo;",
   "\\u2a27"
  );
  HTML5_ENTITIES.put(
   "pm;",
   "\\xb1"
  );
  HTML5_ENTITIES.put(
   "Poincareplane;",
   "\\u210c"
  );
  HTML5_ENTITIES.put(
   "pointint;",
   "\\u2a15"
  );
  HTML5_ENTITIES.put(
   "Popf;",
   "\\u2119"
  );
  HTML5_ENTITIES.put(
   "popf;",
   "\\U0001d561"
  );
  HTML5_ENTITIES.put(
   "pound",
   "\\xa3"
  );
  HTML5_ENTITIES.put(
   "pound;",
   "\\xa3"
  );
  HTML5_ENTITIES.put(
   "Pr;",
   "\\u2abb"
  );
  HTML5_ENTITIES.put(
   "pr;",
   "\\u227a"
  );
  HTML5_ENTITIES.put(
   "prap;",
   "\\u2ab7"
  );
  HTML5_ENTITIES.put(
   "prcue;",
   "\\u227c"
  );
  HTML5_ENTITIES.put(
   "prE;",
   "\\u2ab3"
  );
  HTML5_ENTITIES.put(
   "pre;",
   "\\u2aaf"
  );
  HTML5_ENTITIES.put(
   "prec;",
   "\\u227a"
  );
  HTML5_ENTITIES.put(
   "precapprox;",
   "\\u2ab7"
  );
  HTML5_ENTITIES.put(
   "preccurlyeq;",
   "\\u227c"
  );
  HTML5_ENTITIES.put(
   "Precedes;",
   "\\u227a"
  );
  HTML5_ENTITIES.put(
   "PrecedesEqual;",
   "\\u2aaf"
  );
  HTML5_ENTITIES.put(
   "PrecedesSlantEqual;",
   "\\u227c"
  );
  HTML5_ENTITIES.put(
   "PrecedesTilde;",
   "\\u227e"
  );
  HTML5_ENTITIES.put(
   "preceq;",
   "\\u2aaf"
  );
  HTML5_ENTITIES.put(
   "precnapprox;",
   "\\u2ab9"
  );
  HTML5_ENTITIES.put(
   "precneqq;",
   "\\u2ab5"
  );
  HTML5_ENTITIES.put(
   "precnsim;",
   "\\u22e8"
  );
  HTML5_ENTITIES.put(
   "precsim;",
   "\\u227e"
  );
  HTML5_ENTITIES.put(
   "Prime;",
   "\\u2033"
  );
  HTML5_ENTITIES.put(
   "prime;",
   "\\u2032"
  );
  HTML5_ENTITIES.put(
   "primes;",
   "\\u2119"
  );
  HTML5_ENTITIES.put(
   "prnap;",
   "\\u2ab9"
  );
  HTML5_ENTITIES.put(
   "prnE;",
   "\\u2ab5"
  );
  HTML5_ENTITIES.put(
   "prnsim;",
   "\\u22e8"
  );
  HTML5_ENTITIES.put(
   "prod;",
   "\\u220f"
  );
  HTML5_ENTITIES.put(
   "Product;",
   "\\u220f"
  );
  HTML5_ENTITIES.put(
   "profalar;",
   "\\u232e"
  );
  HTML5_ENTITIES.put(
   "profline;",
   "\\u2312"
  );
  HTML5_ENTITIES.put(
   "profsurf;",
   "\\u2313"
  );
  HTML5_ENTITIES.put(
   "prop;",
   "\\u221d"
  );
  HTML5_ENTITIES.put(
   "Proportion;",
   "\\u2237"
  );
  HTML5_ENTITIES.put(
   "Proportional;",
   "\\u221d"
  );
  HTML5_ENTITIES.put(
   "propto;",
   "\\u221d"
  );
  HTML5_ENTITIES.put(
   "prsim;",
   "\\u227e"
  );
  HTML5_ENTITIES.put(
   "prurel;",
   "\\u22b0"
  );
  HTML5_ENTITIES.put(
   "Pscr;",
   "\\U0001d4ab"
  );
  HTML5_ENTITIES.put(
   "pscr;",
   "\\U0001d4c5"
  );
  HTML5_ENTITIES.put(
   "Psi;",
   "\\u03a8"
  );
  HTML5_ENTITIES.put(
   "psi;",
   "\\u03c8"
  );
  HTML5_ENTITIES.put(
   "puncsp;",
   "\\u2008"
  );
  HTML5_ENTITIES.put(
   "Qfr;",
   "\\U0001d514"
  );
  HTML5_ENTITIES.put(
   "qfr;",
   "\\U0001d52e"
  );
  HTML5_ENTITIES.put(
   "qint;",
   "\\u2a0c"
  );
  HTML5_ENTITIES.put(
   "Qopf;",
   "\\u211a"
  );
  HTML5_ENTITIES.put(
   "qopf;",
   "\\U0001d562"
  );
  HTML5_ENTITIES.put(
   "qprime;",
   "\\u2057"
  );
  HTML5_ENTITIES.put(
   "Qscr;",
   "\\U0001d4ac"
  );
  HTML5_ENTITIES.put(
   "qscr;",
   "\\U0001d4c6"
  );
  HTML5_ENTITIES.put(
   "quaternions;",
   "\\u210d"
  );
  HTML5_ENTITIES.put(
   "quatint;",
   "\\u2a16"
  );
  HTML5_ENTITIES.put(
   "quest;",
   "?"
  );
  HTML5_ENTITIES.put(
   "questeq;",
   "\\u225f"
  );
  HTML5_ENTITIES.put(
   "QUOT",
   "\""
  );
  HTML5_ENTITIES.put(
   "quot",
   "\""
  );
  HTML5_ENTITIES.put(
   "QUOT;",
   "\""
  );
  HTML5_ENTITIES.put(
   "quot;",
   "\""
  );
  HTML5_ENTITIES.put(
   "rAarr;",
   "\\u21db"
  );
  HTML5_ENTITIES.put(
   "race;",
   "\\u223d\\u0331"
  );
  HTML5_ENTITIES.put(
   "Racute;",
   "\\u0154"
  );
  HTML5_ENTITIES.put(
   "racute;",
   "\\u0155"
  );
  HTML5_ENTITIES.put(
   "radic;",
   "\\u221a"
  );
  HTML5_ENTITIES.put(
   "raemptyv;",
   "\\u29b3"
  );
  HTML5_ENTITIES.put(
   "Rang;",
   "\\u27eb"
  );
  HTML5_ENTITIES.put(
   "rang;",
   "\\u27e9"
  );
  HTML5_ENTITIES.put(
   "rangd;",
   "\\u2992"
  );
  HTML5_ENTITIES.put(
   "range;",
   "\\u29a5"
  );
  HTML5_ENTITIES.put(
   "rangle;",
   "\\u27e9"
  );
  HTML5_ENTITIES.put(
   "raquo",
   "\\xbb"
  );
  HTML5_ENTITIES.put(
   "raquo;",
   "\\xbb"
  );
  HTML5_ENTITIES.put(
   "Rarr;",
   "\\u21a0"
  );
  HTML5_ENTITIES.put(
   "rArr;",
   "\\u21d2"
  );
  HTML5_ENTITIES.put(
   "rarr;",
   "\\u2192"
  );
  HTML5_ENTITIES.put(
   "rarrap;",
   "\\u2975"
  );
  HTML5_ENTITIES.put(
   "rarrb;",
   "\\u21e5"
  );
  HTML5_ENTITIES.put(
   "rarrbfs;",
   "\\u2920"
  );
  HTML5_ENTITIES.put(
   "rarrc;",
   "\\u2933"
  );
  HTML5_ENTITIES.put(
   "rarrfs;",
   "\\u291e"
  );
  HTML5_ENTITIES.put(
   "rarrhk;",
   "\\u21aa"
  );
  HTML5_ENTITIES.put(
   "rarrlp;",
   "\\u21ac"
  );
  HTML5_ENTITIES.put(
   "rarrpl;",
   "\\u2945"
  );
  HTML5_ENTITIES.put(
   "rarrsim;",
   "\\u2974"
  );
  HTML5_ENTITIES.put(
   "Rarrtl;",
   "\\u2916"
  );
  HTML5_ENTITIES.put(
   "rarrtl;",
   "\\u21a3"
  );
  HTML5_ENTITIES.put(
   "rarrw;",
   "\\u219d"
  );
  HTML5_ENTITIES.put(
   "rAtail;",
   "\\u291c"
  );
  HTML5_ENTITIES.put(
   "ratail;",
   "\\u291a"
  );
  HTML5_ENTITIES.put(
   "ratio;",
   "\\u2236"
  );
  HTML5_ENTITIES.put(
   "rationals;",
   "\\u211a"
  );
  HTML5_ENTITIES.put(
   "RBarr;",
   "\\u2910"
  );
  HTML5_ENTITIES.put(
   "rBarr;",
   "\\u290f"
  );
  HTML5_ENTITIES.put(
   "rbarr;",
   "\\u290d"
  );
  HTML5_ENTITIES.put(
   "rbbrk;",
   "\\u2773"
  );
  HTML5_ENTITIES.put(
   "rbrace;",
   "}"
  );
  HTML5_ENTITIES.put(
   "rbrack;",
   "]"
  );
  HTML5_ENTITIES.put(
   "rbrke;",
   "\\u298c"
  );
  HTML5_ENTITIES.put(
   "rbrksld;",
   "\\u298e"
  );
  HTML5_ENTITIES.put(
   "rbrkslu;",
   "\\u2990"
  );
  HTML5_ENTITIES.put(
   "Rcaron;",
   "\\u0158"
  );
  HTML5_ENTITIES.put(
   "rcaron;",
   "\\u0159"
  );
  HTML5_ENTITIES.put(
   "Rcedil;",
   "\\u0156"
  );
  HTML5_ENTITIES.put(
   "rcedil;",
   "\\u0157"
  );
  HTML5_ENTITIES.put(
   "rceil;",
   "\\u2309"
  );
  HTML5_ENTITIES.put(
   "rcub;",
   "}"
  );
  HTML5_ENTITIES.put(
   "Rcy;",
   "\\u0420"
  );
  HTML5_ENTITIES.put(
   "rcy;",
   "\\u0440"
  );
  HTML5_ENTITIES.put(
   "rdca;",
   "\\u2937"
  );
  HTML5_ENTITIES.put(
   "rdldhar;",
   "\\u2969"
  );
  HTML5_ENTITIES.put(
   "rdquo;",
   "\\u201d"
  );
  HTML5_ENTITIES.put(
   "rdquor;",
   "\\u201d"
  );
  HTML5_ENTITIES.put(
   "rdsh;",
   "\\u21b3"
  );
  HTML5_ENTITIES.put(
   "Re;",
   "\\u211c"
  );
  HTML5_ENTITIES.put(
   "real;",
   "\\u211c"
  );
  HTML5_ENTITIES.put(
   "realine;",
   "\\u211b"
  );
  HTML5_ENTITIES.put(
   "realpart;",
   "\\u211c"
  );
  HTML5_ENTITIES.put(
   "reals;",
   "\\u211d"
  );
  HTML5_ENTITIES.put(
   "rect;",
   "\\u25ad"
  );
  HTML5_ENTITIES.put(
   "REG",
   "\\xae"
  );
  HTML5_ENTITIES.put(
   "reg",
   "\\xae"
  );
  HTML5_ENTITIES.put(
   "REG;",
   "\\xae"
  );
  HTML5_ENTITIES.put(
   "reg;",
   "\\xae"
  );
  HTML5_ENTITIES.put(
   "ReverseElement;",
   "\\u220b"
  );
  HTML5_ENTITIES.put(
   "ReverseEquilibrium;",
   "\\u21cb"
  );
  HTML5_ENTITIES.put(
   "ReverseUpEquilibrium;",
   "\\u296f"
  );
  HTML5_ENTITIES.put(
   "rfisht;",
   "\\u297d"
  );
  HTML5_ENTITIES.put(
   "rfloor;",
   "\\u230b"
  );
  HTML5_ENTITIES.put(
   "Rfr;",
   "\\u211c"
  );
  HTML5_ENTITIES.put(
   "rfr;",
   "\\U0001d52f"
  );
  HTML5_ENTITIES.put(
   "rHar;",
   "\\u2964"
  );
  HTML5_ENTITIES.put(
   "rhard;",
   "\\u21c1"
  );
  HTML5_ENTITIES.put(
   "rharu;",
   "\\u21c0"
  );
  HTML5_ENTITIES.put(
   "rharul;",
   "\\u296c"
  );
  HTML5_ENTITIES.put(
   "Rho;",
   "\\u03a1"
  );
  HTML5_ENTITIES.put(
   "rho;",
   "\\u03c1"
  );
  HTML5_ENTITIES.put(
   "rhov;",
   "\\u03f1"
  );
  HTML5_ENTITIES.put(
   "RightAngleBracket;",
   "\\u27e9"
  );
  HTML5_ENTITIES.put(
   "RightArrow;",
   "\\u2192"
  );
  HTML5_ENTITIES.put(
   "Rightarrow;",
   "\\u21d2"
  );
  HTML5_ENTITIES.put(
   "rightarrow;",
   "\\u2192"
  );
  HTML5_ENTITIES.put(
   "RightArrowBar;",
   "\\u21e5"
  );
  HTML5_ENTITIES.put(
   "RightArrowLeftArrow;",
   "\\u21c4"
  );
  HTML5_ENTITIES.put(
   "rightarrowtail;",
   "\\u21a3"
  );
  HTML5_ENTITIES.put(
   "RightCeiling;",
   "\\u2309"
  );
  HTML5_ENTITIES.put(
   "RightDoubleBracket;",
   "\\u27e7"
  );
  HTML5_ENTITIES.put(
   "RightDownTeeVector;",
   "\\u295d"
  );
  HTML5_ENTITIES.put(
   "RightDownVector;",
   "\\u21c2"
  );
  HTML5_ENTITIES.put(
   "RightDownVectorBar;",
   "\\u2955"
  );
  HTML5_ENTITIES.put(
   "RightFloor;",
   "\\u230b"
  );
  HTML5_ENTITIES.put(
   "rightharpoondown;",
   "\\u21c1"
  );
  HTML5_ENTITIES.put(
   "rightharpoonup;",
   "\\u21c0"
  );
  HTML5_ENTITIES.put(
   "rightleftarrows;",
   "\\u21c4"
  );
  HTML5_ENTITIES.put(
   "rightleftharpoons;",
   "\\u21cc"
  );
  HTML5_ENTITIES.put(
   "rightrightarrows;",
   "\\u21c9"
  );
  HTML5_ENTITIES.put(
   "rightsquigarrow;",
   "\\u219d"
  );
  HTML5_ENTITIES.put(
   "RightTee;",
   "\\u22a2"
  );
  HTML5_ENTITIES.put(
   "RightTeeArrow;",
   "\\u21a6"
  );
  HTML5_ENTITIES.put(
   "RightTeeVector;",
   "\\u295b"
  );
  HTML5_ENTITIES.put(
   "rightthreetimes;",
   "\\u22cc"
  );
  HTML5_ENTITIES.put(
   "RightTriangle;",
   "\\u22b3"
  );
  HTML5_ENTITIES.put(
   "RightTriangleBar;",
   "\\u29d0"
  );
  HTML5_ENTITIES.put(
   "RightTriangleEqual;",
   "\\u22b5"
  );
  HTML5_ENTITIES.put(
   "RightUpDownVector;",
   "\\u294f"
  );
  HTML5_ENTITIES.put(
   "RightUpTeeVector;",
   "\\u295c"
  );
  HTML5_ENTITIES.put(
   "RightUpVector;",
   "\\u21be"
  );
  HTML5_ENTITIES.put(
   "RightUpVectorBar;",
   "\\u2954"
  );
  HTML5_ENTITIES.put(
   "RightVector;",
   "\\u21c0"
  );
  HTML5_ENTITIES.put(
   "RightVectorBar;",
   "\\u2953"
  );
  HTML5_ENTITIES.put(
   "ring;",
   "\\u02da"
  );
  HTML5_ENTITIES.put(
   "risingdotseq;",
   "\\u2253"
  );
  HTML5_ENTITIES.put(
   "rlarr;",
   "\\u21c4"
  );
  HTML5_ENTITIES.put(
   "rlhar;",
   "\\u21cc"
  );
  HTML5_ENTITIES.put(
   "rlm;",
   "\\u200f"
  );
  HTML5_ENTITIES.put(
   "rmoust;",
   "\\u23b1"
  );
  HTML5_ENTITIES.put(
   "rmoustache;",
   "\\u23b1"
  );
  HTML5_ENTITIES.put(
   "rnmid;",
   "\\u2aee"
  );
  HTML5_ENTITIES.put(
   "roang;",
   "\\u27ed"
  );
  HTML5_ENTITIES.put(
   "roarr;",
   "\\u21fe"
  );
  HTML5_ENTITIES.put(
   "robrk;",
   "\\u27e7"
  );
  HTML5_ENTITIES.put(
   "ropar;",
   "\\u2986"
  );
  HTML5_ENTITIES.put(
   "Ropf;",
   "\\u211d"
  );
  HTML5_ENTITIES.put(
   "ropf;",
   "\\U0001d563"
  );
  HTML5_ENTITIES.put(
   "roplus;",
   "\\u2a2e"
  );
  HTML5_ENTITIES.put(
   "rotimes;",
   "\\u2a35"
  );
  HTML5_ENTITIES.put(
   "RoundImplies;",
   "\\u2970"
  );
  HTML5_ENTITIES.put(
   "rpar;",
   ")"
  );
  HTML5_ENTITIES.put(
   "rpargt;",
   "\\u2994"
  );
  HTML5_ENTITIES.put(
   "rppolint;",
   "\\u2a12"
  );
  HTML5_ENTITIES.put(
   "rrarr;",
   "\\u21c9"
  );
  HTML5_ENTITIES.put(
   "Rrightarrow;",
   "\\u21db"
  );
  HTML5_ENTITIES.put(
   "rsaquo;",
   "\\u203a"
  );
  HTML5_ENTITIES.put(
   "Rscr;",
   "\\u211b"
  );
  HTML5_ENTITIES.put(
   "rscr;",
   "\\U0001d4c7"
  );
  HTML5_ENTITIES.put(
   "Rsh;",
   "\\u21b1"
  );
  HTML5_ENTITIES.put(
   "rsh;",
   "\\u21b1"
  );
  HTML5_ENTITIES.put(
   "rsqb;",
   "]"
  );
  HTML5_ENTITIES.put(
   "rsquo;",
   "\\u2019"
  );
  HTML5_ENTITIES.put(
   "rsquor;",
   "\\u2019"
  );
  HTML5_ENTITIES.put(
   "rthree;",
   "\\u22cc"
  );
  HTML5_ENTITIES.put(
   "rtimes;",
   "\\u22ca"
  );
  HTML5_ENTITIES.put(
   "rtri;",
   "\\u25b9"
  );
  HTML5_ENTITIES.put(
   "rtrie;",
   "\\u22b5"
  );
  HTML5_ENTITIES.put(
   "rtrif;",
   "\\u25b8"
  );
  HTML5_ENTITIES.put(
   "rtriltri;",
   "\\u29ce"
  );
  HTML5_ENTITIES.put(
   "RuleDelayed;",
   "\\u29f4"
  );
  HTML5_ENTITIES.put(
   "ruluhar;",
   "\\u2968"
  );
  HTML5_ENTITIES.put(
   "rx;",
   "\\u211e"
  );
  HTML5_ENTITIES.put(
   "Sacute;",
   "\\u015a"
  );
  HTML5_ENTITIES.put(
   "sacute;",
   "\\u015b"
  );
  HTML5_ENTITIES.put(
   "sbquo;",
   "\\u201a"
  );
  HTML5_ENTITIES.put(
   "Sc;",
   "\\u2abc"
  );
  HTML5_ENTITIES.put(
   "sc;",
   "\\u227b"
  );
  HTML5_ENTITIES.put(
   "scap;",
   "\\u2ab8"
  );
  HTML5_ENTITIES.put(
   "Scaron;",
   "\\u0160"
  );
  HTML5_ENTITIES.put(
   "scaron;",
   "\\u0161"
  );
  HTML5_ENTITIES.put(
   "sccue;",
   "\\u227d"
  );
  HTML5_ENTITIES.put(
   "scE;",
   "\\u2ab4"
  );
  HTML5_ENTITIES.put(
   "sce;",
   "\\u2ab0"
  );
  HTML5_ENTITIES.put(
   "Scedil;",
   "\\u015e"
  );
  HTML5_ENTITIES.put(
   "scedil;",
   "\\u015f"
  );
  HTML5_ENTITIES.put(
   "Scirc;",
   "\\u015c"
  );
  HTML5_ENTITIES.put(
   "scirc;",
   "\\u015d"
  );
  HTML5_ENTITIES.put(
   "scnap;",
   "\\u2aba"
  );
  HTML5_ENTITIES.put(
   "scnE;",
   "\\u2ab6"
  );
  HTML5_ENTITIES.put(
   "scnsim;",
   "\\u22e9"
  );
  HTML5_ENTITIES.put(
   "scpolint;",
   "\\u2a13"
  );
  HTML5_ENTITIES.put(
   "scsim;",
   "\\u227f"
  );
  HTML5_ENTITIES.put(
   "Scy;",
   "\\u0421"
  );
  HTML5_ENTITIES.put(
   "scy;",
   "\\u0441"
  );
  HTML5_ENTITIES.put(
   "sdot;",
   "\\u22c5"
  );
  HTML5_ENTITIES.put(
   "sdotb;",
   "\\u22a1"
  );
  HTML5_ENTITIES.put(
   "sdote;",
   "\\u2a66"
  );
  HTML5_ENTITIES.put(
   "searhk;",
   "\\u2925"
  );
  HTML5_ENTITIES.put(
   "seArr;",
   "\\u21d8"
  );
  HTML5_ENTITIES.put(
   "searr;",
   "\\u2198"
  );
  HTML5_ENTITIES.put(
   "searrow;",
   "\\u2198"
  );
  HTML5_ENTITIES.put(
   "sect",
   "\\xa7"
  );
  HTML5_ENTITIES.put(
   "sect;",
   "\\xa7"
  );
  HTML5_ENTITIES.put(
   "semi;",
   ";"
  );
  HTML5_ENTITIES.put(
   "seswar;",
   "\\u2929"
  );
  HTML5_ENTITIES.put(
   "setminus;",
   "\\u2216"
  );
  HTML5_ENTITIES.put(
   "setmn;",
   "\\u2216"
  );
  HTML5_ENTITIES.put(
   "sext;",
   "\\u2736"
  );
  HTML5_ENTITIES.put(
   "Sfr;",
   "\\U0001d516"
  );
  HTML5_ENTITIES.put(
   "sfr;",
   "\\U0001d530"
  );
  HTML5_ENTITIES.put(
   "sfrown;",
   "\\u2322"
  );
  HTML5_ENTITIES.put(
   "sharp;",
   "\\u266f"
  );
  HTML5_ENTITIES.put(
   "SHCHcy;",
   "\\u0429"
  );
  HTML5_ENTITIES.put(
   "shchcy;",
   "\\u0449"
  );
  HTML5_ENTITIES.put(
   "SHcy;",
   "\\u0428"
  );
  HTML5_ENTITIES.put(
   "shcy;",
   "\\u0448"
  );
  HTML5_ENTITIES.put(
   "ShortDownArrow;",
   "\\u2193"
  );
  HTML5_ENTITIES.put(
   "ShortLeftArrow;",
   "\\u2190"
  );
  HTML5_ENTITIES.put(
   "shortmid;",
   "\\u2223"
  );
  HTML5_ENTITIES.put(
   "shortparallel;",
   "\\u2225"
  );
  HTML5_ENTITIES.put(
   "ShortRightArrow;",
   "\\u2192"
  );
  HTML5_ENTITIES.put(
   "ShortUpArrow;",
   "\\u2191"
  );
  HTML5_ENTITIES.put(
   "shy",
   "\\xad"
  );
  HTML5_ENTITIES.put(
   "shy;",
   "\\xad"
  );
  HTML5_ENTITIES.put(
   "Sigma;",
   "\\u03a3"
  );
  HTML5_ENTITIES.put(
   "sigma;",
   "\\u03c3"
  );
  HTML5_ENTITIES.put(
   "sigmaf;",
   "\\u03c2"
  );
  HTML5_ENTITIES.put(
   "sigmav;",
   "\\u03c2"
  );
  HTML5_ENTITIES.put(
   "sim;",
   "\\u223c"
  );
  HTML5_ENTITIES.put(
   "simdot;",
   "\\u2a6a"
  );
  HTML5_ENTITIES.put(
   "sime;",
   "\\u2243"
  );
  HTML5_ENTITIES.put(
   "simeq;",
   "\\u2243"
  );
  HTML5_ENTITIES.put(
   "simg;",
   "\\u2a9e"
  );
  HTML5_ENTITIES.put(
   "simgE;",
   "\\u2aa0"
  );
  HTML5_ENTITIES.put(
   "siml;",
   "\\u2a9d"
  );
  HTML5_ENTITIES.put(
   "simlE;",
   "\\u2a9f"
  );
  HTML5_ENTITIES.put(
   "simne;",
   "\\u2246"
  );
  HTML5_ENTITIES.put(
   "simplus;",
   "\\u2a24"
  );
  HTML5_ENTITIES.put(
   "simrarr;",
   "\\u2972"
  );
  HTML5_ENTITIES.put(
   "slarr;",
   "\\u2190"
  );
  HTML5_ENTITIES.put(
   "SmallCircle;",
   "\\u2218"
  );
  HTML5_ENTITIES.put(
   "smallsetminus;",
   "\\u2216"
  );
  HTML5_ENTITIES.put(
   "smashp;",
   "\\u2a33"
  );
  HTML5_ENTITIES.put(
   "smeparsl;",
   "\\u29e4"
  );
  HTML5_ENTITIES.put(
   "smid;",
   "\\u2223"
  );
  HTML5_ENTITIES.put(
   "smile;",
   "\\u2323"
  );
  HTML5_ENTITIES.put(
   "smt;",
   "\\u2aaa"
  );
  HTML5_ENTITIES.put(
   "smte;",
   "\\u2aac"
  );
  HTML5_ENTITIES.put(
   "smtes;",
   "\\u2aac\\ufe00"
  );
  HTML5_ENTITIES.put(
   "SOFTcy;",
   "\\u042c"
  );
  HTML5_ENTITIES.put(
   "softcy;",
   "\\u044c"
  );
  HTML5_ENTITIES.put(
   "sol;",
   "/"
  );
  HTML5_ENTITIES.put(
   "solb;",
   "\\u29c4"
  );
  HTML5_ENTITIES.put(
   "solbar;",
   "\\u233f"
  );
  HTML5_ENTITIES.put(
   "Sopf;",
   "\\U0001d54a"
  );
  HTML5_ENTITIES.put(
   "sopf;",
   "\\U0001d564"
  );
  HTML5_ENTITIES.put(
   "spades;",
   "\\u2660"
  );
  HTML5_ENTITIES.put(
   "spadesuit;",
   "\\u2660"
  );
  HTML5_ENTITIES.put(
   "spar;",
   "\\u2225"
  );
  HTML5_ENTITIES.put(
   "sqcap;",
   "\\u2293"
  );
  HTML5_ENTITIES.put(
   "sqcaps;",
   "\\u2293\\ufe00"
  );
  HTML5_ENTITIES.put(
   "sqcup;",
   "\\u2294"
  );
  HTML5_ENTITIES.put(
   "sqcups;",
   "\\u2294\\ufe00"
  );
  HTML5_ENTITIES.put(
   "Sqrt;",
   "\\u221a"
  );
  HTML5_ENTITIES.put(
   "sqsub;",
   "\\u228f"
  );
  HTML5_ENTITIES.put(
   "sqsube;",
   "\\u2291"
  );
  HTML5_ENTITIES.put(
   "sqsubset;",
   "\\u228f"
  );
  HTML5_ENTITIES.put(
   "sqsubseteq;",
   "\\u2291"
  );
  HTML5_ENTITIES.put(
   "sqsup;",
   "\\u2290"
  );
  HTML5_ENTITIES.put(
   "sqsupe;",
   "\\u2292"
  );
  HTML5_ENTITIES.put(
   "sqsupset;",
   "\\u2290"
  );
  HTML5_ENTITIES.put(
   "sqsupseteq;",
   "\\u2292"
  );
  HTML5_ENTITIES.put(
   "squ;",
   "\\u25a1"
  );
  HTML5_ENTITIES.put(
   "Square;",
   "\\u25a1"
  );
  HTML5_ENTITIES.put(
   "square;",
   "\\u25a1"
  );
  HTML5_ENTITIES.put(
   "SquareIntersection;",
   "\\u2293"
  );
  HTML5_ENTITIES.put(
   "SquareSubset;",
   "\\u228f"
  );
  HTML5_ENTITIES.put(
   "SquareSubsetEqual;",
   "\\u2291"
  );
  HTML5_ENTITIES.put(
   "SquareSuperset;",
   "\\u2290"
  );
  HTML5_ENTITIES.put(
   "SquareSupersetEqual;",
   "\\u2292"
  );
  HTML5_ENTITIES.put(
   "SquareUnion;",
   "\\u2294"
  );
  HTML5_ENTITIES.put(
   "squarf;",
   "\\u25aa"
  );
  HTML5_ENTITIES.put(
   "squf;",
   "\\u25aa"
  );
  HTML5_ENTITIES.put(
   "srarr;",
   "\\u2192"
  );
  HTML5_ENTITIES.put(
   "Sscr;",
   "\\U0001d4ae"
  );
  HTML5_ENTITIES.put(
   "sscr;",
   "\\U0001d4c8"
  );
  HTML5_ENTITIES.put(
   "ssetmn;",
   "\\u2216"
  );
  HTML5_ENTITIES.put(
   "ssmile;",
   "\\u2323"
  );
  HTML5_ENTITIES.put(
   "sstarf;",
   "\\u22c6"
  );
  HTML5_ENTITIES.put(
   "Star;",
   "\\u22c6"
  );
  HTML5_ENTITIES.put(
   "star;",
   "\\u2606"
  );
  HTML5_ENTITIES.put(
   "starf;",
   "\\u2605"
  );
  HTML5_ENTITIES.put(
   "straightepsilon;",
   "\\u03f5"
  );
  HTML5_ENTITIES.put(
   "straightphi;",
   "\\u03d5"
  );
  HTML5_ENTITIES.put(
   "strns;",
   "\\xaf"
  );
  HTML5_ENTITIES.put(
   "Sub;",
   "\\u22d0"
  );
  HTML5_ENTITIES.put(
   "sub;",
   "\\u2282"
  );
  HTML5_ENTITIES.put(
   "subdot;",
   "\\u2abd"
  );
  HTML5_ENTITIES.put(
   "subE;",
   "\\u2ac5"
  );
  HTML5_ENTITIES.put(
   "sube;",
   "\\u2286"
  );
  HTML5_ENTITIES.put(
   "subedot;",
   "\\u2ac3"
  );
  HTML5_ENTITIES.put(
   "submult;",
   "\\u2ac1"
  );
  HTML5_ENTITIES.put(
   "subnE;",
   "\\u2acb"
  );
  HTML5_ENTITIES.put(
   "subne;",
   "\\u228a"
  );
  HTML5_ENTITIES.put(
   "subplus;",
   "\\u2abf"
  );
  HTML5_ENTITIES.put(
   "subrarr;",
   "\\u2979"
  );
  HTML5_ENTITIES.put(
   "Subset;",
   "\\u22d0"
  );
  HTML5_ENTITIES.put(
   "subset;",
   "\\u2282"
  );
  HTML5_ENTITIES.put(
   "subseteq;",
   "\\u2286"
  );
  HTML5_ENTITIES.put(
   "subseteqq;",
   "\\u2ac5"
  );
  HTML5_ENTITIES.put(
   "SubsetEqual;",
   "\\u2286"
  );
  HTML5_ENTITIES.put(
   "subsetneq;",
   "\\u228a"
  );
  HTML5_ENTITIES.put(
   "subsetneqq;",
   "\\u2acb"
  );
  HTML5_ENTITIES.put(
   "subsim;",
   "\\u2ac7"
  );
  HTML5_ENTITIES.put(
   "subsub;",
   "\\u2ad5"
  );
  HTML5_ENTITIES.put(
   "subsup;",
   "\\u2ad3"
  );
  HTML5_ENTITIES.put(
   "succ;",
   "\\u227b"
  );
  HTML5_ENTITIES.put(
   "succapprox;",
   "\\u2ab8"
  );
  HTML5_ENTITIES.put(
   "succcurlyeq;",
   "\\u227d"
  );
  HTML5_ENTITIES.put(
   "Succeeds;",
   "\\u227b"
  );
  HTML5_ENTITIES.put(
   "SucceedsEqual;",
   "\\u2ab0"
  );
  HTML5_ENTITIES.put(
   "SucceedsSlantEqual;",
   "\\u227d"
  );
  HTML5_ENTITIES.put(
   "SucceedsTilde;",
   "\\u227f"
  );
  HTML5_ENTITIES.put(
   "succeq;",
   "\\u2ab0"
  );
  HTML5_ENTITIES.put(
   "succnapprox;",
   "\\u2aba"
  );
  HTML5_ENTITIES.put(
   "succneqq;",
   "\\u2ab6"
  );
  HTML5_ENTITIES.put(
   "succnsim;",
   "\\u22e9"
  );
  HTML5_ENTITIES.put(
   "succsim;",
   "\\u227f"
  );
  HTML5_ENTITIES.put(
   "SuchThat;",
   "\\u220b"
  );
  HTML5_ENTITIES.put(
   "Sum;",
   "\\u2211"
  );
  HTML5_ENTITIES.put(
   "sum;",
   "\\u2211"
  );
  HTML5_ENTITIES.put(
   "sung;",
   "\\u266a"
  );
  HTML5_ENTITIES.put(
   "sup1",
   "\\xb9"
  );
  HTML5_ENTITIES.put(
   "sup1;",
   "\\xb9"
  );
  HTML5_ENTITIES.put(
   "sup2",
   "\\xb2"
  );
  HTML5_ENTITIES.put(
   "sup2;",
   "\\xb2"
  );
  HTML5_ENTITIES.put(
   "sup3",
   "\\xb3"
  );
  HTML5_ENTITIES.put(
   "sup3;",
   "\\xb3"
  );
  HTML5_ENTITIES.put(
   "Sup;",
   "\\u22d1"
  );
  HTML5_ENTITIES.put(
   "sup;",
   "\\u2283"
  );
  HTML5_ENTITIES.put(
   "supdot;",
   "\\u2abe"
  );
  HTML5_ENTITIES.put(
   "supdsub;",
   "\\u2ad8"
  );
  HTML5_ENTITIES.put(
   "supE;",
   "\\u2ac6"
  );
  HTML5_ENTITIES.put(
   "supe;",
   "\\u2287"
  );
  HTML5_ENTITIES.put(
   "supedot;",
   "\\u2ac4"
  );
  HTML5_ENTITIES.put(
   "Superset;",
   "\\u2283"
  );
  HTML5_ENTITIES.put(
   "SupersetEqual;",
   "\\u2287"
  );
  HTML5_ENTITIES.put(
   "suphsol;",
   "\\u27c9"
  );
  HTML5_ENTITIES.put(
   "suphsub;",
   "\\u2ad7"
  );
  HTML5_ENTITIES.put(
   "suplarr;",
   "\\u297b"
  );
  HTML5_ENTITIES.put(
   "supmult;",
   "\\u2ac2"
  );
  HTML5_ENTITIES.put(
   "supnE;",
   "\\u2acc"
  );
  HTML5_ENTITIES.put(
   "supne;",
   "\\u228b"
  );
  HTML5_ENTITIES.put(
   "supplus;",
   "\\u2ac0"
  );
  HTML5_ENTITIES.put(
   "Supset;",
   "\\u22d1"
  );
  HTML5_ENTITIES.put(
   "supset;",
   "\\u2283"
  );
  HTML5_ENTITIES.put(
   "supseteq;",
   "\\u2287"
  );
  HTML5_ENTITIES.put(
   "supseteqq;",
   "\\u2ac6"
  );
  HTML5_ENTITIES.put(
   "supsetneq;",
   "\\u228b"
  );
  HTML5_ENTITIES.put(
   "supsetneqq;",
   "\\u2acc"
  );
  HTML5_ENTITIES.put(
   "supsim;",
   "\\u2ac8"
  );
  HTML5_ENTITIES.put(
   "supsub;",
   "\\u2ad4"
  );
  HTML5_ENTITIES.put(
   "supsup;",
   "\\u2ad6"
  );
  HTML5_ENTITIES.put(
   "swarhk;",
   "\\u2926"
  );
  HTML5_ENTITIES.put(
   "swArr;",
   "\\u21d9"
  );
  HTML5_ENTITIES.put(
   "swarr;",
   "\\u2199"
  );
  HTML5_ENTITIES.put(
   "swarrow;",
   "\\u2199"
  );
  HTML5_ENTITIES.put(
   "swnwar;",
   "\\u292a"
  );
  HTML5_ENTITIES.put(
   "szlig",
   "\\xdf"
  );
  HTML5_ENTITIES.put(
   "szlig;",
   "\\xdf"
  );
  HTML5_ENTITIES.put(
   "Tab;",
   "\\t"
  );
  HTML5_ENTITIES.put(
   "target;",
   "\\u2316"
  );
  HTML5_ENTITIES.put(
   "Tau;",
   "\\u03a4"
  );
  HTML5_ENTITIES.put(
   "tau;",
   "\\u03c4"
  );
  HTML5_ENTITIES.put(
   "tbrk;",
   "\\u23b4"
  );
  HTML5_ENTITIES.put(
   "Tcaron;",
   "\\u0164"
  );
  HTML5_ENTITIES.put(
   "tcaron;",
   "\\u0165"
  );
  HTML5_ENTITIES.put(
   "Tcedil;",
   "\\u0162"
  );
  HTML5_ENTITIES.put(
   "tcedil;",
   "\\u0163"
  );
  HTML5_ENTITIES.put(
   "Tcy;",
   "\\u0422"
  );
  HTML5_ENTITIES.put(
   "tcy;",
   "\\u0442"
  );
  HTML5_ENTITIES.put(
   "tdot;",
   "\\u20db"
  );
  HTML5_ENTITIES.put(
   "telrec;",
   "\\u2315"
  );
  HTML5_ENTITIES.put(
   "Tfr;",
   "\\U0001d517"
  );
  HTML5_ENTITIES.put(
   "tfr;",
   "\\U0001d531"
  );
  HTML5_ENTITIES.put(
   "there4;",
   "\\u2234"
  );
  HTML5_ENTITIES.put(
   "Therefore;",
   "\\u2234"
  );
  HTML5_ENTITIES.put(
   "therefore;",
   "\\u2234"
  );
  HTML5_ENTITIES.put(
   "Theta;",
   "\\u0398"
  );
  HTML5_ENTITIES.put(
   "theta;",
   "\\u03b8"
  );
  HTML5_ENTITIES.put(
   "thetasym;",
   "\\u03d1"
  );
  HTML5_ENTITIES.put(
   "thetav;",
   "\\u03d1"
  );
  HTML5_ENTITIES.put(
   "thickapprox;",
   "\\u2248"
  );
  HTML5_ENTITIES.put(
   "thicksim;",
   "\\u223c"
  );
  HTML5_ENTITIES.put(
   "ThickSpace;",
   "\\u205f\\u200a"
  );
  HTML5_ENTITIES.put(
   "thinsp;",
   "\\u2009"
  );
  HTML5_ENTITIES.put(
   "ThinSpace;",
   "\\u2009"
  );
  HTML5_ENTITIES.put(
   "thkap;",
   "\\u2248"
  );
  HTML5_ENTITIES.put(
   "thksim;",
   "\\u223c"
  );
  HTML5_ENTITIES.put(
   "THORN",
   "\\xde"
  );
  HTML5_ENTITIES.put(
   "thorn",
   "\\xfe"
  );
  HTML5_ENTITIES.put(
   "THORN;",
   "\\xde"
  );
  HTML5_ENTITIES.put(
   "thorn;",
   "\\xfe"
  );
  HTML5_ENTITIES.put(
   "Tilde;",
   "\\u223c"
  );
  HTML5_ENTITIES.put(
   "tilde;",
   "\\u02dc"
  );
  HTML5_ENTITIES.put(
   "TildeEqual;",
   "\\u2243"
  );
  HTML5_ENTITIES.put(
   "TildeFullEqual;",
   "\\u2245"
  );
  HTML5_ENTITIES.put(
   "TildeTilde;",
   "\\u2248"
  );
  HTML5_ENTITIES.put(
   "times",
   "\\xd7"
  );
  HTML5_ENTITIES.put(
   "times;",
   "\\xd7"
  );
  HTML5_ENTITIES.put(
   "timesb;",
   "\\u22a0"
  );
  HTML5_ENTITIES.put(
   "timesbar;",
   "\\u2a31"
  );
  HTML5_ENTITIES.put(
   "timesd;",
   "\\u2a30"
  );
  HTML5_ENTITIES.put(
   "tint;",
   "\\u222d"
  );
  HTML5_ENTITIES.put(
   "toea;",
   "\\u2928"
  );
  HTML5_ENTITIES.put(
   "top;",
   "\\u22a4"
  );
  HTML5_ENTITIES.put(
   "topbot;",
   "\\u2336"
  );
  HTML5_ENTITIES.put(
   "topcir;",
   "\\u2af1"
  );
  HTML5_ENTITIES.put(
   "Topf;",
   "\\U0001d54b"
  );
  HTML5_ENTITIES.put(
   "topf;",
   "\\U0001d565"
  );
  HTML5_ENTITIES.put(
   "topfork;",
   "\\u2ada"
  );
  HTML5_ENTITIES.put(
   "tosa;",
   "\\u2929"
  );
  HTML5_ENTITIES.put(
   "tprime;",
   "\\u2034"
  );
  HTML5_ENTITIES.put(
   "TRADE;",
   "\\u2122"
  );
  HTML5_ENTITIES.put(
   "trade;",
   "\\u2122"
  );
  HTML5_ENTITIES.put(
   "triangle;",
   "\\u25b5"
  );
  HTML5_ENTITIES.put(
   "triangledown;",
   "\\u25bf"
  );
  HTML5_ENTITIES.put(
   "triangleleft;",
   "\\u25c3"
  );
  HTML5_ENTITIES.put(
   "trianglelefteq;",
   "\\u22b4"
  );
  HTML5_ENTITIES.put(
   "triangleq;",
   "\\u225c"
  );
  HTML5_ENTITIES.put(
   "triangleright;",
   "\\u25b9"
  );
  HTML5_ENTITIES.put(
   "trianglerighteq;",
   "\\u22b5"
  );
  HTML5_ENTITIES.put(
   "tridot;",
   "\\u25ec"
  );
  HTML5_ENTITIES.put(
   "trie;",
   "\\u225c"
  );
  HTML5_ENTITIES.put(
   "triminus;",
   "\\u2a3a"
  );
  HTML5_ENTITIES.put(
   "TripleDot;",
   "\\u20db"
  );
  HTML5_ENTITIES.put(
   "triplus;",
   "\\u2a39"
  );
  HTML5_ENTITIES.put(
   "trisb;",
   "\\u29cd"
  );
  HTML5_ENTITIES.put(
   "tritime;",
   "\\u2a3b"
  );
  HTML5_ENTITIES.put(
   "trpezium;",
   "\\u23e2"
  );
  HTML5_ENTITIES.put(
   "Tscr;",
   "\\U0001d4af"
  );
  HTML5_ENTITIES.put(
   "tscr;",
   "\\U0001d4c9"
  );
  HTML5_ENTITIES.put(
   "TScy;",
   "\\u0426"
  );
  HTML5_ENTITIES.put(
   "tscy;",
   "\\u0446"
  );
  HTML5_ENTITIES.put(
   "TSHcy;",
   "\\u040b"
  );
  HTML5_ENTITIES.put(
   "tshcy;",
   "\\u045b"
  );
  HTML5_ENTITIES.put(
   "Tstrok;",
   "\\u0166"
  );
  HTML5_ENTITIES.put(
   "tstrok;",
   "\\u0167"
  );
  HTML5_ENTITIES.put(
   "twixt;",
   "\\u226c"
  );
  HTML5_ENTITIES.put(
   "twoheadleftarrow;",
   "\\u219e"
  );
  HTML5_ENTITIES.put(
   "twoheadrightarrow;",
   "\\u21a0"
  );
  HTML5_ENTITIES.put(
   "Uacute",
   "\\xda"
  );
  HTML5_ENTITIES.put(
   "uacute",
   "\\xfa"
  );
  HTML5_ENTITIES.put(
   "Uacute;",
   "\\xda"
  );
  HTML5_ENTITIES.put(
   "uacute;",
   "\\xfa"
  );
  HTML5_ENTITIES.put(
   "Uarr;",
   "\\u219f"
  );
  HTML5_ENTITIES.put(
   "uArr;",
   "\\u21d1"
  );
  HTML5_ENTITIES.put(
   "uarr;",
   "\\u2191"
  );
  HTML5_ENTITIES.put(
   "Uarrocir;",
   "\\u2949"
  );
  HTML5_ENTITIES.put(
   "Ubrcy;",
   "\\u040e"
  );
  HTML5_ENTITIES.put(
   "ubrcy;",
   "\\u045e"
  );
  HTML5_ENTITIES.put(
   "Ubreve;",
   "\\u016c"
  );
  HTML5_ENTITIES.put(
   "ubreve;",
   "\\u016d"
  );
  HTML5_ENTITIES.put(
   "Ucirc",
   "\\xdb"
  );
  HTML5_ENTITIES.put(
   "ucirc",
   "\\xfb"
  );
  HTML5_ENTITIES.put(
   "Ucirc;",
   "\\xdb"
  );
  HTML5_ENTITIES.put(
   "ucirc;",
   "\\xfb"
  );
  HTML5_ENTITIES.put(
   "Ucy;",
   "\\u0423"
  );
  HTML5_ENTITIES.put(
   "ucy;",
   "\\u0443"
  );
  HTML5_ENTITIES.put(
   "udarr;",
   "\\u21c5"
  );
  HTML5_ENTITIES.put(
   "Udblac;",
   "\\u0170"
  );
  HTML5_ENTITIES.put(
   "udblac;",
   "\\u0171"
  );
  HTML5_ENTITIES.put(
   "udhar;",
   "\\u296e"
  );
  HTML5_ENTITIES.put(
   "ufisht;",
   "\\u297e"
  );
  HTML5_ENTITIES.put(
   "Ufr;",
   "\\U0001d518"
  );
  HTML5_ENTITIES.put(
   "ufr;",
   "\\U0001d532"
  );
  HTML5_ENTITIES.put(
   "Ugrave",
   "\\xd9"
  );
  HTML5_ENTITIES.put(
   "ugrave",
   "\\xf9"
  );
  HTML5_ENTITIES.put(
   "Ugrave;",
   "\\xd9"
  );
  HTML5_ENTITIES.put(
   "ugrave;",
   "\\xf9"
  );
  HTML5_ENTITIES.put(
   "uHar;",
   "\\u2963"
  );
  HTML5_ENTITIES.put(
   "uharl;",
   "\\u21bf"
  );
  HTML5_ENTITIES.put(
   "uharr;",
   "\\u21be"
  );
  HTML5_ENTITIES.put(
   "uhblk;",
   "\\u2580"
  );
  HTML5_ENTITIES.put(
   "ulcorn;",
   "\\u231c"
  );
  HTML5_ENTITIES.put(
   "ulcorner;",
   "\\u231c"
  );
  HTML5_ENTITIES.put(
   "ulcrop;",
   "\\u230f"
  );
  HTML5_ENTITIES.put(
   "ultri;",
   "\\u25f8"
  );
  HTML5_ENTITIES.put(
   "Umacr;",
   "\\u016a"
  );
  HTML5_ENTITIES.put(
   "umacr;",
   "\\u016b"
  );
  HTML5_ENTITIES.put(
   "uml",
   "\\xa8"
  );
  HTML5_ENTITIES.put(
   "uml;",
   "\\xa8"
  );
  HTML5_ENTITIES.put(
   "UnderBar;",
   "_"
  );
  HTML5_ENTITIES.put(
   "UnderBrace;",
   "\\u23df"
  );
  HTML5_ENTITIES.put(
   "UnderBracket;",
   "\\u23b5"
  );
  HTML5_ENTITIES.put(
   "UnderParenthesis;",
   "\\u23dd"
  );
  HTML5_ENTITIES.put(
   "Union;",
   "\\u22c3"
  );
  HTML5_ENTITIES.put(
   "UnionPlus;",
   "\\u228e"
  );
  HTML5_ENTITIES.put(
   "Uogon;",
   "\\u0172"
  );
  HTML5_ENTITIES.put(
   "uogon;",
   "\\u0173"
  );
  HTML5_ENTITIES.put(
   "Uopf;",
   "\\U0001d54c"
  );
  HTML5_ENTITIES.put(
   "uopf;",
   "\\U0001d566"
  );
  HTML5_ENTITIES.put(
   "UpArrow;",
   "\\u2191"
  );
  HTML5_ENTITIES.put(
   "Uparrow;",
   "\\u21d1"
  );
  HTML5_ENTITIES.put(
   "uparrow;",
   "\\u2191"
  );
  HTML5_ENTITIES.put(
   "UpArrowBar;",
   "\\u2912"
  );
  HTML5_ENTITIES.put(
   "UpArrowDownArrow;",
   "\\u21c5"
  );
  HTML5_ENTITIES.put(
   "UpDownArrow;",
   "\\u2195"
  );
  HTML5_ENTITIES.put(
   "Updownarrow;",
   "\\u21d5"
  );
  HTML5_ENTITIES.put(
   "updownarrow;",
   "\\u2195"
  );
  HTML5_ENTITIES.put(
   "UpEquilibrium;",
   "\\u296e"
  );
  HTML5_ENTITIES.put(
   "upharpoonleft;",
   "\\u21bf"
  );
  HTML5_ENTITIES.put(
   "upharpoonright;",
   "\\u21be"
  );
  HTML5_ENTITIES.put(
   "uplus;",
   "\\u228e"
  );
  HTML5_ENTITIES.put(
   "UpperLeftArrow;",
   "\\u2196"
  );
  HTML5_ENTITIES.put(
   "UpperRightArrow;",
   "\\u2197"
  );
  HTML5_ENTITIES.put(
   "Upsi;",
   "\\u03d2"
  );
  HTML5_ENTITIES.put(
   "upsi;",
   "\\u03c5"
  );
  HTML5_ENTITIES.put(
   "upsih;",
   "\\u03d2"
  );
  HTML5_ENTITIES.put(
   "Upsilon;",
   "\\u03a5"
  );
  HTML5_ENTITIES.put(
   "upsilon;",
   "\\u03c5"
  );
  HTML5_ENTITIES.put(
   "UpTee;",
   "\\u22a5"
  );
  HTML5_ENTITIES.put(
   "UpTeeArrow;",
   "\\u21a5"
  );
  HTML5_ENTITIES.put(
   "upuparrows;",
   "\\u21c8"
  );
  HTML5_ENTITIES.put(
   "urcorn;",
   "\\u231d"
  );
  HTML5_ENTITIES.put(
   "urcorner;",
   "\\u231d"
  );
  HTML5_ENTITIES.put(
   "urcrop;",
   "\\u230e"
  );
  HTML5_ENTITIES.put(
   "Uring;",
   "\\u016e"
  );
  HTML5_ENTITIES.put(
   "uring;",
   "\\u016f"
  );
  HTML5_ENTITIES.put(
   "urtri;",
   "\\u25f9"
  );
  HTML5_ENTITIES.put(
   "Uscr;",
   "\\U0001d4b0"
  );
  HTML5_ENTITIES.put(
   "uscr;",
   "\\U0001d4ca"
  );
  HTML5_ENTITIES.put(
   "utdot;",
   "\\u22f0"
  );
  HTML5_ENTITIES.put(
   "Utilde;",
   "\\u0168"
  );
  HTML5_ENTITIES.put(
   "utilde;",
   "\\u0169"
  );
  HTML5_ENTITIES.put(
   "utri;",
   "\\u25b5"
  );
  HTML5_ENTITIES.put(
   "utrif;",
   "\\u25b4"
  );
  HTML5_ENTITIES.put(
   "uuarr;",
   "\\u21c8"
  );
  HTML5_ENTITIES.put(
   "Uuml",
   "\\xdc"
  );
  HTML5_ENTITIES.put(
   "uuml",
   "\\xfc"
  );
  HTML5_ENTITIES.put(
   "Uuml;",
   "\\xdc"
  );
  HTML5_ENTITIES.put(
   "uuml;",
   "\\xfc"
  );
  HTML5_ENTITIES.put(
   "uwangle;",
   "\\u29a7"
  );
  HTML5_ENTITIES.put(
   "vangrt;",
   "\\u299c"
  );
  HTML5_ENTITIES.put(
   "varepsilon;",
   "\\u03f5"
  );
  HTML5_ENTITIES.put(
   "varkappa;",
   "\\u03f0"
  );
  HTML5_ENTITIES.put(
   "varnothing;",
   "\\u2205"
  );
  HTML5_ENTITIES.put(
   "varphi;",
   "\\u03d5"
  );
  HTML5_ENTITIES.put(
   "varpi;",
   "\\u03d6"
  );
  HTML5_ENTITIES.put(
   "varpropto;",
   "\\u221d"
  );
  HTML5_ENTITIES.put(
   "vArr;",
   "\\u21d5"
  );
  HTML5_ENTITIES.put(
   "varr;",
   "\\u2195"
  );
  HTML5_ENTITIES.put(
   "varrho;",
   "\\u03f1"
  );
  HTML5_ENTITIES.put(
   "varsigma;",
   "\\u03c2"
  );
  HTML5_ENTITIES.put(
   "varsubsetneq;",
   "\\u228a\\ufe00"
  );
  HTML5_ENTITIES.put(
   "varsubsetneqq;",
   "\\u2acb\\ufe00"
  );
  HTML5_ENTITIES.put(
   "varsupsetneq;",
   "\\u228b\\ufe00"
  );
  HTML5_ENTITIES.put(
   "varsupsetneqq;",
   "\\u2acc\\ufe00"
  );
  HTML5_ENTITIES.put(
   "vartheta;",
   "\\u03d1"
  );
  HTML5_ENTITIES.put(
   "vartriangleleft;",
   "\\u22b2"
  );
  HTML5_ENTITIES.put(
   "vartriangleright;",
   "\\u22b3"
  );
  HTML5_ENTITIES.put(
   "Vbar;",
   "\\u2aeb"
  );
  HTML5_ENTITIES.put(
   "vBar;",
   "\\u2ae8"
  );
  HTML5_ENTITIES.put(
   "vBarv;",
   "\\u2ae9"
  );
  HTML5_ENTITIES.put(
   "Vcy;",
   "\\u0412"
  );
  HTML5_ENTITIES.put(
   "vcy;",
   "\\u0432"
  );
  HTML5_ENTITIES.put(
   "VDash;",
   "\\u22ab"
  );
  HTML5_ENTITIES.put(
   "Vdash;",
   "\\u22a9"
  );
  HTML5_ENTITIES.put(
   "vDash;",
   "\\u22a8"
  );
  HTML5_ENTITIES.put(
   "vdash;",
   "\\u22a2"
  );
  HTML5_ENTITIES.put(
   "Vdashl;",
   "\\u2ae6"
  );
  HTML5_ENTITIES.put(
   "Vee;",
   "\\u22c1"
  );
  HTML5_ENTITIES.put(
   "vee;",
   "\\u2228"
  );
  HTML5_ENTITIES.put(
   "veebar;",
   "\\u22bb"
  );
  HTML5_ENTITIES.put(
   "veeeq;",
   "\\u225a"
  );
  HTML5_ENTITIES.put(
   "vellip;",
   "\\u22ee"
  );
  HTML5_ENTITIES.put(
   "Verbar;",
   "\\u2016"
  );
  HTML5_ENTITIES.put(
   "verbar;",
   "|"
  );
  HTML5_ENTITIES.put(
   "Vert;",
   "\\u2016"
  );
  HTML5_ENTITIES.put(
   "vert;",
   "|"
  );
  HTML5_ENTITIES.put(
   "VerticalBar;",
   "\\u2223"
  );
  HTML5_ENTITIES.put(
   "VerticalLine;",
   "|"
  );
  HTML5_ENTITIES.put(
   "VerticalSeparator;",
   "\\u2758"
  );
  HTML5_ENTITIES.put(
   "VerticalTilde;",
   "\\u2240"
  );
  HTML5_ENTITIES.put(
   "VeryThinSpace;",
   "\\u200a"
  );
  HTML5_ENTITIES.put(
   "Vfr;",
   "\\U0001d519"
  );
  HTML5_ENTITIES.put(
   "vfr;",
   "\\U0001d533"
  );
  HTML5_ENTITIES.put(
   "vltri;",
   "\\u22b2"
  );
  HTML5_ENTITIES.put(
   "vnsub;",
   "\\u2282\\u20d2"
  );
  HTML5_ENTITIES.put(
   "vnsup;",
   "\\u2283\\u20d2"
  );
  HTML5_ENTITIES.put(
   "Vopf;",
   "\\U0001d54d"
  );
  HTML5_ENTITIES.put(
   "vopf;",
   "\\U0001d567"
  );
  HTML5_ENTITIES.put(
   "vprop;",
   "\\u221d"
  );
  HTML5_ENTITIES.put(
   "vrtri;",
   "\\u22b3"
  );
  HTML5_ENTITIES.put(
   "Vscr;",
   "\\U0001d4b1"
  );
  HTML5_ENTITIES.put(
   "vscr;",
   "\\U0001d4cb"
  );
  HTML5_ENTITIES.put(
   "vsubnE;",
   "\\u2acb\\ufe00"
  );
  HTML5_ENTITIES.put(
   "vsubne;",
   "\\u228a\\ufe00"
  );
  HTML5_ENTITIES.put(
   "vsupnE;",
   "\\u2acc\\ufe00"
  );
  HTML5_ENTITIES.put(
   "vsupne;",
   "\\u228b\\ufe00"
  );
  HTML5_ENTITIES.put(
   "Vvdash;",
   "\\u22aa"
  );
  HTML5_ENTITIES.put(
   "vzigzag;",
   "\\u299a"
  );
  HTML5_ENTITIES.put(
   "Wcirc;",
   "\\u0174"
  );
  HTML5_ENTITIES.put(
   "wcirc;",
   "\\u0175"
  );
  HTML5_ENTITIES.put(
   "wedbar;",
   "\\u2a5f"
  );
  HTML5_ENTITIES.put(
   "Wedge;",
   "\\u22c0"
  );
  HTML5_ENTITIES.put(
   "wedge;",
   "\\u2227"
  );
  HTML5_ENTITIES.put(
   "wedgeq;",
   "\\u2259"
  );
  HTML5_ENTITIES.put(
   "weierp;",
   "\\u2118"
  );
  HTML5_ENTITIES.put(
   "Wfr;",
   "\\U0001d51a"
  );
  HTML5_ENTITIES.put(
   "wfr;",
   "\\U0001d534"
  );
  HTML5_ENTITIES.put(
   "Wopf;",
   "\\U0001d54e"
  );
  HTML5_ENTITIES.put(
   "wopf;",
   "\\U0001d568"
  );
  HTML5_ENTITIES.put(
   "wp;",
   "\\u2118"
  );
  HTML5_ENTITIES.put(
   "wr;",
   "\\u2240"
  );
  HTML5_ENTITIES.put(
   "wreath;",
   "\\u2240"
  );
  HTML5_ENTITIES.put(
   "Wscr;",
   "\\U0001d4b2"
  );
  HTML5_ENTITIES.put(
   "wscr;",
   "\\U0001d4cc"
  );
  HTML5_ENTITIES.put(
   "xcap;",
   "\\u22c2"
  );
  HTML5_ENTITIES.put(
   "xcirc;",
   "\\u25ef"
  );
  HTML5_ENTITIES.put(
   "xcup;",
   "\\u22c3"
  );
  HTML5_ENTITIES.put(
   "xdtri;",
   "\\u25bd"
  );
  HTML5_ENTITIES.put(
   "Xfr;",
   "\\U0001d51b"
  );
  HTML5_ENTITIES.put(
   "xfr;",
   "\\U0001d535"
  );
  HTML5_ENTITIES.put(
   "xhArr;",
   "\\u27fa"
  );
  HTML5_ENTITIES.put(
   "xharr;",
   "\\u27f7"
  );
  HTML5_ENTITIES.put(
   "Xi;",
   "\\u039e"
  );
  HTML5_ENTITIES.put(
   "xi;",
   "\\u03be"
  );
  HTML5_ENTITIES.put(
   "xlArr;",
   "\\u27f8"
  );
  HTML5_ENTITIES.put(
   "xlarr;",
   "\\u27f5"
  );
  HTML5_ENTITIES.put(
   "xmap;",
   "\\u27fc"
  );
  HTML5_ENTITIES.put(
   "xnis;",
   "\\u22fb"
  );
  HTML5_ENTITIES.put(
   "xodot;",
   "\\u2a00"
  );
  HTML5_ENTITIES.put(
   "Xopf;",
   "\\U0001d54f"
  );
  HTML5_ENTITIES.put(
   "xopf;",
   "\\U0001d569"
  );
  HTML5_ENTITIES.put(
   "xoplus;",
   "\\u2a01"
  );
  HTML5_ENTITIES.put(
   "xotime;",
   "\\u2a02"
  );
  HTML5_ENTITIES.put(
   "xrArr;",
   "\\u27f9"
  );
  HTML5_ENTITIES.put(
   "xrarr;",
   "\\u27f6"
  );
  HTML5_ENTITIES.put(
   "Xscr;",
   "\\U0001d4b3"
  );
  HTML5_ENTITIES.put(
   "xscr;",
   "\\U0001d4cd"
  );
  HTML5_ENTITIES.put(
   "xsqcup;",
   "\\u2a06"
  );
  HTML5_ENTITIES.put(
   "xuplus;",
   "\\u2a04"
  );
  HTML5_ENTITIES.put(
   "xutri;",
   "\\u25b3"
  );
  HTML5_ENTITIES.put(
   "xvee;",
   "\\u22c1"
  );
  HTML5_ENTITIES.put(
   "xwedge;",
   "\\u22c0"
  );
  HTML5_ENTITIES.put(
   "Yacute",
   "\\xdd"
  );
  HTML5_ENTITIES.put(
   "yacute",
   "\\xfd"
  );
  HTML5_ENTITIES.put(
   "Yacute;",
   "\\xdd"
  );
  HTML5_ENTITIES.put(
   "yacute;",
   "\\xfd"
  );
  HTML5_ENTITIES.put(
   "YAcy;",
   "\\u042f"
  );
  HTML5_ENTITIES.put(
   "yacy;",
   "\\u044f"
  );
  HTML5_ENTITIES.put(
   "Ycirc;",
   "\\u0176"
  );
  HTML5_ENTITIES.put(
   "ycirc;",
   "\\u0177"
  );
  HTML5_ENTITIES.put(
   "Ycy;",
   "\\u042b"
  );
  HTML5_ENTITIES.put(
   "ycy;",
   "\\u044b"
  );
  HTML5_ENTITIES.put(
   "yen",
   "\\xa5"
  );
  HTML5_ENTITIES.put(
   "yen;",
   "\\xa5"
  );
  HTML5_ENTITIES.put(
   "Yfr;",
   "\\U0001d51c"
  );
  HTML5_ENTITIES.put(
   "yfr;",
   "\\U0001d536"
  );
  HTML5_ENTITIES.put(
   "YIcy;",
   "\\u0407"
  );
  HTML5_ENTITIES.put(
   "yicy;",
   "\\u0457"
  );
  HTML5_ENTITIES.put(
   "Yopf;",
   "\\U0001d550"
  );
  HTML5_ENTITIES.put(
   "yopf;",
   "\\U0001d56a"
  );
  HTML5_ENTITIES.put(
   "Yscr;",
   "\\U0001d4b4"
  );
  HTML5_ENTITIES.put(
   "yscr;",
   "\\U0001d4ce"
  );
  HTML5_ENTITIES.put(
   "YUcy;",
   "\\u042e"
  );
  HTML5_ENTITIES.put(
   "yucy;",
   "\\u044e"
  );
  HTML5_ENTITIES.put(
   "yuml",
   "\\xff"
  );
  HTML5_ENTITIES.put(
   "Yuml;",
   "\\u0178"
  );
  HTML5_ENTITIES.put(
   "yuml;",
   "\\xff"
  );
  HTML5_ENTITIES.put(
   "Zacute;",
   "\\u0179"
  );
  HTML5_ENTITIES.put(
   "zacute;",
   "\\u017a"
  );
  HTML5_ENTITIES.put(
   "Zcaron;",
   "\\u017d"
  );
  HTML5_ENTITIES.put(
   "zcaron;",
   "\\u017e"
  );
  HTML5_ENTITIES.put(
   "Zcy;",
   "\\u0417"
  );
  HTML5_ENTITIES.put(
   "zcy;",
   "\\u0437"
  );
  HTML5_ENTITIES.put(
   "Zdot;",
   "\\u017b"
  );
  HTML5_ENTITIES.put(
   "zdot;",
   "\\u017c"
  );
  HTML5_ENTITIES.put(
   "zeetrf;",
   "\\u2128"
  );
  HTML5_ENTITIES.put(
   "ZeroWidthSpace;",
   "\\u200b"
  );
  HTML5_ENTITIES.put(
   "Zeta;",
   "\\u0396"
  );
  HTML5_ENTITIES.put(
   "zeta;",
   "\\u03b6"
  );
  HTML5_ENTITIES.put(
   "Zfr;",
   "\\u2128"
  );
  HTML5_ENTITIES.put(
   "zfr;",
   "\\U0001d537"
  );
  HTML5_ENTITIES.put(
   "ZHcy;",
   "\\u0416"
  );
  HTML5_ENTITIES.put(
   "zhcy;",
   "\\u0436"
  );
  HTML5_ENTITIES.put(
   "zigrarr;",
   "\\u21dd"
  );
  HTML5_ENTITIES.put(
   "Zopf;",
   "\\u2124"
  );
  HTML5_ENTITIES.put(
   "zopf;",
   "\\U0001d56b"
  );
  HTML5_ENTITIES.put(
   "Zscr;",
   "\\U0001d4b5"
  );
  HTML5_ENTITIES.put(
   "zscr;",
   "\\U0001d4cf"
  );
  HTML5_ENTITIES.put(
   "zwj;",
   "\\u200d"
  );
  HTML5_ENTITIES.put(
   "zwnj;",
   "\\u200c,"
  );

 }

 public HtmlUtils() {

 }

 /**
  * Replace special characters "&", "<" and ">" to HTML-safe
  * sequences. If the optional flag quote is true (the default), the
  * quotation mark characters, both double quote (") and single quote
  * (') characters are also translated.
  */
 public static String escape(
  String s,
  boolean quote) {
  s = s.replace(
    "&",
    "&amp;"
   ) // Must be done first!
   .replace(
    "<",
    "&lt;"
   ).replace(
    ">",
    "&gt;"
   );
  if (quote) {
   s = s.replace(
    "\"",
    "&quot;"
   ).replace(
    "'",
    "&#x27;"
   );
  }
  return s;
 }

 public static String escape(String s) {
  return escape(
   s,
   true
  );
 }

 private static String replaceCharRef(String s) {
  if (s.charAt(0) == '#') {
   // numeric charref
   int num;
   if (s.charAt(1) == 'x' || s.charAt(1) == 'X') {
    num = Integer.parseInt(
     s.substring(2).replaceAll(
      ";$",
      ""
     ),
     16
    );
   } else {
    num = Integer.parseInt(s.substring(1).replaceAll(
     ";$",
     ""
    ));
   }
   if (INVALID_CHARREFS.containsKey(num)) {
    return INVALID_CHARREFS.get(num);
   }
   if ((num >= 0xD800 && num <= 0xDFFF) || num > 0x10FFFF) {
    return "\uFFFD";
   }
   if (INVALID_CODEPOINTS.contains(num)) {
    return "";
   }
   return String.valueOf((char) num);
  } else {
   // named charref
   if (HTML5_ENTITIES.containsKey(s)) {
    return HTML5_ENTITIES.get(s);
   }
   // find the longest matching name (as defined by the standard)
   for (int x = s.length() - 1; x > 1; x--) {
    String substr = s.substring(
     0,
     x
    );
    if (HTML5_ENTITIES.containsKey(substr)) {
     return HTML5_ENTITIES.get(substr) + s.substring(x);
    }
   }
   return "&" + s;
  }
 }

 /**
  * Convert all named and numeric character references (e.g. &gt;,
  * &#62;, &x3e;) in the string s to the corresponding unicode
  * characters. This function uses the rules defined by the HTML 5
  * standard for both valid and invalid character references, and the
  * list of HTML 5 named character references defined in
  * html.entities.html5.
  */
 public static String unescape(String s) {
  if (!s.contains("&")) {
   return s;
  }
  StringBuffer result = new StringBuffer();
  Matcher matcher = CHARREF.matcher(s);
  while (matcher.find()) {
   matcher.appendReplacement(
    result,
    replaceCharRef(matcher.group(1))
   );
  }
  matcher.appendTail(result);
  return result.toString();
 }

 public static String unescape2(String s) {
  return unescape(s.replace(
   "<br>",
   "\r\n"
  ));
 }

 public static String getFirstNonEmpty(
  String first,
  String second) {
  return first != null && !first.isEmpty() ? first : second;
 }
}