package com.kewtoms.whatappsdo.model;

import android.content.Context;

import android.content.SharedPreferences;
import android.util.Log;
import android.widget.Toast;

import com.kewtoms.whatappsdo.data.Constants;


public class UserUsage {
 public static final String TAG = "APP:UserUsage";
 private static final String PREFS_NAME = "MyAppPrefs";
 private static final String USAGE_COUNT_KEY = "usage_count";
 private final Context context;

 public UserUsage(Context context) {
  this.context = context;
 }


 public int getUsageCount() {
  SharedPreferences prefs = context.getSharedPreferences(
   PREFS_NAME,
   Context.MODE_PRIVATE
  );
  return prefs.getInt(
   USAGE_COUNT_KEY,
   0
  );
 }

 public void setUsageCount(int usageCount) {
  SharedPreferences prefs = context.getSharedPreferences(
   PREFS_NAME,
   Context.MODE_PRIVATE
  );
  SharedPreferences.Editor editor = prefs.edit();
  editor.putInt(
   USAGE_COUNT_KEY,
   usageCount
  );
  editor.apply();
 }

 public void resetUsageCount() {
  SharedPreferences prefs = context.getSharedPreferences(
   PREFS_NAME,
   Context.MODE_PRIVATE
  );
  SharedPreferences.Editor editor = prefs.edit();
  editor.putInt(
   USAGE_COUNT_KEY,
   0
  );
  editor.apply();
 }

 public void increaseUsageCount() {
  setUsageCount(getUsageCount() + 1);
 }


 public void promptRegistration() {
  // Show a dialog or redirect to registration activity
  int usageCount = this.getUsageCount();
  String messageStr =
   "Used " + usageCount + " times, exceeded free usage count " + Constants.FREE_USAGE_COUNT_MONTHLY + "\n";
  Log.i(
   TAG,
   messageStr
  );
  Toast.makeText(
   context,
   messageStr + "Please register to continue using this feature.",
   Toast.LENGTH_LONG
  ).show();
 }
}
