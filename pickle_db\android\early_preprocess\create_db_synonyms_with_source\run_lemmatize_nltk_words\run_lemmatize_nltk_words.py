import shutil
import sys
import os
import spacy
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.database_utils.run_pickle_database_split import remove_pickles
from tqdm import tqdm
from server.api.pickle_db.android.early_preprocess.create_db_synonyms_with_source.create_db_synonyms_with_source_utils import (
    update_db_synonyms_with_powertheasaurus_api,
    update_db_synonyms_by_scraping_powertheasaurus,
)
import jsonlines
from server.api.utils.keywords.get_keywords import lemmatization
from nltk.corpus import words
from const_path import proj_ser_api_db_early_create_syn_path

from nltk.corpus import stopwords

stopwords = stopwords.words("english")


def run_lemmatize_nltk_words():
    """
    TODO
    """

    script_directory_path = os.path.dirname(
        os.path.abspath(sys.argv[0]),
    )

    """
    Get synonyms from pt
    """
    # Create a db ntlk lemmatize (in current dir)
    db_ntlk_lemmatize = PickleDatabaseSplit(
        db_fpath=script_directory_path,
        db_name="db_ntlk_lemmatize",
        load_by_thread=False,
    )
    nlp_model = spacy.load("en_core_web_sm", disable=["parser", "ner"])

    # Get words from nltk
    word_list = words.words()

    # Get words that need to scrape (should be lemmatized and skip stopwords).
    dict_wordLemmetized = {}
    for word in tqdm(word_list):
        # Skip stopwords
        if word in stopwords:
            continue

        # Lemmatize.
        word_lemm = lemmatization(
            nlp_model=nlp_model,
            text=word,
        )

        # Skip stopwords for lemmatized word
        if word_lemm in stopwords:
            continue

        dict_wordLemmetized[word_lemm] = 1

    # Update and dump
    db_ntlk_lemmatize.update_data(datas=dict_wordLemmetized)

    db_ntlk_lemmatize.dump_dirty_parts_to_pickles_thread()
    db_ntlk_lemmatize.dump_json()


def main():
    run_lemmatize_nltk_words()


if __name__ == "__main__":
    sys.exit(main())
