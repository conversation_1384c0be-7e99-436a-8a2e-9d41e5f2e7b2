import sys
import pytest
from tomsze_utils.scraping_utils import scrape_apkgk, scrape_apksos, scrape_apksupport


@pytest.mark.skip(reason="No need to test this function since the site is offline")
def test_scrape_apkgk():
    app_id = "com.yusibo.spider"
    success, scrape_data = scrape_apkgk(app_id=app_id)
    assert success == True
    assert scrape_data != {}

    app_id = "com.xx.xx"
    success, scrape_data = scrape_apkgk(app_id=app_id)
    assert success == False
    assert scrape_data == {}


def test_scrape_apksos():
    app_id = "com.Devapp.GymorJailHorrorGigachad"
    success, scrape_data = scrape_apksos(app_id=app_id)
    assert success == True
    assert scrape_data != {}

    app_id = "com.xx.xx"
    success, scrape_data = scrape_apksos(app_id=app_id)
    assert success == False
    assert scrape_data == {}


def test_scrape_apksupport():
    app_id = "com.gamenese.animal.battle.simulator3d"
    success, scrape_data = scrape_apksupport(app_id=app_id)
    assert success == True
    assert scrape_data != {}

    app_id = "com.xx.xx"
    success, scrape_data = scrape_apksupport(app_id=app_id)
    assert success == False
    assert scrape_data == {}


def test_scrape_play_store():
    from tomsze_utils.scraping_utils import scrape_play_store

    app_id = "com.supercell.clashofclans"
    scrap_success, result = scrape_play_store(app_id, lang="en")
    assert scrap_success == True
    assert result != {}
    assert result["source"] == "playstore"

    app_id = "com.xx.xx"
    scrap_success, result = scrape_play_store(app_id, lang="en")
    assert scrap_success == False
    assert result == {}


def main():
    test_scrape_apkgk()
    test_scrape_apksos()
    test_scrape_apksupport()
    test_scrape_play_store()


if __name__ == "__main__":
    sys.exit(main())
