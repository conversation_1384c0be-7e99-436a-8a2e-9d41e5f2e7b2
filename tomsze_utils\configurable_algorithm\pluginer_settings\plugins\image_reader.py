"""App plugin"""

import os
import numpy as np
import cv2
from dataclasses import dataclass
from typing import List
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import Data, parse_data


@dataclass
class PluginImageReader:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        var_pool = data_obj.dict_var

        for image_reader_step in config["all_image_reader"]:
            # parse step name
            step_name = parse_data(
                logger, "step_name", data_obj, image_reader_step, config
            )

            # run only the step that is same as given argument
            if step_name == current_step:
                use_image_path = parse_data(
                    logger, "use_image_path", data_obj, image_reader_step, config
                )
                image_path = parse_data(
                    logger, "image_path", data_obj, image_reader_step, config
                )
                loop_image = parse_data(
                    logger, "loop_image", data_obj, image_reader_step, config
                )
                use_image_folder_path = parse_data(
                    logger,
                    "use_image_folder_path",
                    data_obj,
                    image_reader_step,
                    config,
                )
                image_folder_path = parse_data(
                    logger,
                    "image_folder_path",
                    data_obj,
                    image_reader_step,
                    config,
                )
                loop_folder = parse_data(
                    logger, "loop_folder", data_obj, image_reader_step, config
                )
                image_types = parse_data(
                    logger, "image_types", data_obj, image_reader_step, config
                )
                buffer_to_which = parse_data(
                    logger, "buffer_to_which", data_obj, image_reader_step, config
                )

                # get data_obj paths
                list_image_path = []
                list_image_filename = []
                if not use_image_path and loop_folder:
                    for dirpath, _, filenames in os.walk(image_folder_path):
                        for filename in filenames:
                            filetype = filename.split(".")[-1]

                            if filetype in image_types:
                                image_path = os.path.join(dirpath, filename)
                                list_image_path.append(image_path)
                                list_image_filename.append(filename)
                    list_image_path.sort()
                if use_image_path and loop_image:
                    for path in image_path:
                        filetype = path.split(".")[-1]
                        filename = path.split("/")[-1].replace("." + filetype, "")
                        if filetype in image_types:
                            list_image_path.append(path)
                            list_image_filename.append(filename)
                    list_image_path.sort()

                # read an data_obj
                # assert use_image_folder_path == True and loop_folder == True, \
                # 'if loop_folder is set True, use_image_folder_path must also be True'
                if use_image_path and not loop_image:
                    current_image_path = image_path
                    current_image_filename = image_path.split("/")[-1]
                elif use_image_path and loop_image:
                    current_image_path = list_image_path[
                        data_obj.dict_var["run_counter"]
                    ]
                    current_image_filename = list_image_filename[
                        data_obj.dict_var["run_counter"]
                    ]
                elif use_image_folder_path and not loop_folder:
                    current_image_path = list_image_path[0]
                    current_image_filename = list_image_filename[0]
                elif use_image_folder_path and loop_folder:
                    current_image_path = list_image_path[
                        data_obj.dict_var["run_counter"]
                    ]
                    current_image_filename = list_image_filename[
                        data_obj.dict_var["run_counter"]
                    ]

                filetype = current_image_path.split(".")[-1]
                current_image_front_filename = current_image_filename.replace(
                    "." + filetype, ""
                )
                im = cv2.imread(current_image_path, cv2.IMREAD_COLOR)

                # Image read errors.
                read_error = False
                if not os.path.exists(current_image_path):
                    read_error = True
                    error_text = f'-1:image read error. Image path "{current_image_path}" is not exist.'
                    data_obj.dict_var["flag"] = error_text
                    print(error_text)
                    im = np.zeros((0, 0))

                if im is None:
                    read_error = True
                    data_obj.dict_var["flag"] = "-2:image read error. Image is None"
                    im = np.zeros((0, 0))

                data_obj.dict_var[buffer_to_which] = im
                data_obj.dict_var["current_image_height"] = im.shape[0]
                data_obj.dict_var["current_image_width"] = im.shape[1]
                data_obj.dict_var[f"{current_step}.height"] = im.shape[0]
                data_obj.dict_var[f"{current_step}.width"] = im.shape[1]
                data_obj.dict_var[f"{current_step}.read_error"] = read_error

            # end if step_name == site
        # end for image_reader_step

        # store some results
        data_obj.dict_var[current_step] = "test_result_BasicAoiImageReader"
        if "image_paths" not in data_obj.dict_var.keys():
            data_obj.dict_var["image_paths"] = list_image_path

        data_obj.dict_var["current_image_path"] = current_image_path
        data_obj.dict_var["current_image_filename"] = current_image_filename
        data_obj.dict_var["current_image_front_filename"] = current_image_front_filename

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
