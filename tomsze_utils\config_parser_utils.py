import ast
import pyjson5
import sys
import os
import configparser
import logging
from typing import Any, List
from path import Path
import toml


def create_config_parser_from_path(
    config_path: str,
) -> configparser.ConfigParser:
    """
    Return a configparser.ConfigParser.
    If the given config path does not exist, create it.
    If the given config path exists, read it into the parser.

    Args:
        config_path (str): The path to the configuration file.

    Returns:
        configparser.ConfigParser: A ConfigParser object with the configuration.

    Examples:
        >>> parser = create_config_parser_from_path("config.ini")
        >>> parser["DEFAULT"]["key"] = "value"
        >>> parser.write(open("config.ini", "w"))

        >>> parser = create_config_parser_from_path("config.ini")
        >>> print(parser["DEFAULT"]["key"])  # Output: value
    """
    config_parser = configparser.ConfigParser()
    directory_path = Path(config_path).parent

    if not os.path.exists(directory_path):
        logging.info(f'"{directory_path}" does not exist. Create it.')
        os.makedirs(directory_path)

    if not os.path.exists(config_path):
        logging.info(f'"{config_path}" does not exist. Create it.')
        # config_parser[seciion_name] = {}
        with open(config_path, "w") as configfile:
            config_parser.write(configfile)
    elif os.path.exists(config_path):
        logging.info(f'"{config_path}" exists. Read it config parser.')
        config_parser.read(config_path)

    return config_parser


def set_and_write_parser_to_file(
    config_parser: configparser.ConfigParser,
    config_path: str,
    section: str,
    key: str,
    data: Any,
) -> str:  # Change return type to str
    """
    Set data to config parser and write it to the specified config path.

    Args:
        config_parser (configparser.ConfigParser): The config parser to modify.
        config_path (str): The path to the configuration file.
        section (str): The section in the config where the key-value pair will be set.
        key (str): The key to set in the specified section.
        data (Any): The data to set for the specified key.

    Examples:
        ```python
        set_and_write_parser_to_file(config_parser, "config.ini", section="Settings", key="theme", data="dark")
        ```

        ```python
        set_and_write_parser_to_file(config_parser, "config.ini", section="User", key="username", data="admin")
        ```
    """
    if section not in config_parser:
        config_parser[section] = {}

    config_parser[section][key] = str(data)  # Must be str to write to file.
    if os.path.exists(config_path):
        with open(config_path, "w") as configfile:
            config_parser.write(configfile)

    return "done"


def read_from_config_parser(
    config_parser: configparser.ConfigParser,
    section: str,
    key: str,
    is_string: bool = True,
    default=None,
):
    """
    Read data from config parser.
    """
    data = default

    if config_parser.has_option(section, key):
        # data = config_parser[section][key]
        str_data = config_parser.get(section, key)

        data = evaluate_text(str_data, is_string, default)

    return data


def evaluate_text(text: str, is_string: bool = True, default: Any = None) -> Any:
    """
    Evaluate and convert a text string to its corresponding data type.

    Args:
        text (str): The text to evaluate.
        is_string (bool, optional): If True, return the text as a string. Defaults to True.
        default (Any, optional): The default value to return if evaluation fails. Defaults to None.

    Returns:
        Any: The evaluated data or the default value if evaluation fails.

    Examples:
        >>> evaluate_text("123")
        123

        >>> evaluate_text("true", is_string=False)
        True

        >>> evaluate_text("not_a_boolean", is_string=False, default="default_value")
        'default_value'
    """
    if is_string:
        return text

    if text.lower() == "true":
        text = "True"

    if text.lower() == "false":
        text = "False"

    try:
        data = ast.literal_eval(text)
    except Exception as e:
        return default

    return data


def load_json_config(config_file: str) -> dict:
    """
    Load a configuration file using pyjson5. First, an attempt is made to load
    using the given file. If that fails, the function will try loading an alternative
    file by swapping the extension from '.json' to '.json5' or vice versa.

    Args:
        config_file (str): The path to the configuration file.

    Returns:
        dict: The loaded configuration as a dictionary, or None if both attempts fail.

    Examples:
        >>> config = load_json_config("config.json")
        >>> print(config)  # Output: {'key': 'value'}

        >>> config = load_json_config("non_existent_file.json")
        >>> print(config)  # Output: None
    """
    # First attempt using the provided filename
    try:
        with open(config_file, "r", encoding="utf-8") as file:
            config = pyjson5.load(file)
            return config
    except Exception as first_error:
        # Swap the file extension
        base, ext = os.path.splitext(config_file)
        if ext.lower() == ".json":
            alt_file = base + ".json5"
        elif ext.lower() == ".json5":
            alt_file = base + ".json"
        else:
            alt_file = config_file + ".json5"

        # Second attempt using the alternative filename
        try:
            with open(alt_file, "r", encoding="utf-8") as file:
                config = pyjson5.load(file)
                return config
        except Exception as second_error:
            print(f"Failed to load config: {first_error} \nand also: {second_error}")
            return None


def load_toml_config_absolute_path(config_file: str) -> dict:
    """
    Load a TOML configuration file from the absolute path.

    Args:
        config_file (str): The path to the TOML configuration file.

    Returns:
        dict: The loaded configuration as a dictionary, or None if an error occurs.

    Examples:
        >>> config = load_toml_config_absolute_path("config.toml")
        >>> print(config)  # Output: {'key': 'value'}

        >>> config = load_toml_config_absolute_path("non_existent_file.toml")
        >>> print(config)  # Output: None
    """
    try:
        with open(config_file) as file:
            config = toml.load(file)
            return config
    except OSError as e:
        print(e)
        return None


def main():
    logging.basicConfig(
        level=logging.DEBUG,  # if logging.INFO, logger.debug('xx') will not be logged
        format="%(asctime)s.%(msecs)03d %(levelname)s %(module)s - %(funcName)s: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    config_path = "./test_config.ini"
    config_parser = create_config_parser_from_path(config_path)

    data = ["a", "b"]
    set_and_write_parser_to_file(config_parser, config_path, "data", "key", data)

    read_data = read_from_config_parser(config_parser, "data", "key", False)

    os.remove(config_path)


if __name__ == "__main__":
    sys.exit(main())
