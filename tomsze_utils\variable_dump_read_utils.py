import os
import pickle
from typing import Any, List
from tomsze_utils.time_code_utils import timeit
from tomsze_utils.encrypt_utils import encrypt_data


def load_pickle_file(
    pickle_path: str,
    default: Any,
) -> Any:
    """
    Load a pickle file by a given path.
    Return defined default variable if there is an error opening the file.

    Args:
    ----
    pickle_path: str
        The pickle path to be loaded.
    default: Any
        The default variable to be returned when any error occurs.

    Returns:
    -------
    Any: The default variable or the loaded object.

    Examples:
    ---------
    ```python
    result = load_pickle_file("path/to/file.pkl", default={})
    ```

    ```python
    result = load_pickle_file("non_existent_file.pkl", default="default_value")
    ```
    """
    try:
        with open(pickle_path, "rb") as f:
            obj = pickle.load(f)
    except FileNotFoundError:
        return default
    return obj


def dump_variable_to_pickle(
    variable: Any,
    pickle_path: str,
    dump_only_when_pickle_file_not_exist: bool = False,
    encrypt_variable: bool = False,
    encryption_key: bytes = None,
) -> bool:
    """
    Dump a variable to a pickle file by given a pickle path.

    Parameters:
    -----------
    variable: any
        The variable to be dumped to the pickle path.
    pickle_path: str
        The pickle path for dumping the variable.
    dump_only_when_file_not_exist: bool
        Whether to dump when pickle path is not existed. Default is False.
    encrypt_variable: bool
        Whether to encrypt the variable before dumping. Default is False.
    encryption_key: bytes
        The key used for encrypting the variable. If None, no encryption is applied.

    Returns:
    --------
    bool: True if the variable was dumped successfully, False otherwise.

    Examples:
    ---------
    >>> dump_variable_to_pickle({"key": "value"}, "path/to/pickle.pkl")
    True

    >>> dump_variable_to_pickle({"key": "value"}, "path/to/pickle.pkl", dump_only_when_pickle_file_not_exist=True)
    True

    Note:
    -----
    Take ~ 15ms for 2800KB dict
    """
    dumped = False
    if encrypt_variable and encryption_key is not None:
        variable = encrypt_data(
            pickle.dumps(variable), encryption_key
        )  # Encrypt the variable

    if dump_only_when_pickle_file_not_exist:
        if not os.path.exists(pickle_path):
            with open(pickle_path, "wb") as f:
                if encrypt_variable and encryption_key is not None:
                    f.write(variable)
                else:
                    pickle.dump(variable, f, protocol=-1)
                dumped = True
    else:
        with open(pickle_path, "wb") as f:
            if encrypt_variable and encryption_key is not None:
                f.write(variable)
            else:
                pickle.dump(variable, f)
            dumped = True
    return dumped


def dump_list_str_to_dict_pickle(
    list_str: List[str] = [],
    pickle_path: str = "",
    dict_var: dict = None,
) -> None:
    """
    Dumps a list of strings into a dictionary with empty values and saves it to a pickle file.

    Parameters:
    -----------
    list_str: List[str]
        A list of strings to be added as keys in the dictionary.
    pickle_path: str
        The path where the pickle file will be saved.
    dict_var: dict
        The dictionary to which the keys will be added.

    Returns:
    --------
    None

    Examples:
    ---------
    >>> dump_list_str_to_dict_pickle(["a", "b", "c"], "path/to/pickle.pkl", {"x": "y"})
    # This will create a dictionary {"x": "y", "a": "", "b": "", "c": ""} and save it to "path/to/pickle.pkl".

    >>> dump_list_str_to_dict_pickle([], "path/to/pickle.pkl", {"key": "value"})
    # This will not change the dictionary and will save {"key": "value"} to "path/to/pickle.pkl".
    """
    if not (pickle_path != "" and "/" in pickle_path):
        print("Please check pickle_path")
        return

    if dict_var is None:
        print("var should not be None")
        return

    for item in list_str:
        if item not in dict_var:
            dict_var[item] = ""

    dump_variable_to_pickle(dict_var, pickle_path)
