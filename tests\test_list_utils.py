import collections
import unittest
from tomsze_utils.list_utils import (
    duplicate_list_limited,
    generate_number_list,
    append_and_squeeze_list,
)


class TestDuplicateListLimited(unittest.TestCase):

    def test_basic(self):
        test_list = ["a", "b"]
        result = duplicate_list_limited(test_list, 5)
        expected = ["a", "b", "a", "b", "a"]
        self.assertEqual(result, expected)

    def test_limit_less_than_length(self):
        test_list = ["a", "b", "c", "d"]
        limit = 2
        result = duplicate_list_limited(test_list, limit)
        expected = ["a", "b"]
        self.assertEqual(result, expected)

    def test_empty_list(self):
        test_list = []
        limit = 5
        result = duplicate_list_limited(test_list, limit)
        expected = []
        self.assertEqual(result, expected)

    def test_duplicate_list_limited(self):
        # Test normal duplicate.
        list_to_duplicate = ["a", "b"]
        limit = 11
        list_duplicated = duplicate_list_limited(list_to_duplicate, limit)

        list_correct = [
            "a",
            "b",
            "a",
            "b",
            "a",
            "b",
            "a",
            "b",
            "a",
            "b",
            "a",
        ]
        assert len(list_duplicated) == limit
        assert collections.Counter(list_duplicated) == collections.Counter(list_correct)

        # Test if [list_to_duplicate] is more than limit.
        list_to_duplicate = ["a", "b", "c", "d"]
        limit = 2
        list_duplicated = duplicate_list_limited(list_to_duplicate, limit)

        list_correct = [
            "a",
            "b",
        ]
        assert len(list_duplicated) == limit
        assert collections.Counter(list_duplicated) == collections.Counter(list_correct)

        print("test_duplicate_list_limited done")


import pytest


def test_generate_number_list():
    # Test case 1: Normal case
    result = generate_number_list(5, 2)
    expected = [0, 2, 4, 6, 8]
    assert result == expected

    # Test case 2: Edge case with gap of 0
    result = generate_number_list(5, 0)
    expected = [0, 0, 0, 0, 0]
    assert result == expected

    # Test case 3: Single element
    result = generate_number_list(1, 3)
    expected = [0]
    assert result == expected

    # Test case 4: Large gap
    result = generate_number_list(3, 10)
    expected = [0, 10, 20]
    assert result == expected


import pytest
from tempfile import TemporaryDirectory


def test_update_data_list():
    # Test case 1: Appending new data when the list has not reached fixed size
    data_list = [1, 2]
    new_data = 3
    list_fixed_size = 5
    updated_data = append_and_squeeze_list(data_list, new_data, list_fixed_size)
    expected = [1, 2, 3]
    assert updated_data == expected

    # Test case 2: Popping the earliest element when the list has reached fixed size
    data_list = [1, 2, 3]
    new_data = 4
    list_fixed_size = 3
    updated_data = append_and_squeeze_list(data_list, new_data, list_fixed_size)
    expected = [2, 3, 4]
    assert updated_data == expected


from tomsze_utils.list_utils import calculate_total_len_of_index_range_list


class TestCalculateTotalLenOfIndexRangeList:
    def test_calculate_total_len_of_index_range_list_basic(self):
        index_range_list = [(0, 5), (10, 15)]
        expect = 10
        result = calculate_total_len_of_index_range_list(index_range_list)
        assert result == expect

    def test_calculate_total_len_of_index_range_list_empty(self):
        index_range_list = []
        expected = 0
        result = calculate_total_len_of_index_range_list(index_range_list)
        assert result == expected


if __name__ == "__main__":
    unittest.main()
