import sys
from datetime import datetime
from dateutil import tz


def get_current_system_datetime(format: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Get the current system's date and time.

    Args:
        format (str): The format for the date and time output. Default is 'YYYY-MM-DD HH:MM:SS'.

    Returns:
        str: The current date and time in the specified format.
    """
    # Get the current local date and time
    local_datetime = datetime.now(tz.tzlocal())
    return local_datetime.strftime(format)


from dataclasses import dataclass


@dataclass
class DateTimeComponents:
    year: int
    month: int
    day: int
    hour: int
    minute: int
    second: int


def extract_datetime_components(
    datetime_str: str, format: str = "%Y-%m-%d %H:%M:%S"
) -> DateTimeComponents | None:
    """
    Extract year, month, day, hour, minute, and second from a datetime string.

    Args:
        datetime_str (str): The datetime string to extract from.
        format (str): The format of the datetime string. Default is '%Y-%m-%d %H:%M:%S'.

    Returns:
        DateTimeComponents: An object containing the year, month, day, hour, minute, and second.
              Returns None if the datetime string cannot be parsed.
    """
    try:
        datetime_object = datetime.strptime(datetime_str, format)
        return DateTimeComponents(
            year=datetime_object.year,
            month=datetime_object.month,
            day=datetime_object.day,
            hour=datetime_object.hour,
            minute=datetime_object.minute,
            second=datetime_object.second,
        )
    except ValueError:
        print(
            f"Error: Could not parse datetime string '{datetime_str}' with format '{format}'."
        )
        return None


def main():
    # current_datetime = get_current_system_datetime()
    # print(f"Current system date and time: {current_datetime}")
    # # Current system date and time: 2025-04-05 11:50:54

    datetime_str = "2023-10-26 10:30:00"
    components = extract_datetime_components(datetime_str)
    if components:
        print(f"Datetime components: {components}")
        # Datetime components: {'year': 2023, 'month': 10, 'day': 26, 'hour': 10, 'minute': 30, 'second': 0}

    datetime_str = "2023-10-26"
    components = extract_datetime_components(datetime_str, format="%Y-%m-%d")
    if components:
        print(f"Datetime components: {components}")
        # Datetime components: {'year': 2023, 'month': 10, 'day': 26, 'hour': 0, 'minute': 0, 'second': 0}

    datetime_str = "2023/10/26 10:30:00"
    components = extract_datetime_components(datetime_str, format="%Y/%m/%d %H:%M:%S")
    if components:
        print(f"Datetime components: {components}")
        # Datetime components: {'year': 2023, 'month': 10, 'day': 26, 'hour': 10, 'minute': 30, 'second': 0}

    datetime_str = "invalid datetime"
    components = extract_datetime_components(datetime_str)
    if components:
        print(f"Datetime components: {components}")


if __name__ == "__main__":
    sys.exit(main())
