import sys
import cv2
from tomsze_utils.image_utils.template_matching_utils import use_template_match

def test_template_match_using_box_image():
    image_path = r'./src/tomsze_utils/image_utils/image_white_box_20_30_box_at_10_10.png'
    template_path = r'./src/tomsze_utils/image_utils/template_white_box_20_30.png'
    
    image = cv2.imread(image_path)
    template = cv2.imread(template_path)
    
    corner_loc, center_loc = use_template_match(image, template)
    
    assert corner_loc == (8, 8)
    assert center_loc == (20, 25)
    

def main():
	test_template_match_using_box_image()

if __name__ == "__main__":
    sys.exit(main())