import sys
import os
import time
from tomsze_utils.database_utils.pickle_database import PickleDatabase

# Create db object using PickleDatabase (will also load pickle file)
script_directory_path = os.path.dirname(
    os.path.abspath(sys.argv[0]),
)

pickle_path = os.path.join(
    script_directory_path,
    "one_pickle",
    "server_apps_db.pickle",
)

time_start = time.time()
db = PickleDatabase(
    pickle_db_path=pickle_path,
)
time_end = time.time()
print("time used:" + "{:.2f}".format((time_end - time_start) * 1000) + " ms")
assert len(db.loaded) == 2015
