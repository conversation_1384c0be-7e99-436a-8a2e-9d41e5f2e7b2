import platform
import shutil
import subprocess
import sys
import os
from typing import List


def get_directory_path_from_any_path(path: str) -> str:
    """
    Get the directory path from any given path.
    The path is first checked for existence. If the path does not exist, it will not work.

    Args:
        path (str): The path from which to extract the directory.

    Returns:
        str: The directory path extracted from the given path.

    Examples:
    ```python
    directory = get_directory_path_from_any_path('/home/<USER>/file.txt')
    # returns '/home/<USER>'

    directory = get_directory_path_from_any_path('/home/<USER>/')
    # returns '/home/<USER>'
    ```
    """
    directory_path = ""

    if os.path.isfile(path):
        directory_path = os.path.dirname(path)

    if os.path.isdir(path):
        directory_path = path

    return directory_path


def is_path_contain_directory(
    path: str,
    directory_name: str,
) -> bool:
    """
    Check if the path contains the specified directory name.
    The path is first checked for existence. If the path does not exist, it will not work.

    Args:
        path (str): The path to check.
        directory_name (str): The directory name to look for in the path.

    Returns:
        bool: True if the directory name is in the path, False otherwise.

    Examples:
    ```python
    result = is_path_contain_directory('/home/<USER>/file.txt', directory_name='user')
    # returns True

    result = is_path_contain_directory('/home/<USER>/file.txt', directory_name='admin')
    # returns False
    ```
    """
    directory_path = get_directory_path_from_any_path(path)

    if directory_name in directory_path:
        return True
    else:
        return False


def is_directory_contain_file(
    directory_path: str,
    filename: str,
) -> bool:
    """
    Check if the directory contains the given filename.
    The directory path is first checked for existence. If the directory does not exist, it will not work.

    Args:
        directory_path (str): The path of the directory to check.
        filename (str): The name of the file to look for in the directory.

    Returns:
        bool: True if the directory contains the file, False otherwise.

    Examples:
    ```python
    result = is_directory_contain_file(directory_path='/home/<USER>/docs', filename='file.txt')
    # returns True if 'file.txt' exists in '/home/<USER>/docs'

    result = is_directory_contain_file(directory_path='/home/<USER>/docs', filename='nonexistent.txt')
    # returns False if 'nonexistent.txt' does not exist in '/home/<USER>/docs'
    ```
    """
    if not os.path.isdir(directory_path):
        return False

    for root, dirs, files in os.walk(directory_path):
        if filename in files:
            return True
    return False


def remove_all_files_in_directory(
    directory_path: str,
) -> str:  # Change return type to str
    """
    Remove all files and subdirectories in the given directory.

    Args:
        directory_path (str): The path to the directory to be cleaned.

    Returns:
        str: A confirmation string indicating completion.

    Raises:
        Exception: If there's an error while deleting a file or subdirectory.

    Examples:
    ```python
    remove_all_files_in_directory(directory_path='/home/<USER>/docs')
    # This will remove all files and subdirectories in '/home/<USER>/docs'.

    remove_all_files_in_directory(directory_path='/tmp/test_directory')
    # This will remove all files and subdirectories in '/tmp/test_directory'.
    ```
    """
    for filename in os.listdir(directory_path):
        file_path = os.path.join(directory_path, filename)
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)  # Remove subdirectory
        except Exception as e:
            print(f"Error deleting {file_path}: {e}")
    return "done"  # Return the string "done"


def remove_all_files_of_a_type_in_directory(
    directory_path: str,
    file_type: str,
) -> str:
    """
    Removes all files of a specified type within a given directory.

    Args:
        directory_path (str): The path to the directory to clean.
        file_type (str): The file extension to target (e.g., ".txt", ".log").

    Returns:
        str: A confirmation string indicating completion.

    Examples:
        ```python
        remove_all_files_of_a_type_in_directory(directory_path="/tmp/test", file_type=".log")
        # This will remove all files ending with '.log' in '/tmp/test'.
        ```

        ```python
        remove_all_files_of_a_type_in_directory(directory_path="/home/<USER>/data", file_type=".csv")
        # This will remove all files ending with '.csv' in '/home/<USER>/data'.
        ```
    """
    for filename in os.listdir(directory_path):
        if filename.endswith(file_type):
            file_path = os.path.join(directory_path, filename)
            try:
                os.remove(file_path)
            except Exception as e:
                print(f"Error deleting {file_path}: {e}")
    return "done"


def convert_to_abs_path(path_list: List[str]) -> List[str]:
    """
    Convert a list of paths to their absolute paths.

    Args:
        path_list (List[str]): A list of file or directory paths.

    Returns:
        List[str]: A list of absolute paths corresponding to the input paths.

    Examples:
        ```python
        abs_paths = convert_to_abs_path(["./file1.txt", "../file2.txt"])
        print(abs_paths)  # Output: ['/absolute/path/to/file1.txt', '/absolute/path/to/file2.txt']
        ```

        ```python
        abs_paths = convert_to_abs_path(["/home/<USER>/docs", "./relative/path"])
        print(abs_paths)  # Output: ['/home/<USER>/docs', '/absolute/path/to/relative/path']
        ```
    """
    abs_path_list = []
    for path in path_list:
        abs_path = os.path.realpath(path)
        abs_path_list.append(abs_path)
    return abs_path_list


def get_file_path_in_directory(
    directory_path: str,
    filename: str,
    ignore_dirs: List[str] = [".git", "__pycache__"],
) -> str:
    """
    Get the full file path of a specified filename in a given directory, ignoring specified directories.

    Args:
        directory_path (str): The path to the directory to search in.
        filename (str): The name of the file to find.
        ignore_dirs (List[str]): A list of directory names to ignore during the search.

    Returns:
        str: The full path of the file if found, otherwise an empty string.

    Examples:
        ```python
        file_path = get_file_path_in_directory(directory_path="/path/to/directory", filename="file.txt")
        print(file_path)  # Output: '/path/to/directory/file.txt' if the file exists
        ```

        ```python
        file_path = get_file_path_in_directory(directory_path="/path/to/directory", filename="file.txt", ignore_dirs=[".git"])
        print(file_path)  # Output: '/path/to/directory/file.txt' if the file exists, ignoring the .git directory
        ```
    """
    for root, dirs, files in os.walk(directory_path):
        # Remove ignored directories from the search
        dirs[:] = [d for d in dirs if d not in ignore_dirs]
        if filename in files:
            return os.path.join(root, filename)
    return ""


def is_path_exists(path: str) -> bool:
    """
    Check if a specified path exists.

    Args:
        path (str): The path to check.

    Returns:
        bool: True if the path exists, False otherwise.

    Examples:
        ```python
        exists = is_path_exists("/path/to/file_or_directory")
        print(exists)  # Output: True if the path exists, False otherwise
        ```

        ```python
        exists = is_path_exists("/non/existent/path")
        print(exists)  # Output: False since the path does not exist
        ```
    """
    return os.path.exists(path)


def get_desktop_path() -> str:
    """
    Get the path to the user's desktop using subprocess.

    Returns:
        str: The full path to the desktop.

    Examples:
        ```python
        desktop_path = get_desktop_path()
        print(desktop_path)  # Output: '/Users/<USER>/Desktop' on macOS or 'C:\\Users\\<USER>\\Desktop' on Windows
        ```
    """
    if platform.system() == "Windows":
        return (
            subprocess.check_output("echo %USERPROFILE%\\Desktop", shell=True)
            .decode()
            .strip()
        )
    elif platform.system() == "Darwin":  # For macOS
        return (
            subprocess.check_output("echo $HOME/Desktop", shell=True).decode().strip()
        )
    else:  # For Linux and other systems
        return (
            subprocess.check_output("echo $HOME/Desktop", shell=True).decode().strip()
        )


def find_project_root_using_git_folder(folder_or_file_path: str) -> str:
    """
    Find the root directory of a project by searching for the .git folder.

    Args:
        folder_or_file_path (str): The path to a folder or file within the project.

    Returns:
        str: The path to the project root if found, None otherwise.

    Examples:
        ```python
        root = find_project_root("/path/to/my/project/src/file.py")
        print(root)  # Output: '/path/to/my/project' if .git exists

        root = find_project_root("/path/to/another/project/file.txt")
        print(root)  # Output: None if .git does not exist
        ```
    """
    current_path = os.path.realpath(folder_or_file_path)
    while not os.path.exists(os.path.join(current_path, ".git")):
        if current_path == os.path.dirname(current_path):
            # Reached the root of the file system
            return None
        current_path = os.path.dirname(current_path)
    return current_path


def main():
    directory_path = get_directory_path_from_any_path("./.git")
    # ./.git

    directory_path_2 = get_directory_path_from_any_path(
        "./src/tomsze_utils/path_utils.py"
    )
    # ./src/tomsze_utils

    contain_directory = is_path_contain_directory("./.git", ".git")
    # True

    contain_directory_2 = is_path_contain_directory(
        "./src/tomsze_utils/path_utils.py", "path_utils"
    )
    # False


if __name__ == "__main__":
    sys.exit(main())
