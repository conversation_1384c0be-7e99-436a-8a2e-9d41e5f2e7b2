import sys
import os
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit

# Create db object using PickleDatabaseSplit (will also load pickle file)
script_directory_path = os.path.dirname(
    os.path.abspath(sys.argv[0]),
)

db = PickleDatabaseSplit(
    db_fpath=script_directory_path,
    db_name="apps",
    part_data_count=100,
)

# Split
db.re_split()

# Dump into splited parts
db.dump_all_parts_to_pickles()

g = 1
