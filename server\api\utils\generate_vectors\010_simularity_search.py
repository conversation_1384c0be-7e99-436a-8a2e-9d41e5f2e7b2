import os
import sys
# os.environ["CUDA_VISIBLE_DEVICES"] = "-1"   # Place before import torch or tensorflow

import pandas as pd
from sentence_transformers.util import semantic_search  # pip install -U sentence-transformers
import torch
from datasets import load_dataset
from utils import convert_to_embedding_using_huggingface_model, similarity_search



def main():
    print(f'Using cuda: {torch.cuda.is_available()}')
    
    # Read app description and embeddings from 000 csv.
    script_directory_path = os.path.dirname(os.path.abspath(sys.argv[0]))
    embedding_csv_filename = '000_embeddings_all-MiniLM-L6-v2.csv'
    description_csv_filename = '000_descriptions_all-MiniLM-L6-v2.csv'
    embedding_csv_path = os.path.join(script_directory_path, embedding_csv_filename)
    description_csv_path = os.path.join(script_directory_path, description_csv_filename)
    df_description_embedding = pd.read_csv(embedding_csv_path, sep=",")
    df_description = pd.read_csv(description_csv_path, sep=",")
    list_description = df_description['description'].to_list()
    
    # Semantic search (similarity search).
    query = ["recover"]
    model_id = "sentence-transformers/all-MiniLM-L6-v2"
    output = convert_to_embedding_using_huggingface_model(query, model_id)

    query_embeddings = torch.FloatTensor(output)
    # query_embeddings = query_embeddings.to('cuda')
    np_test_df_description_embedding = df_description_embedding.to_numpy()
    description_embedding = torch.from_numpy(df_description_embedding.to_numpy()).to(torch.float)
    # description_embedding = description_embedding.to('cuda')

    hits = similarity_search(query_embeddings, description_embedding, top_k=5)
    
    print([list_description[hits[0][i]['corpus_id']] for i in range(len(hits[0]))])

    pass

if __name__ == "__main__":
    sys.exit(main())