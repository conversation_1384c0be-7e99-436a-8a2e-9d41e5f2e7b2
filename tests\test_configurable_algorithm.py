import json
import logging
import os
import shutil
import tempfile
import time
import cv2
import pytest
from tomsze_utils.configurable_algorithm.configurable_algorithm import CA
from tomsze_utils.path_utils import remove_all_files_in_directory
from tomsze_utils.plugins.constant.plugin_constants import (
    PLUGIN_ENV_READER_ENV_DICT_VAR_NAME,
    PLUGIN_HTML_PARSER_ELE_RESULTS_VAR_NAME,
    PLUGIN_HTML_PARSER_ELE_STR_RESULTS_VAR_NAME,
    PLUGIN_HTML_PARSER_ELE_TEXT_RESULTS_VAR_NAME,
    PLUGIN_LOOP_COUNTER_VAR_NAME,
    PLUGIN_LOOP_LIST_INDEX_VAR_NAME,
    PLUGIN_LOOP_LIST_ITEM_VAR_NAME,
)
from fake_ssh import Server


def test_validate_setting():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_has_no_type.json",
    )
    validate_ok, validate_error = ca.validate_setting(ca.data_obj)

    assert validate_ok == False
    assert validate_error is not None

    ca.disable_logging_handlers()

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_has_type.json",
    )
    validate_ok, validate_error = ca.validate_setting(ca.data_obj)

    assert validate_ok == True
    assert validate_error is None

    ca.disable_logging_handlers()


def test_run_error():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_has_no_type.json",
    )
    run_ok, run_error = ca.run()

    assert run_ok == False
    assert run_error is not None


def test_variables():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_variables.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var
    assert dict_var.get("var1", None) == 1
    assert dict_var.get("var2", None) == 2
    assert dict_var.get("var3", None) == False
    assert dict_var.get("var4", None) == "abc"
    assert dict_var.get("var5", None) == dict_var.get("var4", None)
    assert dict_var.get("var6", None) == dict_var.get("var4", None)


def test_variable_assigner():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_variable_assigner.json5",
    )
    ca.run()

    # Test.
    data_obj = ca.data_obj

    # Test variable assigner.
    assert data_obj.dict_var["assign_variable_1.var1"] == 1
    assert data_obj.dict_var["assign_variable_1.type"] == "PluginVariableAssigner"
    assert data_obj.dict_var["assign_variable_2.var2"] == 2
    assert data_obj.dict_var["assign_variable_2.type"] == "PluginVariableAssigner"


def test_variable_assigner_on_list():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_variable_assigner_on_list.json5",
    )
    ca.run()

    # Test.
    dict_var = ca.data_obj.dict_var

    assert dict_var["assign_variable_3.var3"] == [1, 2]


def test_log():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_variable_assigner.json5",
    )
    ca.run()

    # Test.
    data_obj = ca.data_obj

    assert os.path.exists(data_obj.log_file_path) == True


def test_image_reader():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_image_reader.json",
    )
    ca.run()

    # Test.
    data_obj = ca.data_obj

    assert "assign_variable_image_path.image_path" in data_obj.dict_var
    # Test image reader.
    assert data_obj.dict_var.get("read_image_1.read_error", True) == False
    assert data_obj.dict_var.get("read_image_1.height", 0) == 101
    assert data_obj.dict_var.get("read_image_1.width", 0) == 101


def test_image_overlayer_use_at():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_image_overlayer_use_at.json",
    )
    ca.run()

    # Test.
    data_obj = ca.data_obj

    assert "assign_variable_image_path.image_path" in data_obj.dict_var

    # Test image overlayer.
    assert data_obj.dict_var.get("read_image_1.read_error", True) == False
    assert data_obj.dict_var.get("read_image_1.height", 0) == 101
    assert data_obj.dict_var.get("read_image_1.width", 0) == 101

    assert data_obj.dict_var.get("overlay_text.save_path", None) is not None
    assert data_obj.dict_var.get("overlay_text.save_path2", None) is not None
    save_path = data_obj.dict_var.get("overlay_text.save_path", None)
    save_path2 = data_obj.dict_var.get("overlay_text.save_path", None)
    assert os.path.exists(save_path) == True
    assert os.path.exists(save_path2) == True


def test_image_overlayer_use_move_to_edge():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_image_overlayer_use_move_to_edge.json",
    )
    ca.run()

    # Test.
    data_obj = ca.data_obj

    assert "assign_variable_image_path.image_path" in data_obj.dict_var

    # Test image overlayer.
    assert data_obj.dict_var.get("read_image_1.read_error", True) == False
    assert data_obj.dict_var.get("read_image_1.height", 0) == 101
    assert data_obj.dict_var.get("read_image_1.width", 0) == 101

    assert data_obj.dict_var.get("overlay_text.save_path", None) is not None
    assert data_obj.dict_var.get("overlay_text.save_path2", None) is not None
    save_path = data_obj.dict_var.get("overlay_text.save_path", None)
    save_path2 = data_obj.dict_var.get("overlay_text.save_path", None)
    assert os.path.exists(save_path) == True
    assert os.path.exists(save_path2) == True


def test_simple_image_creator():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_simple_image_creator.json",
    )
    ca.run()

    # Test.
    data_obj = ca.data_obj
    assert data_obj.dict_var.get("create_simple_image.save_path", None) is not None
    assert data_obj.dict_var.get("create_simple_image.save_path2", None) is not None
    save_path = data_obj.dict_var.get("create_simple_image.save_path", None)
    save_path2 = data_obj.dict_var.get("create_simple_image.save_path", None)
    assert os.path.exists(save_path) == True
    assert os.path.exists(save_path2) == True

    img = cv2.imread(save_path)
    r = img[0, 0, 2]
    g = img[0, 0, 1]
    b = img[0, 0, 0]
    assert r == 255
    assert g == 125
    assert b == 50


def run_simple_image_creator():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_Task_create_green_image_for_desktop.json",
    )
    ca.run()


def test_windows_wallpaper_helper():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_windows_wallpaper_helper.json",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get("get_windows_wallpaper.buffer_to_which", None)
        == "image"
    )
    assert ca.data_obj.dict_var.get("get_windows_wallpaper.width", 0) > 0
    assert ca.data_obj.dict_var.get("get_windows_wallpaper.height", 0) > 0
    save_path = ca.data_obj.dict_var.get("get_windows_wallpaper.save_path", None)
    assert os.path.exists(save_path) == True


def test_get_requester():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_get_requester.json",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("send_get_request.response", None) is not None
    assert ca.data_obj.dict_var.get("send_get_request.headers", None) is not None
    assert ca.data_obj.dict_var.get("send_get_request.content", None) is not None
    text = ca.data_obj.dict_var.get("send_get_request.text", None)
    assert text is not None
    assert ca.data_obj.dict_var.get("send_get_request.status_code", None) == 200
    assert "Example Domain" in text
    assert (
        ca.data_obj.dict_var.get("send_get_request.exception", None) == "no exception"
    )

    response_dict = ca.data_obj.dict_var.get("send_get_request2.response_dict", None)
    assert response_dict.get("url", None) == "https://httpbin.org/get"


def test_env_reader():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_env_reader.json",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("read_env.test1_key", None) == "test1_value"
    assert ca.data_obj.dict_var.get("read_env.test2_key", None) == "test2_value"
    assert (
        ca.data_obj.dict_var.get("read_env.test3_key", None)
        == "test3_value1\ntest3_value2\n"
    )
    assert ca.data_obj.dict_var.get(
        f"read_env.{PLUGIN_ENV_READER_ENV_DICT_VAR_NAME}", None
    ) == {
        "test1_key": "test1_value",
        "test2_key": "test2_value",
        "test3_key": "test3_value1\ntest3_value2\n",
    }


def test_life_span_calculator():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_life_span_calculator.json",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("calculate_life_span.days", None) is not None


def test_datetimer():
    from dateutil import parser

    def is_parsable_date(date_string):
        try:
            parser.parse(date_string)
            return True
        except ValueError:
            return False

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_datetimer.json",
    )
    ca.run()

    datetime = ca.data_obj.dict_var.get("current_datetime.datetime", None)
    assert datetime is not None
    assert is_parsable_date(datetime) == True


def test_result_saver_csv():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_result_saver_csv.json5",
    )
    ca.run()

    csv_path = ca.data_obj.dict_var.get("save_csv_result.save_path", None)
    assert os.path.exists(csv_path)

    import csv

    with open(csv_path) as csv_file:
        reader = csv.reader(csv_file, delimiter=",")

        row = next(reader)
        assert row[0] == "num1"
        assert row[1] == "str1"

        row = next(reader)
        assert row[0] == "123"
        assert row[1] == "strxx"


def test_result_saver_csv_write_twice():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_result_saver_csv_write_twice.json5",
    )
    ca.run()

    csv_path = ca.data_obj.dict_var.get("save_csv_result.save_path", None)
    assert os.path.exists(csv_path)

    import csv

    with open(csv_path) as csv_file:
        reader = csv.reader(csv_file, delimiter=",")

        row = next(reader)
        assert row[0] == "num1"
        assert row[1] == "str1"

        row = next(reader)
        assert row[0] == "123"
        assert row[1] == "strxx"

        row = next(reader)
        assert row[0] == "123"
        assert row[1] == "strxx"


def test_result_saver_csv_use_dict_input():

    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_result_saver_csv_use_dict_input.json5",
    )
    ca.run()

    csv_path = ca.data_obj.dict_var.get("save_csv_result.save_path", None)
    assert os.path.exists(csv_path)

    import csv

    with open(csv_path) as csv_file:
        reader = csv.reader(csv_file, delimiter=",")

        row = next(reader)
        assert row[0] == "num2"
        assert row[1] == "str2"

        row = next(reader)
        assert row[0] == "234"
        assert row[1] == "stryy"


def test_setting_runner():
    # Remove temp folder first.
    temp_folder_path = "./tests/temp"
    if os.path.exists(temp_folder_path):
        shutil.rmtree(temp_folder_path)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_setting_runner_using_var_assigner.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("run_setting_variable_assigner.var1", None) == 1
    assert (
        ca.data_obj.dict_var.get(
            "run_setting_variable_assigner.assign_variable_1.var1", None
        )
        == 1
    )
    assert ca.data_obj.dict_var.get("run_setting_variable_assigner.var2", None) == 2
    assert (
        ca.data_obj.dict_var.get(
            "run_setting_variable_assigner.assign_variable_2.var2", None
        )
        == 2
    )


def test_loop_starter_ender_use_num_loops():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_loop_starter_ender_use_num_loops.json5",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None)
        == 0
    )

    assert ca.num_steps_ran == 9


def test_loop_starter_ender_use_num_loops_20times():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_loop_starter_ender_use_num_loops_20times.json5",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None)
        == 0
    )

    assert ca.num_steps_ran == 3 + 3 + (3 * 19)


def test_loop_starter_ender_use_num_loops_with_ender_at_last():

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_loop_starter_ender_use_num_loops_with_ender_at_last.json5",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None)
        == 0
    )

    assert ca.num_steps_ran == 8


def test_loop_starter_ender_use_loop_list():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_loop_starter_ender_use_loop_list.json5",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None)
        == 0
    )

    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}", None)
        == 1
    )
    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_LIST_ITEM_VAR_NAME}", "")
        is None
    )

    assert ca.data_obj.dict_var.get(f"assign_variable_2.var1", None) == "b"
    assert ca.data_obj.dict_var.get(f"assign_variable_3.var1", None) == "b"

    assert ca.num_steps_ran == 9


def test_loop_starter_ender_use_loop_list_by_variable():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_loop_starter_ender_use_loop_list_by_variable.json5",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_COUNTER_VAR_NAME}", None)
        == 0
    )

    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_LIST_INDEX_VAR_NAME}", None)
        == 1
    )
    assert (
        ca.data_obj.dict_var.get(f"start_loop.{PLUGIN_LOOP_LIST_ITEM_VAR_NAME}", "")
        is None
    )

    assert ca.data_obj.dict_var.get(f"assign_variable_2.var1", None) == 2
    assert ca.data_obj.dict_var.get(f"assign_variable_3.var1", None) == 2

    assert ca.num_steps_ran == 9


def test_loop_starter_ender_use_nested_loop():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_loop_starter_ender_nested_loop.json5",
    )
    ca.run()

    assert ca.num_steps_ran == 19


def test_html_parser_b4soup_use_filter_by_text():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_html_parser_b4soup_use_filter_by_text.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("send_get_request.text", None) is not None

    assert (
        ca.data_obj.dict_var.get(
            f"parse_html.{PLUGIN_HTML_PARSER_ELE_TEXT_RESULTS_VAR_NAME}", None
        )
        is not None
    )
    assert (
        ca.data_obj.dict_var.get(
            f"parse_html.{PLUGIN_HTML_PARSER_ELE_STR_RESULTS_VAR_NAME}", None
        )
        is not None
    )
    assert (
        ca.data_obj.dict_var.get(
            f"parse_html.{PLUGIN_HTML_PARSER_ELE_RESULTS_VAR_NAME}", None
        )
        is not None
    )


def test_html_parser_b4soup_use_filter_by_text_with_split_and_replace():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_html_parser_b4soup_use_filter_by_text_with_split_and_replace.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("send_get_request.text", None) is not None
    assert (
        ca.data_obj.dict_var.get("parse_html.element_text_result", None)
        == "This domain is for use in happy examples in documents"
    )


def test_html_parser_b4soup_use_filter_by_attribute():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_html_parser_b4soup_use_filter_by_attribute.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("send_get_request.text", None) is not None

    assert (
        ca.data_obj.dict_var.get(
            f"parse_html.{PLUGIN_HTML_PARSER_ELE_TEXT_RESULTS_VAR_NAME}", None
        )
        is not None
    )
    assert (
        ca.data_obj.dict_var.get(
            f"parse_html.{PLUGIN_HTML_PARSER_ELE_STR_RESULTS_VAR_NAME}", None
        )
        is not None
    )
    assert (
        ca.data_obj.dict_var.get(
            f"parse_html.{PLUGIN_HTML_PARSER_ELE_RESULTS_VAR_NAME}", None
        )
        is not None
    )


def test_html_parser_b4soup_use_filter_by_attribute_then_find_next_sibling():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_html_parser_b4soup_use_filter_by_attribute_then_find_next_sibling.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("send_get_request.text", None) is not None
    assert (
        "viewport"
        in ca.data_obj.dict_var.get("parse_html.element_str_results", None)[0]
    )


def test_html_parser_b4soup_use_filter_by_text_then_find_next_sibling():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_html_parser_b4soup_use_filter_by_text_then_find_next_sibling.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("send_get_request.text", None) is not None
    assert "org" in ca.data_obj.dict_var.get("parse_html.element_str_results", None)[0]


def test_text_printer():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_text_printer.json5",
    )

    from io import StringIO
    from contextlib import redirect_stdout

    obj_stringio = StringIO()
    with redirect_stdout(obj_stringio):
        ca.run()
    captured_output = obj_stringio.getvalue()

    assert captured_output == "some texts\n"


def test_dict_var_extracter():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_dict_var_extractor.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("extract_dict_var.key1", None) == "val1"
    assert ca.data_obj.dict_var.get("extract_dict_var.key2", None) == "val2"
    assert ca.data_obj.dict_var.get("extract_dict_var2.key3", None) == "val3"
    assert ca.data_obj.dict_var.get("extract_dict_var2.key4", None) == "val4"


def test_command_runner():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_command_runner.json5",
    )
    ca.run()

    assert "Package" in ca.data_obj.dict_var.get("run_command.output_str", None)


def handler(command):
    if command == "ls":
        return "file1\nfile2\n"


@pytest.fixture
def server():
    with Server(command_handler=handler, port=7423) as server:
        yield server


def test_ssh_paramiko_loginer(server):
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_ssh_paramiko_loginer.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("ssh_login.SSHCient", None) is not None


def test_dir_zipper():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_dir_zipper.json5",
    )
    ca.run()

    zip_path = ca.data_obj.dict_var.get("zip_folder.zip_path", None)
    assert zip_path is not None
    assert os.path.exists(zip_path) == True

    save_to_folder_path = ca.data_obj.dict_var.get(
        "zip_folder.save_to_folder_path", None
    )
    shutil.unpack_archive(zip_path, save_to_folder_path)

    # Check if "fake_test_script.py" is in save_to_folder_path using path_utils
    from tomsze_utils.path_utils import is_directory_contain_file

    assert (
        is_directory_contain_file(save_to_folder_path, "fake_test_script_exclude.py")
        == False
    )
    assert is_directory_contain_file(save_to_folder_path, "text_exclude.txt") == False
    assert is_directory_contain_file(save_to_folder_path, "fake_test_script.py") == True

    # Remove all things in zips folder.
    remove_all_files_in_directory(save_to_folder_path)


def test_dir_zipper_include_only():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_dir_zipper_include_only.json5",
    )
    ca.run()

    zip_path = ca.data_obj.dict_var.get("zip_folder.zip_path", None)
    assert zip_path is not None
    assert os.path.exists(zip_path) == True

    save_to_folder_path = ca.data_obj.dict_var.get(
        "zip_folder.save_to_folder_path", None
    )
    shutil.unpack_archive(zip_path, save_to_folder_path)

    # Check if "fake_test_script.py" is in save_to_folder_path using path_utils
    from tomsze_utils.path_utils import is_directory_contain_file

    assert is_directory_contain_file(save_to_folder_path, "text.txt") == True
    assert (
        is_directory_contain_file(save_to_folder_path, "fake_test_script_exclude.py")
        == True
    )

    # Remove all things in zips folder.
    remove_all_files_in_directory(save_to_folder_path)


def test_files_remover():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_files_remover.json5",
    )

    # Create files to be removed.
    test_file1_path = "./tests/test_file1.txt"
    with open(test_file1_path, "w") as file:
        file.write("line1\n")

    test_file2_path = "./tests/test_file2.txt"
    with open(test_file2_path, "w") as file:
        file.write("line1\n")

    assert os.path.exists(test_file1_path)
    assert os.path.exists(test_file2_path)

    ca.run()

    assert ca.data_obj.dict_var.get("remove_file.is_removed", None) == True
    assert ca.data_obj.dict_var.get("remove_file.exception", None) is None

    if os.path.exists(test_file1_path):
        os.remove(test_file1_path)
    if os.path.exists(test_file2_path):
        os.remove(test_file2_path)


def test_script_runner():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_script_runner.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("run_script.captured_output", None) == "fake6\n"


def test_script_runner_with_args():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_script_runner_with_args.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("run_script.captured_output", None) == "3\n"


def test_script_runner_with_return():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_script_runner_with_return.json5",
    )
    ca.run()

    assert ca.data_obj.dict_var.get("run_script.return", None) == (1, 2)


def test_directory_creator_create_empty_false():
    """Test directory creation with 'create_empty' set to false.

    This test verifies that when the 'create_empty' parameter is set to false in the
    configuration, the directory specified by 'directory_path' is created if it does
    not already exist. The test checks the following:

    1. The directory path returned in the data object matches the expected path.
    2. The directory is indeed created on the filesystem.

    After the assertions, the test cleans up by removing the created directory to
    ensure that subsequent tests can run without interference.
    """
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_directory_creator_create_empty_false.json5",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get("create_directory.directory_path", None)
        == "./tests/new_directory"
    )
    assert os.path.exists("./tests/new_directory")  # Check if the directory was created

    # Clean up: remove the created directory after the test
    if os.path.exists("./tests/new_directory"):
        os.rmdir("./tests/new_directory")


def test_directory_creator_create_empty_true():
    """Test directory creation with 'create_empty' set to true.

    This test verifies that when the 'create_empty' parameter is set to true in the
    configuration, the directory specified by 'directory_path' is created if it does
    not already exist. The test also ensures that the directory is empty after
    creation. The following steps are performed in this test:

    1. A temporary directory is created at './tests/new_directory'.
    2. A test file is created within this directory to simulate pre-existing content.
    3. The configuration is loaded from 'setting_directory_creator_create_empty_true.json5'.
    4. The algorithm is executed, which should create the directory and clear any
       existing contents if 'create_empty' is true.
    5. Assertions are made to verify:
       - The directory path returned in the data object matches the expected path.
       - The directory is indeed created on the filesystem.
       - The directory is empty after the algorithm runs.
    6. Finally, the test cleans up by removing the created directory and its contents
       to ensure that subsequent tests can run without interference.
    """
    # Create a txt file in the new directory
    os.makedirs("./tests/new_directory", exist_ok=True)
    with open("./tests/new_directory/test_file.txt", "w") as f:
        f.write("This is a test file.")

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_directory_creator_create_empty_true.json5",
    )
    ca.run()

    assert (
        ca.data_obj.dict_var.get("create_directory.directory_path", None)
        == "./tests/new_directory"
    )
    assert os.path.exists("./tests/new_directory")  # Check if the directory was created
    assert (
        len(os.listdir("./tests/new_directory")) == 0
    )  # Check if the directory is empty

    # Clean up: remove the created directory and its contents after the test
    if os.path.exists("./tests/new_directory"):
        for filename in os.listdir("./tests/new_directory"):
            file_path = os.path.join("./tests/new_directory", filename)
            os.remove(file_path)  # Remove the txt file
        os.rmdir("./tests/new_directory")  # Remove the directory


def test_directory_remover_existing_directory():
    # Setup: Create a directory to remove
    os.makedirs("./tests/test_directory", exist_ok=True)

    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_directory_remover.json5",
    )
    ca.run()

    # Assert that the directory has been removed
    assert not os.path.exists("./tests/test_directory")
    assert ca.data_obj.dict_var.get("remove_directory.directory_removed", None) is True


def test_plugin_waiter():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_waiter.json5",
    )

    # Start time measurement
    start_time = time.time()

    # Run the algorithm
    ca.run()

    # End time measurement
    end_time = time.time()

    # Calculate the elapsed time
    elapsed_time = end_time - start_time

    # Check if the elapsed time is approximately equal to the wait time
    assert abs(elapsed_time - 0.5) < 0.1  # Allowing a small margin for sleep accuracy


def test_dict_to_json_writer():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_dict_to_json_writer.json5",
    )
    ca.run()

    output_json_path = ca.data_obj.dict_var.get("write_dict_to_json.output_path", None)
    assert output_json_path is not None
    assert os.path.exists(output_json_path)

    # Read the output JSON file into a dictionary
    output_json_path = ca.data_obj.dict_var.get("write_dict_to_json.output_path", None)
    with open(output_json_path, "r") as infile:
        output_dict = json.load(infile)

    # Retrieve the expected dictionary that should be written to the JSON file
    expected_dict = ca.data_obj.dict_var.get("write_dict_to_json.dict_to_write", None)

    # Check if the output dictionary matches the expected dictionary
    assert (
        output_dict == expected_dict
    ), "The output dictionary does not match the expected dictionary."


def test_directory_files_lister():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_directory_files_lister.json5",
    )
    ca.run()

    directory_path = ca.data_obj.dict_var.get(
        "list_directory_files.directory_path", None
    )
    assert directory_path is not None

    file_list = ca.data_obj.dict_var.get("list_directory_files.file_list", None)
    assert len(file_list) == 3


def test_text_file_reader():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_text_file_reader.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var

    file_path = dict_var.get("read_text_file.file_path", None)
    assert file_path is not None

    file_content = dict_var.get("read_text_file.file_content", None)
    assert file_content == "a=1\nb=2"


def test_inline_code_in_json_setting():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_inline_code_in_json_setting.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var
    dict_var.get("inline_code_in_json_setting.var1", None) == 2


def test_json_file_reader():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_json_file_reader.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var
    file_content = dict_var.get("read_json_file.file_content", None)
    assert len(file_content) == 2


def test_create_interactive_svg_from_puml():
    config_folder_path = r"./tests/CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_test_create_interactive_svg_from_puml.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var
    directory_path = dict_var.get("list_puml_files.directory_path", None)

    # Get the list of svg file in directory path
    svg_files = [
        f
        for f in os.listdir(directory_path)
        if os.path.isfile(os.path.join(directory_path, f)) and f.endswith(".svg")
    ]

    assert len(svg_files) == 2

    # Remove all svg files in the directory
    for filename in os.listdir(directory_path):
        if filename.endswith(".svg"):
            file_path = os.path.join(directory_path, filename)
            os.remove(file_path)

    # TODO add wrap tag in svg and overwrite
    g = 1


if __name__ == "__main__":
    # run_simple_image_creator()

    # test_validate_setting()
    # test_run_error()
    # test_variables()
    # test_variable_assigner()
    # test_variable_assigner_on_list()
    # test_log()
    # test_image_reader()
    # test_image_overlayer_use_at()
    # test_image_overlayer_use_move_to_edge()
    # test_simple_image_creator()
    # test_windows_wallpaper_helper()
    # test_get_requester()
    # test_env_reader()
    # test_life_span_calculator()
    # test_datetimer()
    # test_result_saver_csv()
    # test_result_saver_csv_write_twice()
    # test_result_saver_csv_use_dict_input()
    # test_setting_runner()
    # test_loop_starter_ender_use_num_loops()
    # test_loop_starter_ender_use_num_loops_20times()
    # test_loop_starter_ender_use_num_loops_with_ender_at_last()
    # test_loop_starter_ender_use_loop_list()
    # test_loop_starter_ender_use_nested_loop()
    # test_html_parser_b4soup_use_filter_by_text()
    # test_html_parser_b4soup_use_filter_by_text_with_split_and_replace()
    # test_html_parser_b4soup_use_filter_by_attribute()
    # test_html_parser_b4soup_use_filter_by_text_then_find_next_sibling()
    # test_html_parser_b4soup_use_filter_by_attribute_then_find_next_sibling()
    # test_text_printer()
    # test_dict_var_extracter()
    # test_command_runner()
    # test_dir_zipper()
    # test_files_remover()
    # test_script_runner()
    # test_script_runner_with_args()
    # test_script_runner_with_return()
    # test_plugin_waiter()
    test_dict_to_json_writer()
