{"general": {"init_steps": [], "steps": ["get_windows_wallpaper"], "variables": {}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "log_to_file": true, "folder_path": "./tests/tmp_CA_logs", "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_windows_wallpaper_setter": [{"step_name": "get_windows_wallpaper", "type": "PluginWindowsWallpaperHelper", "use": true, "is_get_wallpaper": true, "buffer_to_which": "image", "save": true, "save_path": "./tests/temp/test_get_windows_wallpaper.png", "is_set_wallpaper": false, "wallpaper_path": ""}]}