from contextlib import contextmanager
import os


@contextmanager
def change_dir(target_dir: str):
    """
    Context manager to change the current working directory.

    This function temporarily changes the current working directory to the specified target directory
    and reverts back to the original directory upon exit.

    Args:
        target_dir (str): The directory to change to.

    Examples:
        ```python
        with change_dir("/path/to/directory"):
            # Current working directory is now /path/to/directory
            print(os.getcwd())
        ```

        ```python
        with change_dir("/another/path"):
            # Current working directory is now /another/path
            print(os.getcwd())
        ```
    """
    original_dir = os.getcwd()
    os.chdir(target_dir)
    try:
        yield
    finally:
        os.chdir(original_dir)
