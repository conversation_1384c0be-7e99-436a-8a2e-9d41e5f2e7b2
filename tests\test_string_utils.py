import os
import tempfile
import unittest

import pytest
from tomsze_utils.string_utils import (
    count_space_in_front,
    extract_block_of_lines_using_start_end_marker,
    extract_data_from_a_string_of_lines,
    extract_data_from_a_string_of_lines_as_tuple,
    extract_list_elements_containing_string,
    extract_substring_within_marker,
    get_text_list_query_match_count_ratio,
    get_text_query_match_count_with_remove,
    is_string_list_any_in,
    mix,
    mix_with_index,
    remove_html_tags,
    remove_string_after_tag,
    split_command,
    split_text_to_words,
    wrap_text_by_indices,
)

from tomsze_utils.string_utils import (
    highligh_text_by_index_range,
    highlight_match_using_html,
)


class Test(unittest.TestCase):

    def test_split_command(self):
        # Test case 1: Basic command
        command = 'echo "Hello World"'
        expected = ["echo", "Hello World"]
        assert split_command(command) == expected

        # Test case 2: Command with extra spaces and quotes
        command = '   ls   -l   "My Documents"   '
        expected = ["ls", "-l", "My Documents"]
        assert split_command(command) == expected

        # Test case 3: Command with unbalanced quotes (should raise ValueError)
        command = 'echo "Hello World'
        with pytest.raises(ValueError):
            split_command(command)

        # Test case 4: Command with multiple quoted substrings
        command = 'git commit -m "Initial commit"'
        expected = ["git", "commit", "-m", "Initial commit"]
        assert split_command(command) == expected

        # Test case 5: Command with no arguments
        command = ""
        expected = []
        assert split_command(command) == expected

        # Test case 6: Command with newline character
        command = 'echo "Hello\nWorld"'
        expected = ["echo", "Hello\nWorld"]
        assert split_command(command) == expected

    def test_split_text_to_words(self):
        text = " one two three "
        expected_word_list = ["one", "two", "three"]
        word_list = split_text_to_words(text)
        assert expected_word_list == word_list

        text = " one two three 一 二 三 "
        expected_word_list = ["one", "two", "three", "一", "二", "三"]
        word_list = split_text_to_words(text)
        assert expected_word_list == word_list

    def test_aa(self):
        s = 1
        self.assertEqual(s, 1)

    def test_get_text_query_match_count(self):
        text = "one two three "
        query = "one"
        expect_count = 3
        match_count = get_text_query_match_count_with_remove(
            text=text,
            query=query,
        )
        self.assertEqual(expect_count, match_count)

        text = "one two three "
        query = "one two"
        expect_count = 6
        match_count = get_text_query_match_count_with_remove(
            text=text,
            query=query,
        )
        self.assertEqual(expect_count, match_count)

        text = "one two three "
        query = "one one"
        expect_count = 3
        match_count = get_text_query_match_count_with_remove(
            text=text,
            query=query,
        )
        self.assertEqual(expect_count, match_count)

    def test_get_text_list_query_match_count_ratio(self):
        text_list = [
            "one two three",
            # ~~~ 3/11
            "one one two",
            # ~~~ ~~~ 6/9
            "two two three three",
            # 0
            "oneonetwo threethree",
            # ~~~~~~ 6/19
        ]
        query = "one"
        expect_match_count_ratio_list = [
            3 / 11,
            6 / 9,
            0,
            6 / 19,
        ]
        match_count_ratio_list = get_text_list_query_match_count_ratio(
            text_list=text_list,
            query=query,
        )

        self.assertEqual(expect_match_count_ratio_list, match_count_ratio_list)

        text_list = [
            "one two three",
            # ~~~ 6/11
            "one one two",
            # ~~~ ~~~ 9/9
            "two two three three",
            # ~~~ ~~~ 6/16
            "oneonetwo threethree",
            # ~~~~~~~~~ 9/19
        ]
        query = "one two"
        expect_match_count_ratio_list = [
            6 / 11,
            9 / 9,
            6 / 16,
            9 / 19,
        ]
        match_count_ratio_list = get_text_list_query_match_count_ratio(
            text_list=text_list,
            query=query,
        )

        self.assertEqual(expect_match_count_ratio_list, match_count_ratio_list)

    def test_count_space_in_front(self):
        text = "   xx  "
        count = count_space_in_front(text=text)
        self.assertEqual(count, 3)


def test_mix():
    # Test case 1: Basic mix with odd indices
    string1 = "123456"
    string2 = "abcdef"
    expected_output = "1b3d5f"
    assert mix(string1, string2, use_odd=True) == expected_output

    # Test case 2: Basic mix with even indices
    string1 = "abcdef"
    string2 = "123456"
    expected_output = "1b3d5f"
    assert mix(string1, string2, use_odd=False) == expected_output

    # Test case 3: Different lengths
    string1 = "abc"
    string2 = "123456"
    with pytest.raises(ValueError, match="Both strings must be of the same length."):
        mix(string1, string2, use_odd=True)


def test_mix_with_index():
    # Test case 1: Basic mix with specified indices
    string1 = "abcdef"
    string2 = "012345"
    indices = [0, 1, 2, 3, 4, 5]
    expected_output = "012345"
    assert mix_with_index(string1, string2, indices) == expected_output

    # Test case 2: Using a subset of indices
    string1 = "abcdef"
    string2 = "012345"
    indices = [0, 2, 4]
    expected_output = "0b2d4f"
    assert mix_with_index(string1, string2, indices) == expected_output

    # Test case 3: Indices out of range
    string1 = "abc"
    string2 = "123"
    indices = [0, 1, 3]  # 3 is out of range
    with pytest.raises(
        ValueError, match="One or more indices are out of range for the strings."
    ):
        mix_with_index(string1, string2, indices)


def test_is_string_list_any_in():
    # Test case 1: Match found in the string
    string_list = ["abc", "def", "ghi"]
    string = "abcdef"
    assert is_string_list_any_in(string_list, string) == True

    # Test case 2: No match found in the string
    string_list = ["xyz", "uvw"]
    string = "abcdef"
    assert is_string_list_any_in(string_list, string) == False

    # Test case 3: Using temporary files
    with tempfile.TemporaryDirectory() as tmpdir:
        temp_file_path = os.path.join(tmpdir, "temp_file.txt")
        with open(temp_file_path, "w") as f:
            f.write("This is a test file containing abc.")

        string_list = ["abc", "xyz"]
        string = open(temp_file_path).read()
        assert is_string_list_any_in(string_list, string) == True


def test_extract_substring_within_marker():
    # Test case 1: Basic extraction with markers present
    input_string = "This is a test ~~~start~~~Hello World~~~end~~~ string."
    expected_output = "Hello World"
    assert extract_substring_within_marker(input_string) == expected_output

    # Test case 2: No markers present
    input_string = "This string has no markers."
    expected_output = ""
    assert extract_substring_within_marker(input_string) == expected_output


def test_extract_data_from_string():
    # Test case 1: Basic extraction with complete data
    input_string = (
        "time: 2022-01-01 00:00:00, name: Tom, age: 5\n"
        "time: 2022-01-01 00:00:01, name: Jane, age: 6\n"
        "time: 2022-01-01 00:00:02, name: Bob"
    )
    data_list = ["time", "name", "age"]
    expected_output = [
        ("2022-01-01 00:00:00", "Tom", "5"),
        ("2022-01-01 00:00:01", "Jane", "6"),
        ("2022-01-01 00:00:02", "Bob", None),
    ]
    assert (
        extract_data_from_a_string_of_lines(input_string, data_list) == expected_output
    )

    # Test case 2: Missing data in some lines
    input_string = (
        "time: 2022-01-01 00:00:00, name: Tom\n"
        "time: 2022-01-01 00:00:01, age: 6\n"
        "time: 2022-01-01 00:00:02, name: Bob, age: 7"
    )
    data_list = ["time", "name", "age"]
    expected_output = [
        ("2022-01-01 00:00:00", "Tom", None),
        ("2022-01-01 00:00:01", None, "6"),
        ("2022-01-01 00:00:02", "Bob", "7"),
    ]
    assert (
        extract_data_from_a_string_of_lines(input_string, data_list) == expected_output
    )


def test_extract_block_of_lines_using_start_end_marker():
    # Test case 1: Basic extraction with markers included
    file_content = "START\nLine 1\nLine 2\nEND\n"
    start_marker = "START"
    end_marker = "END"
    expected_output = ["START\nLine 1\nLine 2\nEND\n"]

    result = extract_block_of_lines_using_start_end_marker(
        file_content, start_marker, end_marker, is_include_markers_line=True
    )
    assert result == expected_output

    # Test case 1.1: Basic extraction with markers included where the line has extra characters
    file_content = "--START--\nLine 1\nLine 2\n--END--\n"
    start_marker = "START"
    end_marker = "END"
    expected_output = ["--START--\nLine 1\nLine 2\n--END--\n"]

    result = extract_block_of_lines_using_start_end_marker(
        file_content, start_marker, end_marker, is_include_markers_line=True
    )
    assert result == expected_output

    # Test case 2: Extraction without markers
    file_content = "BEGIN\nLine A\nLine B\nFINISH\nExtra Line\n"
    start_marker = "BEGIN"
    end_marker = "FINISH"
    expected_output = ["Line A\nLine B\n"]

    result = extract_block_of_lines_using_start_end_marker(
        file_content, start_marker, end_marker, is_include_markers_line=False
    )
    assert result == expected_output

    # Test case 2.1: Extraction without markers where the line has extra characters
    file_content = "--BEGIN--\nLine A\nLine B\n--FINISH--\nExtra Line\n"
    start_marker = "BEGIN"
    end_marker = "FINISH"
    expected_output = ["Line A\nLine B\n"]

    result = extract_block_of_lines_using_start_end_marker(
        file_content, start_marker, end_marker, is_include_markers_line=False
    )
    assert result == expected_output

    # Test case 3: Extracting multiple blocks
    file_content = "START\nLine 1\nLine 2\nEND\nSTART\nLine 3\nLine 4\nEND\n"
    start_marker = "START"
    end_marker = "END"
    expected_output = [
        "START\nLine 1\nLine 2\nEND\n",
        "START\nLine 3\nLine 4\nEND\n",
    ]

    result = extract_block_of_lines_using_start_end_marker(
        file_content, start_marker, end_marker, is_include_markers_line=True
    )
    assert result == expected_output


def test_extract_block_of_lines_using_start_end_marker_with_dash():
    # Test case 4: xxx
    file_content = "time:xx, ---,\nRequest ip: Line 1\nLine 2\ntime:xxx, ---,\nRequest ip: Line 3\nLine 4\ntime:xxx, ---,\n"
    start_marker = "Request ip"
    end_marker = "--,"
    expected_output = [
        "Request ip: Line 1\nLine 2\ntime:xxx, ---,\n",
        "Request ip: Line 3\nLine 4\ntime:xxx, ---,\n",
    ]

    result = extract_block_of_lines_using_start_end_marker(
        file_content, start_marker, end_marker, is_include_markers_line=True
    )
    assert result == expected_output


def test_extract_data_from_a_string_of_lines_as_tuple():
    # Test case 1: Normal extraction
    input_string = "time:aa, name:tom,\ntime:bb, name:may,\n"
    key_list = ["time", "name"]
    expected_result = ("bb", "may")

    result = extract_data_from_a_string_of_lines_as_tuple(input_string, key_list)
    assert result == expected_result

    # Test case 2: Missing keys
    input_string = "time:aa,\nname:tom\n"
    key_list = ["time", "name"]
    expected_result = ("aa", "tom")

    result = extract_data_from_a_string_of_lines_as_tuple(input_string, key_list)
    assert result == expected_result

    # Test case 3: Extracting data with multiple entries for the same key
    input_string = "time:aa, name:tom,\nname:may\n"
    key_list = ["time", "name"]
    expected_result = ("aa", "may")

    result = extract_data_from_a_string_of_lines_as_tuple(input_string, key_list)
    assert result == expected_result

    # Test case 4: with || and ,
    input_string = "time:aa || name:tom,\nname:may ||\n"
    key_list = ["time", "name"]
    expected_result = ("aa", "may")

    result = extract_data_from_a_string_of_lines_as_tuple(input_string, key_list)
    assert result == expected_result


class TestRemoveStringAfterTag:

    def test_remove_string_after_tag_with_tag(self):
        input_text = "This is a test string <tool_call> with some content."
        expected_output = "This is a test string"
        result = remove_string_after_tag(input_text)
        assert result == expected_output

    def test_remove_string_after_tag_without_tag(self):
        input_text = "This string does not contain the tag."
        expected_output = "This string does not contain the tag."
        result = remove_string_after_tag(input_text)
        assert result == expected_output


class TestExtractListElementsContainingString:

    def test_extract_list_elements_containing_string_found(self):
        string_list = ["apple", "banana", "cherry"]
        substring = "an"
        expected_result = ["banana"]

        result = extract_list_elements_containing_string(string_list, substring)
        assert result == expected_result

    def test_extract_list_elements_containing_string_not_found(self):
        string_list = ["apple", "banana", "cherry"]
        substring = "xyz"
        expected_result = []

        result = extract_list_elements_containing_string(string_list, substring)
        assert result == expected_result


import shutil
from tomsze_utils.string_utils import find_best_match_index


class TestFindBestMatchIndex:

    def test_find_best_match_index_found(self):
        text = "hello world"
        query = "world"
        expected_result = 6

        result = find_best_match_index(text, query)
        assert result == expected_result

    def test_find_best_match_index_not_found(self):
        text = "hello world"
        query = "xyz"
        expected_result = -1

        result = find_best_match_index(text, query)
        assert result == expected_result

    def test_find_best_match_index_with_ignore_ranges(self):
        text = "world world"
        query = "world"
        ignore_ranges = [(0, 5)]
        expected_result = 6

        result = find_best_match_index(text, query, ignore_ranges=ignore_ranges)
        assert result == expected_result

    def test_find_best_match_index_with_ignore_ranges_not_found(self):
        text = "hello world world"
        query = "world"
        ignore_ranges = [(0, 15)]
        expected_result = -1

        result = find_best_match_index(text, query, ignore_ranges=ignore_ranges)
        assert result == expected_result


class TestHighlightMatchUsingHtml:

    def test_highlight_match_using_html_basic(self):
        text = "example"
        expected_result = "<span style='color:blue;font-weight:bold'>example</span>"
        result = highlight_match_using_html(text)
        assert result == expected_result

    def test_highlight_match_using_html_xss(self):
        text = "<script>alert('XSS')</script>"
        expected_result = "<span style='color:blue;font-weight:bold'>&lt;script&gt;alert(&#x27;XSS&#x27;)&lt;/script&gt;</span>"
        result = highlight_match_using_html(text)
        assert result == expected_result


class TestHighlightTextByIndexRange:

    def test_highlight_text_by_index_range_basic(self):
        text = "hello world"
        index_range_list = [(0, 5)]
        expected_result = "\033[1;34mhello\033[0m world"
        result = highligh_text_by_index_range(text, index_range_list)
        assert result == expected_result

    def test_highlight_text_by_index_range_multiple_ranges(self):
        text = "hello world abc"
        index_range_list = [(0, 5), (6, 11)]
        expected_result = "\033[1;34mhello\033[0m \033[1;34mworld\033[0m abc"
        result = highligh_text_by_index_range(text, index_range_list)
        assert result == expected_result

    # ....for html....
    def test_highlight_text_by_index_range_basic(self):
        text = "hello world"
        index_range_list = [(0, 5)]
        expected_result = "<span style='color:blue;font-weight:bold'>hello</span> world"
        result = highligh_text_by_index_range(
            text, index_range_list, highlight_type="html"
        )
        assert result == expected_result

    def test_highlight_text_by_index_range_multiple_ranges(self):
        text = "hello world abc"
        index_range_list = [(0, 5), (6, 11)]
        expected_result = "<span style='color:blue;font-weight:bold'>hello</span> <span style='color:blue;font-weight:bold'>world</span> abc"
        result = highligh_text_by_index_range(
            text, index_range_list, highlight_type="html"
        )
        assert result == expected_result


class TestRemoveHTMLTags:

    def test_remove_html_tags(self):
        text = "<p>hello world</p>"
        result = remove_html_tags(text)
        assert result == "hello world"


import pytest
from tomsze_utils.string_utils import get_number_of_words


class TestGetNumberOfWords:
    def test_get_number_of_words_basic(self):
        text = "hello world"
        result = get_number_of_words(text)
        assert result == 2

    def test_get_number_of_words_two_spaces(self):
        text = "hello world  "
        result = get_number_of_words(text)
        assert result == 2

    def test_get_number_of_words_empty(self):
        text = ""
        result = get_number_of_words(text)
        assert result == 0


import unittest
import pytest
from tomsze_utils.string_utils import get_number_of_words_by_index_range_list


class TestGetNumberOfWordsByIndexRangeList:
    def test_basic_wrap(self):
        result = wrap_text_by_indices("hello world", 0, 4, "<a>", "</a>")
        assert result == "<a>hello</a> world"

    def test_middle_wrap(self):
        result = wrap_text_by_indices("python is fun", 7, 8, "[", "]")
        assert result == "python [is] fun"

    def test_end_wrap(self):
        result = wrap_text_by_indices("wrap me up", 8, 9, "*", "*")
        assert result == "wrap me *up*"


if __name__ == "__main__":
    unittest.main()
