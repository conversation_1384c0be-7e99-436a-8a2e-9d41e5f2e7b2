"""App plugin"""

import os
import cv2
import numpy as np
from dataclasses import dataclass
from tomsze_utils.configurable_algorithm.pluginer_settings.plugins.image_overlayer_util import (
    recursive_paint_circle,
)
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
    parse_data_and_store,
)


@dataclass
class PluginImageOverlayer:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        var_pool = data_obj.dict_var
        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        # draw overlays
        process_on_which_default = parse_data_and_store(
            logger,
            "process_on_which_default",
            data_obj,
            step_config,
            config,
        )

        process_on_which = parse_data_and_store(
            logger, "process_on_which", data_obj, step_config, config
        )

        if process_on_which in data_obj.dict_var.keys():
            im_overlay = data_obj.dict_var[process_on_which].copy()
        else:
            im_overlay = data_obj.dict_var[process_on_which_default].copy()

        # chagne to bgr
        if len(im_overlay.shape) == 2:
            im_overlay = cv2.cvtColor(im_overlay, cv2.COLOR_GRAY2BGR)

        # draw text
        if "all_text" in step_config.keys():
            for text_step in step_config["all_text"]:
                # TODO: allow preceision to number of decimal
                use = parse_data_and_store(logger, "use", data_obj, text_step, config)
                use_at = parse_data_and_store(
                    logger,
                    "use_at",
                    data_obj,
                    text_step,
                    config,
                    type="bool",
                    default=False,
                )
                at = parse_data_and_store(logger, "at", data_obj, text_step, config)

                use_move_to_edge = parse_data_and_store(
                    logger,
                    "use_move_to_edge",
                    data_obj,
                    text_step,
                    config,
                    type="bool",
                    default=False,
                )

                move_to_top = parse_data_and_store(
                    logger,
                    "move_to_top",
                    data_obj,
                    text_step,
                    config,
                    type="bool",
                    default=False,
                )

                move_to_bottom = parse_data_and_store(
                    logger,
                    "move_to_bottom",
                    data_obj,
                    text_step,
                    config,
                    type="bool",
                    default=False,
                )

                move_to_left = parse_data_and_store(
                    logger,
                    "move_to_left",
                    data_obj,
                    text_step,
                    config,
                    type="bool",
                    default=False,
                )

                move_to_right = parse_data_and_store(
                    logger,
                    "move_to_right",
                    data_obj,
                    text_step,
                    config,
                    type="bool",
                    default=False,
                )

                vertical_edge_gap = parse_data_and_store(
                    logger,
                    "vertical_edge_gap",
                    data_obj,
                    text_step,
                    config,
                    type="int",
                    default=0,
                )

                horizontal_edge_gap = parse_data_and_store(
                    logger,
                    "horizontal_edge_gap",
                    data_obj,
                    text_step,
                    config,
                    type="int",
                    default=0,
                )

                font_face = parse_data_and_store(
                    logger, "font_face", data_obj, text_step, config
                )

                font_scale = parse_data_and_store(
                    logger, "font_scale", data_obj, text_step, config
                )

                color = parse_data_and_store(
                    logger, "color", data_obj, text_step, config
                )
                color = color[::-1]

                thickness = parse_data_and_store(
                    logger, "thickness", data_obj, text_step, config
                )
                assert (
                    isinstance(thickness, str) == False
                ), "Do you have pixel_size and unit in dict_var? if not, assign one."
                thickness = max(1, int(thickness))

                line_gap = parse_data_and_store(
                    logger, "line_gap", data_obj, text_step, config
                )
                line_gap = max(1, int(line_gap))

                text = parse_data_and_store(logger, "text", data_obj, text_step, config)

                fill_background = parse_data_and_store(
                    logger,
                    "fill_background",
                    data_obj,
                    text_step,
                    config,
                )

                background_border_thickness = parse_data_and_store(
                    logger,
                    "background_border_thickness",
                    data_obj,
                    text_step,
                    config,
                    default=1.0,
                )

                background_color = parse_data_and_store(
                    logger,
                    "background_color",
                    data_obj,
                    text_step,
                    config,
                )

                background_margin = parse_data_and_store(
                    logger,
                    "background_margin",
                    data_obj,
                    text_step,
                    config,
                )
                background_margin = max(1, int(background_margin))

                # get text height
                (text_width, text_height), _ = cv2.getTextSize(
                    "Test", font_face, font_scale, thickness
                )
                data_obj.dict_var[f"{current_step}.font_height"] = text_height

                if use:
                    if use_move_to_edge:
                        # max line width
                        max_line_width = 0
                        for i, line in enumerate(text.split("\n")):
                            (_width, _height), _ = cv2.getTextSize(
                                line, font_face, font_scale, thickness
                            )
                            if _width > max_line_width:
                                max_line_width = _width

                        num_lines = len(text.split("\n"))

                        rect_width = background_margin * 2 + max_line_width
                        rect_height = (
                            background_margin * 2
                            + (num_lines) * (text_height)
                            + (num_lines - 1) * (line_gap)
                        )
                        num_channels = 1
                        if im_overlay.shape[2] == 3:
                            num_channels = 3

                        im_blank = np.ones(
                            (rect_height, rect_width, num_channels), dtype=np.uint8
                        )

                        if fill_background:
                            im_blank = cv2.rectangle(
                                im_blank,
                                pt1=(0, 0),
                                pt2=(im_blank.shape[1] - 1, im_blank.shape[0] - 1),
                                color=background_color,
                                thickness=-1,
                            )
                        else:
                            im_blank = cv2.rectangle(
                                im_blank,
                                pt1=(0, 0),
                                pt2=(im_blank.shape[1] - 1, im_blank.shape[0] - 1),
                                color=background_color,
                                thickness=background_border_thickness,
                            )

                        # Draw text.
                        x0 = background_margin
                        y0 = background_margin + text_height

                        for i, line in enumerate(text.split("\n")):
                            y = y0 + i * (text_height + line_gap)
                            cv2.putText(
                                im_blank,
                                line,
                                org=(x0, y),
                                fontFace=font_face,
                                fontScale=font_scale,
                                color=color,
                                thickness=thickness,
                            )

                        # Place im_blank to im_overlay
                        if move_to_top and move_to_bottom:
                            move_to_bottom = False
                        if move_to_left and move_to_right:
                            move_to_right = False

                        place_x = 0
                        place_y = 0
                        if move_to_top:
                            place_y = vertical_edge_gap
                        if move_to_bottom:
                            place_y = (
                                (im_overlay.shape[0] - 1)
                                - im_blank.shape[0]
                                - vertical_edge_gap
                            )
                        if move_to_left:
                            place_x = horizontal_edge_gap
                        if move_to_right:
                            place_x = (
                                (im_overlay.shape[1] - 1)
                                - im_blank.shape[1]
                                - horizontal_edge_gap
                            )
                        im_overlay[
                            place_y : place_y + im_blank.shape[0],
                            place_x : place_x + im_blank.shape[1],
                            :,
                        ] = im_blank

                    if use_at:
                        if fill_background:
                            # max line width
                            max_line_width = 0
                            for i, line in enumerate(text.split("\n")):
                                (_width, _height), _ = cv2.getTextSize(
                                    line, font_face, font_scale, thickness
                                )
                                if _width > max_line_width:
                                    max_line_width = _width

                            num_lines = len(text.split("\n"))

                            pt1 = (
                                at[0] - background_margin,
                                at[1] - text_height - background_margin,
                            )
                            pt2 = (
                                at[0] + max_line_width + background_margin,
                                at[1]
                                + (num_lines - 1) * (text_height)
                                + (num_lines - 1) * (line_gap)
                                + background_margin,
                            )

                            cv2.rectangle(
                                im_overlay,
                                pt1=pt1,
                                pt2=pt2,
                                color=background_color,
                                thickness=-1,
                            )  # thickness -1 to fill rectangle

                        # Draw text.
                        y0 = at[1]
                        text_size, _ = cv2.getTextSize(
                            text, font_face, font_scale, thickness
                        )

                        for i, line in enumerate(text.split("\n")):
                            y = y0 + i * (text_size[1] + line_gap)
                            cv2.putText(
                                im_overlay,
                                line,
                                org=(at[0], y),
                                fontFace=font_face,
                                fontScale=font_scale,
                                color=color,
                                thickness=thickness,
                            )

        # draw rectangles
        if "all_rectangle" in step_config.keys():
            for rectangle_step in step_config["all_rectangle"]:
                use = parse_data_and_store(
                    logger, "use", data_obj, rectangle_step, config
                )
                color = parse_data_and_store(
                    logger, "color", data_obj, rectangle_step, config
                )
                color = color[::-1]

                thickness = parse_data_and_store(
                    logger,
                    "thickness",
                    data_obj,
                    rectangle_step,
                    config,
                )

                top_left = parse_data_and_store(
                    logger,
                    "top_left",
                    data_obj,
                    rectangle_step,
                    config,
                )  # TODO check if is int

                bottom_right = parse_data_and_store(
                    logger,
                    "bottom_right",
                    data_obj,
                    rectangle_step,
                    config,
                )  # TODO check if is int

                # change to 2d array for drawing mutiple circles
                if len(np.array(top_left).shape) == 1:
                    top_left = [top_left]
                if len(np.array(bottom_right).shape) == 1:
                    bottom_right = [bottom_right]

                if use and not isinstance(top_left, str):
                    for top_left_each, bottom_right_each in zip(
                        np.array(top_left), np.array(bottom_right)
                    ):

                        top_left_each_tmp = (
                            int(top_left_each[0]),
                            int(top_left_each[1]),
                        )
                        bottom_right_each_tmp = (
                            int(bottom_right_each[0]),
                            int(bottom_right_each[1]),
                        )

                        cv2.rectangle(
                            im_overlay,
                            top_left_each_tmp,
                            bottom_right_each_tmp,
                            color=color,
                            thickness=thickness,
                        )

        # draw rectangles
        if "all_line" in step_config.keys():
            for rectangle_step in step_config["all_line"]:
                use = parse_data_and_store(
                    logger, "use", data_obj, rectangle_step, config
                )
                color = parse_data_and_store(
                    logger, "color", data_obj, rectangle_step, config
                )
                color = color[::-1]

                thickness = parse_data_and_store(
                    logger,
                    "thickness",
                    data_obj,
                    rectangle_step,
                    config,
                )

                point1 = parse_data_and_store(
                    logger, "point1", data_obj, rectangle_step, config
                )  # TODO check if is int

                point2 = parse_data_and_store(
                    logger, "point2", data_obj, rectangle_step, config
                )  # TODO check if is int

                # change to 2d array for drawing mutiple circles
                if len(np.array(point1).shape) == 1:
                    point1 = [point1]
                if len(np.array(point2).shape) == 1:
                    point2 = [point2]

                if use and not isinstance(point1, str):
                    for point1_each, point2_each in zip(
                        np.array(point1), np.array(point2)
                    ):
                        point1_tmp = (int(point1_each[0]), int(point1_each[1]))
                        point2_tmp = (
                            int(point2_each[0]),
                            (
                                im_overlay.shape[0] - 1
                                if point2_each[1] > im_overlay.shape[0]
                                else int(point2_each[1])
                            ),
                        )
                        # point2 = (0,0)
                        cv2.line(
                            im_overlay,
                            point1_tmp,
                            point2_tmp,
                            color=color,
                            thickness=thickness,
                        )

        # draw arrows
        if "all_arrow" in step_config.keys():
            for arrow_step in step_config["all_arrow"]:
                use = parse_data_and_store(logger, "use", data_obj, arrow_step, config)
                scan_line = parse_data_and_store(
                    logger, "scan_line", data_obj, arrow_step, config
                )

                color = parse_data_and_store(
                    logger, "color", data_obj, arrow_step, config
                )
                color = color[::-1]

                thickness = parse_data_and_store(
                    logger, "thickness", data_obj, arrow_step, config
                )

                if use and not isinstance(scan_line, str):
                    for scan_line_each in scan_line:
                        for scan_line_start, scan_line_end in scan_line_each:
                            cv2.arrowedLine(
                                im_overlay,
                                scan_line_start,
                                scan_line_end,
                                color=color,  # yellow in BGR
                                thickness=thickness,
                            )

        # draw circle
        if "all_circle" in step_config.keys():
            for circle_step in step_config["all_circle"]:
                use = parse_data_and_store(logger, "use", data_obj, circle_step, config)
                color = parse_data_and_store(
                    logger, "color", data_obj, circle_step, config
                )
                color = color[::-1]

                radius = parse_data_and_store(
                    logger, "radius", data_obj, circle_step, config
                )

                thickness = parse_data_and_store(
                    logger, "thickness", data_obj, circle_step, config
                )

                center = parse_data_and_store(
                    logger, "center", data_obj, circle_step, config
                )

                if use and not isinstance(center, str):
                    recursive_paint_circle(
                        center,
                        im_overlay,
                        radius,
                        color,
                        thickness,
                    )

        # which image to buffer to
        buffer_to_which = step_config["buffer_to_which"]
        data_obj.dict_var[buffer_to_which] = im_overlay

        # save image to path
        if step_config["save"]:
            save_path = parse_data_and_store(
                logger, "save_path", data_obj, step_config, config
            )

            save2 = parse_data_and_store(logger, "save2", data_obj, step_config, config)

            save_path2 = parse_data_and_store(
                logger, "save_path2", data_obj, step_config, config
            )

            save_folder_path = save_path.replace(save_path.split("/")[-1], "")
            if not os.path.exists(save_folder_path):
                os.makedirs(save_folder_path, exist_ok=True)

            cv2.imwrite(save_path, im_overlay)
            data_obj.dict_var[f"{current_step}.save_path"] = save_path

            if save2 and save_path2 != "":
                save_folder_path = save_path2.replace(save_path2.split("/")[-1], "")
                if not os.path.exists(save_folder_path):
                    os.makedirs(save_folder_path, exist_ok=True)

                cv2.imwrite(save_path2, im_overlay)
                data_obj.dict_var[f"{current_step}.save_path2"] = save_path2

        data_obj.dict_var[current_step] = "test_result_BasicAoiOverlay"

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
