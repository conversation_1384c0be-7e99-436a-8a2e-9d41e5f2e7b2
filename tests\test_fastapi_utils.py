from fastapi import FastAPI
import pytest
from tomsze_utils.fastapi_utils import try_asyncio_func
from tomsze_utils.fastapi_utils import (
    AppSettings,
    check_request_violation,
    initialize_fastapi_app,
    post_request_with_json_body,
    post_request_with_params,
)


def test_initialize_fastapi_app(tmp_path):
    # Create a temporary .env file
    env_file = tmp_path / ".env"
    env_file.write_text("PRODUCTION=0\nLOG_RESPONSE_BODY=1\nENCRYPT_RESPONSE=1")

    # Initialize the FastAPI app
    app, app_settings = initialize_fastapi_app(str(env_file))

    # Assert the app settings
    assert app_settings.is_production == "0"
    assert app_settings.is_log_response_body == "1"

    # Assert the app is an instance of FastAPI
    assert isinstance(app, FastAPI)


def test_post_request_with_params_success():
    """
    This test requires an internet connection to send a POST request to the specified API URL.
    """
    api_url = "https://httpbin.org/post"
    query_params = {"key": "value"}
    headers = {"Authorization": "Bearer token"}

    response = post_request_with_params(api_url, query_params, headers)

    assert response["headers"]["Authorization"] == "Bearer token"


def test_post_request_with_json_body_success():
    """
    This test requires an internet connection to send a POST request to the specified API URL.
    """
    api_url = "https://httpbin.org/post"
    json_body = {"key": "value"}
    headers = {"Authorization": "Bearer token"}

    response = post_request_with_json_body(api_url, json_body, headers)

    assert response["headers"]["Authorization"] == "Bearer token"


# ---------------------------------------------------------------------------


def test_check_request_violation_within_limit():
    # Test case 1: Within limit
    request_times = [
        "2023-10-01T12:00:00",
        "2023-10-01T12:01:00",
        "2023-10-01T12:02:00",
    ]
    max_requests = 5
    time_period = 2  # 2 minutes
    expect_violate = False
    assert expect_violate == check_request_violation(
        request_times, max_requests, time_period
    )


def test_check_request_violation_exceeds_limit():
    # Test case 2: Exceeds limit
    request_times = [
        "2023-10-01T12:00:00",
        "2023-10-01T12:01:00",
        "2023-10-01T12:02:00",
        "2023-10-01T12:03:00",
        "2023-10-01T12:04:00",
        "2023-10-01T12:05:00",
    ]
    max_requests = 5
    time_period = 5  # 5 minutes
    expect_violate = True
    assert expect_violate == check_request_violation(
        request_times, max_requests, time_period
    )


def test_check_request_violation_edge_case():
    # Test case 3: Edge case
    request_times = [
        "2023-10-01T12:02:00",
        "2023-10-01T12:06:00",
        "2023-10-01T12:07:00",
    ]
    max_requests = 2
    time_period = 2  # 2 minutes
    expect_violate = False
    assert expect_violate == check_request_violation(
        request_times, max_requests, time_period
    )


def test_check_request_violation_previous_violated_current_ok():
    # Test case 3: Edge case
    request_times = [
        "2023-10-01T12:02:00",
        "2023-10-01T12:02:01",
        "2023-10-01T12:02:02",  # << previous violated
        "2023-10-01T12:05:00",
        "2023-10-01T12:08:00",  # << current ok
    ]
    max_requests = 2
    time_period = 1  # 1 minutes
    expect_violate = False
    assert expect_violate == check_request_violation(
        request_times, max_requests, time_period
    )


def test_check_request_violation_previous_violated_current_not_ok():
    # Test case 3: Edge case
    request_times = [
        "2023-10-01T12:02:00",
        "2023-10-01T12:02:01",
        "2023-10-01T12:02:02",  # << previous violated
        "2023-10-01T12:05:00",
        "2023-10-01T12:08:00",
        "2023-10-01T12:08:01",
        "2023-10-01T12:08:02",  # << current not ok
    ]
    max_requests = 2
    time_period = 1  # 1 minutes
    expect_violate = True
    assert expect_violate == check_request_violation(
        request_times, max_requests, time_period
    )


@pytest.mark.asyncio
async def test_try_asyncio_func_returns_1():
    result = await try_asyncio_func()
    assert result == 1
