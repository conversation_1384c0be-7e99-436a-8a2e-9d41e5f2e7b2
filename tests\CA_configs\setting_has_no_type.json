{"general": {"init_steps": [], "steps": ["assign_variable_1"], "variables": {"var_online": false}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "log_to_file": true, "folder_path": "./tests/tmp_CA_logs", "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_variable_assigner": [{"step_name": "assign_variable_1", "use": true, "var1": 1}]}