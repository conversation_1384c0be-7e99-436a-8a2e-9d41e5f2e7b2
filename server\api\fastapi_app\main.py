from tomsze_utils.logger_utils import SimpleLogger
from starlette.responses import JSONResponse

logger = SimpleLogger(
    log_base_name="server",
    logs_folder_path="./server_log",
    new_file_for_each="month",
    get_existing_instance=False,
)

import json
from typing import Awaitable, Callable, List
from fastapi import Request, Response
from server.api.end_to_end import api_functions
from server.api.end_to_end.utils import (
    add_ip_request_time_to_db_rate_limit,
    add_new_ip_to_db_rate_limit_if_not_exist,
    check_ip_request_violation_in_db_rate_limit,
    check_is_ip_blocked_in_db_block_ip,
    send_email_after_update,
)
from server.api.end_to_end import data_class
from server.api.end_to_end.data_class import (
    ClientAppsToUpdate,
    CodeVerifyData,
    Data,
    FakeRequestData,
    IsUserSignedUpData,
    ObtainNewAccessTokenByRefreshTokenData,
    SendVerificationEmailData,
    SignInAccessTokenData,
    SignInData,
    SignupData,
    RemoveUserData,
    ValidateUserAccessTokenData,
)
from server.api.end_to_end.api_constant_keys import (
    ApiResponseErrorMessage,
    ApiResponseKeys,
    ClientColEnum,
    EndPointsConstants,
    PostDataKeyConstants,
)
from functools import partial
from dotenv import find_dotenv

from tomsze_utils.env_parser_utils import parse_env
from tomsze_utils.fastapi_utils import (
    initialize_fastapi_app,
    log_request_and_decrypt_and_encrypt_response,
    check_request_violation,
)
from tomsze_utils.encrypt_utils import (
    generate_key_from_key_dict_and_sys_time,
)
from tomsze_utils.auth_utils import validate_token
from const_path import (
    proj_dir_path,
    proj_ser_api_db_rate_limit_path,
    proj_ser_api_db_block_ip_path,
)
from tomsze_utils.api_response_utils import ApiResponse

from mode import is_production
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit

# Initialize the rate limit database if not in debug mode
db_rate_limit = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_rate_limit_path,
    db_name="db_rate_limit",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)
db_block_ip = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_block_ip_path,
    db_name="db_block_ip",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)

# TODO: check are pickles file keys matched


with open("./security/date_strings.json", "r") as file:
    date_strings_dict = json.load(file)

app, appSettings = initialize_fastapi_app(env_path="./.env", logger=logger)


# ======== root endpoint ===========
@app.get(EndPointsConstants.APP_NAME + EndPointsConstants.ROOT)
async def root():
    message = "This is the root if used get"
    api_functions.dummy()
    response = ApiResponse.success_response(data=message, message=message)
    logger.info(f"response: {response}")

    return response


@app.post(EndPointsConstants.APP_NAME + EndPointsConstants.ROOT)
async def post():
    message = "This is the root if used post"
    response = ApiResponse.success_response(data=message, message=message)
    logger.info(f"response: {response}")

    return response


@app.middleware("http")
async def endpoint_middleware(
    request: Request,
    call_next: Callable[[Request], Awaitable[Response]],
) -> Response:
    """
    Middleware to log requests, decrypt request bodies, and encrypt response bodies.

    This middleware processes incoming requests by logging relevant information,
    decrypting the request body, and encrypting the response body before sending it back to the client.

    Args:
        request (Request): The incoming request object.
        call_next (Callable[[Request], Awaitable[Response]]): A function to call the next middleware or endpoint.

    Returns:
        Response: The response object after processing.
    """
    logger.info("endpoint_middleware")

    key = generate_key_from_key_dict_and_sys_time(date_strings_dict)

    response = await log_request_and_decrypt_and_encrypt_response(
        request,
        call_next,
        appSettings,
        key=key,
        logger=logger,
    )

    return response


ignore_check_access_token_endpoint_list = [
    EndPointsConstants.APP_NAME + EndPointsConstants.IS_SERVER_ONLINE,
    EndPointsConstants.APP_NAME + EndPointsConstants.REQUEST_DATETIME,
    EndPointsConstants.APP_NAME + EndPointsConstants.SEND_VERIFICATION_EMAIL,
    EndPointsConstants.APP_NAME + EndPointsConstants.SIGN_UP,
    EndPointsConstants.APP_NAME + EndPointsConstants.SIGN_IN,
    EndPointsConstants.APP_NAME + EndPointsConstants.SIGN_IN_ACCESS_TOKEN,
    EndPointsConstants.APP_NAME + EndPointsConstants.SIGN_OUT,
    EndPointsConstants.APP_NAME + EndPointsConstants.GET_DASHBOARD_INFO,
    EndPointsConstants.APP_NAME + EndPointsConstants.CHECK_CLIENT_APP,
    EndPointsConstants.APP_NAME + EndPointsConstants.VALIDATE_USER_ACCESS_TOKEN,
    EndPointsConstants.APP_NAME
    + EndPointsConstants.OBTAIN_NEW_ACCESS_TOKEN_BY_REFRESH_TOKEN,
    EndPointsConstants.APP_NAME + EndPointsConstants.REMOVE_USER,
    EndPointsConstants.APP_NAME + EndPointsConstants.IS_USER_SIGNED_UP,
    EndPointsConstants.APP_NAME + EndPointsConstants.OBTAIN_VERIFICATION_CODE,
]


@app.middleware("http")
async def check_access_token_middleware(
    request: Request,
    call_next: Callable[[Request], Awaitable[Response]],
) -> Response:
    """
    Check if the current endpoint is in the list of
    endpoints that do not require access token validation
    """
    logger.info("check_access_token_middleware")
    endpoint = request.url.path  # The link after port number
    if endpoint == EndPointsConstants.ROOT:
        return JSONResponse(
            content=ApiResponse.error_response(error="No / endpoint"),
            status_code=400,
        )
    elif endpoint in ignore_check_access_token_endpoint_list:
        response = await call_next(request)
        return response

    # Obtain the access token from the request headers
    header_dict = dict(request.headers)
    access_token = header_dict.get("authorization", None)
    if not access_token:
        logger.error(f"Missing access token for endpoint: {endpoint}")
        return JSONResponse(
            content={
                ApiResponseKeys.API_ERROR: {
                    ApiResponseKeys.API_ERROR: {
                        ApiResponseErrorMessage.API_MISSING_ACCESS_TOKEN
                    }
                }
            },
            status_code=401,
        )

    access_token = access_token.replace("Bearer ", "")

    # Validate the access token
    payload, error = validate_token(access_token)
    if error:
        if "Invalid token" in error:
            return JSONResponse(
                content={ApiResponseKeys.API_ERROR: "Invalid token"}, status_code=401
            )
    # Continue processing the request
    response = await call_next(request)
    return response


@app.middleware("http")
async def rate_limit_middleware(
    request: Request,
    call_next: Callable[[Request], Awaitable[Response]],
) -> Response:
    logger.info("rate_limit_middleware")
    # Get ip.
    client_ip = request.headers.get("X-Forwarded-For", request.client.host)

    # Check if the client IP is already in the rate limit database and add it if not
    add_new_ip_to_db_rate_limit_if_not_exist(
        ip=client_ip,
        db_rate_limit=db_rate_limit,
    )

    # Add the request time to db rate limt.
    add_ip_request_time_to_db_rate_limit(
        db_rate_limit=db_rate_limit,
        client_ip=client_ip,
    )

    # Check if the client IP has exceeded the request limit in the rate limit database
    is_violated = check_ip_request_violation_in_db_rate_limit(
        db_rate_limit=db_rate_limit,
        client_ip=client_ip,
    )

    # Check if the request exceeds the rate limit
    if not is_violated:
        response: Response = await call_next(request)
        return response
    else:
        # Prepare the response for rate-limited clients
        return Response(
            content=ApiResponse.error_response(error="Rate limited", code=429),
            media_type="application/json",
            status_code=429,
        )


@app.middleware("http")
async def block_ip_middleware(
    request: Request,
    call_next: Callable[[Request], Awaitable[Response]],
) -> Response:
    logger.info("block_ip_middleware")
    # Get ip.
    client_ip = request.headers.get("X-Forwarded-For", request.client.host)

    # Check if the client IP is blocked in the block IP database
    is_blocked = check_is_ip_blocked_in_db_block_ip(
        ip=client_ip,
        db_block_ip=db_block_ip,
    )

    # Check if the client IP is blocked
    if not is_blocked:
        response: Response = await call_next(request)
        return response
    else:
        # Prepare the response for blocked clients
        return Response(
            content=ApiResponse.error_response(error="Forbidden", code=403),
            media_type="application/json",
            status_code=403,
        )


# ======== auth endpoints ===========


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.SEND_VERIFICATION_EMAIL
)  # post request from app
async def send_verification_email_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        # Deal with query is "".
        if data_dict[PostDataKeyConstants.EMAIL_ADDRESS].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.EMAIL_ADDRESS} should not be empty"
            )
            proceed = False

        if proceed:
            send_verification_email_data = SendVerificationEmailData(
                email_address=data_dict[PostDataKeyConstants.EMAIL_ADDRESS],
            )
            response = api_functions.send_verification_email(
                send_verification_email_data=send_verification_email_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.SIGN_UP
)  # post request from app
async def sign_up_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        # Deal with query is "".
        if data_dict[PostDataKeyConstants.EMAIL_ADDRESS].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.EMAIL_ADDRESS} should not be empty"
            )
            proceed = False

        if data_dict[PostDataKeyConstants.PASSWORD].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.PASSWORD} should not be empty"
            )
            proceed = False

        if data_dict[PostDataKeyConstants.VERIFICATION_CODE].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.VERIFICATION_CODE} should not be empty"
            )
            proceed = False

        if proceed:
            signup_data = SignupData(
                email_address=data_dict[PostDataKeyConstants.EMAIL_ADDRESS],
                password=data_dict[PostDataKeyConstants.PASSWORD],
                verification_code=data_dict[PostDataKeyConstants.VERIFICATION_CODE],
            )
            response = api_functions.sign_up(
                sign_up_data=signup_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.SIGN_IN
)  # post request from app
async def sign_in_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        # Deal with query is "".
        if data_dict[PostDataKeyConstants.EMAIL_ADDRESS].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.EMAIL_ADDRESS} should not be empty"
            )
            proceed = False

        if data_dict[PostDataKeyConstants.PASSWORD].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.PASSWORD} should not be empty"
            )
            proceed = False

        if proceed:
            sign_in_data = SignInData(
                email_address=data_dict[PostDataKeyConstants.EMAIL_ADDRESS],
                password=data_dict[PostDataKeyConstants.PASSWORD],
            )
            response = api_functions.sign_in(
                sign_in_data=sign_in_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.SIGN_IN_ACCESS_TOKEN
)  # post request from app
async def sign_in_access_token_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        if data_dict[PostDataKeyConstants.ACCESS_TOKEN].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.ACCESS_TOKEN} should not be empty"
            )
            proceed = False

        if proceed:
            sign_in_data = SignInAccessTokenData(
                access_token=data_dict[PostDataKeyConstants.ACCESS_TOKEN],
            )
            response = api_functions.sign_in_access_token(
                sign_in_data=sign_in_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.REQUEST_RESOURCE_X
)  # post request from app
async def request_resource_endpoint_function(
    data: Data,
    request: Request,
):
    """
    This endpoint serves as an example of how to get resource of db_resource_x.
    """
    response = {}
    proceed = True

    header_dict = dict(request.headers)
    access_token = header_dict.get("Authorization", None)
    if not access_token:
        return ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_MISSING_ACCESS_TOKEN
        )

    access_token = access_token.replace("Bearer ", "")

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""

        if proceed:
            # TODO decouple checking of access token from here
            fake_request_data = FakeRequestData(
                access_token=access_token,
                resource_item=data_dict["request_item"],
            )

            response = api_functions.request_fake_resource(
                fake_request_data=fake_request_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.REMOVE_USER
)  # post request from app
async def remove_user_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        # Deal with query is "".
        if data_dict[PostDataKeyConstants.EMAIL_ADDRESS].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.EMAIL_ADDRESS} should not be empty"
            )
            proceed = False

        if proceed:
            remove_user_data = RemoveUserData(
                email_address=data_dict[PostDataKeyConstants.EMAIL_ADDRESS],
            )
            response = api_functions.remove_user(
                remove_user_data=remove_user_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.get(
    EndPointsConstants.APP_NAME + EndPointsConstants.IS_SERVER_ONLINE
)  # get request from app
async def request_is_server_online():
    response = {}
    proceed = True

    try:
        """main start"""
        if proceed:
            response = api_functions.get_is_server_online()
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.get(
    EndPointsConstants.APP_NAME + EndPointsConstants.REQUEST_DATETIME
)  # get request from app
async def request_datetime():
    response = {}
    proceed = True

    try:
        """main start"""
        if proceed:
            response = api_functions.get_datetime()
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.IS_USER_SIGNED_UP
)  # post request from app
async def is_user_signed_up_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        # Deal with query is "".
        if data_dict[PostDataKeyConstants.EMAIL_ADDRESS].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.EMAIL_ADDRESS} should not be empty"
            )
            proceed = False

        if proceed:
            is_user_signed_up_data = IsUserSignedUpData(
                email_address=data_dict[PostDataKeyConstants.EMAIL_ADDRESS],
            )
            response = api_functions.is_user_signed_up(
                is_user_signed_up_data=is_user_signed_up_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.VALIDATE_USER_ACCESS_TOKEN
)  # post request from app
async def validate_user_access_token_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)
        """ main start"""
        # Deal with query is "".
        if data_dict[PostDataKeyConstants.ACCESS_TOKEN].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.ACCESS_TOKEN} should not be empty"
            )
            proceed = False

        if proceed:
            validate_user_access_token_data = ValidateUserAccessTokenData(
                access_token=data_dict[PostDataKeyConstants.ACCESS_TOKEN],
            )
            response = api_functions.validate_user_access_token(
                validate_user_access_token_data=validate_user_access_token_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME
    + EndPointsConstants.OBTAIN_NEW_ACCESS_TOKEN_BY_REFRESH_TOKEN
)  # post request from app
async def obtain_new_access_token_by_refresh_token_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        # Deal with query is "".
        if data_dict[PostDataKeyConstants.REFRESH_TOKEN].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.REFRESH_TOKEN} should not be empty"
            )
            proceed = False
        if data_dict[PostDataKeyConstants.PASSWORD].strip() == "":
            response = ApiResponse.error_response(
                error=f"{PostDataKeyConstants.PASSWORD} should not be empty"
            )
            proceed = False

        if proceed:
            obtain_new_access_token_by_refresh_tokendata = (
                ObtainNewAccessTokenByRefreshTokenData(
                    refresh_token=data_dict[PostDataKeyConstants.REFRESH_TOKEN],
                )
            )
            response = api_functions.obtain_new_access_token_by_refresh_token(
                obtain_new_access_token_by_refresh_tokendata=obtain_new_access_token_by_refresh_tokendata,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.OBTAIN_VERIFICATION_CODE
)  # post request from app
async def obtain_verification_code_endpoint_function(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        proceed, response = api_functions.validate_data_dict_item_empty(
            data_dict, [PostDataKeyConstants.EMAIL_ADDRESS]
        )

        if proceed:
            obtain_verification_code_data = data_class.ObtainVerificationCodeData(
                email=data_dict[PostDataKeyConstants.EMAIL_ADDRESS],
            )
            response = api_functions.obtain_verification_code_and_send_email(
                obtain_verification_code_data=obtain_verification_code_data,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


# ======== end of auth endpoints ===========
# ======== business endpoints ===========


@app.get(
    EndPointsConstants.APP_NAME + EndPointsConstants.GET_DASHBOARD_INFO
)  # get request from app
async def get_dashboard_info_endpoint_function():
    response = {}
    proceed = True

    try:

        """main start"""

        if proceed:
            response = api_functions.get_dashboard_info()
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.SEARCH_USER_APP
)  # search user app
async def search_user_app(
    data: Data,
):
    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        # Deal with query is "".
        if data_dict[ClientColEnum.QUERY].strip() == "":
            response = ApiResponse.error_response(
                error=f"{ClientColEnum.QUERY} should not be empty"
            )
            proceed = False

        if proceed:
            response = api_functions.search_user_app(
                client_search_data=data_dict,
                debug_mode=False,
            )
        """ main end"""
    except Exception as e:
        logger.error(f"Exception: {e}")
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    return response


@app.post(
    EndPointsConstants.APP_NAME + EndPointsConstants.CHECK_CLIENT_APP
)  # check client apps
async def cca(
    data: Data,
):
    logger.debug("cca run")

    response = {}
    proceed = True

    try:
        data_str = data.data_str
        data_dict = json.loads(data_str)

        """ main start"""
        if proceed:
            response = api_functions.check_client_apps_need_scrape_and_update_db_to_add(
                client_apps=data_dict
            )
        """ main end"""
    except ValueError as e:
        logger.error(e)
        response = ApiResponse.error_response(
            error=ApiResponseErrorMessage.API_INTERNAL_SERVER_ERROR
        )

    logger.debug("cca done")
    return response


@app.post(EndPointsConstants.APP_NAME + "/usa")  # update server apps
async def usa(
    client_apps_to_update: ClientAppsToUpdate,
):
    """
    Run update_server_db_apps_and_db_to_add in middleware in background
    Note: Dont remove this endpoint. client_apps_to_update will be used.
    """
    response = ApiResponse.success_response(data="Database will be updated soon.")
    logger.info(f"response: {response}")
    return response


# ======= end of business endpoints ============
