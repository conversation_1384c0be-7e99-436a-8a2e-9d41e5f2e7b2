"""App plugin"""

from dataclasses import dataclass

import paramiko
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginSSHParamikoLoginer:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        ssh_username = parse_data_and_store(
            logger,
            "ssh_username",
            data_obj,
            step_config,
            config,
            type="str",
            default="root",
        )

        ssh_host = parse_data_and_store(
            logger,
            "ssh_host",
            data_obj,
            step_config,
            config,
        )

        ssh_private_key = parse_data_and_store(
            logger,
            "ssh_private_key",
            data_obj,
            step_config,
            config,
        )

        port = parse_data_and_store(
            logger,
            "port",
            data_obj,
            step_config,
            config,
            type="int",
            default=22,
        )

        c = paramiko.SSHClient()
        c.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        try:
            c.connect(
                hostname=ssh_host,
                port=port,
                username=ssh_username,
                password="",
                allow_agent=False,
                look_for_keys=False,
            )
            data_obj.dict_var[f"{current_step}.SSHCient"] = c
        except Exception as e:
            data_obj.dict_var[f"{current_step}.Exception"] = str(e)

        # return c.exec_command("ls")[1].read().decode().splitlines()

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
