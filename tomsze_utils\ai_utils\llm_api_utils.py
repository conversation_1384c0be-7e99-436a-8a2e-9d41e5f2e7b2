import logging
from typing import Annotated, Callable, List
from autogen.function_utils import get_function_schema

from tomsze_utils.script_analyse_utils import extract_function_names_from_script_in_dir
from tomsze_utils.import_utils import (
    get_callable_from_module_by_function_name,
    import_module_by_path,
)

logger = logging.getLogger(__name__)


def create_function_schema(
    function_callable: Callable,
    name: str,
    description: str,
) -> dict:
    """
    Creates a function schema for the given callable.

    Args:
        function_callable (Callable): The function for which the schema is created.
        name (str): The name of the function.
        description (str): A brief description of the function.

    Returns:
        dict: The generated function schema.

    Examples:
        ```python
        def f(a: Annotated[str, "Parameter a"], b: int = 2, c: Annotated[float, "Parameter c"] = 0.1) -> None:
            pass

        schema = create_function_schema(function_callable=f, name="f", description="function f")
        {'type': 'function',
         'function': {'description': 'function f',
             'name': 'f',
             'parameters': {'type': 'object',
                'properties': {'a': {'type': 'str', 'description': 'Parameter a'},
                    'b': {'type': 'int', 'description': 'b'},
                    'c': {'type': 'float', 'description': 'Parameter c'}},
                'required': ['a']}}}
        ```

        ```python
        schema = create_function_schema(function_callable=another_function, name="another_function", description="This function does another thing.")
        ```
    """
    return get_function_schema(
        f=function_callable,
        name=name,
        description=description,
    )


def create_function_schema_list_from_dir(
    dir: str,
    include_function_names: List[str] = None,
) -> List[dict]:
    """
    Creates a list of function schemas from all Python scripts in the specified directory.

    Args:
        dir (str): The path to the directory containing Python scripts.
        include_function_names (List[str], optional): A list of function names to include. If None, all functions are included.

    Returns:
        List[dict]: A list of generated function schemas.

    Examples:
        ```python
        schemas = create_function_schema_list_from_dir("path/to/scripts")
        ```

        ```python
        schemas = create_function_schema_list_from_dir("/absolute/path/to/scripts", include_function_names=["function1", "function2"])
        ```
    """
    function_meta_list = extract_function_names_from_script_in_dir(
        script_dir=dir,
    )

    function_schema_list = []
    for function_meta in function_meta_list:
        if (
            include_function_names
            and function_meta.function_name not in include_function_names
        ):
            continue

        script_path = function_meta.file_path
        error, module = import_module_by_path(absolute_path=script_path)

        if error:
            logger.error(error)
            continue
        fun_callable = get_callable_from_module_by_function_name(
            module=module,
            function_name=function_meta.function_name,
        )

        if not fun_callable:
            continue
        function_schema = create_function_schema(
            name=function_meta.function_name,
            function_callable=fun_callable,
            description=function_meta.function_description,
        )
        function_schema_list.append(function_schema)

    return function_schema_list


def create_tool_descriptoin_string_from_function_schema_list(
    function_schema_list: List[dict],
) -> str:
    """
    Creates a description string from a list of function schemas.

    Args:
        function_schema_list (List[dict]): A list of function schemas.

    Returns:
        str: A formatted string describing the functions.

    Examples:
        ```python
        schemas = [
            {"function": {"name": "add", "description": "Adds two numbers"}},
            {"function": {"name": "subtract", "description": "Subtracts two numbers"}},
        ]
        result = create_tool_descriptoin_string_from_function_schema_list(schemas)
        # result will be "add: Adds two numbers\nsubtract: Subtracts two numbers"
        ```

        ```python
        schemas = []
        result = create_tool_descriptoin_string_from_function_schema_list(schemas)
        # result will be an empty string ""
        ```
    """
    return "\n".join(
        [
            f"({schema['function']['name']}): ({schema['function']['description']})"
            for schema in function_schema_list
        ]
    )


def main():

    # # try create_function_schema()
    # def f(
    #     a: Annotated[str, "Parameter a"],
    #     b: int = 2,
    #     c: Annotated[float, "Parameter c"] = 0.1,
    # ) -> None:
    #     pass

    # schema = create_function_schema(
    #     function_callable=f,
    #     name="f",
    #     description="function f",
    # )

    g = 1


if __name__ == "__main__":
    main()
