"""App plugin"""

from dataclasses import dataclass
import os
import zipfile
from tomsze_utils.path_utils import convert_to_abs_path
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)
import logging

from tomsze_utils.string_utils import is_string_list_any_in


@dataclass
class PluginDirZipper:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        folder_to_zip_path = parse_data_and_store(
            logger,
            "folder_to_zip_path",
            data_obj,
            step_config,
            config,
        )

        zip_name = parse_data_and_store(
            logger,
            "zip_name",
            data_obj,
            step_config,
            config,
        )

        exclude_path_list = parse_data_and_store(
            logger,
            "exclude_path_list",
            data_obj,
            step_config,
            config,
            default=[],
        )

        include_only_path_list = parse_data_and_store(
            logger,
            "include_only_path_list",
            data_obj,
            step_config,
            config,
            default=[],
        )

        save_to_folder_path = parse_data_and_store(
            logger,
            "save_to_folder_path",
            data_obj,
            step_config,
            config,
        )

        if not os.path.exists(save_to_folder_path):
            os.makedirs(save_to_folder_path, exist_ok=True)

        output_zip_path = os.path.join(save_to_folder_path, zip_name)

        # Zipping.
        with zipfile.ZipFile(output_zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            exclude_path_list = convert_to_abs_path(exclude_path_list)
            include_only_path_list = convert_to_abs_path(include_only_path_list)

            if include_only_path_list:
                for include_path in include_only_path_list:
                    if os.path.exists(include_path):
                        if os.path.isdir(include_path):
                            for dirpath, dirnames, files in os.walk(include_path):
                                for file in files:
                                    file_path = os.path.join(dirpath, file)
                                    zipf.write(
                                        file_path,
                                        os.path.relpath(file_path, folder_to_zip_path),
                                    )
                        else:
                            zipf.write(
                                include_path,
                                os.path.relpath(include_path, folder_to_zip_path),
                            )
                    else:
                        logging.warning(
                            f"Include only path does not exist: {include_path}"
                        )
            else:
                for dirpath, dirnames, files in os.walk(folder_to_zip_path):
                    for file in files:
                        # Exclude files
                        file_path = os.path.join(dirpath, file)
                        abs_file_path = os.path.realpath(file_path)
                        if not is_string_list_any_in(exclude_path_list, abs_file_path):
                            # Create a relative path for the zip file
                            zipf.write(file_path)

        data_obj.dict_var[f"{current_step}.zip_path"] = output_zip_path

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
