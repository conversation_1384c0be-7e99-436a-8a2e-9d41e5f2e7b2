# 2025-07-05T13:07:35.823323100Z:
test_suite_meta_data {
  scheduled_test_case_count: 1
}
test_status: FAILED
test_result {
  test_case {
    test_class: "SignInTest"
    test_package: "com.kewtoms.whatappsdo.auth"
    test_method: "test_sign_in_success_mock"
    start_time {
      seconds: 1751720788
      nanos: 29000000
    }
    end_time {
      seconds: 1751720854
      nanos: 693000000
    }
  }
  test_status: FAILED
  error {
    error_message: "androidx.test.espresso.PerformException: Error performing \'single click - At Coordinates: 539, 1040 and precision: 16, 16\' on view \'view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>\'.\nat androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)\nat androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)\nat androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)\nat androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)\nat androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)\nat androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)\nat androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)\nat androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)\nat com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)\n... 33 trimmed\nCaused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.\nat androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)\nat androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)\nat androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)\nat androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)\nat androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)\nat androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)\nat androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)\nat androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)\nat androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)\nat androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)\nat androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)\nat java.util.concurrent.FutureTask.run(FutureTask.java:264)\nat android.os.Handler.handleCallback(Handler.java:942)\nat android.os.Handler.dispatchMessage(Handler.java:99)\nat android.os.Looper.loopOnce(Looper.java:201)\nat android.os.Looper.loop(Looper.java:288)\nat android.app.ActivityThread.main(ActivityThread.java:7924)\nat java.lang.reflect.Method.invoke(Native Method)\nat com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)\nat com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)\n"
    error_type: "androidx.test.espresso.AppNotIdleException"
    stack_trace: "androidx.test.espresso.PerformException: Error performing \'single click - At Coordinates: 539, 1040 and precision: 16, 16\' on view \'view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>\'.\nat androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)\nat androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)\nat androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)\nat androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)\nat androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)\nat androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)\nat androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)\nat androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)\nat com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)\n... 33 trimmed\nCaused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.\nat androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)\nat androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)\nat androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)\nat androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)\nat androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)\nat androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)\nat androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)\nat androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)\nat androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)\nat androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)\nat androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)\nat java.util.concurrent.FutureTask.run(FutureTask.java:264)\nat android.os.Handler.handleCallback(Handler.java:942)\nat android.os.Handler.dispatchMessage(Handler.java:99)\nat android.os.Looper.loopOnce(Looper.java:201)\nat android.os.Looper.loop(Looper.java:288)\nat android.app.ActivityThread.main(ActivityThread.java:7924)\nat java.lang.reflect.Method.invoke(Native Method)\nat com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)\nat com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)\n"
  }
  output_artifact {
    label {
      label: "logcat"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\logcat-com.kewtoms.whatappsdo.auth.SignInTest-test_sign_in_success_mock.txt"
    }
  }
  output_artifact {
    label {
      label: "device-info"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\device-info.pb"
    }
  }
  output_artifact {
    label {
      label: "device-info.meminfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\meminfo"
    }
  }
  output_artifact {
    label {
      label: "device-info.cpuinfo"
      namespace: "android"
    }
    source_path {
      path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\cpuinfo"
    }
  }
}
output_artifact {
  label {
    label: "test-results.log"
    namespace: "com.google.testing.platform.runtime.android.driver.AndroidInstrumentationDriver"
  }
  source_path {
    path: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\outputs\\androidTest-results\\connected\\debug\\Pixel_6_API_33(AVD) - 13\\testlog\\test-results.log"
  }
  type: TEST_DATA
  mime_type: "text/plain"
}
