from dataclasses import dataclass
from typing import Dict
from server.api.end_to_end.data_class import VerifiedAppResult


def verify_client_apps(
    client_apps: Dict,
    server_app_ids: Dict,
) -> VerifiedAppResult:
    """
    Verify whether client apps are in server.

    Returns VerifiedAppResult.
    """

    print("check_if_each_client_app_in_server")

    app_ids = client_apps["appIds"]
    new_apps = {}
    is_new = False
    for id in app_ids:
        if id not in server_app_ids:
            new_apps[id] = 1
            is_new = True

    return VerifiedAppResult(new_apps, is_new)
