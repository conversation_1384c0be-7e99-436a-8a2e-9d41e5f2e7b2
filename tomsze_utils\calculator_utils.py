from typing import Annotated, Literal

Operator = Literal["+", "-", "*", "/"]


def calculator(
    a: int,
    b: int,
    operator: Annotated[Operator, "operator"],
) -> int:
    """
    A calculator to perform basic arithmetic operations.

    Args:
        a (int): The first operand.
        b (int): The second operand.
        operator (Annotated[Operator, "operator"]): The operation to perform.
            Must be one of "+", "-", "*", or "/".

    Returns:
        int: The result of the arithmetic operation.

    Examples:
        ```python
        result = calculator(a=5, b=3, operator="+")  # result will be 8
        ```

        ```python
        result = calculator(a=10, b=2, operator="/")  # result will be 5
        ```
    """
    if operator == "+":
        return a + b
    elif operator == "-":
        return a - b
    elif operator == "*":
        return a * b
    elif operator == "/":
        return int(a / b)
