import http
from multiprocessing.pool import Thread<PERSON>ool
import threading
from typing import List
from tqdm import tqdm
from server.api.utils.keywords.get_keywords import lemmatization
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.scraping_utils import calculate_suitable_api_key
from tomsze_utils.list_utils import duplicate_list_limited
from nltk.corpus import stopwords

from server.api.utils.synonyms.get_synonyms import (
    thread_get_one_synonyms_with_scraping_ant,
)
from api_keys import dict_account_apikey

stopwords = stopwords.words("english")


def create_db_synonyms_with_nltk_source(
    db_synonyms: PickleDatabaseSplit,
    keywords: List[str],
    nlp_model,
    exclude_stopwords: bool = True,
):
    """
    For a keyword,
        lemmatize it,
        get synonyms from nltk,
        update to db.
    """
    from nltk.corpus import wordnet as wn

    for keyword in tqdm(keywords):
        if exclude_stopwords:
            if keyword in stopwords:
                continue

        # Lemmatize keyword
        keyword = lemmatization(
            nlp_model=nlp_model,
            text=keyword,
        )

        # get synonyms from nltk
        synonyms = wn.synonyms(keyword)
        synonyms_tmp = []
        for list_syns in synonyms:
            for syn in list_syns:
                if not syn in synonyms_tmp:
                    synonyms_tmp.append(syn)
        synonyms = synonyms_tmp.copy()

        # update db
        db_synonyms.update_data_with_key_and_col(
            key=keyword,
            col="nltk",
            data=synonyms,
            do_merge_if_exist=True,
        )


def update_db_synonyms_with_powertheasaurus_api(
    db_synonyms: PickleDatabaseSplit,
    keywords: List[str],
    nlp_model,
    exclude_stopwords: bool = True,
):
    """
    For a keyword,
        lemmatize it,
        get synonyms from powertheasaurus,
        update to db.

    Note: The api has limit usage.
        Exceed the limit the IP will be blocked.
    """
    from server.api.utils.generate_vectors.powerthresaurus import PowerThesaurus

    count = 0
    pt = PowerThesaurus()
    for keyword in tqdm(keywords):
        if exclude_stopwords:
            if keyword in stopwords:
                continue

        # Lemmatize keyword
        keyword = lemmatization(
            nlp_model=nlp_model,
            text=keyword,
        )

        # asf = db_synonyms.query_key_col(key=keyword, col="pt", default=None)
        if (
            db_synonyms.query_key_col(
                key=keyword,
                col="pt",
                default=None,
            )
            is not None
        ):
            continue

        # get synonyms from powertheasaurus
        (
            request_success,
            term_found,
            robot_detected,
            synonyms,
        ) = pt.thesaurus(keyword)

        # if not synonyms:  # remove since we also save empty list
        #     continue

        assert request_success == True
        assert robot_detected == False

        # update db
        db_synonyms.update_data_v2(
            key=keyword,
            col="pt",
            data=synonyms,
            do_merge_if_exist=True,
        )
        count += 1

        # dump
        if count == 10:
            db_synonyms.dump_dirty_parts_to_pickles_thread()
            count = 0


def update_db_synonyms_by_scraping_powertheasaurus(
    db_synonyms: PickleDatabaseSplit,
    lemmatized_keywords: List[str],
    total_request_needed: int,
):
    """
    TODO
    """
    # total_request_needed = len(lemmatized_keywords) // 1000
    # total_request_needed = 50000

    # Get suitable api keys.
    list_api_key = list(dict_account_apikey.values())
    list_api_key_to_use, total_credit, is_credit_enough = calculate_suitable_api_key(
        list_api_key,
        total_request_needed,
    )
    n_api_key_to_use = len(list_api_key_to_use)
    assert n_api_key_to_use > 0

    threads = n_api_key_to_use
    min_dump_counter = n_api_key_to_use  # After n requests, dump results to pickle.

    # Initiate different conn for each api key to avoid Request-sent error.
    list_conn = [
        http.client.HTTPSConnection("api.scrapingant.com")
        for i in range(n_api_key_to_use)
    ]

    # Initiate different lock for each api key to avoid race condition on using api_key.
    list_api_key_lock = [threading.Lock() for i in range(n_api_key_to_use)]

    # Create thread arguments.
    list_api_key_duplicated = duplicate_list_limited(
        list_api_key_to_use,
        total_request_needed,
    )
    list_conn_duplicated = duplicate_list_limited(
        list_conn,
        total_request_needed,
    )
    list_api_key_lock_duplicated = duplicate_list_limited(
        list_api_key_lock,
        total_request_needed,
    )

    list_args = list(
        zip(
            list_api_key_lock_duplicated,
            list_conn_duplicated,
            list_api_key_duplicated,
            lemmatized_keywords,
        )
    )

    # Split [list_args] into sub-list by [min_dump_counter] so we dont need to wait
    # for all requests to be sent then dump data.
    list_list_args_split = [
        list_args[i : i + min_dump_counter]
        for i in range(0, len(list_args), min_dump_counter)
    ]

    for list_args_split in tqdm(list_list_args_split):
        # Start threads.
        list_thread_result = ThreadPool(threads).starmap(
            thread_get_one_synonyms_with_scraping_ant,
            list_args_split,
        )

        for thread_result in list_thread_result:
            word_underscore = thread_result[1]
            list_synonym = thread_result[2]

            # update.
            db_synonyms.update_data_v2(
                key=word_underscore,
                col="pt",
                data=list_synonym,
            )

        # dump
        db_synonyms.dump_dirty_parts_to_pickles_thread()
