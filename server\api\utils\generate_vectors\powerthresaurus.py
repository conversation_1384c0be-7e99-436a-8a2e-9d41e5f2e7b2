#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
# WIP script to integrate powerthesaurus.org with Vim.

# Modified from:
# https://github.com/manu-mannattil/vim-powerthesaurus/blob/master/plugin/powerthesaurus.py

import re
import sys
import time
import requests
from random import uniform

API_URL = r"https://api.powerthesaurus.org"

HEADERS = {
    "content-type": "application/json",
    "Origin": "https://www.powerthesaurus.org",
    "Referer": "https://www.powerthesaurus.org",
    "User-Agent": "Mozilla/5.0 (compatible)",
}

GQL_TERM_QUERY = r"""
query TERM_QUERY($term: String!) {
  term(slugEqual: $term) {
    has_abbreviations
    counters
    name
    slug
    rating_min_good
    id
    isFavorite
    inflected {
      name
      id
      slug
      counters
      __typename
    }
    images
    isBad
    __typename
  }
}"""

GQL_THESAURUS_QUERY = r"""
query THESAURUSES_QUERY($after: String, $first: Int, $before: String, 
                        $last: Int, $termID: ID!, $list: List!, 
                        $sort: ThesaurusSorting!, $tagID: Int, 
                        $posID: Int, $syllables: Int, $type: Type) {
  thesauruses(
    termId: $termID
    sort: $sort
    list: $list
    after: $after
    first: $first
    before: $before
    last: $last
    tagId: $tagID
    partOfSpeechId: $posID
    syllables: $syllables
    type: $type
  ) {
    limit
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
      __typename
    }
    edges {
      node {
        _type
        id
        isPinned
        targetTerm {
          id
          name
          slug
          __typename
        }
        relations
        rating
        vote {
          voteType
          id
          __typename
        }
        votes
        __typename
      }
      __typename
    }
    __typename
  }
}"""

PARTS_OF_SPEECH = [
    "adjective",
    "noun",
    "pronoun",
    "adverb",
    "idiom",
    "verb",
    "interjection",
    "phrase",
    "conjunction",
    "preposition",
    "phrasal verb",
]

PARTS_OF_SPEECH_SHORT = [
    "adj.",
    "n.",
    "pr.",
    "adv.",
    "idi.",
    "v.",
    "int.",
    "phr.",
    "conj.",
    "prep.",
    "phr. v.",
]


def timeit(method):
    def timed(*args, **kw):
        ts = time.time()
        result = method(*args, **kw)
        te = time.time()
        if "log_time" in kw:
            name = kw.get("log_name", method.__name__.upper())
            kw["log_time"][name] = int((te - ts) * 1000)
        else:
            print("%r  %2.2f ms" % (method.__name__, (te - ts) * 1000))
        return result

    return timed


class PowerThesaurus:

    def hi(self):
        print("hi")

    def term_id(self, term):
        request_success = False
        term_found = False

        params = {
            "operationName": "TERM_QUERY",
            "variables": {"term": term},
            "query": GQL_TERM_QUERY,
        }

        r = requests.post(API_URL, json=params, headers=HEADERS)
        term = r.json()["data"]["term"]

        if r.status_code == 200:
            request_success = True

        if term:
            term_found = True
            return request_success, term_found, term["id"]
        else:
            return request_success, term_found, None

    @timeit
    def thesaurus(self, term, limit=999, kind="synonym"):
        request_success = False
        term_found = False
        robot_detected = False
        list_synonym = []
        request_success, term_found, term_id = self.term_id(term)
        random_wait = uniform(10, 15)
        time.sleep(random_wait)

        if not term_id:
            return request_success, term_found, robot_detected, list_synonym

        params = {
            "operationName": "THESAURUSES_QUERY",
            "variables": {
                "termID": term_id,
                "sort": {"field": "RATING", "direction": "DESC"},
                "limit": limit,
                "syllables": None,
                "list": kind.upper(),
                "posID": None,
                "query": None,
                "tagID": None,
                "first": 50,
                "after": "",
            },
            "query": GQL_THESAURUS_QUERY,
        }

        random_wait = uniform(10, 15)
        time.sleep(random_wait)

        r = requests.post(API_URL, json=params, headers=HEADERS)

        if r.status_code != 200:
            request_success = False
            return request_success, term_found, robot_detected, list_synonym

        json_response = r.json()

        if json_response is None:
            request_success = False
            return request_success, term_found, robot_detected, list_synonym

        if "error" in json_response:
            robot_detected = True
            request_success = False
            if "detected" in json_response["error"]:
                print("Robot detected...")
                robot_detected = True
                request_success = False
                return request_success, term_found, robot_detected, list_synonym
            if "forbidden" in json_response["error"]:
                print("Robot detected...")
                robot_detected = True
                request_success = False
                return request_success, term_found, robot_detected, list_synonym
            return request_success, term_found, robot_detected, list_synonym

        if "data" not in json_response:
            request_success = False
            return request_success, term_found, robot_detected, list_synonym

        data = json_response["data"]

        if "thesauruses" not in data:
            request_success = False
            return request_success, term_found, robot_detected, list_synonym

        thesauruses = data["thesauruses"]

        if thesauruses is None:
            request_success = False
            return request_success, term_found, robot_detected, list_synonym

        if "edges" not in thesauruses:
            request_success = False
            return request_success, term_found, robot_detected, list_synonym

        for res in json_response["data"]["thesauruses"]["edges"]:
            # yield {
            #     "name": res["node"]["targetTerm"]["name"],
            #     "pos": res["node"]["relations"]["parts_of_speech"],
            # }
            list_synonym.append(res["node"]["targetTerm"]["name"])

        return request_success, term_found, robot_detected, list_synonym

    # @staticmethod
    # def vim_menu(results, pos_short=True):
    #     items = []

    #     for r in results:
    #         name = r["name"]
    #         pos = r["pos"]

    #         if pos:
    #             if pos_short:
    #                 pos = ", ".join(PARTS_OF_SPEECH_SHORT[_ - 1] for _ in pos)
    #             else:
    #                 pos = ", ".join(PARTS_OF_SPEECH[_ - 1] for _ in pos)
    #             pos = f"[{pos}]"
    #         else:
    #             pos = ""

    #         items.append(f"{{ 'word': {repr(name)}, 'menu': {repr(pos)} }}")

    #     return items


if __name__ == "__main__":
    pt = PowerThesaurus()
    # query = " ".join(sys.argv[1:]).strip()
    # query = re.sub(r"[^\w ]+", "", query)

    query = "good morning"

    request_success, term_found, robot_detected, list_synonym = pt.thesaurus(query)

    print(list_synonym)

    g = 1

"""
Tom:

The use of the api is kind of complicated if no example is given.
First we need to get id of a word (or term) using a request in term_id method.
Then look for synonyms using another request in thesaurus method with term id.

Total takes around 2.4 s

"""
