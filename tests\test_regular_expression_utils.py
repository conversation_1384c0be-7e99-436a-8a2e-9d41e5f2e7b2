import pytest

from tomsze_utils.regular_expression_utils import (
    extract_docstring_descryption,
    extract_tool_call_args_from_completion_message_content,
    extract_tool_name_from_string,
    ToolCallArgs,
)


class TestExtractDocstringDescryption:

    def test_extract_docstring_descryption_removes_sections_correctly(self):
        docstring = """
        This is a sample function.

        Args:
            arg1 (int): The first argument.
            arg2 (str): The second argument.

        Returns:
            bool: The return value.

        Examples:
            >>> result = sample_function(1, "test")
            >>> print(result)
            True
        """
        expected_output = "This is a sample function."
        assert extract_docstring_descryption(docstring) == expected_output

    def test_extract_docstring_descryption_no_sections(self):
        docstring = "This function does something important."
        assert (
            extract_docstring_descryption(docstring) == docstring
        )  # No sections to remove


class TestExtractToolNameFromString:  # Fixed typo in class name

    def test_extract_tool_name_success(self):
        input_text = "Tool to be used: ```calculator``` abcxx"  # Updated to match the example format
        expected_output = "calculator"
        result = extract_tool_name_from_string(input_text)
        assert result == expected_output

    def test_extract_tool_name_no_tool(self):
        input_text = "This string does not contain a tool name."
        expected_output = ""
        result = extract_tool_name_from_string(input_text)
        assert result == expected_output


class TestExtractToolCallArgsFromCompletionMessage:

    def test_extract_tool_call_arguments_success(self):
        message_content_str = 'tool_call (write_text_to_file) with arguments {"file_path": "playground/test_util.py", "text": "import unittest\\nfrom utils import add, multiply\\n\\nclass TestUtils(unittest.TestCase):\\n    def test_add(self):\\n        self.assertEqual(add(1, 2), 3)\\n        self.assertEqual(add(-1, 1), 0)\\n\\n    def test_multiply(self):\\n        self.assertEqual(multiply(2, 3), 6)\\n        self.assertEqual(multiply(-1, 1), -1)\\n\\nif __name__ == \'__main__\':\\n    unittest.main()"}'
        expected_output = toolCallArgs(
            name="write_text_to_file",
            arguments={
                "file_path": "playground/test_util.py",
                "text": "import unittest\nfrom utils import add, multiply\n\nclass TestUtils(unittest.TestCase):\n    def test_add(self):\n        self.assertEqual(add(1, 2), 3)\n        self.assertEqual(add(-1, 1), 0)\n\n    def test_multiply(self):\n        self.assertEqual(multiply(2, 3), 6)\n        self.assertEqual(multiply(-1, 1), -1)\n\nif __name__ == '__main__':\n    unittest.main()",
            },
        )
        result = extract_tool_call_args_from_completion_message_content(
            message_content_str
        )
        assert result == expected_output

    def test_extract_tool_call_arguments_no_tool_name(self):
        message_content_str = """
        tool_call with arguments {"name": "calculator", "arguments": {"a": 2, "b": 2, "operator": "+"}}
        """
        expected_output = toolCallArgs(
            name="",
            arguments={
                "name": "calculator",
                "arguments": {
                    "a": 2,
                    "b": 2,
                    "operator": "+",
                },
            },
        )
        result = extract_tool_call_args_from_completion_message_content(
            message_content_str
        )
        assert result == expected_output
