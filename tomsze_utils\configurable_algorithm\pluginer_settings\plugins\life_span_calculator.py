"""App plugin"""

from datetime import datetime
from dataclasses import dataclass
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginLifeSpanCalculator:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        birth_year = parse_data_and_store(
            logger,
            "birth_year",
            data_obj,
            step_config,
            config,
            type="int",
            default=0,
        )

        birth_month = parse_data_and_store(
            logger,
            "birth_month",
            data_obj,
            step_config,
            config,
            type="int",
            default=0,
        )

        birth_day = parse_data_and_store(
            logger,
            "birth_day",
            data_obj,
            step_config,
            config,
            type="int",
            default=0,
        )

        live_to_age = parse_data_and_store(
            logger,
            "live_to_age",
            data_obj,
            step_config,
            config,
            type="int",
            default=0,
        )

        life_span_days_buffer_to = parse_data_and_store(
            logger,
            "life_span_days_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
            default=0,
        )

        life_span_days = (
            datetime(
                year=birth_year + live_to_age,
                month=birth_month,
                day=birth_day,
            )
            - datetime.now()
        )
        life_span_days = int(life_span_days.total_seconds() / (24 * 60 * 60))
        data_obj.dict_var[f"{current_step}.{life_span_days_buffer_to}"] = life_span_days

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
