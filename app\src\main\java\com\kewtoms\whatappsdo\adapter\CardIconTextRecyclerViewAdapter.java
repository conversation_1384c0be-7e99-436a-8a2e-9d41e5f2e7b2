package com.kewtoms.whatappsdo.adapter;

import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.kewtoms.whatappsdo.R;

public class CardIconTextRecyclerViewAdapter
 extends RecyclerView.Adapter<CardIconTextRecyclerViewAdapter.ViewHolder> {

 private final String[] captions;
 private final String[] imagePath;
 private Listener listener;

 ;

 public CardIconTextRecyclerViewAdapter(
  String[] captions,
  String[] imagePath) {
  this.captions = captions;
  this.imagePath = imagePath;
 }

 @Override
 public int getItemCount() {
  return captions.length;
 }

 public void setListener(Listener listener) {
  this.listener = listener;
 }

 @NonNull
 @Override
 public ViewHolder onCreateViewHolder(
  @NonNull ViewGroup parent,
  int viewType) {
  // Use what layout.
  CardView cv =
   (CardView) LayoutInflater.from(parent.getContext()).inflate(
    R.layout.card_icon_text,
    parent,
    false
   );
  return new ViewHolder(cv);
 }

 @Override
 public void onBindViewHolder(
  @NonNull ViewHolder holder,
  int position) {
  int currentPosition = holder.getAdapterPosition();

  // Bind data to ViewHolder.
  CardView cardView = holder.cardView;
  ImageView imageView = cardView.findViewById(R.id.id_app_icon);
  Drawable drawable =
   Drawable.createFromPath(imagePath[currentPosition]);

  imageView.setImageDrawable(drawable);
  imageView.setContentDescription(captions[currentPosition]);
  TextView textView = cardView.findViewById(R.id.id_app_package_name);
  textView.setText(captions[currentPosition]);

  cardView.setOnClickListener(new View.OnClickListener() {
   @Override
   public void onClick(View v) {
    if (listener != null) {
     listener.onClick(currentPosition);
    }
   }
  });
 }

 public interface Listener {
  void onClick(int position);
 }

 public static class ViewHolder
  extends RecyclerView.ViewHolder {
  // Define the view to be used for each data item.
  private final CardView cardView;

  public ViewHolder(CardView v) {
   super(v);
   cardView = v;
  }
 }
}
