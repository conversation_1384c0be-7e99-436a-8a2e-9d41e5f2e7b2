name: D:\code\my_packages\tomsze_utils\env_proj
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
  - https://repo.anaconda.com/pkgs/msys2
dependencies:
  - bzip2=1.0.8=h2bbff1b_6
  - ca-certificates=2025.2.25=haa95532_0
  - libffi=3.4.4=hd77b12b_1
  - openssl=1.1.1w=h2bbff1b_0
  - pip=25.1=pyhc872135_2
  - python=3.10.0=h96c0403_3
  - sqlite=3.45.3=h2bbff1b_0
  - tk=8.6.14=h0416ee5_0
  - tzdata=2025b=h04d1e81_0
  - vc=14.42=haa95532_5
  - vs2015_runtime=14.42.34433=hbfb602d_5
  - wheel=0.45.1=py310haa95532_0
  - xz=5.6.4=h4754444_1
  - zlib=1.2.13=h8cc25b3_1
  - pip:
      - ag2==0.9.1.post0
      - annotated-types==0.7.0
      - anyio==4.9.0
      - arrow==1.3.0
      - astunparse==1.6.3
      - asyncer==0.0.8
      - autogen==0.9.1.post0
      - bcrypt==4.3.0
      - beautifulsoup4==4.12.3
      - binaryornot==0.4.4
      - blis==1.2.0
      - catalogue==2.0.10
      - certifi==2025.4.26
      - cffi==1.17.1
      - chardet==3.0.4
      - charset-normalizer==3.4.2
      - click==8.1.8
      - cloudpathlib==0.21.1
      - colorama==0.4.6
      - colorlog==6.9.0
      - confection==0.1.5
      - cookiecutter==2.6.0
      - cryptography==45.0.2
      - cymem==2.0.11
      - diskcache==5.6.3
      - docker==7.1.0
      - en-core-web-sm==3.8.0
      - events==0.5
      - exceptiongroup==1.3.0
      - fake-ssh==0.1.0a5
      - fastapi==0.115.12
      - filelock==3.13.1
      - flet==0.23.2
      - flet-core==0.23.2
      - flet-runtime==0.23.2
      - freezegun==1.5.1
      - fsspec==2024.6.1
      - google-play-scraper==1.2.7
      - google-re2==1.1
      - googletrans==4.0.0rc1
      - h11==0.9.0
      - h2==3.2.0
      - hpack==3.0.0
      - hstspreload==2025.1.1
      - httpcore==0.9.1
      - httptools==0.6.4
      - httpx==0.13.3
      - hyperframe==5.2.0
      - idna==2.10
      - inflect==7.5.0
      - iniconfig==2.1.0
      - jellyfish==1.2.0
      - jinja2==3.1.6
      - langcodes==3.5.0
      - language-data==1.3.0
      - line-profiler==4.2.0
      - logbook==1.8.1
      - marisa-trie==1.2.1
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - mdurl==0.1.2
      - more-itertools==10.7.0
      - mpmath==1.3.0
      - murmurhash==1.0.13
      - networkx==3.4.2
      - numpy==1.26.4
      - oauthlib==3.2.2
      - opencv-python==4.10.0.84
      - packaging==23.2
      - paramiko==2.12.0
      - path==17.1.0
      - path-py==12.5.0
      - pillow==10.4.0
      - platformdirs==4.3.8
      - pluggy==1.6.0
      - portalocker==2.10.1
      - preshed==3.0.9
      - prettytable==3.10.2
      - psutil==7.0.0
      - py-cpuinfo==9.0.0
      - pycparser==2.22
      - pydantic==2.11.5
      - pydantic-core==2.33.2
      - pygments==2.19.1
      - pyjson5==1.6.6
      - pyjwt==2.10.1
      - pymongo==3.10.0
      - pynacl==1.5.0
      - pypng==0.20220715.0
      - pytest==8.3.5
      - pytest-asyncio==1.0.0
      - python-dateutil==2.9.0
      - python-dotenv==1.0.1
      - python-slugify==8.0.4
      - pywin32==310
      - pyyaml==6.0.2
      - qrcode==7.4.2
      - regex==2024.11.6
      - repath==0.9.0
      - requests==2.32.3
      - rfc3986==1.5.0
      - rich==14.0.0
      - segtok==1.5.11
      - semver==3.0.4
      - setuptools==72.1.0
      - shellingham==1.5.4
      - six==1.17.0
      - smart-open==7.1.0
      - sniffio==1.3.1
      - soupsieve==2.7
      - spacy==3.8.7
      - spacy-legacy==3.0.12
      - spacy-loggers==1.0.5
      - srsly==2.5.1
      - starlette==0.46.2
      - sympy==1.13.3
      - tabulate==0.9.0
      - termcolor==3.1.0
      - text-unidecode==1.3
      - thinc==8.3.4
      - tiktoken==0.9.0
      - toml==0.10.2
      - tomli==2.2.1
      - torch==2.1.0+cu121
      - torchaudio==2.1.0+cu121
      - torchvision==0.16.0+cu121
      - tqdm==4.66.1
      - typeguard==4.4.2
      - typer==0.15.4
      - types-python-dateutil==2.9.0.20250516
      - typing-extensions==4.13.2
      - typing-inspection==0.4.1
      - urllib3==2.4.0
      - uvicorn==0.34.2
      - wasabi==1.1.3
      - watchdog==4.0.0
      - watchfiles==1.0.5
      - wcwidth==0.2.13
      - weasel==0.4.1
      - webcolors==24.11.1
      - websockets==15.0.1
      - wrapt==1.17.2
      - xmltodict==0.14.2
      - yake==0.4.8
prefix: D:\code\my_packages\tomsze_utils\env_proj
