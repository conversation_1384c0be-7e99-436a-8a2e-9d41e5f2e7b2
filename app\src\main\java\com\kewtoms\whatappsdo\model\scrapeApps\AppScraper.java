package com.kewtoms.whatappsdo.model.scrapeApps;

import static com.kewtoms.whatappsdo.utils.RequestUtils.sendPostJsonVolleyFuture;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import com.google.gson.Gson;
import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScanAppData;
import com.kewtoms.whatappsdo.data.ScrapeData;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;
import com.kewtoms.whatappsdo.utils.CodeRunManager;
import com.kewtoms.whatappsdo.utils.RequestUtils;
import com.kewtoms.whatappsdo.utils.ScrapeApkgkUtils;
import com.kewtoms.whatappsdo.utils.ScrapeApksosUtils;
import com.kewtoms.whatappsdo.utils.ScrapeApksupportUtils;
import com.kewtoms.whatappsdo.utils.ScrapePlayStoreUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Reader;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class AppScraper {

  public static final String TAG = "APP:AppScraper";
  private final List<String> appsNeedScrapeList = new ArrayList<>();

  /**
   * Check with server for apps that need scraping.
   * <p>
   * Note: This method needs to work a server
   */
  public List<String> checkAppNeedScrape(
    Context context,
    String url,
    ScanAppData scanAppData) {

    String methodName = "checkAppNeedScrape";

    if (!CodeRunManager.getInstance()
      .isEnabled(AppScraper.class.getSimpleName() + "." + methodName)) {
      Log.d(
        TAG,
        methodName + ": disabled"
      );
      return new ArrayList<>();
    }

    Log.d(
      TAG,
      methodName + ": run"
    );

    // Send post request (client apps) to server.
    JSONObject jsonBody = new JSONObject();
    JSONObject dataJSONObject = new JSONObject();
    try {
      dataJSONObject.put(
        "appIds",
        new JSONArray(scanAppData.getPackageNames())
      );
    } catch (JSONException e) {
      Toast toast = Toast.makeText(
        context,
        e.toString(),
        Toast.LENGTH_LONG
      );
      toast.show();
    }

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key; // use key in strings.xml
    try {
      jsonBody.put(
        requestEncryptedJsonKeyStr,
        dataJSONObject.toString()
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }

    try {
      Log.i(
        TAG,
        methodName + ": " + "Sending request to:" + url
      );
      RequestUtils.sendPostEncryptedJsonVolley(
        context,
        url,
        jsonBody,
        -1,
        false,
        new PostResponseCallback() {
          @Override
          public void onPostSuccess(JSONObject response) {

            try {
              Log.i(
                TAG,
                methodName + ": " + "Server check client app success"
              );

              if (response.has(Constants.response_json_error_key)) {
                Toast toast = Toast.makeText(
                  context,
                  response.toString(),
                  Toast.LENGTH_SHORT
                );
                toast.show();
              }

              // Extract result from response using updated API parsing
              JSONObject responseData = RequestUtils.getApiResponseDataFromResponseJSONObject(
                context, response);
              JSONArray packageNamesjsonArray = responseData.getJSONArray("packageNames");

              Log.i(
                TAG,
                methodName + ": done extracting result"
              );

              //              // Loop the list in JSONObject
              //              appsNeedScrapeList.clear();
              //              for (int i = 0; i < jsonArray.length(); i++) {
              //                try {
              //                  String packageName = jsonArray.getString(i);
              //                  appsNeedScrapeList.add(packageName);
              //                } catch (JSONException e) {
              //                  throw new RuntimeException(e);
              //                }
              //              }

            } catch (RuntimeException e) {
              Log.e(
                TAG,
                methodName + ": " + "RuntimeException: " + e.toString()
              );
              throw new RuntimeException(e);
            } catch (JSONException e) {
              throw new RuntimeException(e);
            }

            Log.d(
              TAG,
              methodName + ": done"
            );

          }

          @Override
          public void onPostError(String errorMessage) {
            Log.i(
              TAG,
              methodName + ": " + "Server check client app failed " + "with error message: " + errorMessage
            );
            Toast toast = Toast.makeText(
              context,
              errorMessage,
              Toast.LENGTH_SHORT
            );
            toast.show();
          }


        }
      );
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }

    Log.i(
      TAG,
      methodName + ": done sending post request"
    );


    Log.d(
      TAG,
      methodName + ": done"
    );

    return appsNeedScrapeList;

  }

  public List<String> getAppsNeedScrapeList() {
    return appsNeedScrapeList;
  }

  public Callable<ScrapeData> createScrapeGooglePlayStoreCallable(String packageName) {
    return new Callable<ScrapeData>() {
      @Override
      public ScrapeData call() {
        String appName = "";
        List<HashMap<String, Object>> categories = new ArrayList<>();
        String description = "";
        String summary = "";
        String genre = "";
        String genreId = "";
        String source = "playstore";

        ScrapePlayStoreUtils scrapePlayStoreUtils =
          new ScrapePlayStoreUtils();


        String url = buildPlayStoreUrl(packageName);
        try {
          Document doc = Jsoup.connect(url).get();
          Element appNameElement =
            doc.select(scrapePlayStoreUtils.appNameSectorStr).first();
          if (appNameElement != null) {
            appName = appNameElement.text();
          }
          HashMap<String, Object> dataMap =
            scrapePlayStoreUtils.getAppInfoFromAFInitData(doc);
          description = (String) dataMap.get("description");
          genre = (String) dataMap.get("genre");
          genreId = (String) dataMap.get("genreId");
          summary = (String) dataMap.get("summary");
          categories =
            (List<HashMap<String, Object>>) dataMap.get("categories");

          int g = 1;

        } catch (IOException e) {
          return new ScrapeData(
            packageName,
            appName,
            genre,
            genreId,
            categories,
            description,
            summary,
            source,
            false,
            e.toString()
          );
        }

        return new ScrapeData(
          packageName,
          appName,
          genre,
          genreId,
          categories,
          description,
          summary,
          source,
          true,
          ""
        );
      }

    };
  }


  private String buildPlayStoreUrl(String packageName) {
    return "https://play.google.com/store/apps/details?id=" + packageName;
  }

  public List<ScrapeData> scrapeGooglePlay(
    List<String> packageNames) {


    String methodName = "scrapeGooglePlay";
    Log.d(
      TAG,
      methodName + ": run"
    );

    List<ScrapeData> listScrapeData = new ArrayList<>();

    if (packageNames.isEmpty()) {
      return listScrapeData;
    }

    // Create executorService to scrape apps.
    ExecutorService executorService =
      Executors.newFixedThreadPool(packageNames.size());

    // Create callables to scrape apps.
    Collection<Callable<ScrapeData>> callables = new ArrayList<>();
    for (String packageName : packageNames) {
      Callable<ScrapeData> callable =
        createScrapeGooglePlayStoreCallable(packageName);
      callables.add(callable);
    }

    // Invoke callables by executorService and wait for result.
    try {
      List<Future<ScrapeData>> taskListFuture =
        executorService.invokeAll(callables);

      for (Future<ScrapeData> future : taskListFuture) {
        listScrapeData.add(future.get());
      }
    } catch (ExecutionException | InterruptedException e) {
      Log.e(
        TAG,
        methodName + ": " + "RuntimeException: " + e
      );
      throw new RuntimeException(e);
    }

    Log.d(
      TAG,
      methodName + ": done"
    );

    return listScrapeData;
  }

  public List<ScrapeData> scrapeApkgk(
    List<String> packageNames) {


    String methodName = "scrapeApkgk";
    Log.d(
      TAG,
      methodName + ": start"
    );

    List<ScrapeData> listScrapeData = new ArrayList<>();

    if (packageNames.isEmpty()) {
      return listScrapeData;
    }
    ExecutorService executorService =
      Executors.newFixedThreadPool(packageNames.size());

    // Create callables to scrape apps.
    Collection<Callable<ScrapeData>> callables = new ArrayList<>();
    for (String packageName : packageNames) {
      Callable<ScrapeData> callable =
        ScrapeApkgkUtils.createScrapeApkgkCallable(packageName);
      callables.add(callable);
    }

    // Invoke callables by executorService and wait for result.
    try {
      List<Future<ScrapeData>> taskListFuture =
        executorService.invokeAll(callables);

      for (Future<ScrapeData> future : taskListFuture) {
        listScrapeData.add(future.get());
      }
    } catch (ExecutionException | InterruptedException e) {
      throw new RuntimeException(e);
    }

    Log.d(
      TAG,
      methodName + ": done"
    );

    return listScrapeData;
  }

  public List<ScrapeData> scrapeApksos(
    List<String> packageNames) {

    String methodName = "scrapeApksos";
    Log.d(
      TAG,
      methodName + ": start"
    );

    List<ScrapeData> listScrapeData = new ArrayList<>();

    if (packageNames.isEmpty()) {
      return listScrapeData;
    }
    ExecutorService executorService =
      Executors.newFixedThreadPool(packageNames.size());

    // Create callables to scrape apps.
    Collection<Callable<ScrapeData>> callables = new ArrayList<>();
    for (String packageName : packageNames) {
      Callable<ScrapeData> callable =
        ScrapeApksosUtils.createScrapeApksosCallable(packageName);
      callables.add(callable);
    }

    // Invoke callables by executorService and wait for result.
    try {
      List<Future<ScrapeData>> taskListFuture =
        executorService.invokeAll(callables);

      for (Future<ScrapeData> future : taskListFuture) {
        listScrapeData.add(future.get());
      }
    } catch (ExecutionException | InterruptedException e) {
      throw new RuntimeException(e);
    }

    Log.d(
      TAG,
      methodName + ": done"
    );

    return listScrapeData;
  }

  public List<ScrapeData> scrapeApksupport(
    List<String> packageNames) {

    String methodName = "scrapeApksupport";
    Log.d(
      TAG,
      methodName + ": start"
    );

    List<ScrapeData> listScrapeData = new ArrayList<>();

    if (packageNames.isEmpty()) {
      return listScrapeData;
    }
    ExecutorService executorService =
      Executors.newFixedThreadPool(packageNames.size());

    // Create callables to scrape apps.
    Collection<Callable<ScrapeData>> callables = new ArrayList<>();
    for (String packageName : packageNames) {
      Callable<ScrapeData> callable =
        ScrapeApksupportUtils.createScrapeApksupportCallable(packageName);
      callables.add(callable);
    }

    // Invoke callables by executorService and wait for result.
    try {
      List<Future<ScrapeData>> taskListFuture =
        executorService.invokeAll(callables);

      for (Future<ScrapeData> future : taskListFuture) {
        listScrapeData.add(future.get());
      }
    } catch (ExecutionException | InterruptedException e) {
      throw new RuntimeException(e);
    }

    Log.d(
      TAG,
      methodName + ": done"
    );

    return listScrapeData;
  }

  /**
   * Scrape only when json file of those package name not existed.
   *
   * @Return The list of app scraped while the server has not.
   */
  public List<ScrapeData> scrapeWhenNeeded(
    String scrapeCacheDirPath,
    List<String> appsNeedScrapeListStr) {

    String methodName = "scrapeWhenNeeded";
    if (!CodeRunManager.getInstance()
      .isEnabled(AppScraper.class.getSimpleName() + "." + methodName)) {
      Log.d(
        TAG,
        methodName + ": disabled"
      );
      return new ArrayList<>();
    }
    Log.d(
      TAG,
      methodName + ": run"
    );

    List<String> returnPackageNamelist = new ArrayList<>();

    List<String> packageNameNotCached = new ArrayList<>();
    for (String packageName : appsNeedScrapeListStr) {
      File jsonFile = buildScrapeCacheJsonFile(
        scrapeCacheDirPath,
        packageName
      );
      if (!jsonFile.exists()) {
        packageNameNotCached.add(packageName);
      }
    }

    // Scrape (only if and not cached).
    List<ScrapeData> listScrapeData = new ArrayList<>();
    try {
      listScrapeData = scrapeGooglePlay(packageNameNotCached);
    } catch (Exception e) {
      Log.e(
        TAG,
        methodName + ": scrapeGooglePlay error",
        e
      );
      throw new RuntimeException(e);
    }

    // Cache (only if and not cached).
    for (ScrapeData appsNeedScrape : listScrapeData) {
      String packageName = appsNeedScrape.getPackageName();
      String jsonPath = buildScrapeCacheJsonFile(
        scrapeCacheDirPath,
        packageName
      ).toString();

      if (new File(jsonPath).exists()) {
        continue;
      }

      ScrapeData scrapeData = new ScrapeData(
        appsNeedScrape.getPackageName(),
        appsNeedScrape.getAppName(),
        appsNeedScrape.getGenre(),
        appsNeedScrape.getGenreId(),
        appsNeedScrape.getCategories(),
        appsNeedScrape.getDescription(),
        appsNeedScrape.getSummary(),
        appsNeedScrape.getSource(),
        appsNeedScrape.getSuccess(),
        appsNeedScrape.getException()
      );
      Gson gson = new Gson();
      try (Writer writer = new FileWriter(jsonPath)) {
        gson.toJson(
          scrapeData,
          writer
        );
      } catch (IOException e) {
        Log.e(
          TAG,
          "scrapeWhenNeeded: " + e.toString()
        );
      }

      returnPackageNamelist.add(packageName);
    }

    // Merge cache result to return.
    for (String packageName : appsNeedScrapeListStr) {
      File jsonFile = buildScrapeCacheJsonFile(
        scrapeCacheDirPath,
        packageName
      );
      if (jsonFile.exists()) {
        // Read json file.
        Gson gson = new Gson();

        try (Reader reader = new FileReader(jsonFile)) {
          ScrapeData scrapeData = gson.fromJson(
            reader,
            ScrapeData.class
          );

          if (!returnPackageNamelist.contains(packageName)) {
            listScrapeData.add(scrapeData);
          }

        } catch (IOException e) {
          Log.e(
            TAG,
            "scrapeWhenNeeded: " + e.toString()
          );
        }

      }
    }

    Log.d(
      TAG,
      methodName + ": done"
    );

    return listScrapeData;

  }

  private File buildScrapeCacheJsonFile(
    String scrapeCacheDirPath,
    String packageName) {

    Log.d(
      TAG,
      "buildScrapeCacheJsonFile: run"
    );
    Log.d(
      TAG,
      "buildScrapeCacheJsonFile: done"
    );

    return new File(
      scrapeCacheDirPath,
      packageName + ".json"
    );
  }


  public void sendScrapeData(
    Context context,
    String url,
    List<ScrapeData> listScrapeData) {

    String methodName = "sendScrapeData";
    if (!CodeRunManager.getInstance()
      .isEnabled(AppScraper.class.getSimpleName() + "." + methodName)) {
      Log.d(
        TAG,
        methodName + ": disabled"
      );
      return;
    }

    Log.d(
      TAG,
      "sendScrapeData: run"
    );

    if (listScrapeData.isEmpty()) {
      Log.i(
        TAG,
        "sendScrapeData: scrape data list is empty, skip send scrape data"
      );

      Log.d(
        TAG,
        "sendScrapeData: done"
      );
      return;
    }

    // Send post request (client apps) to server.
    List<String> packageNamesListStr = new ArrayList<>();
    List<JSONObject> scrapeResultList = new ArrayList<>();
    for (ScrapeData scrapeData : listScrapeData) {
      packageNamesListStr.add(scrapeData.getPackageName());

      JSONObject scrapeResultJsonObject = new JSONObject();
      try {
        scrapeResultJsonObject.put(
          "title",
          scrapeData.getAppName()
        );
        scrapeResultJsonObject.put(
          "description",
          scrapeData.getDescription()
        );
        scrapeResultJsonObject.put(
          "summary",
          scrapeData.getSummary()
        );
        scrapeResultJsonObject.put(
          "genre",
          scrapeData.getGenre()
        );
        scrapeResultJsonObject.put(
          "genreId",
          scrapeData.getGenreId()
        );
        scrapeResultJsonObject.put(
          "exception",
          scrapeData.getException()
        );
        scrapeResultJsonObject.put(
          "categories",
          new JSONArray(scrapeData.getCategories())
        );
      } catch (JSONException e) {
        throw new RuntimeException(e);
      }
      scrapeResultList.add(scrapeResultJsonObject);
    }

    JSONObject jsonBodyJSONObject = new JSONObject();
    try {
      jsonBodyJSONObject.put(
        "appIds",
        new JSONArray(packageNamesListStr)
      );
      jsonBodyJSONObject.put(
        "scrape_results",
        new JSONArray(scrapeResultList)
      );
    } catch (JSONException e) {
      throw new RuntimeException(e);
    }
    JSONObject responseJsonObj = sendPostJsonVolleyFuture(
      context,
      url,
      jsonBodyJSONObject,
      true
    );

    Log.d(
      TAG,
      "sendScrapeData: done"
    );
  }
}
