{"general": {"init_steps": [], "steps": ["step_waiter"], "variables": {"var_online": false}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "folder_path": "./tests/tmp_CA_logs", "log_to_file": true, "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_steps": [{"step_name": "step_waiter", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "use": true, "wait_time": 0.5}]}