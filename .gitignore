__pycache__/
.pytest_cache/
tmp/

# log related
logs/
tmp_CA_logs/
server_log/
tests/test_api_function_log/

.env
.env_github_repo
.env_prod
.env_security
.env_backup_secrets
.env_email
date_strings.json
.secrets
ScrapingAnt_api_key.txt
*.pickle
*.bkp

api_key.py
server/tools/test_fill_new_apps_db_backup_zip_folder/*.zip
server/tools/*/*.zip

lock_file
update_ver.txt

# deploy related
deploy.zip

# Not ignore
!server/api/pickle_db/android/db_app_small/apps_00000.pickle
!server/api/pickle_db/android/db_app_small_back/apps_00000.pickle