@startuml User Sign up
' using the teoz rendering engine
!pragma teoz true

' client side
box "client side"
    participant "user" as user
    ' client side app
    box "App" #LightBlue
        participant "email\n textfield" as email_textfield
        participant "password\n textfield" as password_textfield
        participant "sign\n in\n button" as sign_in_button
        participant "request\n resource\n button" as request_resource_button
    end box
end box

' server side
box "server side"
    ' fastapi
    box "fastapi" #LightBlue
        participant "sign\n in\n endpoint" as sign_in_endpoint
        participant "resource\n endpoint" as resource_endpoint
    end box
end box


user -> email_textfield: Enter email
user -> password_textfield: Enter password
user -> sign_in_button: Click
sign_in_button -> sign_in_endpoint: POST /sign_in
note left
	sign in data
	    email address
	    hashed password
end note

sign_in_endpoint -> sign_in_endpoint: Process sign in data and create jwt
alt email not signed up
    sign_in_endpoint -> user: Send error
else email not verified
    sign_in_endpoint -> user: Send error
else
    sign_in_endpoint -> user: Send jwt
end
user <[#red]- user: Save jwt
request_resource_button -> resource_endpoint: Post /get_resource_x
note left
	fake request resource data
	    access token
	    resource item
end note
resource_endpoint -> resource_endpoint: Verify jwt
alt jwt invalid
    resource_endpoint -> user: Send error
else
    resource_endpoint -> user: Send resource
end


@enduml