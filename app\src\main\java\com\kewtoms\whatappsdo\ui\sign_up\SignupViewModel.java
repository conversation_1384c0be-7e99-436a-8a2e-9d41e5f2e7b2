package com.kewtoms.whatappsdo.ui.sign_up;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;

public class SignupViewModel
  extends ViewModel {

  private final MutableLiveData<String> mEmailAddress;
  private final MutableLiveData<String> mPassword;
  private final MutableLiveData<String> mConfirmPassword;
  private final MutableLiveData<String> mVerificationCode;
  private final MutableLiveData<String> mStatus;
  private Configuration config = Configuration.getInstance();

  public SignupViewModel() {
    mEmailAddress = new MutableLiveData<>();
    mPassword = new MutableLiveData<>();
    mConfirmPassword = new MutableLiveData<>();
    mVerificationCode = new MutableLiveData<>();
    mStatus = new MutableLiveData<>();

    if (config.isTest()) {
      mEmailAddress.setValue(Constants.test_email_address);
      mPassword.setValue(Constants.test_password);
      mConfirmPassword.setValue(Constants.test_confirm_password);
      mVerificationCode.setValue(Constants.test_verification_code);
      mStatus.setValue("");
    }
  }

  public LiveData<String> getEmailAddress() {
    return mEmailAddress;
  }

  public void setEmailAddress(String emailAddress) {
    mEmailAddress.postValue(emailAddress);
  }

  public LiveData<String> getPassword() {
    return mPassword;
  }

  public void setPassword(String password) {
    mPassword.postValue(password);
  }

  public LiveData<String> getConfirmPassword() {
    return mConfirmPassword;
  }

  public LiveData<String> getVerificationCode() {
    return mVerificationCode;
  }

  public void setVerificationCode(String verificationCode) {
    mVerificationCode.postValue(verificationCode);
  }

  public void setConfirmPassword(String confirmPassword) {
    mConfirmPassword.postValue(confirmPassword);
  }

  public LiveData<String> getStatus() {
    return mStatus;
  }

  public void setStatus(String status) {
    mStatus.postValue(status);
  }
}