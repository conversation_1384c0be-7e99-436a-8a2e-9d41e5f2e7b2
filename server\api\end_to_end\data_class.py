from dataclasses import dataclass
from typing import Dict, List
from pydantic import BaseModel


# ----- Client side -----


class Data(BaseModel):
    """
    A Pydantic model representing the data structure for incoming requests.

    Attributes:
        data_str (str): A string containing the data to be processed.
                         Defaults to an empty string.
    """

    data_str: str = ""


class SignupData(BaseModel):
    email_address: str = ""
    password: str = ""
    verification_code: str = ""


class SignInData(BaseModel):
    email_address: str = ""
    password: str = ""


class SignInAccessTokenData(BaseModel):
    access_token: str = ""


class SendVerificationEmailData(BaseModel):
    email_address: str = ""


class FakeRequestData(BaseModel):
    access_token: str = ""
    resource_item: str = ""


class RemoveUserData(BaseModel):
    email_address: str = ""


class IsUserSignedUpData(BaseModel):
    email_address: str = ""


class ValidateUserAccessTokenData(BaseModel):
    access_token: str = ""


class ObtainNewAccessTokenByRefreshTokenData(BaseModel):
    email: str = ""
    refresh_token: str = ""


class ObtainVerificationCodeData(BaseModel):
    email: str = ""


class AccessTokeData(BaseModel):
    email_address: str
    right: str
    time_limit: int = 15  # in minutes


class CodeVerifyData(BaseModel):
    email_address: str = ""
    verification_code: str = ""
    hashed_password: str = ""


class TestBody(BaseModel):
    test: str = ""
    testBool: bool = False
    testListStr: List[str] = []


class ClientSearchData(BaseModel):
    allowSearch: bool = False
    appIds: List[str] = []
    downloadSource: List[str] = []
    query: str = ""


class ClientApps(BaseModel):
    """
    A pydantic BaseModel.

    ```
    allowSearch: bool = False
    appIds: List[str] = []
    downloadSource: List[str] = []
    ```

    Use by:
    ```
    client_apps = ClientApps()
    client_apps.allowSearch = True
    client_apps.appIds.append("com.miniclip.eightballpool")
    client_apps.appIds.append("com.miniclip.footballstrike")
    ```
    """

    allowSearch: bool = False
    appIds: List[str] = []
    downloadSource: List[str] = []


class ClientAppsToUpdate(BaseModel):
    """
    A pydantic BaseModel.

    ```
    appIds: List[str] = []
    scrape_results: List[Dict] = []
    ```

    Use by:
    ```
    client_apps = ClientAppsToUpdate()
    client_apps.appIds.append("com.miniclip.eightballpool")

    scrape_success, result = scrap_play_store(
        device_app_id=test_app_id,
        lang="en",
    )
    assert scrape_success == True
    client_apps_to_update.scrape_results.append(result)

    ```
    """

    appIds: List[str] = []
    scrape_results: List[Dict] = []


client_apps = {
    "appIds": [],
    "downloadSource": [],
}


# ----- Server side -----
class ServerApps(BaseModel):
    appIds: Dict[str, int] = {}


@dataclass
class VerifiedAppResult:
    new_apps: Dict
    is_new: bool


class ReceiveUserInstalledAppsResponse(BaseModel):
    new_apps: Dict = {}
    is_new: bool = False
