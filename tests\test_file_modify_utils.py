import os
import tempfile

import pytest

from tomsze_utils.file_modify_utils import (
    copy_file,
    move_file,
    open_file_folder_or_exe_with_default_software,
    read_file_to_string,
    remove_file,
    rename_file,
)


class TestRenameFile:

    def test_rename_file_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            old_file_path = os.path.join(temp_dir, "old_file.txt")
            new_file_path = os.path.join(temp_dir, "new_file.txt")
            with open(old_file_path, "w") as f:
                f.write("This is a test file.")

            result = rename_file(old_file_path, new_file_path)

            assert (
                result
                == f"File {old_file_path} renamed to {new_file_path} successfully"
            )
            assert not os.path.exists(old_file_path)
            assert os.path.exists(new_file_path)

    def test_rename_file_not_found(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            old_file_path = os.path.join(temp_dir, "non_existent_file.txt")
            new_file_path = os.path.join(temp_dir, "new_file.txt")

            with pytest.raises(FileNotFoundError):
                rename_file(old_file_path, new_file_path)

    def test_rename_file_exists(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            old_file_path = os.path.join(temp_dir, "old_file.txt")
            new_file_path = os.path.join(temp_dir, "new_file.txt")
            with open(old_file_path, "w") as f:
                f.write("This is a test file.")
            with open(new_file_path, "w") as f:
                f.write("This file already exists.")

            with pytest.raises(FileExistsError):
                rename_file(old_file_path, new_file_path)


class TestMoveFile:

    def test_move_file_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            source_path = os.path.join(temp_dir, "source_file.txt")
            destination_path = os.path.join(temp_dir, "destination_file.txt")
            with open(source_path, "w") as f:
                f.write("This is a test file.")

            result = move_file(source_path, destination_path)

            assert (
                result
                == f"File ({source_path}) moved to ({destination_path}) successfully"
            )
            assert not os.path.exists(source_path)
            assert os.path.exists(destination_path)

    def test_move_file_not_found(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            source_path = os.path.join(temp_dir, "non_existent_file.txt")
            destination_path = os.path.join(temp_dir, "destination_file.txt")

            with pytest.raises(FileNotFoundError):
                move_file(source_path, destination_path)


class TestRemoveFile:

    def test_remove_file_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "file_to_remove.txt")
            with open(file_path, "w") as f:
                f.write("This file will be removed.")

            result = remove_file(file_path)

            assert result == f"File {file_path} removed successfully"
            assert not os.path.exists(file_path)

    def test_remove_file_not_found(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            non_existent_file_path = os.path.join(temp_dir, "non_existent_file.txt")

            with pytest.raises(FileNotFoundError):
                remove_file(non_existent_file_path)


class TestReadFileToString:

    def test_read_file_to_string_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_file.txt")
            expected_content = "Hello, World!"
            with open(file_path, "w") as f:
                f.write(expected_content)

            content = read_file_to_string(file_path)
            assert content == expected_content

    def test_read_file_to_string_not_found(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            non_existent_file_path = os.path.join(temp_dir, "non_existent_file.txt")

            with pytest.raises(FileNotFoundError):
                read_file_to_string(non_existent_file_path)


class TestCopyFile:

    def test_copy_file_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            source_path = os.path.join(temp_dir, "source_file.txt")
            destination_path = os.path.join(temp_dir, "destination_file.txt")
            expected_content = "This is a test file."

            with open(source_path, "w") as f:
                f.write(expected_content)

            result = copy_file(source_path, destination_path)

            assert (
                result
                == f"File copied from {source_path} to {destination_path} successfully"
            )
            with open(destination_path, "r") as f:
                content = f.read()
                assert content == expected_content

    def test_copy_file_not_found(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            source_path = os.path.join(temp_dir, "non_existent_file.txt")
            destination_path = os.path.join(temp_dir, "destination_file.txt")

            with pytest.raises(FileNotFoundError):
                copy_file(source_path, destination_path)


class TestOpenFileFolderOrExeWithDefaultSoftware:

    def test_open_file_with_default_software_success(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_file.txt")
            content = "This is a test file."
            with open(file_path, "w") as f:
                f.write(content)

            # Here we would normally call the function to open the file,
            # but since it opens the file with the default software, we can't assert the outcome.
            # Instead, we can just check if no exceptions are raised.
            try:
                open_file_folder_or_exe_with_default_software(file_path)
            except Exception:
                pytest.fail(
                    "open_file_with_default_software raised an exception unexpectedly!"
                )

    def test_open_file_with_default_software_not_found(self):
        non_existent_file_path = "non_existent_file.txt"
        result = open_file_folder_or_exe_with_default_software(non_existent_file_path)
        assert (
            result
            == f"The specified path does not exist: {os.path.realpath(non_existent_file_path)}"
        )
