from typing import Any, Dict, Optional
from tomsze_utils.api_response_utils_constants import (
    ApiResponseUtilsKeys,
    ApiResponseUtilsMessages,
)


class ApiResponse:

    def __init__(
        self,
        code: int = 200,
        success: bool = True,
        message: str = "",
        data: Optional[Any] = None,
        error: Optional[Any] = None,
    ):
        """
        Initialize an API response.

        Args:
            code (int): HTTP-like status code (defaults to 200).
            success (bool): Boolean indicating the success of the operation.
            message (str): A human-readable message about the result.
            data (Optional[Any]): The payload/data returned on success.
            error (Optional[Any]): Details of the error (if any).

        Examples:
            ```python
            # Example 1: Basic successful response with user data
            ApiResponse(code=200, success=True, message="User fetched", data={"id": 1, "name": "<PERSON> Doe"})
            ```

            ```python
            # Example 2: Error response with detailed error information
            ApiResponse(code=404, success=False, message="User not found", error={"code": "USER_404", "detail": "User does not exist"})
            ```
        """
        self.code = code
        self.success = success
        self.message = message
        self.data = data
        self.error = error

    def to_dict(self) -> Dict[str, Any]:
        """
        Converts the instance to a dictionary, filtering out keys with None values.

        Returns:
            Dict[str, Any]: Dictionary representation of the API response with non-None values only.

        Examples:
            ```python
            # Convert a success response to dictionary
            response = ApiResponse(code=200, success=True, message="Success", data={"key": "value"})
            response.to_dict()
            # Output: {'code': 200, 'success': True, 'message': 'Success', 'data': {'key': 'value'}}
            ```

            ```python
            # Convert an error response to dictionary
            response = ApiResponse(code=500, success=False, message="Internal error", error="Database connection failed")
            response.to_dict()
            # Output: {'code': 500, 'success': False, 'message': 'Internal error', 'error': 'Database connection failed'}
            ```
        """
        response = {
            ApiResponseUtilsKeys.CODE: self.code,
            ApiResponseUtilsKeys.IS_SUCCESS: self.success,
            ApiResponseUtilsKeys.MESSAGE: self.message,
            ApiResponseUtilsKeys.DATA: self.data,
            ApiResponseUtilsKeys.ERROR: self.error,
        }
        # Optionally remove None values for clarity
        return {k: v for k, v in response.items() if v is not None}

    @classmethod
    def success_response(
        cls,
        data: Any,
        message: str = ApiResponseUtilsMessages.OPERATION_SUCCESSFUL,
        code: int = 200,
    ) -> Dict[str, Any]:
        """
        Creates a standardized success response.

        Args:
            data (Any): The payload to return.
            message (str): A message associated with the success.
            code (int): Status code (defaults to 200).

        Returns:
            Dict[str, Any]: A dictionary representing the API response.

        Examples:
            ```python
            # Success response with user data
            ApiResponse.success_response(data={"user_id": 123, "name": "Alice"}, message="User retrieved successfully!")
            # Output: {'code': 200, 'success': True, 'message': 'User retrieved successfully!', 'data': {'user_id': 123, 'name': 'Alice'}}
            ```

            ```python
            # Minimal success response without message override
            ApiResponse.success_response(data={"status": "OK"})
            # Output: {'code': 200, 'success': True, 'message': 'Operation successful', 'data': {'status': 'OK'}}
            ```
        """
        return cls(code=code, success=True, message=message, data=data).to_dict()

    @classmethod
    def error_response(
        cls,
        error: Any,
        message: str = ApiResponseUtilsMessages.OPERATION_FAILED,
        code: int = 400,
    ) -> Dict[str, Any]:
        """
        Creates a standardized error response.

        Args:
            error (Any): Details of the error.
            message (str): A message associated with the error.
            code (int): Status code (defaults to 400).

        Returns:
            Dict[str, Any]: A dictionary representing the API response.

        Examples:
            ```python
            # Error response with custom message and code
            ApiResponse.error_response(error={"code": "NOT_FOUND", "details": "Resource not found"}, message="Item not found", code=404)
            # Output: {'code': 404, 'success': False, 'message': 'Item not found', 'error': {'code': 'NOT_FOUND', 'details': 'Resource not found'}}
            ```

            ```python
            # Simple error response using default message
            ApiResponse.error_response(error="Invalid input")
            # Output: {'code': 400, 'success': False, 'message': 'Operation failed', 'error': 'Invalid input'}
            ```
        """
        return cls(code=code, success=False, message=message, error=error).to_dict()


# Example usage:
if __name__ == "__main__":
    # A successful API call response
    success_result = ApiResponse.success_response(
        data={"user_id": 123, "name": "Alice"}, message="User retrieved successfully!"
    )
    print("Success Response:")
    print(success_result)

    # An error API call response
    error_result = ApiResponse.error_response(
        error={
            "code": "USER_NOT_FOUND",
            "details": "No user exists with the provided id.",
        },
        message="User retrieval failed",
        code=404,
    )
    print("\nError Response:")
    print(error_result)

    """
Success Response:
{'code': 200, 'success': True, 'message': 'User retrieved successfully!', 'data': {'user_id': 123, 'name': 'Alice'}}

Error Response:
{'code': 404, 'success': False, 'message': 'User retrieval failed', 'error': {'code': 'USER_NOT_FOUND', 'details': 'No user exists with the provided id.'}}
    """
