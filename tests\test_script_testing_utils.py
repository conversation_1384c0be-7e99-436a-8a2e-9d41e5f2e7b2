import pytest
import tempfile
import os
from tomsze_utils.script_testing_utils import run_pytest


class TestRunPytest:

    def test_run_pytest_success(self):
        # Create a temporary directory and test script
        with tempfile.TemporaryDirectory() as temp_dir:
            test_script_path = os.path.join(temp_dir, "test_success.py")
            with open(test_script_path, "w") as f:
                f.write("def test_pass():\n    assert True\n")

            # Run the pytest function
            result = run_pytest(test_script_path)
            assert "All tests in" in result  # Check for success message

    def test_run_pytest_failure(self):
        # Create a temporary directory and test script
        with tempfile.TemporaryDirectory() as temp_dir:
            test_script_path = os.path.join(temp_dir, "test_failure.py")
            with open(test_script_path, "w") as f:
                f.write("def test_fail():\n    assert False\n")

            # Run the pytest function
            result = run_pytest(test_script_path)
            assert "Tests in" in result  # Check for failure message
