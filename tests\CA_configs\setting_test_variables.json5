{
    "general":{
        "init_steps":[
        ],
        "steps":[
        ],
        "variables":{
            "var1":1,
            "var2":2,
            "var3":false,
            "var4":'abc',
            "var5":'{var4}',
            "var6":'{var5}',
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
    ]
   

}
