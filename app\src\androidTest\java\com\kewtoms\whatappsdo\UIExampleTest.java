package com.kewtoms.whatappsdo;

import androidx.test.ext.junit.rules.ActivityScenarioRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withContentDescription;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withText;

@RunWith(AndroidJUnit4.class)
public class UIExampleTest {

 // Rule to launch the MainActivity
 @Rule
 public ActivityScenarioRule<MainActivity> activityScenarioRule =
  new ActivityScenarioRule<>(MainActivity.class);

 @Test
 public void testActivityInteraction() {
  // Open navigation drawer
  onView(withContentDescription("Open navigation drawer")).perform(click());

  // Click on "Settings" item
  onView(withText("Sign-up")).perform(click());
 }
}