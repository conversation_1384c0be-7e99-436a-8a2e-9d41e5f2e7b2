import os
from tomsze_utils.dict_utils import extract_keys_from_dict_that_partly_contains
from tomsze_utils.plugins.pluginer import (
    ClassNameVisitor,
    Pluginer,
    get_classname_to_script_path_dict_in_dir,
)
from tomsze_utils.plugins.constant import plugin_constants


def test_pluginer():

    proj_plugin_setting_folder_path = r"./tests/pluginer_settings"
    proj_plugin_setting_file_path = os.path.join(
        proj_plugin_setting_folder_path, "project_plugins_settings.json"
    )

    dict_app_data = {}

    # Init.
    pluginer = Pluginer()
    pluginer.init_from_json(
        proj_plugin_setting_file_path,
        plugin_constants.PLUGIN_SETTING_MAIN_KEY,
    )

    # Run.
    for plugin_type in pluginer.dict_obj_plugin.keys():
        pluginer.run_plugin(
            dict_app_data=dict_app_data,
            plugin_type=plugin_type,
        )

    # Test.
    assert dict_app_data["test_result"] == "abc"
    for key in pluginer.dict_obj_plugin.keys():
        assert "PluginSample" in key
        assert pluginer.dict_obj_plugin[key].__dict__["aa"] == "a"

    assert "PluginSampleNotUse" not in pluginer.dict_obj_plugin


def test_init_from_json():
    proj_plugin_setting_folder_path = r"./tests/pluginer_settings"
    proj_plugin_setting_file_path = os.path.join(
        proj_plugin_setting_folder_path, "project_plugins_settings.json"
    )

    # Create an instance of Pluginer
    pluginer = Pluginer()

    # Initialize from JSON
    pluginer.init_from_json(
        proj_plugin_setting_file_path,
        plugin_constants.PLUGIN_SETTING_MAIN_KEY,
    )

    # Assertions to verify the state of the pluginer after initialization
    assert len(pluginer.dict_obj_plugin) == 1  # Expecting one plugins to be loaded
    assert "PluginSampleNotUse" not in pluginer.dict_obj_plugin
    for key in pluginer.dict_obj_plugin.keys():
        assert "PluginSample" in key
        assert pluginer.dict_plugin_use[key] is True
    assert "PluginSampleNotUse" not in pluginer.dict_obj_plugin


def test_init_from_json_and_dir():
    proj_plugin_setting_folder_path = r"./tests/pluginer_settings"
    proj_plugin_setting_file_path = os.path.join(
        proj_plugin_setting_folder_path, "project_plugins_settings.json"
    )
    plugin_script_dir = (
        r"./tests/test_plugins"  # Assuming this directory contains test plugin files
    )

    # Create an instance of Pluginer
    pluginer = Pluginer()

    # Initialize from JSON and directory
    pluginer.init_from_json_and_dir(
        proj_plugin_setting_file_path,
        plugin_constants.PLUGIN_SETTING_MAIN_KEY,
        plugin_script_dir,
    )

    # Assertions to verify the state of the pluginer after initialization
    assert len(pluginer.dict_obj_plugin) == 1  # Expecting one plugin to be loaded
    assert "PluginSampleNotUse" not in pluginer.dict_obj_plugin
    for key in pluginer.dict_obj_plugin.keys():
        assert "PluginSample" in key
        assert pluginer.dict_plugin_use[key] is True


def test_get_classname_to_script_path_dict_in_dir():
    class_name_visitor = ClassNameVisitor()
    test_dir = (
        "./tests/test_plugins"  # Assuming this directory contains test plugin files
    )

    # Create a temporary directory and files for testing
    os.makedirs(test_dir, exist_ok=True)
    with open(os.path.join(test_dir, "TestPlugin.py"), "w") as f:
        f.write(
            """
class PluginTest:
    pass
"""
        )
    with open(os.path.join(test_dir, "AnotherPlugin.py"), "w") as f:
        f.write(
            """
class PluginAnother:
    pass
"""
        )

    # Call the function to test
    result = get_classname_to_script_path_dict_in_dir(class_name_visitor, test_dir)

    # Check the results
    expected_result = {
        "PluginTest": os.path.join(test_dir, "TestPlugin.py").replace("\\", "/"),
        "PluginAnother": os.path.join(test_dir, "AnotherPlugin.py").replace("\\", "/"),
    }

    assert result == expected_result

    # Clean up the temporary directory after the test
    for filename in os.listdir(test_dir):
        file_path = os.path.join(test_dir, filename)
        os.remove(file_path)
    os.rmdir(test_dir)


if __name__ == "__main__":
    test_pluginer()
