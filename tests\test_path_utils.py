import os
import shutil
import tempfile

import pytest
from tomsze_utils.path_utils import (
    find_project_root_using_git_folder,
    get_desktop_path,
    is_directory_contain_file,
    is_path_exists,
)
from tomsze_utils.plugins.pluginer import Pluginer
from tomsze_utils.plugins.constant import plugin_constants


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


def test_is_directory_contain_file():
    # Create a temporary directory for testing
    import tempfile
    import shutil

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a test file in the temporary directory
        test_file = "test_file.txt"
        with open(os.path.join(temp_dir, test_file), "w") as f:
            f.write("Test content")

        # Test when file exists in directory
        assert is_directory_contain_file(temp_dir, test_file) == True

        # Test when file doesn't exist in directory
        non_existent_file = "non_existent.txt"
        assert is_directory_contain_file(temp_dir, non_existent_file) == False

        # Test with subdirectory
        subdir = os.path.join(temp_dir, "subdir")
        os.mkdir(subdir)
        with open(os.path.join(subdir, "subdir_file.txt"), "w") as f:
            f.write("Subdir test content")

        assert is_directory_contain_file(temp_dir, "subdir_file.txt") == True

    # Test with non-existent directory
    non_existent_dir = "/path/to/non/existent/directory"
    assert is_directory_contain_file(non_existent_dir, "any_file.txt") == False


def test_remove_all_files_in_directory():
    import tempfile
    import shutil
    from tomsze_utils.path_utils import remove_all_files_in_directory

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create some test files and subdirectories
        test_file1 = os.path.join(temp_dir, "test_file1.txt")
        test_file2 = os.path.join(temp_dir, "test_file2.txt")
        subdir = os.path.join(temp_dir, "subdir")
        subdir_file = os.path.join(subdir, "subdir_file.txt")

        # Create files and subdirectory
        with open(test_file1, "w") as f:
            f.write("Test content 1")
        with open(test_file2, "w") as f:
            f.write("Test content 2")
        os.mkdir(subdir)
        with open(subdir_file, "w") as f:
            f.write("Subdir test content")

        # Verify files and subdirectory exist
        assert os.path.exists(test_file1)
        assert os.path.exists(test_file2)
        assert os.path.exists(subdir)
        assert os.path.exists(subdir_file)

        # Call the function to remove all files
        remove_all_files_in_directory(temp_dir)

        # Verify all files and subdirectories are removed
        assert not os.path.exists(test_file1)
        assert not os.path.exists(test_file2)
        assert not os.path.exists(subdir)
        assert not os.path.exists(subdir_file)

        # Verify the temp_dir itself still exists (as it's managed by the context manager)
        assert os.path.exists(temp_dir)

        # Verify the directory is empty
        assert len(os.listdir(temp_dir)) == 0


def test_convert_to_abs_path():
    import tempfile
    import os
    from tomsze_utils.path_utils import convert_to_abs_path

    # Test case 1: Relative paths
    with tempfile.TemporaryDirectory() as tmpdir:
        relative_path1 = os.path.join(tmpdir, "file1.txt")
        relative_path2 = os.path.join(tmpdir, "subdir", "file2.txt")
        os.makedirs(os.path.dirname(relative_path2), exist_ok=True)
        with open(relative_path1, "w") as f:
            f.write("Test content 1")
        with open(relative_path2, "w") as f:
            f.write("Test content 2")

        abs_paths = convert_to_abs_path([relative_path1, relative_path2])
        assert all(os.path.isabs(path) for path in abs_paths)
        assert abs_paths[0] == os.path.realpath(relative_path1)
        assert abs_paths[1] == os.path.realpath(relative_path2)

    # Test case 2: Non-existent paths
    non_existent_paths = ["./non_existent_file.txt", "./another_non_existent_file.txt"]
    abs_paths_non_existent = convert_to_abs_path(non_existent_paths)
    assert all(os.path.isabs(path) for path in abs_paths_non_existent)
    assert abs_paths_non_existent[0] == os.path.realpath(non_existent_paths[0])
    assert abs_paths_non_existent[1] == os.path.realpath(non_existent_paths[1])


def test_get_file_path_in_directory_found():
    import tempfile
    import os
    from tomsze_utils.path_utils import get_file_path_in_directory

    with tempfile.TemporaryDirectory() as tmpdir:
        # Create a test file
        test_file = os.path.join(tmpdir, "test_file.txt")
        with open(test_file, "w") as f:
            f.write("Test content")

        # Call the function to get the file path
        found_file_path = get_file_path_in_directory(tmpdir, "test_file.txt")

        # Verify the file path is correct
        assert found_file_path == test_file


def test_get_file_path_in_directory_not_found():
    import tempfile
    import os
    from tomsze_utils.path_utils import get_file_path_in_directory

    with tempfile.TemporaryDirectory() as tmpdir:
        # Call the function to get a non-existent file path
        found_file_path = get_file_path_in_directory(tmpdir, "non_existent_file.txt")

        # Verify that the returned path is empty
        assert found_file_path == ""


def test_get_file_path_in_directory_with_ignore_dirs():
    import tempfile
    import os
    from tomsze_utils.path_utils import get_file_path_in_directory

    with tempfile.TemporaryDirectory() as tmpdir:
        # Create a test directory and a file inside it
        test_dir = os.path.join(tmpdir, "test_dir")
        os.makedirs(test_dir, exist_ok=True)
        test_file = os.path.join(test_dir, "test_file.txt")
        with open(test_file, "w") as f:
            f.write("Test content")

        # Create an ignored directory and a file inside it
        ignored_dir = os.path.join(tmpdir, ".git")
        os.makedirs(ignored_dir, exist_ok=True)
        ignored_file = os.path.join(ignored_dir, "ignored_file.txt")
        with open(ignored_file, "w") as f:
            f.write("Ignored content")

        # Call the function to get the file path, ignoring the .git directory
        found_file_path = get_file_path_in_directory(
            tmpdir,
            "test_file.txt",
            ignore_dirs=[".git"],
        )

        # Verify the file path is correct
        assert found_file_path == test_file

        # Call the function to get the ignored file path
        found_ignored_file_path = get_file_path_in_directory(
            tmpdir, "ignored_file.txt", ignore_dirs=[".git"]
        )

        # Verify that the returned path is empty for the ignored file
        assert found_ignored_file_path == ""


class TestIsPathExists:

    def test_path_exists(self):
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test file in the temporary directory
            test_file_path = os.path.join(temp_dir, "test_file.txt")
            with open(test_file_path, "w") as f:
                f.write("Test content")

            # Verify that the path exists
            assert is_path_exists(test_file_path) == True

    def test_path_does_not_exist(self):
        # Test with a non-existent file path
        non_existent_path = "/path/to/non/existent/file.txt"
        assert is_path_exists(non_existent_path) == False


class TestGetDesktopPath:

    def test_get_desktop_path_success(self):
        # This test checks if the desktop path is returned correctly.
        desktop_path = get_desktop_path()
        assert os.path.isdir(
            desktop_path
        ), "The returned path should be a valid directory."

    def test_get_desktop_path_not_empty(self):
        # This test checks if the desktop path is not empty.
        desktop_path = get_desktop_path()
        assert desktop_path != "", "The desktop path should not be an empty string."


class TestFindProjectRoot:

    def test_find_project_root_using_git_folder_success(self, temp_dir_path):
        # Create a temporary directory structure with a .git folder
        os.makedirs(os.path.join(temp_dir_path, ".git"))
        # Call the function to find the project root

        path_of_interest = os.path.join(temp_dir_path, "subdir")
        project_root = find_project_root_using_git_folder(path_of_interest)
        # Verify that the project root is correctly identified
        assert project_root == temp_dir_path

    def test_find_project_root_using_git_folder_no_git(self, temp_dir_path):
        # Create a temporary directory without a .git folder
        os.makedirs(os.path.join(temp_dir_path), exist_ok=True)

        path_of_interest = os.path.join(temp_dir_path, "subdir")

        # Call the function to find the project root
        project_root = find_project_root_using_git_folder(path_of_interest)
        # Verify that the project root is None since .git does not exist
        assert project_root is None


if __name__ == "__main__":
    test_is_directory_contain_file()
    test_remove_all_files_in_directory()
