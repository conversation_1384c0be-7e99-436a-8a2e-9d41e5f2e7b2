# %%
import sys

from server.api.end_to_end.api_constant_keys import ClientColEnum, DBAppsColEnum

sys.path.insert(1, "D:/code/my_projects/search_phone_app")

from typing import Dict
from googletrans import Translator
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.time_code_utils import timeit
from server.api.utils.app_id import verify_client_apps
from server.api.end_to_end.data_class import (
    ClientApps,
    ReceiveUserInstalledAppsResponse,
)
from const_path import (
    proj_ser_api_db_apps_path,
    proj_ser_api_db_apps_small_path,
    proj_ser_api_db_synonyms_small_path,
)
from server.api.utils.descriptions.get_description import (
    scrape_play_store_thread,
)
from server.api.utils.keywords.get_keywords import post_process_keywords
from server.api.utils.generate_vectors.utils import (
    convert_to_embedding,
    extract_device_app_synonyms,
    extract_device_app_synonyms_v2,
    extract_keyword_using_yake,
    normalize_list,
    post_process_app_description,
    search_list_text_en_zh,
    similarity_search,
    split_text,
)
from server.api.utils.translation.translate import check_is_eng, translate_to_eng
from sentence_transformers import SentenceTransformer


print("Done importing")

# %%
## Database load.
db_apps = PickleDatabaseSplit(
    # db_fpath=proj_ser_api_db_apps_path,
    db_fpath=proj_ser_api_db_apps_small_path,
    db_name="db_app",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)
db_synonyms = PickleDatabaseSplit(
    # db_fpath=proj_ser_api_db_apps_path,
    db_fpath=proj_ser_api_db_synonyms_small_path,
    db_name="db_synonyms",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)

translator = Translator()

model_id = "multi-qa-MiniLM-L6-cos-v1"
embedder = SentenceTransformer(model_id)

max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20


# %%

## Database load.
db_apps = PickleDatabaseSplit(
    # db_fpath=proj_ser_api_db_apps_path,
    db_fpath=proj_ser_api_db_apps_small_path,
    db_name="db_app",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)
db_synonyms = PickleDatabaseSplit(
    # db_fpath=proj_ser_api_db_apps_path,
    db_fpath=proj_ser_api_db_synonyms_small_path,
    db_name="db_synonyms",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)

translator = Translator()

model_id = "multi-qa-MiniLM-L6-cos-v1"
embedder = SentenceTransformer(model_id)

max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20


@timeit
# @profile
def search_user_app(
    client_apps: Dict,
    client_search: str,
):
    """
    To be ran in enpoint 'sua'

    Search user apps from server apps.
    """
    print(f"------ Client data ---------")
    list_device_apps = client_apps[ClientColEnum.APPIDS]
    print(f"search text: '{client_search}'")
    print(f"client apps: {list_device_apps}")
    print(f"----------------------------")

    # Check if client search is english, else translate to eng
    success, is_eng = check_is_eng(translator, client_search)
    assert success == True  # TODO log error
    if not is_eng:
        success, client_search = translate_to_eng(translator, client_search)

    assert success == True  # TODO log error
    # --------------------------------------------------------
    """
    Check if each client app in db, 
    if those not, 
    scrape from google play, 
    post process app description (desc), 
    convert app desc to embedding,
    extract keywords from app desc,
    get synonyms for each keyword,
    and store to db, but dump after response to client
    
    if those in, fetch embeddings from database
    
    merge both, proceed to search ..
    """
    # Check if client apps in db
    is_new, new_appIds, existing_keys = db_apps.check_keys_in_db(
        list_device_apps,
    )

    # Get client in db embeddings
    client_in_db_embds = db_apps.query_as_list(
        keys=existing_keys,
        col=DBAppsColEnum.EMBD,
    )

    # For some client apps not in db
    if is_new:
        # Scrape play store
        (
            scrap_successes,
            scrap_results,
            app_ids,
        ) = scrape_play_store_thread(new_appIds)
        assert True in scrap_successes  # TODO log error

        for _, scrap_result, app_id in zip(
            scrap_successes,
            scrap_results,
            app_ids,
        ):
            # Post-process app description.
            app_desc_orig, app_desc = post_process_app_description(scrap_result)

            # Convert app desc to embedding.
            output_embedding_tensor = (
                convert_to_embedding(  # TODO change to best embedder
                    embedder, app_desc, device="cpu", batch_size=128
                )
            )

            # Extract keywords.
            list_kw = extract_keyword_using_yake(
                lang="en",
                max_ngram_size=max_ngram_size,
                deduplication_threshold=deduplication_threshold,
                max_num_keywords=max_num_keywords,
                app_description_en_original=app_desc_orig,
            )

            list_kw = post_process_keywords(list_kw)

            # Get synonyms for each keyword.
            # TODO do it in another endpoint ...

            # Update to database var (dump to file later)
            db_apps.update_data_v2(
                key=app_id,
                col=DBAppsColEnum.IND,
                data=1,
            )
            db_apps.update_data_v2(
                key=app_id,
                col=DBAppsColEnum.EMBD,
                data=output_embedding_tensor,
            )
            db_apps.update_data_v2(
                key=app_id,
                col=DBAppsColEnum.DESC,
                data=app_desc,
            )
            db_apps.update_data_v2(
                key=app_id,
                col=DBAppsColEnum.KEYW,
                data=list_kw,
            )

            g = 1

    """
    Semantic search (similarity search).
    """
    query_text = client_search
    query = [query_text]
    # output = convert_to_embedding_using_huggingface_model(query, f"sentence-transformers/{model_id}")
    # query_embedding = torch.FloatTensor(output)

    query_embedding = convert_to_embedding(
        embedder,
        query,
        device="cpu",
        batch_size=128,
    )

    list_device_embedding_tensor = []
    list_device_description = []
    dict_device_id = {}
    dict_lang_listDescription = {}
    for device_app_id in list_device_apps:
        # device_app_id = device_app["id"]
        ff = db_apps.is_key_in(device_app_id)
        if db_apps.is_key_in(device_app_id):
            list_device_embedding_tensor.append(
                # dict_embedding_tensor[device_app_id],
                db_apps.query(
                    key=device_app_id,
                    col=DBAppsColEnum.EMBD,
                )
            )
            list_device_description.append(
                # dict_description[device_app_id],
                db_apps.query(
                    key=device_app_id,
                    col=DBAppsColEnum.DESC,
                )
            )

            dict_device_id[device_app_id] = ""
    dict_lang_listDescription["en"] = list_device_description

    # Similarity search.
    hits = similarity_search(
        query_embedding,
        list_device_embedding_tensor,
        top_k=30,
    )

    # Word search.
    list_query_words = split_text(query_text)
    list_num_matches = search_list_text_en_zh(
        dict_lang_listDescription, list_query_words
    )
    list_normalized_word_score = normalize_list(list_num_matches)
    list_normalized_word_score_reorder = []
    hits = hits[0]  # Get the hits for the first query (Use this for app is correct)
    for hit in hits:  # Reorder by similarity search result
        list_normalized_word_score_reorder.append(
            list_normalized_word_score[hit["corpus_id"]]
        )

    # Word search on synonyms.
    dict_lang_listSynonym = {}

    list_device_synonyms = extract_device_app_synonyms_v2(
        list_device_apps=list_device_apps,
        # dict_keywords=dict_keywords,
        # dict_synonyms=dict_synonyms,
        db_apps=db_apps,
        db_synonyms=db_synonyms,
    )

    dict_lang_listSynonym["en"] = list_device_synonyms
    list_num_matches_syn = search_list_text_en_zh(
        dict_lang_listSynonym, list_query_words
    )
    list_normalized_word_score_syn = normalize_list(list_num_matches_syn)
    list_normalized_word_score_reorder_syn = []
    for hit in hits:  # Reorder by similarity search result
        list_normalized_word_score_reorder_syn.append(
            list_normalized_word_score_syn[hit["corpus_id"]]
        )

    g = 1


def try_search_user_app():
    client_apps = ClientApps()
    client_apps.appIds.append("com.whatsapp")  # already exists in database
    client_apps.appIds.append("com.miniclip.footballstrike")
    client_apps = client_apps.dict()
    client_search = "eat"

    search_user_app(
        client_apps=client_apps,
        client_search=client_search,
    )


def main():
    try_search_user_app()


if __name__ == "__main__":
    main()

# %%
