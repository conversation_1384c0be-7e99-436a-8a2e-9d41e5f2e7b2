package com.kewtoms.whatappsdo.utils;

import static com.kewtoms.whatappsdo.data.Constants.mockIsEncryptDecryptKey;
import static com.kewtoms.whatappsdo.utils.SecurePrefsManager.getAccessToken;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.RequestFuture;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.generated.api_constant_keys.ApiResponseKeys;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ResponseKeys;
import com.kewtoms.whatappsdo.interfaces.GetResponseCallback;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;
import com.kewtoms.whatappsdo.utils.EncryptionUtils;
import com.tomsze.EncryptUtils;
import com.tomsze.JsonUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.Base64;

public class RequestUtils {
  public static final String TAG = "APP:RequestUtils";

  /**
   * Sends a POST request with JSON body using Volley in a separate
   * thread and returns the response.
   *
   * @param context The context used to initialize the Volley request
   * queue.
   * @param url The URL to which the POST request is sent.
   * @param jsonBody The JSON object as the body of the POST request.
   * @param isShowToast Determines whether to show a Toast message
   * when an exception occurs.
   * @return Returns the JSONObject response from the server, or null
   * if the request fails or times out.
   */
  public static JSONObject sendPostJsonVolleyFuture(
    Context context,
    String url,
    JSONObject jsonBody,
    Boolean isShowToast) {

    String methodName = "sendPostJsonVolleyFuture";
    Log.d(
      TAG,
      methodName + ": run"
    );

    // Array to hold the response, using an array to allow access from inner classes.
    final JSONObject[] response = new JSONObject[1];

    // Creates a future object to handle the request response.
    RequestFuture<JSONObject> requestFuture =
      RequestFuture.newFuture();

    // Creates a new thread to send the POST request.
    Thread thread = new Thread(new Runnable() {
      @Override
      public void run() {
        // Initializes the Volley request queue.
        RequestQueue queue = Volley.newRequestQueue(context);

        // Creates a JsonObjectRequest object.
        JsonObjectRequest request = new JsonObjectRequest(
          Request.Method.POST,
          url,
          jsonBody,
          requestFuture,
          requestFuture
        );

        // Set the timeout for the request
        int timeoutMillis = 10000; // 10 seconds
        request.setRetryPolicy(new DefaultRetryPolicy(
          timeoutMillis,
          DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
          DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        ));

        // adding request to queue to post the data.
        queue.add(request);
      }
    });

    // Starts the thread to execute the network request.
    thread.start();

    try {
      // Waits for the requestFuture object to complete within the specified time and retrieves the response.
      response[0] = requestFuture.get(
        10,
        TimeUnit.SECONDS
      );
    } catch (InterruptedException | ExecutionException |
             TimeoutException e) {
      // Handles exceptions, sets the response to null.
      response[0] = null;

      // Optionally show a Toast message with the exception information.
      if (isShowToast) {
        Toast toast = Toast.makeText(
          context,
          e.toString(),
          Toast.LENGTH_SHORT
        );
        toast.show();
      }
    }

    Log.d(
      TAG,
      methodName + ": done"
    );

    // Returns the response or null.
    return response[0];

  }

  /**
   * Sends a POST request with an encrypted JSON body and returns the
   * response.
   *
   * @param context (Context): The context from which the request is
   * made.
   * @param url (String): The URL to which the request is sent.
   * @param jsonBody (JSONObject): The JSON body to be encrypted and
   * sent.
   * @param isShowToast (Boolean): Flag to indicate whether to show a
   * toast message on error.
   * @return response (JSONObject): The response from the server.
   * @throws Exception if an error occurs during the request.
   * @example <pre>{@code
   * import org.json.JSONObject;
   * import android.content.Context;
   *
   * // Example of sending a valid encrypted JSON request
   * JSONObject jsonBody = new JSONObject();
   * jsonBody.put("key", "value");
   * JSONObject response = RequestUtils.sendPostEncryptedJsonVolleyFuture(context, "https://example.com/api", jsonBody, true);
   *
   * // Example of handling a request with a toast on error
   * JSONObject invalidJsonBody = new JSONObject();
   * invalidJsonBody.put("invalidKey", "invalidValue");
   * JSONObject errorResponse = RequestUtils.sendPostEncryptedJsonVolleyFuture(context, "https://example.com/api", invalidJsonBody, true);
   * }</pre>
   */
  public static JSONObject sendPostEncryptedJsonVolleyFuture(
    Context context,
    String url,
    JSONObject jsonBody,
    // key:data_str value:a string from JSONObject, see
    // testSendPostJsonVolleyFutureWithEncryptedData
    Boolean isShowToast)
    throws
    Exception {

    String methodName = "sendPostEncryptedJsonVolleyFuture";
    Log.d(
      TAG,
      methodName + ": run"
    );

    final JSONObject[] response = new JSONObject[1];
    RequestFuture<JSONObject> requestFuture =
      RequestFuture.newFuture();

    // Encrypt data
    JSONObject encryptedJsonBody =
      new JSONObject(jsonBody.toString());

    String requestEncryptedJsonKeyStr =
      Constants.request_encrypted_json_key; // use
    // key
    // in
    // strings.xml
    String dataStr =
      encryptedJsonBody.getString(requestEncryptedJsonKeyStr); // get value of
    // jsonBody

    Map<String, String[]> dateStringMap =
      JsonUtils.loadJsonFromResourcesToMap("date_strings.json"); // TODO: encapsulate it
    // in next jar update
    byte[] keyBytes =
      EncryptionUtils.generateKeyFromKeyDictAndServerDatetime(
        dateStringMap,
        null,
        null
      );
    byte[] newKeyBytes = Base64.getUrlDecoder().decode(keyBytes);
    byte[] dataBytes = dataStr.getBytes();
    String encryptedDataStr = EncryptUtils.encryptData(
      dataBytes,
      newKeyBytes
    );
    encryptedJsonBody.put(
      requestEncryptedJsonKeyStr,
      encryptedDataStr
    ); // Put back encrypted
    // data


    Thread thread = new Thread(new Runnable() {
      @Override
      public void run() {
        RequestQueue queue = Volley.newRequestQueue(context);

        JsonObjectRequest request = new JsonObjectRequest(
          Request.Method.POST,
          url,
          encryptedJsonBody,
          requestFuture,
          requestFuture
        );

        // Set the timeout for the request
        int timeoutMillis = 10000; // 10 seconds
        request.setRetryPolicy(new DefaultRetryPolicy(
          timeoutMillis,
          DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
          DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        ));

        // adding request to queue to post the data.
        queue.add(request);
      }
    });

    thread.start();

    try {
      response[0] = requestFuture.get(
        10,
        TimeUnit.SECONDS
      );

      // Decrypt data - get the first key from the response (middleware uses first key)
      String firstKey = response[0].keys().next();
      String token_str = response[0].getString(firstKey);
      String decryptedDataStr = EncryptUtils.decryptData(
        newKeyBytes,
        token_str,
        null
      );
      JSONObject decryptedDataJSONObject =
        new JSONObject(decryptedDataStr);

      // Replace the entire response with the decrypted data (no wrapping)
      response[0] = decryptedDataJSONObject;

    } catch (InterruptedException | ExecutionException |
             TimeoutException e) {
      response[0] = null;

      // Handle exceptions
      if (isShowToast) {
        Toast toast = Toast.makeText(
          context,
          e.toString(),
          Toast.LENGTH_SHORT
        );
        toast.show();
      }
    }

    Log.d(
      TAG,
      methodName + ": done"
    );

    return response[0];

  }

  /**
   * Sends a POST request with JSON data using Volley network library.
   * This method runs in a new thread to avoid blocking the main
   * thread.
   *
   * @param context The context used to initialize the Volley request
   * queue.
   * @param url The URL to which the POST request is sent.
   * @param callback The callback interface for handling success and
   * error responses.
   * @param jsonBody The JSON object as the body of the POST request.
   */
  public static void sendPostJsonVolley(
    Context context,
    String url,
    PostResponseCallback callback,
    JSONObject jsonBody) {

    String methodName = "sendPostJsonVolley";
    Log.d(
      TAG,
      methodName + ": run"
    );

    // Create a new thread to execute the network request
    Thread thread = new Thread(new Runnable() {
      @Override
      public void run() {
        // Initialize the Volley request queue
        RequestQueue queue = Volley.newRequestQueue(context);

        // Create a JsonObjectRequest instance
        JsonObjectRequest request = new JsonObjectRequest(
          Request.Method.POST,
          url,
          jsonBody,
          new Response.Listener<JSONObject>() {
            @Override
            public void onResponse(JSONObject response) {
              // Handle the success response through the callback interface
              callback.onPostSuccess(response);
            }
          },
          new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
              // Handle the error response through the callback interface
              callback.onPostError(error.getMessage());
            }
          }
        ) {
          @Override
          public Map<String, String> getHeaders() {
            Map<String, String> headers =
              new HashMap<String, String>();
            // Add Authorization header with Bearer token
            headers.put(
              "Authorization",
              "Bearer " + getAccessToken(context)
            );
            // Add Content-Type header if needed
            headers.put(
              "Content-Type",
              "application/json; charset=UTF-8"
            );
            return headers;
          }
        };

        // Set the timeout for the request
        int timeoutMillis = 100000; // 100 seconds
        // Set the request retry policy, including timeout, maximum retries, and backoff multiplier
        request.setRetryPolicy(new DefaultRetryPolicy(
          timeoutMillis,
          DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
          DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        ));

        // adding request to queue to post the data.
        queue.add(request);
      }
    });

    // Start the thread to execute the network request
    thread.start();

    Log.d(
      TAG,
      methodName + ": done"
    );
  }

  public static void sendPostEncryptedJsonVolley(
    Context context,
    String url,
    JSONObject jsonBody,
    int timeoutMillis,
    boolean isWaitForThread,
    PostResponseCallback callback)
    throws
    InterruptedException {

    String methodName = "sendPostEncryptedJsonVolley";

    Log.d(
      TAG,
      methodName + ": run"
    );

    // Set default timeout of 10 seconds if set to -1
    timeoutMillis = timeoutMillis == -1 ? 1000000 : timeoutMillis;
    int finalTimeoutMillis = timeoutMillis;

    // Create a new thread to execute the network request
    Thread thread = new Thread(new Runnable() {
      @Override
      public void run() {

        Log.d(
          TAG,
          methodName + ": " + "Run run() method of Thread"
        );

        // Create a new JSONObject instance from jsonBody for encrypting
        JSONObject encryptedJsonBody;
        try {
          encryptedJsonBody = new JSONObject(jsonBody.toString());
        } catch (JSONException e) {
          Log.e(
            TAG,
            methodName + ": JSONException: " + e
          );
          throw new RuntimeException(e);
        }

        boolean isMockEncryptDecrypt = false;
        if (context instanceof Activity) {
          Intent intent = ((Activity) context).getIntent();
          if (intent != null) {
            isMockEncryptDecrypt = intent.getBooleanExtra(
              mockIsEncryptDecryptKey,
              false
            );
          }
        }

        // Encrypt data
        String requestEncryptedJsonKeyStr = "";
        String encryptedDataStr = "";
        byte[] newKeyBytes = null;
        if (!isMockEncryptDecrypt) {
          // Get key in strings.xml
          requestEncryptedJsonKeyStr =
            Constants.request_encrypted_json_key; // use
          String dataStr; // get value of
          try {
            dataStr =
              encryptedJsonBody.getString(requestEncryptedJsonKeyStr);
          } catch (JSONException e) {
            Log.e(
              TAG,
              methodName + ": JSONException: " + e
            );
            throw new RuntimeException(e);
          }

          Map<String, String[]> dateStringMap; // TODO: encapsulate it
          try {
            dateStringMap =
              JsonUtils.loadJsonFromResourcesToMap("date_strings.json");
          } catch (IOException e) {
            Log.e(
              TAG,
              methodName + ": IOException: " + e
            );
            throw new RuntimeException(e);
          }

          // Get the key bytes using a jar function
          byte[] keyBytes;
          try {
            keyBytes =
              EncryptionUtils.generateKeyFromKeyDictAndServerDatetime(
                dateStringMap,
                null,
                Constants.post_req_url_request_datetime_link
              );
            if (keyBytes != null) {
              Log.d(
                TAG,
                methodName + ": keyBytes: " + Arrays.toString(keyBytes)
              );
            }
          } catch (Exception e) {
            Log.e(
              TAG,
              methodName + ": Exception: " + e
            );
            throw new RuntimeException(e);
          }

          // Create an encrypted data string
          newKeyBytes = Base64.getUrlDecoder().decode(keyBytes);
          byte[] dataBytes = dataStr.getBytes();
          encryptedDataStr = EncryptUtils.encryptData(
            dataBytes,
            newKeyBytes
          );
        }


        try {
          if (isMockEncryptDecrypt) {
            encryptedJsonBody.put(
              requestEncryptedJsonKeyStr,
              jsonBody.toString()
            ); // Put back encrypted data
          } else {
            encryptedJsonBody.put(
              requestEncryptedJsonKeyStr,
              encryptedDataStr
            ); // Put back encrypted data
          }
        } catch (JSONException e) {
          Log.e(
            TAG,
            methodName + ": JSONException: " + e
          );
          throw new RuntimeException(e);
        }

        Log.i(
          TAG,
          methodName + ": " + "done encrypting data"
        );

        // Initialize the Volley request queue
        RequestQueue queue = Volley.newRequestQueue(context);

        // Create a JsonObjectRequest instance
        JsonObjectRequest request = getJsonObjectRequestForVolley(
          encryptedJsonBody,
          newKeyBytes,
          url,
          context,
          !isMockEncryptDecrypt,
          // if mock, no decrypt
          callback,
          finalTimeoutMillis
        );

        // Adding request to queue to post the data.
        queue.add(request);

        Log.i(
          TAG,
          methodName + ": " + "End of Thread run."
        );
      } // end of thread run
    });

    // Start the thread to execute the network request
    thread.start();

    Log.i(
      TAG,
      methodName + ": " + "done starting thread"
    );

    if (isWaitForThread) {
      thread.join();
    }

    Log.i(
      TAG,
      methodName + ": done"
    );
  }

  @NonNull
  private static JsonObjectRequest getJsonObjectRequestForVolley(
    JSONObject encryptedJsonBody,
    byte[] newKeyBytes,
    String url,
    Context context,
    boolean isDecryptData,
    PostResponseCallback callback,
    int finalTimeoutMillis) {

    JsonObjectRequest request = new JsonObjectRequest(
      Request.Method.POST,
      url,
      encryptedJsonBody,
      new Response.Listener<JSONObject>() {
        @Override
        public void onResponse(JSONObject responseJSONObject) {
          String methodName = "getJsonObjectRequest.onResponse";

          Log.d(
            TAG,
            methodName + ": " + "run"
          );

          // Handle the success response through the callback interface
          // Decrypt data - get the first key from the response (middleware uses first key)
          String firstKey = responseJSONObject.keys().next();
          String token_str;
          try {
            token_str = responseJSONObject.getString(firstKey);
          } catch (JSONException e) {
            Log.e(
              TAG,
              methodName + ": JSONException: " + e
            );
            throw new RuntimeException(e);
          }


          if (isDecryptData) {

            // Decrypt data only if the token_str contain no error
            String decryptedDataStr = null;
            if (token_str.toLowerCase().contains("error")) {
              decryptedDataStr = token_str;
            } else {
              try {
                decryptedDataStr = EncryptUtils.decryptData(
                  newKeyBytes,
                  token_str,
                  null
                );
              } catch (Exception e) {
                Log.e(
                  TAG,
                  methodName + ": Exception: " + e
                );
                throw new RuntimeException(e);
              }
            }

            JSONObject finalDataJSONObject;
            try {
              // Use the decrypted data directly without wrapping in ApiResponse
              finalDataJSONObject = new JSONObject(decryptedDataStr);
            } catch (JSONException e) {
              Log.e(
                TAG,
                methodName + ": JSONException: " + e
              );
              throw new RuntimeException(e);
            }

            Log.i(
              TAG,
              methodName + ": " + "done decrypting data"
            );
            Log.d(
              TAG,
              methodName + ": " + "done"
            );


            callback.onPostSuccess(finalDataJSONObject);
          } else {
            Log.d(
              TAG,
              methodName + ": " + "done"
            );
            callback.onPostSuccess(responseJSONObject);
          }

        }
      },
      new Response.ErrorListener() {
        @Override
        public void onErrorResponse(VolleyError error) {
          String methodName = "getJsonObjectRequest.onErrorResponse";

          // Handle the error response through the callback interface
          Log.e(
            TAG,
            methodName + ": " + "VolleyError: " + error
          );
          callback.onPostError(error.toString());
        }
      }
    ) {
      @Override
      public Map<String, String> getHeaders() {
        Map<String, String> headers = new HashMap<String, String>();
        // Add Authorization header with Bearer token
        headers.put(
          "Authorization",
          "Bearer " + getAccessToken(context)
        );
        // Add Content-Type header if needed
        headers.put(
          "Content-Type",
          "application/json; charset=UTF-8"
        );
        return headers;
      }
    };

    // Set the timeout for the request
    // Set the request retry policy, including timeout, maximum retries, and backoff multiplier
    request.setRetryPolicy(new DefaultRetryPolicy(
      finalTimeoutMillis,
      DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
      DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
    ));
    return request;
  }

  /**
   * Sends a GET request to a specified URL and handles the response
   * or error.
   *
   * @param context The context in which the network request is made,
   * used to initialize the RequestQueue.
   * @param url The URL to which the GET request is sent.
   * @param callback The callback interface for handling the response
   * or error.
   * <p>
   * This method creates a new thread to perform the network request,
   * avoiding network operations on the main thread. It uses the
   * Volley library to send a GET request and handle the response or
   * error by implementing the Response.Listener and
   * Response.ErrorListener interfaces. Headers are added to the
   * request to provide necessary information for the request.
   */
  public static void sendGet(
    Context context,
    String url,
    GetResponseCallback callback) {

    String methodName = "sendGet";
    Log.i(
      TAG,
      methodName + ": run"
    );

    Thread thread = new Thread(new Runnable() {
      @Override
      public void run() {
        Log.i(
          TAG,
          methodName + ": " + "Run run() method of Thread"
        );

        // Instantiate the RequestQueue
        RequestQueue queue = Volley.newRequestQueue(context);

        // Request a string response from the provided URL
        StringRequest stringRequest = new StringRequest(
          Request.Method.GET,
          url,
          new Response.Listener<String>() {
            @Override
            public void onResponse(String response) {
              // Handle the response
              Log.d(
                TAG,
                response
              );

              callback.onGetSuccess(response);
            }
          },
          new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {
              // Handle errors
              Log.e(
                TAG,
                error.toString()
              );

              callback.onGetError(error.toString());
            }
          }
        ) {
          // this is the part, that adds the header to the request
          @Override
          public Map<String, String> getHeaders() {
            Map<String, String> params =
              new HashMap<String, String>();
            params.put(
              "key",
              "e6d64b73a7b241b396c102212241405"
            );
            params.put(
              "q",
              "Berlin"
            );
            params.put(
              "content-type",
              "application/json"
            );
            return params;
          }
        };

        // Add the request to the RequestQueue
        queue.add(stringRequest);
      }
    });

    thread.start();

    Log.i(
      TAG,
      methodName + ": done"
    );
  }

  /**
   * Gets the API response data from the server response JSON object.
   * Updated to match the actual server API response format.
   * <p>
   * Server response structure (after decryption): { "code": 200,
   * "is_success": true, "message": "Operation successful.", "data": {
   * ... actual payload ... } }
   *
   * @param context Android context
   * @param responseJSONObject The decrypted response from server
   * @return JSONObject containing the data payload
   */
  public static JSONObject getApiResponseDataFromResponseJSONObject(
    Context context,
    JSONObject responseJSONObject) {

    String methodName = "getApiResponseDataFromResponseJSONObject";

    Log.d(
      TAG,
      methodName + ": run"
    );

    try {
      // Check if data field exists in the direct response
      if (responseJSONObject.has("data")) {
        Log.d(
          TAG,
          methodName + ": " + "done"
        );
        return responseJSONObject.getJSONObject("data");
      } else {
        // Return empty object if no data field
        Log.d(
          TAG,
          methodName + ": " + "no data field, returning empty object"
        );
        return new JSONObject();
      }
    } catch (JSONException e) {
      Log.e(
        TAG,
        methodName + ": JSONException: " + e
      );
      throw new RuntimeException(e);
    }
  }

  /**
   * Gets the complete API response object from the server response.
   * This replaces the old
   * getApiFunResultJsonObjectFromResponseJSONObject method.
   *
   * @param context Android context
   * @param responseJSONObject The decrypted response from server
   * @return JSONObject containing the complete response
   */
  public static JSONObject getApiResponseFromResponseJSONObject(
    Context context,
    JSONObject responseJSONObject) {

    String methodName = "getApiResponseFromResponseJSONObject";

    Log.d(
      TAG,
      methodName + ": run"
    );

    Log.d(
      TAG,
      methodName + ": " + "done"
    );
    // Return the response directly since it's no longer wrapped
    return responseJSONObject;
  }

  /**
   * @deprecated Use getApiResponseDataFromResponseJSONObject instead.
   * This method is kept for backward compatibility but should be
   * updated.
   */
  @Deprecated
  public static JSONObject getDbResultJsonObjectFromResponseJSONObject(
    Context context,
    JSONObject responseJSONObject) {

    String methodName = "getDbResultJsonObjectFromResponseJSONObject";
    Log.d(
      TAG,
      methodName + ": DEPRECATED - Use getApiResponseDataFromResponseJSONObject instead"
    );

    // For backward compatibility, try to extract data from the new format
    try {
      return getApiResponseDataFromResponseJSONObject(
        context,
        responseJSONObject
      );
    } catch (Exception e) {
      // Fallback to old format if new format fails
      try {
        return responseJSONObject.getJSONObject(ApiResponseKeys.API_RESPONSE)
          .getJSONObject(Constants.response_json_apiFunResult_key)
          .getJSONObject(ResponseKeys.RESULT);
      } catch (JSONException ex) {
        Log.e(
          TAG,
          methodName + ": JSONException: " + ex
        );
        throw new RuntimeException(ex);
      }
    }
  }

  /**
   * @deprecated Use getApiResponseFromResponseJSONObject instead.
   * This method is kept for backward compatibility but should be
   * updated.
   */
  @Deprecated
  public static JSONObject getApiFunResultJsonObjectFromResponseJSONObject(
    Context context,
    JSONObject responseJSONObject) {

    String methodName =
      "getApiFunResultJsonObjectFromResponseJSONObject";
    Log.d(
      TAG,
      methodName + ": DEPRECATED - Use getApiResponseFromResponseJSONObject instead"
    );

    // For backward compatibility, return the ApiResponse object
    return getApiResponseFromResponseJSONObject(
      context,
      responseJSONObject
    );
  }

  // Helper methods for common response parsing patterns

  /**
   * Checks if the API response indicates success.
   *
   * @param context Android context
   * @param responseJSONObject The decrypted response from server
   * @return boolean indicating if the operation was successful
   */
  public static boolean isApiResponseSuccessful(
    Context context,
    JSONObject responseJSONObject) {

    try {
      return responseJSONObject.optBoolean(
        "is_success",
        false
      );
    } catch (Exception e) {
      Log.e(
        TAG,
        "isApiResponseSuccessful: Exception: " + e
      );
      return false;
    }
  }

  /**
   * Gets the message from the API response.
   *
   * @param context Android context
   * @param responseJSONObject The decrypted response from server
   * @return String message from the response
   */
  public static String getApiResponseMessage(
    Context context,
    JSONObject responseJSONObject) {

    try {
      return responseJSONObject.optString(
        "message",
        ""
      );
    } catch (Exception e) {
      Log.e(
        TAG,
        "getApiResponseMessage: Exception: " + e
      );
      return "";
    }
  }

  /**
   * Gets the HTTP status code from the API response.
   *
   * @param context Android context
   * @param responseJSONObject The decrypted response from server
   * @return int HTTP status code
   */
  public static int getApiResponseCode(
    Context context,
    JSONObject responseJSONObject) {

    try {
      return responseJSONObject.optInt(
        "code",
        200
      );
    } catch (Exception e) {
      Log.e(
        TAG,
        "getApiResponseCode: Exception: " + e
      );
      return 500;
    }
  }

  /**
   * Determines the appropriate URL to use based on the context's
   * intent extras or defaults to the production URL.
   *
   * @param context (Context): The context from which the request is
   * made, typically an Activity.
   * @param mockRequestUrlKey (String): The key used to retrieve the
   * mock URL from the intent extras.
   * @param prodRequestUrlLink (String): The default production URL to
   * use if no mock URL is provided.
   * @return url (String): The selected URL, either from the intent
   * extras or the production URL.
   * @example <pre>{@code
   * String mockKey = "mock_url_key";
   * String prodUrl = "https://api.example.com/endpoint";
   *
   * // Case 1: Context has an intent with the mock URL extra
   * Intent intent = new Intent();
   * intent.putExtra(mockKey, "https://mock.api.com/endpoint");
   * Activity activity = mock(Activity.class);
   * when(activity.getIntent()).thenReturn(intent);
   *
   * String resultUrl = getMockOrProdUrl(activity, mockKey, prodUrl);
   * // Expected result: "https://mock.api.com/endpoint"
   * assertEquals("https://mock.api.com/endpoint", resultUrl);
   *
   * // Case 2: Context does not have the mock URL extra
   * when(activity.getIntent()).thenReturn(null);
   *
   * resultUrl = getMockOrProdUrl(activity, mockKey, prodUrl);
   * // Expected result: "https://api.example.com/endpoint"
   * assertEquals(prodUrl, resultUrl);
   * }</pre>
   */
  public static String getMockUrlFromTestIntentOrProdUrl(
    Context context,
    String mockRequestUrlKey,
    String prodRequestUrlLink) {

    String url = null;
    if (context instanceof Activity) {
      Intent intent = ((Activity) context).getIntent();
      if (intent != null) {
        url = intent.getStringExtra(mockRequestUrlKey);
      }
    }
    if (url == null) {
      url = prodRequestUrlLink; // Production URL
    }
    return url;
  }


}
