from dataclasses import dataclass, field
import os
import sys
import ast
from tomsze_utils.regular_expression_utils import extract_docstring_descryption

# Define a visitor class to traverse the AST and extract function names
import ast
from typing import Dict, List


class FunctionNameVisitor(ast.NodeVisitor):
    """
    A visitor class for traversing the Abstract Syntax Tree (AST) of a Python script
    to extract function names and their corresponding docstrings.

    Attributes:
        ignore_class_functions (bool): A flag indicating whether to ignore functions
        defined within classes. Defaults to False.
        current_context (list): A stack to keep track of the current context (e.g.,
        whether inside a class).
        function_name_list (list): A list to store the names of the extracted functions.
        function_docstring_list (list): A list to store the docstrings of the extracted functions.
    """

    def __init__(self, ignore_class_functions=False):
        self.ignore_class_functions = ignore_class_functions
        self.current_context = []
        self.function_name_list = []
        self.function_docstring_list = []  # Initialize the list for docstrings

    def visit_ClassDef(self, node):
        self.current_context.append("class")
        self.generic_visit(node)
        self.current_context.pop()

    def visit_FunctionDef(self, node):
        if self.ignore_class_functions and "class" in self.current_context:
            return  # Ignore functions within classes if option is True
        self.function_name_list.append(node.name)
        self.function_docstring_list.append(
            ast.get_docstring(node) or ""
        )  # Store the docstring
        self.generic_visit(node)

    def clear_list_function_name(self):
        self.function_name_list.clear()
        self.function_docstring_list.clear()  # Clear the docstring list as well


@dataclass
class FunctionMeta:
    filename: str
    file_path: str
    function_name: str
    function_description: str
    function_docstring: str


def extract_function_names_from_script(
    script_path: str,
    ignore_class_functions: bool = True,
    ignore_test_functions: bool = True,
    ignore_function_names: List[str] = [
        "main",
        "get_callable_from_module_by_function_name",
        "remove_all_logging_handlers",
        "get_scrapingant_api_credit_status",
        "on_time_up",
        "timeit",
        "timed",
        "register_tool_functions_in_dir",
        "register_tool_functions_in_dir_v2",
        "register",
        "draw_match_result",
        "use_template_match",
        "use_sift_feature_flann_match",
        "generate_key_from_string",
        "benchmark_func",
        "benchmark_funcs",
        "get_classname_to_script_path_dict_in_dir",
        "create_function_schema",
        "partial_funcA",
        "extract_tool_name_from_string",
    ],
) -> List[FunctionMeta]:
    """
    Extracts function names and their corresponding docstrings from a Python script.

    Args:
        script_path (str): The path to the Python script file.
        ignore_class_functions (bool): A flag indicating whether to ignore functions
        defined within classes. Defaults to True.
        ignore_test_functions (bool): A flag indicating whether to ignore functions
        whose names start with "test". Defaults to True.
        ignore_function_names (List[str]): A list of function names to ignore. Defaults to ["main"].

    Returns:
        List[FunctionMeta]: A list of FunctionMeta objects containing function names
        and their docstrings.

    Examples:
        ```python
        # Example 1: Extracting function names while ignoring class functions
        functionMeta_list = extract_function_names_from_script("path/to/script.py", ignore_class_functions=True)

        # Example 2: Extracting function names including class functions
        functionMeta_list = extract_function_names_from_script("path/to/script.py", ignore_class_functions=False)

        # Example 3: Extracting function names while ignoring test functions
        functionMeta_list = extract_function_names_from_script("path/to/script.py", ignore_test_functions=True)

        # Example 4: Extracting function names while ignoring specific function names
        functionMeta_list = extract_function_names_from_script("path/to/script.py", ignore_function_names=["main"])
        ```
    """
    with open(script_path, "r", encoding="utf-8") as f:
        tree = ast.parse(f.read())
    visitor = FunctionNameVisitor(ignore_class_functions)
    visitor.visit(tree)

    functionMeta_list = []
    for function_name, function_docstring in zip(
        visitor.function_name_list, visitor.function_docstring_list
    ):
        if ignore_test_functions and function_name.startswith("test"):
            continue  # Ignore functions whose names start with "test"
        if function_name in ignore_function_names:
            continue  # Ignore specified function names

        function_description = extract_docstring_descryption(function_docstring)
        functionMeta_list.append(
            FunctionMeta(
                filename=os.path.basename(script_path),
                file_path=script_path,
                function_name=function_name,
                function_description=function_description,
                function_docstring=function_docstring,
            )
        )

    return functionMeta_list


def extract_function_names_from_script_in_dir(
    script_dir: str,
    ignore_class_functions: bool = True,
    ignore_test_functions: bool = True,
    ignore_function_names: List[str] = [
        "main",
        "get_callable_from_module_by_function_name",
        "remove_all_logging_handlers",
        "get_scrapingant_api_credit_status",
        "on_time_up",
        "timeit",
        "timed",
        "register_tool_functions_in_dir",
        "register_tool_functions_in_dir_v2",
        "register",
        "draw_match_result",
        "use_template_match",
        "use_sift_feature_flann_match",
        "generate_key_from_string",
        "benchmark_func",
        "benchmark_funcs",
        "get_classname_to_script_path_dict_in_dir",
        "create_function_schema",
        "partial_funcA",
        "extract_tool_name_from_string",
    ],
    ignore_filenames: List[str] = [
        "config_parser_utils.py",
        "context_manager_utils.py",
        "fastapi_utils.py",
    ],
) -> List[FunctionMeta]:
    """
    Extracts function names and their corresponding docstrings from all Python scripts
    in a specified directory.

    Args:
        script_dir (str): The path to the directory containing Python scripts.
        ignore_class_functions (bool): A flag indicating whether to ignore functions
        defined within classes. Defaults to True.
        ignore_function_names (List[str]): A list of function names to ignore. Defaults to ["main"].
        ignore_filenames (List[str]): A list of filenames to ignore. Defaults to ["config_parser_utils.py"].

    Returns:
        List[FunctionMeta]: A list of FunctionMeta objects containing function names
        and their docstrings.

    Examples:
        ```python
        # Example 1: Extracting function names while ignoring class functions
        function_meta_list = extract_function_names_from_script_in_dir("path/to/scripts", ignore_class_functions=True)

        # Example 2: Extracting function names including class functions
        function_meta_list = extract_function_names_from_script_in_dir("path/to/scripts", ignore_class_functions=False)
        ```
    """
    function_meta_list = []

    list_file_type = [
        "py",
    ]
    for dirpath, dirnames, filenames in os.walk(script_dir):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            if filetype in list_file_type and filename not in ignore_filenames:
                script_path = os.path.join(dirpath, filename)
                function_meta_list.extend(
                    extract_function_names_from_script(
                        script_path=script_path,
                        ignore_class_functions=ignore_class_functions,
                        ignore_test_functions=ignore_test_functions,
                        ignore_function_names=ignore_function_names,
                    )
                )
    return function_meta_list


def extract_function_meta_dict_from_script_in_dir(
    script_dir: str,
    ignore_class_functions: bool = True,
    ignore_test_functions: bool = True,
    ignore_function_names: List[str] = [
        "main",
        "get_callable_from_module_by_function_name",
        "remove_all_logging_handlers",
        "get_scrapingant_api_credit_status",
        "on_time_up",
        "timeit",
        "timed",
        "register_tool_functions_in_dir",
        "register_tool_functions_in_dir_v2",
        "register",
        "draw_match_result",
        "use_template_match",
        "use_sift_feature_flann_match",
        "generate_key_from_string",
        "benchmark_func",
        "benchmark_funcs",
        "get_classname_to_script_path_dict_in_dir",
        "create_function_schema",
        "partial_funcA",
        "extract_tool_name_from_string",
    ],
    ignore_filenames: List[str] = [
        "config_parser_utils.py",
        "context_manager_utils.py",
        "fastapi_utils.py",
    ],
) -> dict:
    """
    Extracts function names and their corresponding docstrings from all Python scripts
    in a specified directory.

    Args:
        script_dir (str): The path to the directory containing Python scripts.
        ignore_class_functions (bool): A flag indicating whether to ignore functions
        defined within classes. Defaults to True.
        ignore_function_names (List[str]): A list of function names to ignore. Defaults to ["main"].
        ignore_filenames (List[str]): A list of filenames to ignore. Defaults to ["config_parser_utils.py"].

    Returns:
        dict: A dictionary where each key is a function name and the value is the corresponding FunctionMeta object.

    Examples:
        ```python
        # Example 1: Extracting function names while ignoring class functions
        function_meta_dict = extract_function_meta_dict_from_script_in_dir("path/to/scripts", ignore_class_functions=True)

        # Example 2: Extracting function names including class functions
        function_meta_dict = extract_function_meta_dict_from_script_in_dir("path/to/scripts", ignore_class_functions=False)
        ```
    """
    function_meta_dict = {}

    list_file_type = [
        "py",
    ]
    for dirpath, dirnames, filenames in os.walk(script_dir):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            if filetype in list_file_type and filename not in ignore_filenames:
                script_path = os.path.join(dirpath, filename)
                function_meta_list = extract_function_names_from_script(
                    script_path=script_path,
                    ignore_class_functions=ignore_class_functions,
                    ignore_test_functions=ignore_test_functions,
                    ignore_function_names=ignore_function_names,
                )
                for function_meta in function_meta_list:
                    function_meta_dict[function_meta.function_name] = function_meta
    return function_meta_dict


def read_script_file_to_string(file_path: str) -> str:
    """
    Reads the contents of a script file and returns it as a string.

    Args:
        file_path (str): The path to the script file to be read.

    Returns:
        str: The contents of the script file.

    Raises:
        FileNotFoundError: If the specified file does not exist.
        IOError: If there is an error reading the file.

    Examples:
        ```python
        script_content = read_script_file_to_string("path/to/script.py")
        print(script_content)
        ```
    """
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"The specified file does not exist: {file_path}")

    with open(file_path, "r") as file:
        return file.read()


def get_function_name_to_path_dict_in_dir(directory: str) -> Dict[str, str]:
    """
    Traverse a directory to find Python files and extract function names along with their file paths.

    Args:
        directory (str): The path to the directory to traverse.

    Returns:
        Dict[str, str]: A dictionary mapping function names to their corresponding file paths.

    Examples:
        ```python
        function_dict = get_function_name_to_path_dict_in_dir("/path/to/scripts")
        print(function_dict)  # Output might be {'function_one': '/path/to/scripts/script.py'}
        ```

        ```python
        function_dict = get_function_name_to_path_dict_in_dir("/another/path")
        print(function_dict)  # Output might be {'function_two': '/another/path/another_script.py'}
        ```
    """
    function_name_to_path_dict = {}
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(".py"):
                file_path = os.path.join(root, file)
                with open(file_path, "r") as f:
                    try:
                        node = ast.parse(f.read())
                        for n in ast.walk(node):
                            if isinstance(n, ast.FunctionDef):
                                print(f"Function {n.name} defined in {file_path}")
                                function_name_to_path_dict[n.name] = file_path
                    except SyntaxError:
                        print(f"Syntax error in {file_path}")
    return function_name_to_path_dict


def get_function_call_list_from_source_code(
    source_code: str,
    is_no_duplicated: bool = False,
) -> List[str]:
    """
    Extracts function calls from the given source code.

    Args:
        source_code (str): The source code to analyze.

    Returns:
        List[str]: A list of function names that are called within the source code.

    Examples:
        ```python
        calls = get_function_calls_from_source_code("def example():\n    my_function()")
        print(calls)  # Output: ['my_function']
        ```
    """
    function_calls = []
    try:
        node = ast.parse(source_code)
        for n in ast.walk(node):
            if isinstance(n, ast.Call) and isinstance(n.func, ast.Name):
                if is_no_duplicated:
                    if n.func.id not in function_calls:
                        function_calls.append(n.func.id)
                else:
                    function_calls.append(n.func.id)
    except SyntaxError as e:
        print("Syntax error in the provided source code.")
        print(f"syntax error: {e.text}")

    return function_calls


@dataclass
class AstFuncData:
    function_name: str = ""
    script_path: str = ""
    source_code: str = ""
    func_call_list: List[str] = field(default_factory=list)
    called_by_func_list: List[str] = field(default_factory=list)


import astunparse


def get_astFuncData_list_in_script(script_path: str) -> List[AstFuncData]:
    """
    Extracts function data using AST from a single Python script.

    Args:
        script_path (str): The path to the Python script file.

    Returns:
        List[AstFuncData]: A list of AstFuncData objects containing function metadata.

    Examples:
        ```python
        data_list = get_astFuncData_list_in_script("/path/to/script.py")
        print(data_list)  # Output: List of AstFuncData objects for each function found in the script.
        ```
    """
    astFuncData_list = []
    try:
        with open(script_path, "r", encoding="utf-8") as f:
            script_content = f.read()

        node = ast.parse(script_content)
        for n in ast.walk(node):
            if isinstance(n, ast.FunctionDef):
                source_code = astunparse.unparse(n)
                function_call_list = get_function_call_list_from_source_code(
                    source_code=source_code,
                    is_no_duplicated=True,
                )
                astFuncData_list.append(
                    AstFuncData(
                        function_name=n.name,
                        script_path=script_path,
                        source_code=source_code,
                        func_call_list=function_call_list,
                        called_by_func_list=[],  # This can be populated later if needed
                    )
                )
    except (SyntaxError, FileNotFoundError) as e:
        print(f"Error processing the script at {script_path}: {e}")

    return astFuncData_list


def get_astFuncData_dict_in_script(script_path: str) -> Dict[str, AstFuncData]:
    """
    Extracts function data using AST from a single Python script and returns it as a dictionary.

    Args:
        script_path (str): The path to the Python script file.

    Returns:
        Dict[str, AstFuncData]: A dictionary mapping function names to their corresponding AstFuncData objects.

    Examples:
        ```python
        func_data_dict = get_astFuncData_dict_in_script("/path/to/script.py")
        print(func_data_dict)  # Output: {'function_name': AstFuncData_object}
        ```

        ```python
        func_data_dict = get_astFuncData_dict_in_script("/another/path/to/script.py")
        print(func_data_dict)  # Output: {'another_function_name': AstFuncData_object}
        ```
    """
    astFuncData_list = get_astFuncData_list_in_script(script_path)
    return {func_data.function_name: func_data for func_data in astFuncData_list}


def get_astFuncData_list_in_dir(directory: str) -> List[AstFuncData]:
    """
    Extracts function data using ast from all Python scripts in the specified directory.

    Args:
        directory (str): The directory to search for Python scripts.

    Returns:
        List[AstFuncData]: A list of AstFuncData objects containing function metadata.

    Examples:
        ```python
        data_list = get_astFuncData_list_in_dir("/path/to/scripts")
        print(data_list)  # Output: List of AstFuncData objects for each function found.
        ```

        ```python
        data_list = get_astFuncData_list_in_dir("/another/path")
        print(data_list)  # Output: List of AstFuncData objects for functions in another directory.
        ```
    """
    astFuncData_list = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if not file.endswith(".py"):
                continue

            file_path = os.path.join(root, file)
            with open(file_path, "r", encoding="utf-8") as f:
                script_content = f.read()

            try:
                node = ast.parse(script_content)
                for n in ast.walk(node):
                    if not isinstance(n, ast.FunctionDef):
                        continue

                    source_code = astunparse.unparse(n)
                    function_call_list = get_function_call_list_from_source_code(
                        source_code=source_code,
                        is_no_duplicated=True,
                    )
                    astFuncData_list.append(
                        AstFuncData(
                            function_name=n.name,
                            script_path=file_path,
                            source_code=source_code,
                            func_call_list=function_call_list,
                        )
                    )
            except SyntaxError:
                print(f"Syntax error in {file_path}")

    return astFuncData_list


from tomsze_utils.dict_utils import transform_by_relate_dict_keys


def get_refactored_astFuncData_list_in_dir(directory: str) -> List[AstFuncData]:
    """
    Refactors the function call lists of AstFuncData objects in a given directory.

    This function retrieves AstFuncData objects, transforms their function call relationships,
    and updates the function call lists accordingly.

    Args:
        directory (str): The directory path containing Python scripts to analyze.

    Returns:
        List[AstFuncData]: A list of AstFuncData objects with updated function call lists.

    Examples:
        ```python
        data_list = get_refactored_astFuncData_list_in_dir("/path/to/scripts")
        print(data_list)  # Output: List of AstFuncData objects with updated function calls.
        ```

        ```python
        data_list = get_refactored_astFuncData_list_in_dir("/another/path")
        print(data_list)  # Output: List of AstFuncData objects for functions in another directory.
        ```
    """
    ast_func_data_list = get_astFuncData_list_in_dir(directory)

    function_name_call_list_dict = {}
    for ast_func_data in ast_func_data_list:
        function_name_call_list_dict[ast_func_data.function_name] = (
            ast_func_data.func_call_list
        )

    transformed_function_name_call_list_dict = transform_by_relate_dict_keys(
        function_name_call_list_dict
    )
    for dict_key in transformed_function_name_call_list_dict.keys():
        for ast_func_data in ast_func_data_list:
            if ast_func_data.function_name == dict_key:
                ast_func_data.func_call_list = transformed_function_name_call_list_dict[
                    dict_key
                ]
                break

    # Also fill in the called_by_func_list here
    for ast_func_data in ast_func_data_list:
        for ast_func_data_2 in ast_func_data_list:
            if ast_func_data.function_name in ast_func_data_2.func_call_list:
                ast_func_data.called_by_func_list.append(ast_func_data_2.function_name)

    return ast_func_data_list


def get_refactored_astFuncData_dict_in_dir(directory: str) -> Dict[str, AstFuncData]:
    """
    Retrieves a dictionary of AstFuncData objects with their function names as keys in a directory.

    This function calls `get_refactored_astFuncData_list_in_dir` to obtain a list of AstFuncData
    objects and then constructs a dictionary mapping function names to their corresponding AstFuncData.

    Args:
        directory (str): The directory path containing Python scripts to analyze.

    Returns:
        Dict[str, AstFuncData]: A dictionary where keys are function names and values are AstFuncData objects.

    Examples:
        ```python
        data_dict = get_refactored_astFuncData_dict_in_dir("/path/to/scripts")
        print(data_dict)  # Output: Dictionary of AstFuncData objects keyed by function names.
        ```

        ```python
        data_dict = get_refactored_astFuncData_dict_in_dir("/another/path")
        print(data_dict)  # Output: Dictionary of AstFuncData objects for functions in another directory.
        ```
    """
    ast_func_data_list = get_refactored_astFuncData_list_in_dir(directory)

    ast_func_data_dict = {}
    for ast_func_data in ast_func_data_list:
        ast_func_data_dict[ast_func_data.function_name] = ast_func_data

    return ast_func_data_dict


def get_refactored_astFuncData_dict_in_dir_list(
    directory_list: List[str],
) -> Dict[str, AstFuncData]:
    """
    Retrieves a dictionary of AstFuncData objects with their function names as keys from a list of directories.

    This function iterates through each directory in the provided list and calls
    `get_refactored_astFuncData_dict_in_dir` to obtain function data.

    Args:
        directory_list (List[str]): A list of directory paths containing Python scripts to analyze.

    Returns:
        Dict[str, AstFuncData]: A dictionary where keys are function names and values are AstFuncData objects.

    Examples:
        ```python
        data_dict = get_refactored_astFuncData_dict_in_dir_list(["/path/to/scripts", "/another/path"])
        print(data_dict)  # Output: Dictionary of AstFuncData objects from both directories.
        ```

        ```python
        data_dict = get_refactored_astFuncData_dict_in_dir_list(["/single/directory"])
        print(data_dict)  # Output: Dictionary of AstFuncData objects from the single directory.
        ```
    """
    ast_func_data_dict = {}
    for directory in directory_list:
        ast_func_data_dict.update(get_refactored_astFuncData_dict_in_dir(directory))

    return ast_func_data_dict


def main():
    # try extract_function_names_from_script
    script_path = os.path.realpath("./tomsze_utils/script_analyse_utils.py")
    if os.path.exists(script_path):
        functionMeta_list = extract_function_names_from_script(script_path)

    # try extract_function_names_from_script_in_dir
    script_dir_path = os.path.realpath("./tests/fake_test_script")
    if os.path.exists(script_path):
        functionMeta_list = extract_function_names_from_script_in_dir(script_dir_path)

    g = 1


if __name__ == "__main__":
    sys.exit(main())
