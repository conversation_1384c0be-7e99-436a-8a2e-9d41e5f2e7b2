package com.kewtoms.whatappsdo.ui.home;

import static com.kewtoms.whatappsdo.data.Constants.post_req_url_check_client_app_link;
import static com.kewtoms.whatappsdo.data.Constants.post_req_url_usa_link;

import android.app.AlertDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kewtoms.whatappsdo.R;
import com.kewtoms.whatappsdo.adapter.CardIconTextRecyclerViewAdapter;
import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.ScanAppData;
import com.kewtoms.whatappsdo.data.ScrapeData;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ErrorMessages;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ResponseKeys;
import com.kewtoms.whatappsdo.databinding.FragmentHomeBinding;
import com.kewtoms.whatappsdo.interfaces.AppSearcherCallback;
import com.kewtoms.whatappsdo.interfaces.PostResponseCallback;
import com.kewtoms.whatappsdo.interfaces.ScanCallback;
import com.kewtoms.whatappsdo.model.AppSearcher.AppSearcher;
import com.kewtoms.whatappsdo.model.UserUsage;
import com.kewtoms.whatappsdo.model.auth.Authenticator;
import com.kewtoms.whatappsdo.model.scanApps.AppScanner;
import com.kewtoms.whatappsdo.model.scrapeApps.AppScraper;
import com.kewtoms.whatappsdo.utils.CodeRunManager;
import com.kewtoms.whatappsdo.utils.RequestUtils;
import com.tomsze.APIUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.List;
import java.util.concurrent.Future;

public class HomeFragment
  extends Fragment {

  public static final String TAG = "APP:HomeFragment";
  HomeViewModel homeViewModel;
  ScanAppData scanAppData;
  private FragmentHomeBinding binding;
  private List<ScrapeData> listScrapeData;
  private List<String> appsNeedScrapeListStr;
  private String scrapeCacheDirPath;
  private Configuration config = Configuration.getInstance();

  public View onCreateView(
    @NonNull LayoutInflater inflater,
    ViewGroup container,
    Bundle savedInstanceState) {

    String methodName = "onCreateView: ";

    Log.d(
      TAG,
      methodName + "run"
    );

    homeViewModel =
      new ViewModelProvider(this).get(HomeViewModel.class);

    binding = FragmentHomeBinding.inflate(
      inflater,
      container,
      false
    );
    View root = binding.getRoot();

    final TextView textView = binding.textHome;
    UserUsage userUsage = new UserUsage(requireActivity());


    // Observe LiveData
    homeViewModel.getText().observe(
      getViewLifecycleOwner(),
      textView::setText
    );
    homeViewModel.getSearchText().observe(
      getViewLifecycleOwner(),
      binding.searchField::setText
    );
    homeViewModel.getProgress().observe(
      getViewLifecycleOwner(),
      binding.progressBar::setProgress
    );
    homeViewModel.getEnable().observe(
      getViewLifecycleOwner(),
      binding.searchField::setEnabled
    );
    homeViewModel.getEnable().observe(
      getViewLifecycleOwner(),
      binding.buttonSearch::setEnabled
    );
    homeViewModel.getRVAdapter().observe(
      getViewLifecycleOwner(),
      new Observer<CardIconTextRecyclerViewAdapter>() {

        /**
         * This method is called when the data changes, updating the RecyclerView adapter and setting a click event listener.
         * @param cardIconTextRecyclerViewAdapter The updated instance of the RecyclerView adapter.
         */
        @Override
        public void onChanged(CardIconTextRecyclerViewAdapter cardIconTextRecyclerViewAdapter) {

          // Set a new listener to handle click events on RecyclerView items.
          cardIconTextRecyclerViewAdapter.setListener(new CardIconTextRecyclerViewAdapter.Listener() {
            /**
             * This method is called when an item in the RecyclerView is clicked.
             * @param position The position of the clicked item.
             */
            public void onClick(int position) {
              // Log the position of the click event.
              Log.i(
                TAG,
                "onClick: " + position
              );

              try {
                // Get the package name corresponding to the clicked item.
                String packageNameClicked =
                  scanAppData.getPackageNames().get(position);
                // Get the PackageManager object to obtain the launch Intent for the application.
                PackageManager packageManager =
                  requireActivity().getPackageManager();
                // Obtain the launch Intent for the clicked item.
                Intent intent =
                  packageManager.getLaunchIntentForPackage(packageNameClicked);
                // If the Intent is not null, start the application.
                if (intent != null) {
                  startActivity(intent);
                }
              } catch (Exception e) {
                // If an exception occurs, display a Toast with the exception message.
                Toast toast = Toast.makeText(
                  requireActivity(),
                  e.toString(),
                  Toast.LENGTH_LONG
                );
                toast.show();
              }

            }
          });
          // Set the adapter for the RecyclerView.
          binding.idAppIconTextRecycler.setAdapter(cardIconTextRecyclerViewAdapter);
          // Set the layout manager for the RecyclerView to be a horizontal LinearLayoutManager.
          RecyclerView.LayoutManager layoutManager =
            new LinearLayoutManager(
              getActivity(),
              LinearLayoutManager.HORIZONTAL,
              false
            );
          binding.idAppIconTextRecycler.setLayoutManager(layoutManager);
        }

      }
    );

    // Set searchField on editor action.
    binding.searchField.setOnEditorActionListener((v, actionId, event) -> {
      if (actionId == EditorInfo.IME_ACTION_SEARCH) {
        binding.buttonSearch.performClick();
        return true;
      }
      return false;
    });

    // Set button on click.
    binding.buttonSearch.setOnClickListener(v -> {
      Log.i(
        TAG,
        "onCreateView: searchButton onClick"
      );
      // Check user usage count and prompt registration if necessary.
      Log.i(
        TAG,
        "onCreateView: userUsageCount: " + userUsage.getUsageCount()
      );
      int usageCount = userUsage.getUsageCount();
      if (usageCount > Constants.FREE_USAGE_COUNT_MONTHLY) {
        userUsage.promptRegistration();
        return;
      }

      // In test mode, simulated a search click.
      if (config.isTest()) {
        Log.i(
          TAG,
          "In test mode, simulated a search click."
        );
        userUsage.increaseUsageCount();
        // Hide soft keyboard.
        WindowCompat.getInsetsController(
          requireActivity().getWindow(),
          binding.searchField
        ).hide(WindowInsetsCompat.Type.ime());
        return;
      }

      // Search app and update RecyclerView
      AppSearcherCallback callback =
        (newAppNames, newPackageNames, newImagePaths) -> {
          PackageManager pm = requireActivity().getPackageManager();
          updateThisRecyclerViewAdapter(
            pm,
            binding.idAppIconTextRecycler,
            newAppNames,
            newPackageNames,
            newImagePaths
          );
        };
      AppSearcher appSearcher = new AppSearcher(
        requireActivity(),
        callback
      );

      String searchText = binding.searchField.getText().toString();

      try {
        appSearcher.searchApp(
          searchText,
          scanAppData
        );
      } catch (InterruptedException e) {
        Log.e(
          TAG,
          e.toString()
        );
        throw new RuntimeException(e);
      }

      userUsage.increaseUsageCount();

      // Hide soft keyboard.
      WindowCompat.getInsetsController(
        requireActivity().getWindow(),
        binding.searchField
      ).hide(WindowInsetsCompat.Type.ime());
      Log.i(
        TAG,
        "Hided soft keyboard"
      );
    });

    // Check and cache package names and icons);
    runThreadCheckAndCachePackagesRelated();

    // Silent login (auto login)
    if (config.isProd()) {
      silentSignIn();
    }


    // Test buttons
    // TODO move to Gallery Fragment
    binding.testClearCacheButton.setOnClickListener(createTestClearCacheButtonOnClickListener());

    Log.d(
      TAG,
      methodName + "done"
    );

    return root;
  }

  private void silentSignIn() {
    String methodName = "silentSignIn";

    Log.i(
      TAG,
      methodName + ": run"
    );

    Authenticator authenticator =
      new Authenticator(requireActivity());
    try {
      authenticator.silentSignIn(
        new PostResponseCallback() {
          @Override
          public void onPostSuccess(JSONObject responseJsonObj) {
            String callMethodName = "authenticator.silentSignin";
            Log.d(
              TAG,
              methodName + " calls" + callMethodName + ": " + "outer callback message: " + "onPostSuccess run"
            );

            String message;
            // Updated to use new API response parsing method
            message = RequestUtils.getApiResponseMessage(
              requireActivity(),
              responseJsonObj
            );

            //  If the message contains "Expired", refresh token expired.
            if (message.equals(ErrorMessages.TOKEN_EXPIRED)) {
              Log.i(
                TAG,
                "Refresh token expired."
              );

              // Show a popup dialog to prompt user to login again.
              new AlertDialog.Builder(requireActivity()).setTitle(getString(R.string.long_time_not_login_title))
                .setMessage(getString(R.string.long_time_not_login_text))
                .setPositiveButton(
                  getString(R.string.ok_text),
                  (dialog, which) -> {

                    // Navigate to login fragment
                    NavController navController =
                      Navigation.findNavController(binding.getRoot());
                    navController.navigate(R.id.action_nav_home_to_nav_login);

                    dialog.dismiss();
                  }
                )
                .setCancelable(false)
                .create()
                .show();
            }


            Log.d(
              TAG,
              methodName + " calls" + callMethodName + ": " + "outer callback message: " + "onPostSuccess done"
            );
          }

          @Override
          public void onPostError(String errorMessage) {
            Log.i(
              TAG,
              "Post Error"
            );

          }
        },
        true
      );
    } catch (InterruptedException e) {
      Log.d(
        TAG,
        methodName + ": " + "InterruptedException: " + e
      );
      throw new RuntimeException(e);
    }

    Log.d(
      TAG,
      methodName + ": done"
    );
  }


  @NonNull
  private View.OnClickListener createTestClearCacheButtonOnClickListener() {
    return v -> {
      Log.i(
        TAG,
        "clearCache: "
      );

      try {
        File iconCacheDirFile = requireActivity().getCacheDir();
        iconCacheDirFile = new File(
          iconCacheDirFile,
          "icons"
        );

        File[] files = iconCacheDirFile.listFiles();
        if (files != null) {
          for (File file : files) {

            Log.i(
              TAG,
              "clearCache: deleting " + file.getName()
            );
            file.delete(); // Delete the file
          }
        }
      } catch (Exception e) {
        Log.e(
          TAG,
          "clearCache: " + e.getMessage()
        );

      }
    };
  }


  private void updateThisRecyclerViewAdapter(
    PackageManager packageManager,
    RecyclerView recyclerView,
    List<String> newAppNames,
    List<String> newPackageNames,
    List<String> newImagePaths) {
    CardIconTextRecyclerViewAdapter adapter =
      new CardIconTextRecyclerViewAdapter(
        newAppNames.toArray(new String[0]),
        newImagePaths.toArray(new String[0])
      );
    adapter.setListener(position -> {
      Log.i(
        TAG,
        "onClick: " + position
      );
      String packageNameClicked = newPackageNames.get(position);
      Intent intent =
        packageManager.getLaunchIntentForPackage(packageNameClicked);
      if (intent != null) {
        startActivity(intent);
      }
    });
    recyclerView.setAdapter(adapter);
  }

  private void runThreadCheckAndCachePackagesRelated() {
    // Create a string for this method name runThreadCheckAndCachePackagesRelated
    String methodName = "runThreadCheckAndCachePackagesRelated";

    Log.i(
      TAG,
      methodName + ": run"
    );

    boolean isEnabled = CodeRunManager.getInstance()
      .isEnabled(HomeFragment.class.getSimpleName() + "." + methodName);

    if (!isEnabled) {
      Log.i(
        TAG,
        methodName + ": disabled"
      );
      Log.i(
        TAG,
        methodName + ": done"
      );
      return;
    }

    // Create another thread to wait for the future.
    new Thread(() -> {
      Looper.prepare();

      // Set up cache dir path.
      File scrapeCacheDir = new File(
        requireActivity().getCacheDir(),
        "scrape"
      );
      scrapeCacheDirPath = scrapeCacheDir.toString();

      // Create cache dir if not exists.
      if (!scrapeCacheDir.exists()) {
        boolean success = scrapeCacheDir.mkdirs();
        if (success) {
          Log.i(
            TAG,
            methodName + ": Cache directories created successfully"
          );
        } else {
          Log.e(
            TAG,
            methodName + ": Failed to create directories"
          );
        }
      } else {
        Log.i(
          TAG,
          methodName + ": Cache directory already exists"
        );
      }


      try {
        String isServerOnlineUrl =
          RequestUtils.getMockUrlFromTestIntentOrProdUrl(
            getContext(),
            Constants.mockGetIsServerOnlineUrlKey,
            Constants.get_is_server_online_link
          );
        Log.i(
          TAG,
          "sending get request to get_is_server_online_link: " + isServerOnlineUrl
        );

        JSONObject sysDatetimeDict =
          APIUtils.apiGet(isServerOnlineUrl);

        // Directly set server is online if the above runs (correct)
        config.setServerStatus(Configuration.ServerStatus.ONLINE);
      } catch (java.net.SocketTimeoutException e) {
        Log.e(
          TAG,
          ": Socket timeout occurred: " + e.getMessage()
        );

        Log.e(
          TAG,
          ": Server is offline"
        );
        Toast.makeText(
          requireActivity(),
          "Server connection timed out",
          Toast.LENGTH_LONG
        ).show();
        // Handle the timeout scenario appropriately, such as retrying or notifying the user

        // Set server is offline
        config.setServerStatus(Configuration.ServerStatus.OFFLINE);
      } catch (Exception e) {
        Log.e(
          TAG,
          ": Unexpected exception occurred: " + e.getMessage()
        );
        throw new RuntimeException(e);
      }


      try {
        // Scan apps and cache icons.
        ScanCallback scanCallback = createScanCallback();
        AppScanner appScanner = new AppScanner(
          requireActivity(),
          scanCallback
        );
        Future<ScanAppData> future = appScanner.scan();

        // This will block until the result is available.
        scanAppData =
          future.get(); // Note: can also do like checkAppNeedScrape.

        if (config.isServerOnline()) {
          // Ask server which apps need scraping.
          AppScraper appScraper = new AppScraper();

          appsNeedScrapeListStr = appScraper.checkAppNeedScrape(
            requireActivity(),
            post_req_url_check_client_app_link,
            scanAppData
          );

          // Scrape apps.
          listScrapeData = appScraper.scrapeWhenNeeded(
            scrapeCacheDirPath,
            appsNeedScrapeListStr
          );

          // Send the scraped apps to the server.
          appScraper.sendScrapeData(
            requireActivity(),
            post_req_url_usa_link,
            listScrapeData
          );
        } else {
          binding.buttonSearch.setText(requireActivity().getString(R.string.local_search_button_name));
        }


        Log.i(
          TAG,
          methodName + ": done"
        );


      } catch (Exception e) {
        Log.e(
          TAG,
          methodName + ": Exception: " + e
        );
        Toast toast = Toast.makeText(
          requireActivity(),
          e.toString(),
          Toast.LENGTH_LONG
        );
        toast.show();
      }
    }).start();
  }

  @NonNull
  private ScanCallback createScanCallback() {
    return new ScanCallback() {
      @Override
      public void onScanStart() {
        homeViewModel.setEnable(false);
      }

      @Override
      public void onScanning(int progress) {
        Log.i(
          TAG,
          "onScanning: " + Thread.currentThread().getName()
        );
        try {
          Thread.sleep(10);    // ms
        } catch (InterruptedException e) {
          throw new RuntimeException(e);
        }
        homeViewModel.setProgress(progress);
      }

      @Override
      public void onScanDone(ScanAppData scanAppData1) {
        homeViewModel.setProgress(100);
        homeViewModel.setEnable(true);

        homeViewModel.setRVAdapter(new CardIconTextRecyclerViewAdapter(
          scanAppData1.getAppNames().toArray(new String[0]),
          scanAppData1.getImagePaths().toArray(new String[0])
        ));


        // Focus to search field and show soft keyboard.
        requireActivity().runOnUiThread(() -> {
          binding.searchField.requestFocus();
          WindowCompat.getInsetsController(
            requireActivity().getWindow(),
            binding.searchField
          ).show(WindowInsetsCompat.Type.ime());
        });
      }
    };
  }


  @Override
  public void onDestroyView() {
    super.onDestroyView();
    binding = null;
  }


}