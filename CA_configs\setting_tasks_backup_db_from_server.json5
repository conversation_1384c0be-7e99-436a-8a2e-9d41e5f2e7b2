{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "read_env",
            "get_current_dateime",
            "set_pickle_db_zip_path",
            "zip_db_dir",
            "set_download_folder_path",
            "create_download_folder",
            "download_db_zip",
            "remove_db_zip_in_server",
        ],
        "variables":{
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "read_env",
            "type": "PluginEnvReader",
            "use": true,
            "use_env_path": true,
            "env_path": "./.env",
        },
        {
            "step_name": "get_current_dateime",
            "type": "PluginDatetimer",
            "use": true,
            "datetime_format":"%Y-%m-%d_%H-%M-%S",
            "datetime_buffer_to":"datetime",
        },
        {
            "step_name": "set_pickle_db_zip_path",
            "type": "PluginVariableAssigner",
            "use": true,
            "pickle_db_zip_filename":"pickle_db_{get_current_dateime.datetime}.zip",
            "pickle_db_zip_path":"/root/code/tmp/pickle_db_{get_current_dateime.datetime}.zip",
        },
        {
            "step_name": "zip_db_dir",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
cd /root/code/{read_env.REPO}/; \
zip -r {pickle_db_zip_path} ./pickle_db/;",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "set_download_folder_path",
            "type": "PluginVariableAssigner",
            "use": true,
            "download_folder_path":"D:\\code\\download_from_server"
        },
        {
            "step_name": "create_download_folder",
            "type": "PluginCommandRunner",
            "use": true,
            "command":'mkdir -p {download_folder_path}'
        },
        {
            "step_name": "download_db_zip",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
scp -P {read_env.SSH_PORT} {read_env.SSH_USER}@{read_env.SSH_HOST}:{pickle_db_zip_path} {download_folder_path}/{pickle_db_zip_filename}",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "remove_db_zip_in_server",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
rm {pickle_db_zip_path};",
            "output_str_buffer_to": "output_str",
        },
       
    ]
   

    

}
