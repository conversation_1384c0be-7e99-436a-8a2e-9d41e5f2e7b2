import sys
import os
from tqdm import tqdm
import datetime
import shutil
from const_path import proj_ser_tools_path, proj_ser_api_db_apps_path
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
)
from tomsze_utils.scraping_utils import scrape_apkgk, scrape_apksos, scrape_apksupport
from server.api.end_to_end.api_constant_keys import DBAppsColEnum
from server.api.end_to_end.utils import ppd_emb_kw_kwpp_update_using_keys

from server.api.utils.descriptions.get_description import scrape_play_store
from sentence_transformers import SentenceTransformer

from const_model_settings import (
    max_ngram_size,
    deduplication_threshold,
    max_num_keywords,
    model_id,
)

"""
To rebuild db_apps
"""


def rebuild_db_apps(
    db_apps_folder_path,
    db_apps_output_folder_path,
    db_apps_backup_zip_path,
    model_id: str = "multi-qa-MiniLM-L6-cos-v1",
):
    embedder = SentenceTransformer(model_id)

    # Backup the db_apps folder first.
    shutil.make_archive(db_apps_backup_zip_path, "zip", db_apps_folder_path)

    # Load the db_apps from folder
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        dump_db_fpath=db_apps_output_folder_path,
        load_by_thread=False,
    )

    # Remove all keys data.
    db_apps.remove_all_keys_data()

    # For each app_id in original db_apps.
    app_id_list = db_apps.get_db_keys()
    for app_id in tqdm(app_id_list):
        # Scrape from playstore.
        scrap_success, result = scrape_play_store(device_app_id=app_id)

        # Try scrape on more sites.
        if not scrap_success:
            scrap_success, result = scrape_apkgk(app_id=app_id)

        if not scrap_success:
            scrap_success, result = scrape_apksos(app_id=app_id)

        if not scrap_success:
            scrap_success, result = scrape_apksupport(app_id=app_id)

        if scrap_success:
            ppd_emb_kw_kwpp_update_using_keys(
                app_id=app_id,
                db_apps=db_apps,
                scrape_result=result,
                embedder=embedder,
                deduplication_threshold=deduplication_threshold,
                max_ngram_size=max_ngram_size,
                max_num_keywords=max_num_keywords,
            )
    # End of for loop.

    # Remove old pickles.
    db_apps.remove_pickles()

    # Dump db_apps (all parts) to folder.
    db_apps.dump_all_parts_to_pickles()


def run_rebuild_db_apps():
    db_apps_folder_path = proj_ser_api_db_apps_path
    db_apps_output_folder_path = proj_ser_api_db_apps_path

    # Create backup folder.
    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    db_apps_backup_folder_path = os.path.join(
        proj_ser_tools_path,
        "run_rebuild_db_apps_backup_zip_folder",
    )
    if not os.path.exists(db_apps_backup_folder_path):
        os.mkdir(db_apps_backup_folder_path)

    db_apps_backup_zip_path = os.path.join(
        db_apps_backup_folder_path,
        f"db_apps_{time_str}",
    )

    rebuild_db_apps(
        db_apps_folder_path=db_apps_folder_path,
        db_apps_output_folder_path=db_apps_output_folder_path,
        db_apps_backup_zip_path=db_apps_backup_zip_path,
        model_id=model_id,
    )


def main():
    # Run function
    run_rebuild_db_apps()


if __name__ == "__main__":
    sys.exit(main())
