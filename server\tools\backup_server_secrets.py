from tomsze_utils.configurable_algorithm.configurable_algorithm import CA
from tomsze_utils.security_utils import detect_frequent_ips_in_folder


def detect_frequent_ips_in_folder_wrapped(
    folder_path,
    datetime_format,
):
    scan_result = detect_frequent_ips_in_folder(
        folder_path=folder_path,
        datetime_format=datetime_format,
    )
    return scan_result


def run():

    config_folder_path = r"./CA_configs"
    ca = CA(
        config_folder_path=config_folder_path,
        algo_setting_filename="setting_tasks_backup_secrets.json5",
    )
    ca.run()

    dict_var = ca.data_obj.dict_var


if __name__ == "__main__":
    run()
