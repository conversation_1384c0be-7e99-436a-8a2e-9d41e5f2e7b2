{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "list_directory_files",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "list_directory_files",
            "type": "PluginDirectoryFilesLister",
            "use": true,
            "directory_path": "./tests/test_directory_for_listing",
            "file_type_list": [
                '.txt',
                '.abc',
            ],
        }
    ]
   

}
