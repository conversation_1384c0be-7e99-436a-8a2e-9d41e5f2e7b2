import re
import time
from dotenv import find_dotenv, load_dotenv
from tqdm import tqdm


# TODO check repeat
def timeit(method):
    def timed(*args, **kw):
        ts = time.time()
        result = method(*args, **kw)
        te = time.time()
        if "log_time" in kw:
            name = kw.get("log_name", method.__name__.upper())
            kw["log_time"][name] = int((te - ts) * 1000)
        else:
            print("%r  %2.2f ms" % (method.__name__, (te - ts) * 1000))
        return result

    return timed


def remove_emoji(text):
    """
    Given:

    text = "This string contains an emoji 😀"

    Return:

    text_correct = "This string contains an emoji "

    """
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # emoticons
        "\U0001F300-\U0001F5FF"  # symbols & pictographs
        "\U0001F680-\U0001F6FF"  # transport & map symbols
        "\U0001F1E0-\U0001F1FF"  # flags (iOS)
        "]+",
        flags=re.UNICODE,
    )

    clean_text = emoji_pattern.sub(r"", text)

    return clean_text


def has_emoji(text):
    """
    Given:

    text = "This string contains an emoji 😀"

    Return:

    True

    """
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # emoticons
        "\U0001F300-\U0001F5FF"  # symbols & pictographs
        "\U0001F680-\U0001F6FF"  # transport & map symbols
        "\U0001F1E0-\U0001F1FF"  # flags (iOS)
        "]+",
        flags=re.UNICODE,
    )

    list_found = emoji_pattern.findall(text)

    return len(list_found) > 0


def post_process_keywords(list_kw):
    """
    Post process each keyword in a list in following order:

    1. to lower case,
    2. "." change to " ",
    3. " " change to "_",
    4. "xx_yy" split into "xx" and "yy"
    """

    def _post_process_keyword(string):
        string = string.lower().replace(".", " ").strip().replace(" ", "_")
        string = remove_emoji(string)
        return string

    list_keyword = []
    for kw in list_kw:

        if kw not in list_keyword:
            post_kw = _post_process_keyword(kw)
            list_keyword.append(post_kw)

        if "_" in post_kw:
            list_sub_kw = post_kw.split("_")
            for sub_kw in list_sub_kw:
                # post_kw = sub_kw.lower().replace('.', ' ').strip().replace(' ', '_')

                # if sub_kw not in list_keyword:
                #     list_keyword.append(sub_kw)
                list_keyword.append(sub_kw)

    return list_keyword


# TODO check repeat
def is_ascii(s):
    """
    Check if the string contains only ASCII characters in the range -128 to 127
    """
    return all(ord(char) < 128 for char in s)


# TODO check repeat
def extract_keyword_using_yake(
    lang,
    max_ngram_size,
    deduplication_threshold,
    max_num_keywords,
    app_description_en_original,
):
    """
    takes around 30-60 ms for extracting keywords from app description.
    """
    import yake

    kw_extractor = yake.KeywordExtractor(
        lan=lang,
        n=max_ngram_size,
        dedupLim=deduplication_threshold,
        top=max_num_keywords,
        features=None,
    )
    list_keyword_score = kw_extractor.extract_keywords(app_description_en_original)

    list_kw = []
    for keywords_score in list_keyword_score:
        keyword_underscore = keywords_score[0]
        # keyword_underscore = keywords_score[0].replace('.', ' ').strip().replace(' ', '_').lower()
        list_kw.append(keyword_underscore)

    return list_kw


def clean_keywords(
    app_keyword_pickle_path: dict[str, list],
):
    """
    Given:
    dict_keyword = {
        'com.x.y': ['galavisión', 'game_fun'],
        'com.a.b': ['hi', 'hello'],
        'com.c.d': ['hi-there'],
        'com.e.f': ['があります', 'good'],
        'com.g.h': ['😀'],
    }

    Return:
    dict_keyword_correct = {
        'com.x.y': ['game_fun', 'game', 'fun'],
        'com.a.b': ['hi', 'hello'],
        'com.c.d': ['hi-there'],
        'com.e.f': ['good'],
        'com.g.h': [],
    }


    Properties:
    1. This method will not remove any keys in the dict.
    2. Words like xxx_yyy will be splited into xxx and yyy and add to the value.
    3. Non-ascii word will be removed like galavisión and があります.
    4. Emoji will be removed like 😀.

    Examples:
    "ремесленник"
    "tiên"
    "let’s"
    "حرب"
    "𝐃𝐄𝐕𝐄𝐋𝐎𝐏𝐌𝐄𝐍𝐓"

    """
    dict_app_keyword = load_pickle_file(app_keyword_pickle_path, {})

    list_app = list(dict_app_keyword.keys())
    for app_id in tqdm(list_app):
        list_keyword = dict_app_keyword[app_id]
        assert isinstance(list_keyword, list)
        list_keyword_temp = list_keyword.copy()

        for keyword in list_keyword:
            # Remove keyword if keyword is not ascii.
            if not is_ascii(keyword):
                print(f'"{keyword}" is not ascii, remove.')
                list_keyword_temp.remove(keyword)
                dict_app_keyword[app_id] = list_keyword_temp

            # Remove if words after split by _ is not in [dict_app_keyword]
            if "_" in keyword:
                list_split_word = keyword.split("_")
                for split_word in list_split_word:
                    if not split_word in list_keyword_temp:
                        print(f'"{split_word}" from "{keyword}" is not in dict, add.')
                        list_keyword_temp.append(split_word)
                        dict_app_keyword[app_id] = list_keyword_temp

            # Remove keyword if keyword is emoji.
            if has_emoji(keyword):
                print(f'"{keyword}" is emoji, remove.')
                if keyword in list_keyword_temp:
                    list_keyword_temp.remove(keyword)
                    dict_app_keyword[app_id] = list_keyword_temp

    dump_variable_to_pickle(app_keyword_pickle_path, dict_app_keyword)


def test_clean_keywords():
    app_keyword_pickle_path = r"./utils/keywords/test_clean_keywords.pickle"
    dict_keyword = {
        "com.x.y": ["galavisión", "game_fun"],
        "com.a.b": ["hi", "hello"],
        "com.c.d": ["hi-there"],
        "com.e.f": ["があります", "good"],
        "com.g.h": ["😀"],
    }
    dump_variable_to_pickle(app_keyword_pickle_path, dict_keyword)

    # Clean.
    clean_keywords(app_keyword_pickle_path)

    # Check cleaned result.
    dict_keyword_cleaned = load_pickle_file(app_keyword_pickle_path, {})
    dict_keyword_correct = {
        "com.x.y": ["game_fun", "game", "fun"],
        "com.a.b": ["hi", "hello"],
        "com.c.d": ["hi-there"],
        "com.e.f": ["good"],
        "com.g.h": [],
    }
    assert dict_keyword_cleaned == dict_keyword_correct
    assert len(dict_keyword_cleaned) == len(
        dict_keyword
    )  # Check no appid keys are removed.

    print("test_clean_keywords done")


def run_clean_keywords():
    app_keyword_pickle_path = (
        r"./api/pythonanywhere/my_site/dataset/android/004_keywords.pickle"
    )

    # Clean.
    clean_keywords(app_keyword_pickle_path)

    print("app_keyword_pickle_path done")


def store_app_keywords_from_app_description(
    app_description_pickle_path,
    app_keyword_pickle_path,
    language="en",
    max_ngram_size=2,
    deduplication_threshold=0.9,
    max_num_keywords=20,
):
    dict_app_description = load_pickle_file(app_description_pickle_path, {})
    dict_app_keyword = load_pickle_file(app_keyword_pickle_path, {})
    num_before = len(dict_app_keyword)

    for app_id in tqdm(dict_app_description.keys()):
        app_description = dict_app_description[app_id]

        # Extract keywords.
        list_kw = extract_keyword_using_yake(
            lang=language,
            max_ngram_size=max_ngram_size,
            deduplication_threshold=deduplication_threshold,
            max_num_keywords=max_num_keywords,
            app_description_en_original=app_description,
        )

        list_kw = post_process_keywords(list_kw)

        # Dump to pickle.
        dict_app_keyword[app_id] = list_kw

    dump_variable_to_pickle(app_keyword_pickle_path, dict_app_keyword)

    num_after = len(dict_app_keyword)
    return num_before, num_after


def test_store_app_keywords_from_app_description():
    app_description_pickle_path = r"./utils/keywords/test_store_app_keywords_from_app_description_Description.pickle"
    app_keyword_pickle_path = (
        r"./utils/keywords/test_store_app_keywords_from_app_description_Keywords.pickle"
    )

    # Create test pickle files.
    dict_app_description = {
        "com.a.b": "Hi I am David",
        "com.c.d": "Hi I am Mary",
        "com.e.f": "Hi I am John",
    }

    dict_app_keyword = {
        "com.a.b": ["david"],
        "com.c.d": ["mary"],
    }
    dump_variable_to_pickle(app_description_pickle_path, dict_app_description)
    dump_variable_to_pickle(app_keyword_pickle_path, dict_app_keyword)

    # Main function.
    store_app_keywords_from_app_description(
        app_description_pickle_path,
        app_keyword_pickle_path,
    )

    # Check.
    dict_app_keyword_updated = load_pickle_file(app_keyword_pickle_path, {})
    dict_app_keyword_correct = {
        "com.a.b": ["david"],
        "com.c.d": ["mary"],
        "com.e.f": ["john"],
    }

    assert dict_app_keyword_updated == dict_app_keyword_correct

    print("test_store_app_keywords_from_app_description done")


def inspect_dict_id_and_keyword_same_len():
    app_id_pickle_file = r"./api/pythonanywhere/my_site/dataset/android/004_dict_ids_all-MiniLM-L6-v2.pickle"
    app_id_keywords_pickle_file = (
        r"./api/pythonanywhere/my_site/dataset/android/004_keywords.pickle"
    )

    dict_id = load_pickle_file(app_id_pickle_file, {})
    dict_id_keywords = load_pickle_file(app_id_keywords_pickle_file, {})

    assert len(dict_id) > 0
    assert len(dict_id) == len(dict_id_keywords)

    print(f"dict_id len: {len(dict_id)}")
    print(f"dict_id_keywords len: {len(dict_id_keywords)}")
    print("inspect_dict_id_and_keyword done")


def inspect_dict_id_and_description_same_len():
    app_id_pickle_file = r"./api/pythonanywhere/my_site/dataset/android/004_dict_ids_all-MiniLM-L6-v2.pickle"
    app_id_description_pickle_file = r"./api/pythonanywhere/my_site/dataset/android/004_descriptions_all-MiniLM-L6-v2.pickle"

    dict_id = load_pickle_file(app_id_pickle_file, {})
    dict_id_description = load_pickle_file(app_id_description_pickle_file, {})

    assert len(dict_id) > 0
    assert len(dict_id) == len(dict_id_description)

    print(f"dict_id len: {len(dict_id)}")
    print(f"dict_id_description len: {len(dict_id_description)}")
    print("inspect_dict_id_and_description_same_len done")


def test_has_emoji():
    text = "This string contains an emoji 😀"
    has = has_emoji(text)

    assert has == True

    text = "Hello"
    has = has_emoji(text)
    assert has == False

    print("test_has_emoji done")


def run_store_app_keywords_from_app_description():
    app_description_pickle_path = r"./api/pythonanywhere/my_site/dataset/android/004_descriptions_all-MiniLM-L6-v2.pickle"
    app_keyword_pickle_path = (
        r"./api/pythonanywhere/my_site/dataset/android/004_keywords.pickle"
    )

    # Main function.
    num_before, num_after = store_app_keywords_from_app_description(
        app_description_pickle_path,
        app_keyword_pickle_path,
    )

    print(f"dict_app_keyword number before:{num_before}, number after: {num_after}")
    print("run_store_app_keywords_from_app_description done")


from inflect import engine


def word_to_singular_plural(
    inflect_engine: engine,
    word,
):
    singluar = inflect_engine.singular_noun(word)  # False if it is already singular
    plural = inflect_engine.plural_noun(word)  # False if it is already plural
    # Note passing any verb to singular_noun or plural

    if not singluar:
        singluar = word
    if not plural:
        plural = word

    return singluar, plural


def lemmatization(nlp_model, text):

    # Parse the text.
    doc = nlp_model(text)

    # Extract the lemma for each token and join
    result = " ".join([token.lemma_ for token in doc])

    return result


# def lemmatization_cache(pickle_data, data_key):
#     return cache(pickle_data, data_key, lemmatization)


def main():
    # Test.
    # test_extract_keyword_using_yake_and_post_process()
    # test_clean_keywords()
    # test_store_app_keywords_from_app_description()
    # test_remove_emoji()
    # test_has_emoji()

    # Run.
    # run_clean_keywords()
    # run_store_app_keywords_from_app_description()

    # Inspect.
    # inspect_dict_id_and_keyword_same_len()
    # inspect_dict_id_and_description_same_len()

    lemmatization()


if __name__ == "__main__":
    main()
