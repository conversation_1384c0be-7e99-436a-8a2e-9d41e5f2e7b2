from tomsze_utils.conda_utils import create_conda_environment


class TestCreateCondaEnvironment:

    def test_create_conda_environment_success(self):
        """Test creating a conda environment successfully."""
        env_name = "test_env"
        python_version = "3.8"

        result, output_str = create_conda_environment(env_name, python_version)

        assert result.returncode == 0  # Check if the command was successful


import tempfile
from tomsze_utils.conda_utils import pip_install_packages_in_conda_env


class TestInstallPackagesInCondaEnv:

    def test_install_packages_success(self):
        """Test installing packages in a conda environment successfully."""
        env_name = "test_env"
        packages = ["numpy"]

        result, output_str = pip_install_packages_in_conda_env(env_name, packages)

        assert result.returncode == 0  # Check if the command was successful
        assert "Successfully installed" in result.stdout.decode(
            "utf-8"
        ) or "satisfied" in result.stdout.decode("utf-8")

    def test_install_packages_failure(self):
        """Test installing packages in a conda environment with a non-existent package."""
        env_name = "test_env"
        packages = ["non_existent_package"]

        result, output_str = pip_install_packages_in_conda_env(env_name, packages)

        assert result.returncode == 0  # Check if the command failed
        assert "No matching" in output_str  # Check for error message in output
