import datetime
import os
import platform
import sys
from googletrans import Translator
import spacy
from tomsze_utils.benchmark_utils import benchmark_funcs, BenchFunc
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
)
from tomsze_utils.encrypt_utils import decrypt_and_convert_to_dict
from functools import partial
from server.api.end_to_end.data_class import ClientSearchData
from server.api.utils.descriptions.get_description import scrape_play_store
from server.api.utils.generate_vectors.utils import (
    convert_to_embedding,
    extract_keyword_using_yake,
    post_process_app_description,
    similarity_search,
    extract_device_app_synonyms_v2,
)
from sentence_transformers import SentenceTransformer
from server.api.utils.keywords.get_keywords import lemmatization, post_process_keywords
from const_path import (
    proj_bench_basic_path,
    proj_ser_api_db_apps_path,
    proj_ser_api_db_synonyms_path,
    proj_ser_api_db_synonyms_need_powerthesaurus_path,
)
from server.api.utils.translation.translate import check_is_eng, translate_to_eng

model_id = "multi-qa-MiniLM-L6-cos-v1"
embedder = SentenceTransformer(model_id)  # TODO change to best embedder

app_description = """testing description testing description 
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
    """
app_description_original = """testing description testing description 
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
testing description testing description testing description testing description
    """
device_app_id = "com.rovio.BadPiggies"
lang = "en"
scrape_success, result = scrape_play_store(
    device_app_id=device_app_id,
    lang=lang,
)
max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20

lemmatization_model = spacy.load("en_core_web_sm", disable=["parser", "ner"])

query_embedding = convert_to_embedding(
    embedder,
    "eat",
    device="cpu",
    batch_size=128,
)

list_device_embedding_tensor = [query_embedding] * 200

translator = Translator()


debug_mode = False
client_search_data = ClientSearchData()
client_search_data.allowSearch = True
client_search_data.appIds.append("com.android.camera2")
client_search_data.appIds.append("com.android.chrome")
client_search_data.appIds.append("com.android.contacts")
client_search_data.appIds.append("com.android.dialer")
client_search_data.appIds.append("com.android.settings")
client_search_data.appIds.append("com.android.vending")
client_search_data.appIds.append("com.google.android.apps.docs")
client_search_data.appIds.append("com.google.android.apps.maps")
client_search_data.appIds.append("com.google.android.apps.messaging")
client_search_data.appIds.append("com.google.android.apps.photos")
client_search_data.appIds.append("com.google.android.apps.tachyon")
client_search_data.appIds.append("com.google.android.apps.youtube.music")
client_search_data.appIds.append("com.google.android.calendar")
client_search_data.appIds.append("com.google.android.deskclock")
client_search_data.appIds.append("com.google.android.gm")
client_search_data.appIds.append("com.google.android.videos")
client_search_data.appIds.append("com.google.android.youtube")
client_search_data.appIds.append("host.exp.exponent")
client_search_data.appIds.append("com.google.android.apps.wallpaper")
client_search_data.appIds.append("com.google.android.documentsui")
client_search_data.appIds.append("com.google.android.googlequicksearchbox")
client_search_data.appIds.append("org.chromium.webview_shell")
client_search_data.appIds.append("com.aigrind.warspear")
client_search_data.appIds.append("com.example.a06constraintlayout")
client_search_data.appIds.append("com.example.android.courtcounter")
client_search_data.appIds.append("com.example.example")
client_search_data.appIds.append("com.example.flutter_scrollable_app_icons_example")
client_search_data.appIds.append("com.example.flutter_scrollable_icons")
client_search_data.appIds.append("com.example.myapplication")
client_search_data.appIds.append("com.example.progressbarexample")
client_search_data.appIds.append("com.example.search_app_by_description")
client_search_data.appIds.append("com.example.test_scrollable_richtext_widget")
client_search_data.appIds.append("com.sankuai.sailor.afooddelivery")
client_search_data.appIds.append("com.sharmadhiraj.installed_apps_example")
client_search_data.appIds.append("com.whatsapp")
client_search_data.appIds.append("io.metamask")
client_search_data.appIds.append("math.puzzle.games.crossmath.number.puzzles.free")

client_search_data.query = "chat"
client_search_data_dict = client_search_data.dict()

db_apps = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_apps_path,
    db_name="db_app",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)
db_synonyms = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_synonyms_path,
    db_name="db_synonyms",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)

db_synonyms_need_powerthesaurus = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_synonyms_need_powerthesaurus_path,
    db_name="db_synonyms_need_powerthesaurus",
    load_by_thread=False,
)


# post_process_app_description(scrape_result)
def partial_post_process_app_description():
    return partial(post_process_app_description, result=result)


def partial_convert_embedding_cpu():
    return partial(
        convert_to_embedding,
        embedder=embedder,
        list_text=app_description,
        device="cpu",
        batch_size=128,
    )


def partial_convert_embedding_cuda():
    return partial(
        convert_to_embedding,
        embedder=embedder,
        list_text=app_description,
        device="cuda",
        batch_size=128,
    )


def partial_extract_keyword():
    return partial(
        extract_keyword_using_yake,
        lang="en",
        max_ngram_size=max_ngram_size,
        deduplication_threshold=deduplication_threshold,
        max_num_keywords=max_num_keywords,
        app_description_en_original=app_description_original,
    )


def partial_post_process_keywords():
    list_kw = [
        "eat",
    ] * 100
    return partial(
        post_process_keywords,
        list_kw=list_kw,
    )


def partial_lemmatization():
    client_search = "eat"
    return partial(
        lemmatization,
        nlp_model=lemmatization_model,
        text=client_search,
    )


def partial_similarity_search():
    return partial(
        similarity_search,
        query_embeddings=query_embedding,
        list_text_embedding=list_device_embedding_tensor,
        top_k=30,
    )


def partial_extract_device_app_synonyms_v2():
    return partial(
        extract_device_app_synonyms_v2,
        list_device_apps=client_search_data.appIds,
        db_apps=db_apps,
        db_synonyms=db_synonyms,
        lemmatization_model=lemmatization_model,
    )


def partial_pickle_db_search_in_1000_data_part_100():
    num_data = 1000
    part_data_count = 100

    # Load db and create the db
    db_apps_folder_path = os.path.join(
        proj_bench_basic_path,
        f"db_apps_{num_data}_data_part_{part_data_count}",
    )
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )

    return partial(db_apps.query_key, key="a simple query key")


def partial_pickle_db_search_in_1000000_data_part_10000():
    num_data = 1000000
    part_data_count = 10000

    # Load db and create the db
    db_apps_folder_path = os.path.join(
        proj_bench_basic_path,
        f"db_apps_{num_data}_data_part_{part_data_count}",
    )
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )

    return partial(db_apps.query_key, key="a simple query key")


def partial_check_is_eng():
    return partial(check_is_eng, translator=translator, text="中")


def partial_translate_to_eng():
    return partial(translate_to_eng, translator=translator, text="食")


def partial_decrypt_and_convert_to_dict():
    key = b"fGXWSoXcMUqNmK1JNJCGbgcwWvaqLK4o5mR2piEailM="
    encrypted_data = "gAAAAABm9Rn4V5K4SlUkfxEZrrETZHyZj7AF3SKjGE7xMtaZUCQsG8yA_mGsmUVHMZkf9SdOyG5cPOier9JUtPovz4koO9Bzmp4BY5gqjtrv-RRXVsJnTuI="
    return partial(decrypt_and_convert_to_dict, encrypted_data=encrypted_data, key=key)


def main():
    # Prepare a list of partial function to test.
    translate_related_run_times = 5
    extract_device_app_synonyms_v2_run_times = 10
    list_benchfunc = [
        BenchFunc(partial_decrypt_and_convert_to_dict()),
        BenchFunc(
            partial_check_is_eng(),
            num_times_individual=translate_related_run_times,
            note=f"runs {translate_related_run_times} times",
        ),
        BenchFunc(
            partial_translate_to_eng(),
            num_times_individual=translate_related_run_times,
            note=f"runs {translate_related_run_times} times",
        ),
        BenchFunc(partial_post_process_app_description()),
        BenchFunc(partial_convert_embedding_cpu(), "cpu"),
        BenchFunc(partial_convert_embedding_cuda(), "cuda"),
        BenchFunc(partial_extract_keyword()),
        BenchFunc(partial_post_process_keywords()),
        BenchFunc(partial_lemmatization()),
        BenchFunc(partial_similarity_search()),
        BenchFunc(
            partial_extract_device_app_synonyms_v2(),
            num_times_individual=extract_device_app_synonyms_v2_run_times,
        ),
        BenchFunc(
            partial_pickle_db_search_in_1000_data_part_100(), "1000 data, part 100"
        ),
        BenchFunc(
            partial_pickle_db_search_in_1000000_data_part_10000(),
            "1000000 data, part 10000",
        ),
    ]

    # Run benchmark.
    benchmark_string = benchmark_funcs(
        num_times=500,
        # num_times=5,
        list_benchfunc=list_benchfunc,
    )
    print(benchmark_string)

    # Write the result to txt.
    hostname = platform.node()
    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    result_file_path = f"./benchmark/basic/result_{hostname}_{time_str}.txt"
    with open(result_file_path, "w") as file:
        file.write(benchmark_string)


if __name__ == "__main__":
    sys.exit(main())
