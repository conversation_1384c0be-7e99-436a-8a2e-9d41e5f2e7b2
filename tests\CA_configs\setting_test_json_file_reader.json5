{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "read_json_file",
        ],
        "variables":{
            "jar_path":"./tomsze_utils/jars/plantuml_version_has_interactive_svg.jar",
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "read_json_file",
            "type": "PluginJsonFileReader",
            "use": true,
            "json_file_path": "./tests/test_directory_for_json_file_reader/test.json5",
        },
    ]
   

}
