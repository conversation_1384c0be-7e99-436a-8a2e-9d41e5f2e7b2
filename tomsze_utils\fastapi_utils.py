from datetime import datetime
import json
from typing import Dict, List, Tuple

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.responses import StreamingResponse
from fastapi.responses import JSONResponse
import requests

from tomsze_utils.logger_utils import SimpleLogger

logger = SimpleLogger(get_existing_instance=True)

from tomsze_utils.encrypt_utils import (
    decrypt_and_parse_json,
    encrypt_data,
    decrypt_data,
)
from tomsze_utils.env_parser_utils import parse_env


class AppSettings:
    def __init__(
        self,
        is_production: str,
        is_log_response_body: str,
        is_encrypt_response: str,
    ):
        self.is_production = is_production
        self.is_log_response_body = is_log_response_body
        self.is_encrypt_response = is_encrypt_response


def initialize_fastapi_app(
    env_path: str, logger: SimpleLogger = logger
) -> Tuple[FastAPI, AppSettings]:
    """
    Initializes the FastAPI application based on environment variables.

    This function parses environment variables from a specified .env file path, checks if the application is running in production mode,
    and initializes the FastAPI application accordingly. It also sets up logging for the application.

    Args:
        env_path (str): The path to the .env file containing environment variables.

    Returns:
        Tuple[FastAPI, AppSettings]: A Tuple containing the initialized FastAPI application and an instance of AppSettings.

    Examples:
        >>> app, app_settings = initialize_app("./.env")
        >>> app
        FastAPI
        >>> app_settings.is_production
        '0'
        >>> app_settings.is_log_response_body
        '1'
    """
    # Parse the environment variables from the specified .env file path
    root_env_dict = parse_env(env_path)
    # Check if the application is running in production
    # mode by evaluating the 'PRODUCTION' environment variable
    is_production = root_env_dict["PRODUCTION"]
    is_log_response_body = root_env_dict["LOG_RESPONSE_BODY"]
    is_encrypt_response = root_env_dict["ENCRYPT_RESPONSE"]
    logger.info(f"~~~~~~~~~~ Starting fastapi ~~~~~~~~~~")
    logger.info(f"is_production: {is_production}")
    logger.info(f"is_log_response_body: {is_log_response_body}")
    logger.info(f"is_encrypt_response: {is_encrypt_response}")
    logger.info(f"~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
    if is_production == "1":
        # If in production mode, initialize FastAPI without
        # the OpenAPI documentation for security reasons
        app = FastAPI(openapi_url="")
    else:
        # If not in production mode, initialize FastAPI
        # with the default settings, enabling OpenAPI
        # documentation
        app = FastAPI()

    app_settings = AppSettings(is_production, is_log_response_body, is_encrypt_response)

    return app, app_settings


async def log_request_and_response(
    request: Request,
    call_next,
    appSettings: AppSettings,
) -> Tuple[Response, str]:
    """
    Logs the request and response details.

    This function logs the client IP, request method, endpoint, body, response body, and status code.
    The response body is only logged if the 'LOG_RESPONSE_BODY' environment variable is set to '1'.

    Args:
        request (Request): The incoming request.
        call_next: The next function to call in the middleware chain.
        appSettings (AppSettings): The application settings.

    Returns:
        Tuple[Response, str]: The response and response body.

    Examples:
        >>> request = Request()
        >>> call_next = lambda request: Response()
        >>> appSettings = AppSettings("0", "1")
        >>> response, response_body = log_request_and_response(request, call_next, appSettings)
        >>> isinstance(response, Response)
        True
        >>> isinstance(response_body, str)
        True
    """
    client_ip = request.headers.get("X-Forwarded-For", request.client.host)
    logger.info(f"Request ip: {client_ip}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request endpoint: {request.url}")
    logger.info(f"Request body: {await request.body()}")

    response: Response = await call_next(request)

    if appSettings.is_log_response_body:
        response_body = b""
        async for chunk in response.body_iterator:
            response_body += chunk
        response_body = response_body.decode("utf-8")
        logger.info(f"Response body: {response_body}")

    logger.info(f"Status code: {response.status_code}")
    logger.info(f"--------------------------------------")
    return response, response_body


async def decrypt_request_body(
    request: Request,
    key: bytes,
    data_field: str = "data",
) -> Tuple[Request, Dict]:
    # Read the request body
    body_bytes = await request.body()

    # Parse into dict
    try:
        data_dict = json.loads(body_bytes)
    except json.JSONDecodeError:
        error_dict = {"detail": "Not Found"}
        return request, error_dict

    if data_field not in data_dict:
        error_dict = {"detail": "The special data field is Not Found"}
        return request, error_dict

    # Preprocess the data (e.g., decrypt the "data" value)
    try:
        decrypted_data = decrypt_data(
            data_dict[data_field],  # the value should be a dict from client
            key,
        )
    except ValueError as e:
        logger.error(str(e))
        error_dict = {"detail": "Not Found"}
        return request, error_dict

    data_dict[data_field] = decrypted_data

    # Create a new request with modified data
    request._body = json.dumps(data_dict).encode("utf-8")  # Modify the body
    return request, {}


async def try_asyncio_func():
    return 1


async def log_request_and_decrypt_and_encrypt_response(
    request: Request,
    call_next,
    appSettings: AppSettings,
    key: bytes,
    logger: SimpleLogger = logger,
) -> Tuple[Response, str]:
    """
    Middleware function to log requests, decrypt request bodies, and encrypt response bodies.

    This function only decrypts the request body when the request method is POST.
    """

    client_ip = request.headers.get("X-Forwarded-For", request.client.host)
    logger.info(f"======================================")
    logger.info(f"Request ip: {client_ip}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request endpoint: {request.url}")
    logger.info(f"Request body: {await request.body()}")

    # Decrypt the request body before processing
    decrypt_error_dict = {}
    if request.method == "POST":
        request, decrypt_error_dict = await decrypt_request_body(
            request=request,
            key=key,
            data_field="data_str",
        )

    # Check for errors before calling the next middleware or endpoint
    if decrypt_error_dict:

        async def json_streamer():
            yield json.dumps(decrypt_error_dict).encode("utf-8")

        response = StreamingResponse(content=json_streamer(), status_code=404)

    else:
        # Call the next middleware or endpoint in the FastAPI application
        response: StreamingResponse = await call_next(request)

    if appSettings.is_log_response_body:
        # Read the response body from the iterator
        response_body = b""
        async for chunk in response.body_iterator:
            response_body += chunk
        response_body = response_body.decode("utf-8")

        response_body_dict: dict = json.loads(response_body)

        if (
            appSettings.is_encrypt_response
            and "error" not in response_body.lower()
            and decrypt_error_dict == {}
            and request.method == "POST"
        ):
            # Encrypt the response body before sending it back to the client
            dict_key = list(response_body_dict.keys())[0]
            value_dict = response_body_dict[dict_key]

            # assert isinstance(value_dict, dict), "Value should be a dict"

            if isinstance(value_dict, str):
                value_dict = value_dict.replace("'", '"')

            try:
                encrypted_value = encrypt_data(
                    json.dumps(value_dict).encode("utf-8"),
                    key=key,
                    return_as_string=True,
                )
            except Exception as e:
                logger.error(f"Error during encryption: {e}")
                encrypted_value = encrypt_data(
                    value_dict.encode("utf-8"),
                    key=key,
                    return_as_string=True,
                )

            response_body_dict[dict_key] = encrypted_value

        response_body_str = json.dumps(response_body_dict)

        logger.info(f"Response body: {response_body_str}")

        response = Response(
            content=response_body_str,
            media_type="application/json",
            status_code=response.status_code,
        )

    logger.info(f"Status code: {response.status_code}")
    logger.info(f"--------------------------------------")
    return response


def post_request_with_params(
    api_url: str,
    query_params: dict,
    headers: dict = {"Content-type": "application/json"},
) -> dict:
    """
    Sends a POST request to the specified API URL with the given query parameters and headers.

    This function emphasizes uses the `params` argument in the `requests.post` method to include query parameters in the request.

    Args:
        api_url (str): The URL of the API endpoint.
        query_params (dict): The query parameters to include in the request, which will be passed as the `params` argument.
        headers (dict): The headers to include in the request (default is {"Content-type": "application/json"}).

    Returns:
        dict: The JSON response from the API.

    Examples:
        For a fastapi endpoint like this:
        @app.post("/data")
        def post_decrypt(key: str):

        ```python
        response = post_request_with_params("https://api.example.com/data", {"key": "value"})
        print(response)  # Expected output: {'status': 'success', 'data': {...}}
        ```

        ```python
        response = post_request_with_params("https://api.example.com/data", {"key": "value"}, headers={"Authorization": "Bearer token"})
        print(response)  # Expected output: {'status': 'success', 'data': {...}}
        ```
    """
    response = requests.post(
        api_url,
        params=query_params,
        headers=headers,
    )
    return response.json()


def post_request_with_json_body(
    api_url: str,
    json_body: dict,
    headers: dict = {"Content-type": "application/json"},
) -> dict:
    """
    Sends a POST request to the specified API URL with the given JSON body and headers.

    This function uses the `json` argument in the `requests.post` method to include a JSON body in the request.

    Args:
        api_url (str): The URL of the API endpoint.
        json_body (dict): The JSON body to include in the request.
        headers (dict): The headers to include in the request (default is {"Content-type": "application/json"}).

    Returns:
        dict: The JSON response from the API.

    Examples:
        response = post_request_with_json_body("https://api.example.com/data", {"key": "value"})
        print(response)  # Expected output: {'status': 'success', 'data': {...}}
    """
    response = requests.post(
        api_url,
        json=json_body,
        headers=headers,
    )
    return response.json()


def check_request_violation(
    request_time_list: List[str],
    max_requests: int,
    time_period_in_min: int,
) -> bool:
    """
    Check if the number of requests in the specified time period exceeds the allowed maximum.

    Args:
        request_time_list (list[str]): A list of strings containing the timestamps of requests to an API in ISO format (e.g., "%Y-%m-%dT%H:%M:%S").
        max_requests (int): The maximum number of requests allowed within the specified time period.
        time_period_in_min (int): The time period in minutes during which the requests are counted.

    Returns:
        bool: True if the number of requests exceeds the allowed maximum, False otherwise.

    Examples:
        >>> check_request_violation(["2023-10-01T12:00:00", "2023-10-01T12:01:00"], 5, 2)
        False

        >>> check_request_violation(["2023-10-01T12:00:00", "2023-10-01T12:01:00", "2023-10-01T12:02:00", "2023-10-01T12:03:00", "2023-10-01T12:04:00", "2023-10-01T12:05:00"], 5, 5)
        True
    """
    # Convert string timestamps to datetime objects
    request_datetimes = [datetime.fromisoformat(time) for time in request_time_list]

    # Filter requests within the time period
    valid_requests = [
        time
        for time in request_datetimes
        if (request_datetimes[-1] - time).total_seconds() <= time_period_in_min * 60
    ]

    # Check if the number of valid requests exceeds the maximum allowed
    return len(valid_requests) > max_requests
