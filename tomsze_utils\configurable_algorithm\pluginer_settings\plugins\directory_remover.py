"""App plugin"""

import os
from dataclasses import dataclass
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginDirectoryRemover:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        directory_path = parse_data_and_store(
            logger,
            "directory_path",
            data_obj,
            step_config,
            config,
        )

        if os.path.exists(directory_path):
            os.rmdir(directory_path)
            logger.info(f"Removed directory: {directory_path}")
        else:
            logger.warning(f"Directory does not exist: {directory_path}")

        data_obj.dict_var[f"{current_step}.directory_removed"] = (
            os.path.exists(directory_path) is False
        )
        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
