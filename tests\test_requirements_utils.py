import os
import shutil
import pytest
from tomsze_utils.requirements_utils import (
    create_update_requirements,
    remove_req_txt_duplication,
    replace_req_txt_with_pip_list_version,
)
from tomsze_utils.subprocess_utils import subprocess_run, subprocess_run_with_str_output
from tomsze_utils.whls_folder_file_parser_utils import parse_whls_in_folder_to_dict


def test_subprocess_run_with_pip_list():
    command = "pip list"
    out, output_str = subprocess_run_with_str_output(command)
    assert out.returncode == 0
    assert "Package" in output_str  # Check if the output contains the header "Package"


def test_create_update_requirements():
    # # Mock func1 to return a specific value
    # mocker.patch("tomsze_utils.subprocess_utils.subprocess_run", return_value=b"10")
    # pip_list_output_b = subprocess_run("pip list", capture_output=True).stdout

    whl_copy_to_folder_path = r"./tests/local_wheels"
    output_folder_path = r"./tests/req_output"
    no_torch = True
    req_txt_save_path = create_update_requirements(
        whl_copy_to_folder_path,
        output_folder_path=output_folder_path,
        no_torch=no_torch,
    )

    assert os.path.exists(whl_copy_to_folder_path) == True
    assert os.path.exists(output_folder_path) == True
    assert os.path.exists(req_txt_save_path) == True
    package_dict = parse_whls_in_folder_to_dict(whl_copy_to_folder_path)
    assert len(package_dict) > 0  # At least this package is local.

    data = open(req_txt_save_path).read()
    assert len(data) > 0
    assert "torch" not in data

    shutil.rmtree(whl_copy_to_folder_path)
    shutil.rmtree(output_folder_path)


def test_remove_req_txt_duplication():
    req_txt_path = r"./tests/fake_text_file_to_test/fake_reqs_txt.txt"
    update_file, write_to_file_path = remove_req_txt_duplication(
        req_txt_path=req_txt_path,
        overwrite=False,
    )
    assert update_file == True
    assert os.path.exists(write_to_file_path)

    data = open(write_to_file_path).read()
    line_split = data.splitlines()
    assert "aa==2.0.0" == line_split[0]
    assert "bb==3.0.0" == line_split[1]


def test_replace_req_txt_with_pip_list_version():
    req_txt_path = r"./tests/fake_text_file_to_test/fake_replace_pip_list_version.txt"
    pip_list_dict = {
        "aa": {
            "version": "1.2.3",
            "editable_project_path": "",
        },
        "cc": {
            "version": "2.3.4",
            "editable_project_path": "",
        },
        "dd": {
            "version": "3.4.5",
            "editable_project_path": "",
        },
    }
    update_file, write_to_file_path = replace_req_txt_with_pip_list_version(
        req_txt_path=req_txt_path,
        pip_list_dict=pip_list_dict,
        overwrite=False,
    )
    assert update_file == True
    assert os.path.exists(write_to_file_path)

    data = open(write_to_file_path).read()
    line_split = data.splitlines()
    assert "aa==1.2.3" == line_split[0]
    assert "cc==2.3.4" == line_split[2]
    assert "dd" not in data


if __name__ == "__main__":
    test_create_update_requirements()
    # test_remove_req_txt_duplication()
    # test_replace_req_txt_with_pip_list_version()
