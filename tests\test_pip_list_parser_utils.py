import sys

from tomsze_utils.pip_list_parser_utils import (
    parse_pip_list_output_b,
    parse_pip_list_output_str,
)


def test_pip_list_parser_utils():
    pip_list_b = b"Package  Version  Editable project location\r\n------------------------------- ------------ --------------------------------\r\naiohttp   3.8.6\r\naiosignal  1.3.1\r\ntomsze_utils  0.10.0  c:\\code\\my_packages\\tomsze_utils\r\ntorch  2.1.0+cu121\r\n"

    pip_list_dict = parse_pip_list_output_b(pip_list_b)

    expect_dict = {
        "aiohttp": {"version": "3.8.6", "editable_project_path": None},
        "aiosignal": {"version": "1.3.1", "editable_project_path": None},
        "tomsze_utils": {
            "version": "0.10.0",
            "editable_project_path": "c:/code/my_packages/tomsze_utils",
        },
        "torch": {"version": "2.1.0+cu121", "editable_project_path": None},
    }

    assert pip_list_dict == expect_dict


def test_parse_pip_list_output_str_pytest():
    pip_list_str = "Package  Version  Editable project location\r\n------------------------------- ------------ --------------------------------\r\naiohttp   3.8.6\r\naiosignal  1.3.1\r\ntomsze_utils  0.10.0  c:\\code\\my_packages\\tomsze_utils\r\ntorch  2.1.0+cu121\r\n"

    pip_list_dict = parse_pip_list_output_str(pip_list_str)

    expect_dict = {
        "aiohttp": {"version": "3.8.6", "editable_project_path": None},
        "aiosignal": {"version": "1.3.1", "editable_project_path": None},
        "tomsze_utils": {
            "version": "0.10.0",
            "editable_project_path": "c:/code/my_packages/tomsze_utils",
        },
        "torch": {"version": "2.1.0+cu121", "editable_project_path": None},
    }

    assert pip_list_dict == expect_dict


def main():
    test_pip_list_parser_utils()


if __name__ == "__main__":
    sys.exit(main())
