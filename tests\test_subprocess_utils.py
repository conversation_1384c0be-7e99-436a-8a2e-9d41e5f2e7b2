import os
import pytest
from tomsze_utils.subprocess_utils import subprocess_run, subprocess_run_with_str_output


def test_subprocess_run_with_str_output():
    command = "echo hello"
    out, output_str = subprocess_run_with_str_output(command)
    assert out.returncode == 0
    assert output_str == "hello\r\n"


def test_subprocess_run_with_pip_list():
    command = "pip list"
    out, output_str = subprocess_run_with_str_output(command)
    assert out.returncode == 0
    assert "Package" in output_str  # Check if the output contains the header "Package"


def test_subprocess_run_pip_list_output():
    out = subprocess_run("pip list", capture_output=True)
    pip_list_output_b = out.stdout
    assert out.returncode == 0
    assert isinstance(pip_list_output_b, bytes)  # Check if the output is in bytes
    output_str = pip_list_output_b.decode("utf-8")  # Decode bytes to string
    assert "Package" in output_str  # Check if the output contains the header "Package"
    assert len(output_str) > 0  # Ensure that the output is not empty


def test_subprocess_run_pipreqs():
    output_folder_path = "./tests/req_output"
    import os

    # Convert the output folder path to an absolute path
    output_folder_path = os.path.abspath(output_folder_path)
    if not os.path.exists(output_folder_path):
        os.makedirs(output_folder_path)
    req_txt_save_path = os.path.join(output_folder_path, "requirements.txt")
    req_txt_save_path = req_txt_save_path.replace("\\", "/")
    # subprocess_run(f"pipreqs ./ --force --savepath {req_txt_save_path}")
    output = subprocess_run_with_str_output(
        f"pipreqs ./ --force --savepath {req_txt_save_path}"
    )

    while True:
        if os.path.exists(req_txt_save_path) == True:
            break

    assert (
        os.path.exists(req_txt_save_path) == True
    ), "The requirements.txt file does not exist."

    # Delete the generated requirements.txt file
    if os.path.exists(req_txt_save_path):
        os.remove(req_txt_save_path)
