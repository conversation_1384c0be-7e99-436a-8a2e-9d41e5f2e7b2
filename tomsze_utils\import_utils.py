import sys
import os
import importlib
from contextlib import contextmanager
import shutil
from types import ModuleType
from typing import Any, Callable, Generator, Optional, Tuple


def approximated_import_module(
    name: str,
    package: Optional[str] = None,
) -> Any:
    """An approximate implementation of import.

    This function attempts to import a module by its name, resolving the name against the provided package if necessary.

    Note:
    from https://docs.python.org/3/library/importlib.html#approximating-importlib-import-module

    Args:
        name (str): The name of the module to import.
        package (Optional[str]): The package name to resolve the module name against. Defaults to None.

    Returns:
        Any: The imported module.

    Examples:
        ```python
        module = approximated_import_module("my_module")
        ```

        ```python
        module = approximated_import_module("my_submodule", package="my_package")
        ```
    """
    absolute_name = importlib.util.resolve_name(name, package)

    if absolute_name in sys.modules:
        del sys.modules[absolute_name]

    path = None
    if "." in absolute_name:
        parent_name, _, child_name = absolute_name.rpartition(".")
        parent_module = approximated_import_module(parent_name)
        path = parent_module.__spec__.submodule_search_locations
    for finder in sys.meta_path:
        spec = finder.find_spec(absolute_name, path)
        if spec is not None:
            break
    else:
        msg = f"No module named {absolute_name!r}"
        raise ModuleNotFoundError(msg, name=absolute_name)
    module = importlib.util.module_from_spec(spec)
    sys.modules[absolute_name] = module
    spec.loader.exec_module(module)
    if path is not None:
        setattr(parent_module, child_name, module)
    return module


@contextmanager
def add_to_path(path: str) -> Generator[None, None, None]:
    """Temporarily adds a specified path to the system path.

    This context manager allows for the temporary addition of a directory
    to the Python module search path. Once the context is exited, the
    original system path is restored.

    Args:
        path (str): The directory path to add to the system path.

    Yields:
        None: Control is yielded back to the context block.
    """
    old_path = sys.path
    sys.path = sys.path[:]
    sys.path.insert(0, path)
    try:
        yield
    finally:
        sys.path = old_path


def delete_pycache(path: str) -> str:
    """Delete the __pycache__ directory associated with the given file path.

    Args:
        path (str): The file path for which the __pycache__ directory will be deleted.

    Returns:
        str: A confirmation string indicating the operation is done.
    """
    pycache_path = os.path.join(os.path.dirname(path), "__pycache__")
    shutil.rmtree(pycache_path, ignore_errors=True)
    return "done"


def import_module_by_path(
    absolute_path: str,
    working_directory: str = "./",
) -> Tuple[str, Optional[ModuleType]]:
    """
    Dynamically imports a Python module from a given absolute file path.

    This function attempts to import a Python module from a specified file path.
    It first deletes the __pycache__ directory associated with the file to ensure
    a clean import. If the import is successful, it returns an empty string and
    the imported module. If an error occurs during import, it returns the error
    message and None.

    Args:
        absolute_path (str): The absolute file path of the module to import.
        working_directory (str, optional): The working directory to temporarily add to the system path. Defaults to "./".

    Returns:
        Tuple[str, Optional[ModuleType]]: A tuple containing an error message (if any) and the imported module (or None).

    Examples:
        >>> error, module = import_module_by_path('/path/to/valid/module.py')
        >>> if not error:
        >>>     print(module.__name__)  # Output: valid_module

        >>> error, module = import_module_by_path('/path/to/invalid/module.py')
        >>> if error:
        >>>     print(error)  # Output: ImportError: No module named 'invalid_module'

    Note:
        Implementation taken from https://docs.python.org/3/library/importlib.html#importing-a-source-file-directly
    """
    delete_pycache(absolute_path)
    delete_pycache(working_directory)
    error = ""

    try:
        with add_to_path(working_directory):
            spec = importlib.util.spec_from_file_location(absolute_path, absolute_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
    except Exception as e:
        error = str(e)
        return error, None

    return error, module


def get_callable_from_module_by_function_name(
    module: ModuleType,
    function_name: str,
) -> Optional[Callable]:
    """
    Retrieves a callable function from a given module by its name.

    This function checks if the specified function exists in the module and is callable.

    Args:
        module (ModuleType): The module from which to retrieve the function.
        function_name (str): The name of the function to retrieve.

    Returns:
        Optional[Callable]: The callable function if found, otherwise None.

    Examples:
        ```python
        func = get_callable_from_module_by_function_name(module=my_module, function_name='my_function')
        ```

        ```python
        func = get_callable_from_module_by_function_name(module=my_module, function_name='non_existent_function')
        ```

    """
    return (
        getattr(module, function_name, None)
        if callable(getattr(module, function_name, None))
        else None
    )
