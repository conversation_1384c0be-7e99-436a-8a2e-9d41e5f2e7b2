"""App plugin"""

from dataclasses import dataclass
import json

import requests
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginSimpleGetRequester:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        url = parse_data_and_store(
            logger,
            "url",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        headers = parse_data_and_store(
            logger,
            "headers",
            data_obj,
            step_config,
            config,
        )

        assert isinstance(headers, dict) == True
        this_config = headers.copy()
        for key in headers.keys():
            headers[key] = parse_data_and_store(
                logger,
                key,
                data_obj,
                this_config,
                config,
            )

        response_buffer_to = parse_data_and_store(
            logger,
            "response_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        headers_buffer_to = parse_data_and_store(
            logger,
            "headers_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        content_buffer_to = parse_data_and_store(
            logger,
            "content_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        text_buffer_to = parse_data_and_store(
            logger,
            "text_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        status_code_buffer_to = parse_data_and_store(
            logger,
            "status_code_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        exception_buffer_to = parse_data_and_store(
            logger,
            "exception_buffer_to",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        try:
            response = requests.get(url, headers=headers)

            data_obj.dict_var[f"{current_step}.{response_buffer_to}"] = response
            data_obj.dict_var[f"{current_step}.{headers_buffer_to}"] = response.headers
            data_obj.dict_var[f"{current_step}.{content_buffer_to}"] = response.content
            data_obj.dict_var[f"{current_step}.{text_buffer_to}"] = response.text
            data_obj.dict_var[f"{current_step}.{status_code_buffer_to}"] = (
                response.status_code
            )
            data_obj.dict_var[f"{current_step}.{exception_buffer_to}"] = "no exception"

            # Parse to dict of it is a json string.
            try:
                if_dict_buffer_to = parse_data_and_store(
                    logger,
                    "if_dict_buffer_to",
                    data_obj,
                    step_config,
                    config,
                )

                response_dict = json.loads(response.text)
                data_obj.dict_var[f"{current_step}.{if_dict_buffer_to}"] = response_dict

            except Exception as e:
                pass
        except Exception as e:
            print(str(e))
            data_obj.dict_var[f"{current_step}.{exception_buffer_to}"] = str(e)

        data_obj.dict_var[current_step] = "test_result"

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
