package com.kewtoms.whatappsdo.utils;

import static com.kewtoms.whatappsdo.utils.APIUtils.apiGetCurrentTimeInZone;
import static com.tomsze.APIUtils.apiGet;
import static com.tomsze.TimeUtils.convertDateString;

import android.annotation.SuppressLint;

import com.kewtoms.whatappsdo.data.generated.api_constant_keys.ApiFunResponseKeys;
import com.kewtoms.whatappsdo.data.generated.api_constant_keys.ApiResponseKeys;
import com.kewtoms.whatappsdo.data.generated.user_database_constants.ResponseKeys;
import com.macasaet.fernet.Token;
import com.macasaet.fernet.Key;
import com.macasaet.fernet.Validator;
import com.macasaet.fernet.StringValidator;

import org.json.JSONObject;

import java.security.spec.KeySpec;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Map;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;

public class EncryptionUtils {
  public static final String TAG = "APP:EncryptionUtils";

  /**
   * Encrypts the given data using Fernet encryption.
   *
   * @param data The data to be encrypted.
   * @param keyBytes The encryption key (must be 32 bytes for
   * Fernet).
   * @return The encrypted data as a Base64 encoded string.
   * @example <pre>{@code
   * byte[] data = "Hello, World!".getBytes();
   * byte[] key = "01234567890123456789012345678901".getBytes(); // 32 bytes key for Fernet
   * String encryptedString = encryptData(data, key); // returns Base64 encoded string
   * }</pre>
   * @package com.kewtoms.whatappsdo.utils
   */
  public static String encryptData(
    byte[] data,
    byte[] keyBytes) {
    Key key = new Key(keyBytes);
    Token token = Token.generate(
      key,
      data
    );

    return token.serialise();
  }


  /**
   * Decrypts the given token string using the specified key.
   *
   * @param keyBytes The decryption key (must be 32 bytes for
   * Fernet).
   * @param tokenString The encrypted token string to be decrypted.
   * @return The decrypted data as a string, or null if decryption
   * fails.
   * @example <pre>{@code
   * byte[] key = "01234567890123456789012345678901".getBytes(); // 32 bytes key for Fernet
   * String token = "gAAAAABg..."; // Example of an encrypted token
   * String decryptedData = decryptData(key, token); // returns the original data as a string
   * }</pre>
   * @example <pre>{@code
   * byte[] wrongKey = "wrongkeywrongkeywrongkeywrongkey".getBytes(); // Invalid key
   * String token = "gAAAAABg..."; // Example of an encrypted token
   * String result = decryptData(wrongKey, token); // returns null due to invalid key
   * }</pre>
   */
  public static String decryptData(
    byte[] keyBytes,
    String tokenString) {
    // Deserialize the key
    Key key = new Key(keyBytes);

    // Deserialize the token
    Token token =
      Token.fromString(tokenString);    // Do not use Token.fromBytes()

    // Create a validator
    Validator<String> validator = new StringValidator() {
    };

    // Validate and decrypt the token
    try {
      return token.validateAndDecrypt(
        key,
        validator
      );
    } catch (Exception e) {
      e.printStackTrace();
      return null;
    }
  }

  /**
   * Generates a new encryption key.
   *
   * @return A byte array representing the generated encryption key.
   * @example <pre>{@code
   * byte[] myKey = generateKey(); // Generates a new encryption key
   * }</pre>
   * @example <pre>{@code
   * byte[] anotherKey = generateKey(); // Generates another encryption key
   * }</pre>
   */
  public static byte[] generateKey() {
    return Key.generateKey().serialise().getBytes();
  }

  /**
   * Generates a key from a given string using PBKDF2 with HMAC
   * SHA-256.
   *
   * @param string The input string to derive the key from.
   * @param salt The salt to be used in the key generation process.
   * Defaults to "default_salt" if not provided.
   * @param iterations The number of iterations for the key
   * derivation. Defaults to 100000 if null.
   * @param length The desired length of the generated key in bytes.
   * Defaults to 32 if not provided.
   * @return A byte array representing the generated key encoded as a
   * URL-safe base64 string. The returned byte array will have a
   * length of 44 bytes.
   * @example <pre>{@code
   * byte[] salt = "randomSalt".getBytes();
   * byte[] key = generateKeyFromString(string: "myPassword", salt: salt, iterations: 100000, length: 32);
   * }</pre>
   * @example <pre>{@code
   * byte[] key = generateKeyFromString(string: "myPassword", iterations: null, length: 32); // Uses default salt and default iterations
   * }</pre>
   */
  public static byte[] generateKeyFromString(
    String string,
    byte[] salt,
    Integer iterations,
    Integer length)
    throws
    Exception {
    if (salt == null) {
      salt = "default_salt".getBytes(); // Default salt
    }
    // Set default iterations if null
    if (iterations == null) {
      iterations = 100000; // Default iterations
    }
    // Set default length if null
    if (length == null) {
      length = 32; // Default length
    }
    // Define the key specification
    KeySpec spec = new PBEKeySpec(
      string.toCharArray(),
      salt,
      iterations,
      length * 8
    ); // length in bits

    // Create a SecretKeyFactory for PBKDF2 with HMAC SHA-256
    SecretKeyFactory factory =
      SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");

    // Generate the key
    byte[] key = factory.generateSecret(spec).getEncoded();

    // Encode the key to a URL-safe base64 string
    return Base64.getUrlEncoder().encodeToString(key).getBytes();
  }


  public static byte[] generateKeyFromKeyDictAndServerDatetime(
    Map<String, String[]> dateStringDict,
    String timeFormat,
    String urlString)
    throws
    Exception {

    // Set default time_format if null
    if (timeFormat == null) {
      timeFormat = "##yyyy~~MM--dd**"; // Default time format
    }

    // Fetch current time from API
    JSONObject sysDatetimeDict =
      apiGet(urlString);     // might cause Read Timeout
    Integer yearInteger =
      sysDatetimeDict.getJSONObject(ApiResponseKeys.API_RESPONSE)
        .getJSONObject(ApiFunResponseKeys.RESULT)
        .getInt("year");
    Integer monthInteger =
      sysDatetimeDict.getJSONObject(ApiResponseKeys.API_RESPONSE)
        .getJSONObject(ApiFunResponseKeys.RESULT)
        .getInt("month");
    Integer dayInteger =
      sysDatetimeDict.getJSONObject(ApiResponseKeys.API_RESPONSE)
        .getJSONObject(ApiFunResponseKeys.RESULT)
        .getInt("day");
    @SuppressLint("DefaultLocale") String sysDatetimeStr =
      String.format(
        "%04d-%02d-%02d",
        yearInteger,
        monthInteger,
        dayInteger
      );
    String formattedDatetimeStr = convertDateString(
      sysDatetimeStr,
      timeFormat
    );

    // Combine keys and formatted datetime string
    @SuppressLint("DefaultLocale") String formatted_month_day =
      String.format(
        "%02d",
        monthInteger
      ) + String.format(
        "%02d",
        dayInteger
      );
    String[] XStrArr = dateStringDict.get(formatted_month_day);
    assert XStrArr != null;
    String key1 = XStrArr[0];
    String key2 = XStrArr[1];
    String baseKeyString = key1 + formattedDatetimeStr + key2;
    return generateKeyFromString(
      baseKeyString,
      null,
      null,
      null
    );
  }

}
