from tomsze_utils.scoring_utils import normalize_list


def test_normalize_list():
    list_num = [1, 2, 3, 4]
    expect = [0.1, 0.2, 0.3, 0.4]
    result = normalize_list(list_num)
    assert result == expect

    list_num = [0, 0, 0, 0]
    expect = [0, 0, 0, 0]
    result = normalize_list(list_num)
    assert result == expect


import pytest
from tomsze_utils.scoring_utils import calculate_total_ratio_of_index_range_list


class TestCalculateTotalRatioOfIndexRangeList:
    def test_calculate_total_ratio_of_index_range_list_basic(self):
        index_range_list = [(0, 5), (10, 15)]
        length = 20
        expect = 0.5
        result = calculate_total_ratio_of_index_range_list(index_range_list, length)
        assert result == expect

    def test_calculate_total_ratio_of_index_range_list_empty(self):
        index_range_list = []
        length = 20
        expect = 0
        result = calculate_total_ratio_of_index_range_list(index_range_list, length)
        assert result == expect

    def test_calculate_total_ratio_of_index_range_list_zero_length(self):
        index_range_list = [(0, 5), (10, 15)]
        length = 0
        expect = 0
        result = calculate_total_ratio_of_index_range_list(index_range_list, length)
        assert result == expect


from tomsze_utils.scoring_utils import calculate_matched_len_ratio


class TestCalculateMatchedLenRatio:
    def test_calculate_matched_len_ratio_basic(self):
        matched_len = 5
        whole_text_len = 10
        expect = 0.5
        result = calculate_matched_len_ratio(matched_len, whole_text_len)
        assert result == expect

    def test_calculate_matched_len_ratio_zero_length(self):
        matched_len = 0
        whole_text_len = 10
        expect = 0.0
        result = calculate_matched_len_ratio(matched_len, whole_text_len)
        assert result == expect


from tomsze_utils.scoring_utils import (
    calculate_matched_word_ratio_using_index_range_list,
)


class TestCalculateMatchedWordRatioUsingIndexRangeList:
    def test_calculate_matched_word_ratio_using_index_range_list_basic(self):
        index_range_list = [(0, 5)]
        text = "hello world"
        expect = 0.5
        result = calculate_matched_word_ratio_using_index_range_list(
            index_range_list, text
        )
        assert result == expect

    def test_calculate_matched_word_ratio_using_index_range_list_multiple_ranges(self):
        index_range_list = [(0, 5), (6, 11)]
        text = "hello world abc"
        expect = 2 / 3
        result = calculate_matched_word_ratio_using_index_range_list(
            index_range_list, text
        )
        assert result == expect


if __name__ == "__main__":
    test_normalize_list()
