from dotenv import dotenv_values


def parse_env(
    env_path: str,
    parse_list_in_str: bool = True,
) -> dict:
    """
    Parse .env into a dict.

    Args:
        env_path (str): The path to the .env file.
        parse_list_in_str (bool): Flag to indicate if lists in the .env should be parsed.
                                   "LIST" must be present in the key name for this to work.
                                   In the .env file, the list value can be a comma-separated string.

    Returns:
        dict: A dictionary containing the parsed environment variables.

    Examples:
        >>> env_dict = parse_env('.env')
        >>> print(env_dict)
        {'KEY1': 'value1', 'KEY2_LIST': ['value2a', 'value2b']}

        >>> env_dict = parse_env('.env', parse_list_in_str=False)
        >>> print(env_dict)
        {'KEY1': 'value1', 'KEY2_LIST': 'value2a, value2b'}

        Example of .env file content:
        VAR_LIST2="line1,line2,line3"
        VAR_LIST3="  line1,  line2,  line3"
        VAR_LIST4="line1  ,line2  ,line3  "
    """
    value_dict = dotenv_values(env_path)

    if parse_list_in_str:
        for key in value_dict.keys():
            if "LIST" in key.upper():
                value = value_dict[key].replace("\n", "")
                val_list = value.split(",")
                val_list = [
                    val.strip() for val in val_list
                ]  # trim leading and trail space.

                value_dict[key] = val_list

    return value_dict
