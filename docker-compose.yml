version: "3"

services:
  fastapi:
    image: "search_phone_app_server"
    build: ./
    # restart: always
    volumes:
      - "./const_model_settings.py/:/code/const_model_settings.py/"
      - "./const_path.py/:/code/const_path.py/"
      - "./mode.py/:/code/mode.py/"
      - "./.env_prod/:/code/.env/"
      - "./pickle_db/:/code/pickle_db/"
      - "./security/.env_security/:/code/security/.env_security/"
      - "./security/date_strings.json/:/code/security/date_strings.json/"
    ports:
      - 8000:8000
    command: gunicorn server.api.fastapi_app.main:app --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000