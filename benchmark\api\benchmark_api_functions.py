import datetime
import os
import platform
import sys
from googletrans import Translator
import spacy
from tomsze_utils.benchmark_utils import benchmark_funcs, BenchFunc
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
)
from functools import partial
from server.api.end_to_end.api_functions import search_user_app
from server.api.end_to_end.data_class import ClientSearchData
from server.api.utils.descriptions.get_description import scrape_play_store
from server.api.utils.generate_vectors.utils import (
    convert_to_embedding,
)
from sentence_transformers import SentenceTransformer
from server.api.utils.keywords.get_keywords import lemmatization, post_process_keywords
from const_path import (
    proj_ser_api_db_apps_path,
    proj_ser_api_db_synonyms_path,
    proj_ser_api_db_synonyms_need_powerthesaurus_path,
)
from server.api.utils.translation.translate import check_is_eng, translate_to_eng

model_id = "multi-qa-MiniLM-L6-cos-v1"
embedder = SentenceTransformer(model_id)  # TODO change to best embedder

device_app_id = "com.rovio.BadPiggies"
lang = "en"
scrape_success, result = scrape_play_store(
    device_app_id=device_app_id,
    lang=lang,
)
max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20

lemmatization_model = spacy.load("en_core_web_sm", disable=["parser", "ner"])

query_embedding = convert_to_embedding(
    embedder,
    "eat",
    device="cpu",
    batch_size=128,
)

list_device_embedding_tensor = [query_embedding] * 200

translator = Translator()


debug_mode = False
client_search_data = ClientSearchData()
client_search_data.allowSearch = True
client_search_data.appIds.append("com.android.camera2")
client_search_data.appIds.append("com.android.chrome")
client_search_data.appIds.append("com.android.contacts")
client_search_data.appIds.append("com.android.dialer")
client_search_data.appIds.append("com.android.settings")
client_search_data.appIds.append("com.android.vending")
client_search_data.appIds.append("com.google.android.apps.docs")
client_search_data.appIds.append("com.google.android.apps.maps")
client_search_data.appIds.append("com.google.android.apps.messaging")
client_search_data.appIds.append("com.google.android.apps.photos")
client_search_data.appIds.append("com.google.android.apps.tachyon")
client_search_data.appIds.append("com.google.android.apps.youtube.music")
client_search_data.appIds.append("com.google.android.calendar")
client_search_data.appIds.append("com.google.android.deskclock")
client_search_data.appIds.append("com.google.android.gm")
client_search_data.appIds.append("com.google.android.videos")
client_search_data.appIds.append("com.google.android.youtube")
client_search_data.appIds.append("host.exp.exponent")
client_search_data.appIds.append("com.google.android.apps.wallpaper")
client_search_data.appIds.append("com.google.android.documentsui")
client_search_data.appIds.append("com.google.android.googlequicksearchbox")
client_search_data.appIds.append("org.chromium.webview_shell")
client_search_data.appIds.append("com.aigrind.warspear")
client_search_data.appIds.append("com.example.a06constraintlayout")
client_search_data.appIds.append("com.example.android.courtcounter")
client_search_data.appIds.append("com.example.example")
client_search_data.appIds.append("com.example.flutter_scrollable_app_icons_example")
client_search_data.appIds.append("com.example.flutter_scrollable_icons")
client_search_data.appIds.append("com.example.myapplication")
client_search_data.appIds.append("com.example.progressbarexample")
client_search_data.appIds.append("com.example.search_app_by_description")
client_search_data.appIds.append("com.example.test_scrollable_richtext_widget")
client_search_data.appIds.append("com.sankuai.sailor.afooddelivery")
client_search_data.appIds.append("com.sharmadhiraj.installed_apps_example")
client_search_data.appIds.append("com.whatsapp")
client_search_data.appIds.append("io.metamask")
client_search_data.appIds.append("math.puzzle.games.crossmath.number.puzzles.free")

client_search_data.query = "chat"
client_search_data_dict = client_search_data.dict()

db_apps = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_apps_path,
    db_name="db_app",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)
db_synonyms = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_synonyms_path,
    db_name="db_synonyms",
    load_by_thread=False,  # not use thread to load this moment for faster develop
)

db_synonyms_need_powerthesaurus = PickleDatabaseSplit(
    db_fpath=proj_ser_api_db_synonyms_need_powerthesaurus_path,
    db_name="db_synonyms_need_powerthesaurus",
    load_by_thread=False,
)


def partial_search_user_app():

    return partial(
        search_user_app,
        client_search_data=client_search_data_dict,
        debug_mode=debug_mode,
        db_apps=db_apps,
        db_synonyms=db_synonyms,
        db_synonyms_need_powerthesaurus=db_synonyms_need_powerthesaurus,
    )


def main():
    # Prepare a list of partial function to test.

    benchmark_search_user_app_test_times = (
        10  # not too much, need to call translate related api
    )
    benchmark_search_user_app_note = f"num client app: {len(client_search_data.appIds)}\nnum db_apps keys: {db_apps.get_db_num_keys()}\nnum db_synonyms keys: {db_synonyms.get_db_num_keys()}"
    list_benchfunc = [
        BenchFunc(
            partial_search_user_app(),
            note=benchmark_search_user_app_note,
            num_times_individual=benchmark_search_user_app_test_times,
        ),
    ]

    # Run benchmark.
    benchmark_string = benchmark_funcs(
        num_times=500,
        # num_times=5,
        list_benchfunc=list_benchfunc,
    )
    print(benchmark_string)

    # Write the result to txt.
    hostname = platform.node()
    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    result_file_path = f"./benchmark/api/result_{hostname}_{time_str}.txt"
    with open(result_file_path, "w") as file:
        file.write(benchmark_string)


if __name__ == "__main__":
    sys.exit(main())
