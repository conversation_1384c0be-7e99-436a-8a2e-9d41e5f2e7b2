import os
from typing import List, <PERSON><PERSON>

from tomsze_utils.dir_utils import create_nested_folders
from tomsze_utils.string_utils import count_space_in_front


def replace_file_text(
    file_path: str,
    replace_text: str,
    replace_as: str,
    overwrite: bool = False,
) -> Tuple[bool, str]:
    """
    Replaces occurrences of a specified text in a file and writes the result to a new file or overwrites the existing one.

    Args:
        file_path (str): The path to the file where text replacement will occur.
        replace_text (str): The text to be replaced.
        replace_as (str): The text to replace with.
        overwrite (bool): If True, overwrite the original file; otherwise, create a new file. Default is False.

    Returns:
        Tuple[bool, str]: A Tuple containing a boolean indicating if the file was updated and the path to the written file.

    Examples:
        ```python
        updated, new_file_path = replace_file_text(
            file_path="example.txt",
            replace_text="old text",
            replace_as="new text",
            overwrite=False
        )
        ```

        ```python
        updated, new_file_path = replace_file_text(
            file_path="example.txt",
            replace_text="foo",
            replace_as="bar",
            overwrite=True
        )
        ```
    """
    update_file = False

    assert os.path.exists(file_path)

    # Read script.
    data = open(file_path).read()

    write_to_file_path = file_path
    if not overwrite:
        ext = file_path.split(".")[-1]
        write_to_file_path = file_path.replace(f".{ext}", "") + f"_new.{ext}"

    data = data.replace(replace_text, replace_as)
    update_file = True

    if update_file:
        with open(write_to_file_path, "w") as file:
            file.write(data)

    return update_file, write_to_file_path


def replace_file_line_contains(
    file_path: str,
    contains: str,
    replace_as: str,
    overwrite: bool = False,
) -> Tuple[bool, str]:
    """
    Replaces lines in a file that contain a specific substring with a new line.

    Args:
        file_path (str): The path to the file where text replacement will occur.
        contains (str): The substring to search for in each line.
        replace_as (str): The text to replace matching lines with.
        overwrite (bool): If True, overwrite the original file; otherwise, create a new file. Default is False.

    Returns:
        Tuple[bool, str]: A Tuple containing a boolean indicating if the file was updated and the path to the written file.

    Examples:
        ```python
        updated, new_file_path = replace_file_line_contains(
            file_path="example.txt",
            contains="old line",
            replace_as="new line",
            overwrite=False
        )
        ```

        ```python
        updated, new_file_path = replace_file_line_contains(
            file_path="example.txt",
            contains="foo",
            replace_as="bar",
            overwrite=True
        )
        ```
    """
    update_file = False

    assert os.path.exists(file_path)

    # Read script.
    data = open(file_path).read()

    write_to_file_path = file_path
    if not overwrite:
        ext = file_path.split(".")[-1]
        write_to_file_path = file_path.replace(f".{ext}", "") + f"_new.{ext}"

    # data = data.replace(replace_text, replace_as)
    update_file = True

    if update_file:
        with open(write_to_file_path, "w") as file:
            line_list = data.splitlines()
            for line in line_list:
                if contains in line:
                    num_spaces = count_space_in_front(text=line)
                    space_str = "".join([" "] * num_spaces)
                    new_line = space_str + replace_as
                    file.write(new_line + "\n")
                else:
                    file.write(line + "\n")

    return update_file, write_to_file_path


def write_string_list_to_file(
    file_path: str,
    string_list: List[str],
    add_newline: bool = True,
    overwrite: bool = False,
) -> Tuple[bool, str]:
    """
    Writes a list of strings to a specified file.

    Args:
        file_path (str): The path to the file where the strings will be written.
        string_list (List[str]): A list of strings to write to the file.
        add_newline (bool): Whether to add a newline after each string. Defaults to True.
        overwrite (bool): Whether to overwrite the existing file. Defaults to False.

    Returns:
        Tuple[bool, str]: A Tuple indicating if the file was updated and the path to the written file.

    Examples:
        ```python
        updated, new_file_path = write_string_list_to_file(
            file_path="example.txt",
            string_list=["line 1", "line 2", "line 3"],
            add_newline=True,
            overwrite=False
        )
        ```

        ```python
        updated, new_file_path = write_string_list_to_file(
            file_path="example.txt",
            string_list=["line 1", "line 2"],
            add_newline=False,
            overwrite=True
        )
        ```
    """
    update_file = False

    assert os.path.exists(file_path), f"{file_path} does not exist"

    write_to_file_path = file_path
    if not overwrite:
        ext = file_path.split(".")[-1]
        write_to_file_path = file_path.replace(f".{ext}", "") + f"_new.{ext}"

    update_file = True

    if update_file:
        with open(write_to_file_path, "w") as file:
            for line in string_list:
                if add_newline:
                    file.write(line + "\n")
                else:
                    file.write(line)

    return update_file, write_to_file_path


def write_text_to_file(
    file_path: str,
    text: str,
) -> str:
    """
    Writes the specified text to a file.

    Args:
        file_path (str): The path to the file where the text will be written.
        text (str): The text content to write to the file.

    Returns:
        str: A message indicating the file creation and the text written.

    Examples:
        ```python
        result = write_text_to_file(file_path="output.txt", text="Hello, World!")
        ```

        ```python
        result = write_text_to_file(file_path="example.txt", text="This is a test.")
        ```
    """
    with open(file_path, "w") as file:
        file.write(text)
    return f"file: {file_path} created with text: {text}"


def append_string_to_file(file_path: str, string_to_append: str) -> str:
    """
    Appends a string to a text file, adding a newline before the string if the file is not empty.

    Args:
        file_path (str): The path to the file where the string will be appended.
        string_to_append (str): The string content to append to the file.

    Returns:
        str: A message indicating the string has been appended to the file.

    Examples:
        ```python
        result = append_string_to_file(file_path="output.txt", string_to_append="Hello, World!")
        ```
    """
    with open(file_path, "a") as file:
        # Check if the file is not empty
        if file.tell() > 0:
            file.write("\n")
        file.write(string_to_append)
    return f"Appended to file: {file_path} the string: {string_to_append}"
