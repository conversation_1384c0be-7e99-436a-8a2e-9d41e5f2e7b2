import json
import os
import sys

from server.api.utils.generate_vectors.utils import scrap_play_store
from const_path import proj_ser_tools_path


def run_scrape_play_store():
    device_app_id_list = [
        "com.sankuai.sailor.afooddelivery",
        "tw.txwy.and.arknights&pcampaignid",
        "com.HoYoverse.hkrpgoversea",
        "com.google.android.apps.maps",
        "com.yusibo.spider",
    ]

    output_dir = os.path.join(
        proj_ser_tools_path,
        "run_scrape_play_store_output",
    )
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for device_app_id in device_app_id_list:
        scrap_success, result = scrap_play_store(
            device_app_id=device_app_id,
            lang="en",
        )

        if not scrap_success:
            print(f"{device_app_id} scrape failed!")
            continue

        json_path = os.path.join(output_dir, f"{device_app_id}.json")
        with open(json_path, "w") as outfile:
            json.dump(result, outfile, indent=4)

    g = 1


def main():

    # Run function
    run_scrape_play_store()


if __name__ == "__main__":
    sys.exit(main())
