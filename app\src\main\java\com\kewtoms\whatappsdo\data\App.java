package com.kewtoms.whatappsdo.data;

import com.kewtoms.whatappsdo.R;

/**
 * Represents an application with a name and an associated image
 * resource. This class is used to define the properties of an app
 * that can be displayed in the user interface, such as in a list or
 * grid of applications.
 *
 * <p>Example usage:</p>
 * <pre>
 *     App myApp = new App("My Application", R.drawable.my_app_icon);
 *     String appName = myApp.getName(); // Returns "My Application"
 *     int appIcon = myApp.getImageResourceId(); // Returns the resource ID for the app icon
 * </pre>
 *
 * <p>Predefined applications:</p>
 * <ul>
 *     <li>Diavolo - associated with R.drawable.diavolo</li>
 *     <li>Funghi - associated with R.drawable.funghi</li>
 * </ul>
 */
public class App {
 public static final App[] apps = {new App(
  "Diavolo",
  R.drawable.diavolo
 ), new App(
  "Funghi",
  R.drawable.funghi
 ),};
 private final String name;
 private final int imageResourceId;

 // Constructor
 private App(
  String name,
  int imageResourceId) {
  this.name = name;
  this.imageResourceId = imageResourceId;
 }

 /**
  * Retrieves the name of the application.
  *
  * @return the name of the application as a String.
  */
 public String getName() {
  return name;
 }

 /**
  * Retrieves the resource ID of the application's associated image.
  *
  * @return the resource ID as an integer, which can be used to access
  * the image resource in the application's drawable resources.
  */
 public int getImageResourceId() {
  return imageResourceId;
 }

}
