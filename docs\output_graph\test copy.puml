@startuml test_verify_client_apps_trace
skinparam ranksep 0
allowmixing

note "This is a note" as N1

node node [
This is a <b>node
----
You can use separator
====
of different kind
....
and style
]

note left
This is **bold**
This is //italics//
This is ""monospaced""
This is --stricken-out--
This is __underlined__
This is ~~wave-underlined~~
end note

note as n
**test list 1**
* Bullet list
* Second item
** Sub item
*** Sub sub item
* Third item
----
**test list 2**
# Numbered list
# Second item
## Sub item
## Another sub item
# Third item
end note

object user {
name = "Dummy"
id = 123
}

map CapitalCity {
USA => Washington
Germany => Berlin
}

' below will not work
' map CapitalCity {
  ' SDG
  ' SDG
  ' GDRE
' }

json JSON {
"fruit":"Apple",
"size":"Large",
"color": ["Red", "Green"]
}

map A {
c =>
}

frame script{
  card "fastapi app"{
    usecase "signup" as api_signup_endpoint
    usecase "signin" as api_signin_endpoint
  }

  note right of api_signup_endpoint : "This is the api endpoint"

  card "script api function"{
    usecase "signup" as api_signup
    usecase "signin" as api_signin
  }
}




note as nasf
**test list 1**
* Bullet list
* Second item
** Sub item
*** Sub sub item
* Third item
----
**test list 2**
# Numbered list
# Second item
## Sub item
## Another sub item
# Third item
end note

script --> nasf

api_signup_endpoint --> api_signup


class Foo {
+ field1
+ field2
}
class Bar {
+ field3
+ field4
}
Foo::field1 --> Bar::field3 : foo
Foo::field2 --> Bar::field4 : bar

note right of Foo::field1
a note
end note

note right of Foo
{{
  skinparam backgroundcolor transparent
  start

  while (count < max?)
    :count = count + increment;
  endwhile

  stop
}}
end note

note left of Foo
{{
  skinparam backgroundcolor transparent

  Alice -> Bob: some texts
  Bob --> Alice: some texts

  Alice -> Bob: some texts
  Alice <-- Bob: some texts
}}
end note

@enduml


