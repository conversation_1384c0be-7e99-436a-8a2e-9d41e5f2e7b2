import subprocess


def run_pytest(test_script: str) -> str:
    """
    Run a Python test script using pytest.

    Args:
        test_script (str): The path to the test script to run.

    Raises:
        FileNotFoundError: If the test script does not exist.
        subprocess.CalledProcessError: If pytest fails to run.

    Examples:
        ```python
        run_pytest("tests/test_dir_utils.py")
        ```
    """
    try:
        result = subprocess.run(
            ["pytest", test_script], check=False, capture_output=True, text=True
        )
        print(result.stdout)
        if result.returncode == 0:
            return f"All tests in ({test_script}) passed with result ({result.stdout})."
        else:
            print(f"Error running pytest: {result.stderr}")
            return f"Tests in ({test_script}) failed with result ({result.stdout})."
    except FileNotFoundError:
        raise FileNotFoundError(f"The test script {test_script} does not exist.")
