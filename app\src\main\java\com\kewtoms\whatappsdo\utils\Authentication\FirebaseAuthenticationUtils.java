package com.kewtoms.whatappsdo.utils.Authentication;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.util.Patterns;

import androidx.annotation.NonNull;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseAuth;
import com.google.android.gms.tasks.TaskCompletionSource;
import com.google.android.gms.tasks.Tasks;
import com.google.firebase.auth.FirebaseUser;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


public class FirebaseAuthenticationUtils {
  private static final String TAG = "APP:FirebaseAuthenticationUtils";
  private static final String SUCCESS_STR = "Registration Successful";
  private static final String EMAIL_OR_PASS_EMPTY_STR =
    "Email and Password cannot be empty";
  private static final String FAIL_STR = "Registration Failed";

  public static void registerNewUser(
    Context context,
    FirebaseAuth auth,
    String email,
    String password,
    String confirmPassword,
    RegistrationCallback callback) {
    if (email.isEmpty() || password.isEmpty()) {
      // return new RegisterResult(false, EMAIL_OR_PASS_EMPTY_STR);
      callback.onResult(new RegisterResult(
        false,
        EMAIL_OR_PASS_EMPTY_STR
      ));
      return;
    }


    if (!isValidEmail(email)) {
      // return new RegisterResult(false, "Invalid email format");
      callback.onResult(new RegisterResult(
        false,
        "Invalid email format."
      ));
      return;
    }

    if (!isStrongPassword(password)) {
      // return new RegisterResult(false, "Weak password. Please use a stronger password.");
      callback.onResult(new RegisterResult(
        false,
        "Weak password. Please use a stronger " + "password."
      ));
      return;
    }

    if (!isPasswordMatch(
      password,
      confirmPassword
    )) {
      callback.onResult(new RegisterResult(
        false,
        "Password not matched."
      ));
      return;
    }

    auth.createUserWithEmailAndPassword(
      email,
      password
    ).addOnCompleteListener(
      (Activity) context,
      new OnCompleteListener<AuthResult>() {
        @Override
        public void onComplete(@NonNull Task<AuthResult> task) {
          if (task.isSuccessful()) {
            // taskCompletionSource.setResult(new RegisterResult(true,
            // SUCCESS_STR));
            callback.onResult(new RegisterResult(
              true,
              SUCCESS_STR
            ));
          } else {
            // taskCompletionSource.setResult(new RegisterResult(false, FAIL_STR));
            callback.onResult(new RegisterResult(
              false,
              FAIL_STR
            ));
          }
        }
      }
    );
  }

  public static boolean isValidEmail(String email) {
    return Patterns.EMAIL_ADDRESS.matcher(email).matches();
  }

  /**
   * Checks if a password is strong. A strong password is defined as
   * one that is at least 8 characters long and contains at least one
   * uppercase letter, one lowercase letter, one number, and one
   * special character.
   *
   * @param password The password to be validated.
   * @return true if the password is strong; otherwise, false.
   */
  public static boolean isStrongPassword(String password) {
    // Checks if the password is at least 8 characters long
    // Checks if the password contains at least one uppercase letter
    // Checks if the password contains at least one lowercase letter
    // Checks if the password contains at least one number
    // Checks if the password contains at least one special character
    return password.length() >= 8 && password.matches(".*[A-Z].*") && password.matches(".*[a-z].*") && password.matches(".*\\d.*") && password.matches(".*[~`!@#$%^&+=,./?<>:;'\"{}].*");
  }

  /**
   * Checks if the password matches the confirm password.
   *
   * @param password the user's input password
   * @param confirmPassword the user's input confirm password
   * @return true if the password and confirm password are the same,
   * false otherwise
   */
  public static boolean isPasswordMatch(
    String password,
    String confirmPassword) {
    // Compares the password and confirm password for equality
    return password.equals(confirmPassword);
  }

  public static void sendVerificationEmail(
    FirebaseAuth auth,
    SendVerfyEmailCallback callback) {
    // Check if auth is null
    if (auth == null) {
      Log.e(
        TAG,
        "FirebaseAuth is null"
      );
      callback.onResult(new SendEmailResult(
        false,
        "FirebaseAuth is null"
      ));
      return;
    }

    FirebaseUser user = auth.getCurrentUser();

    if (user == null) {
      Log.e(
        TAG,
        "Current user is null"
      );
      callback.onResult(new SendEmailResult(
        false,
        "Current user is null"
      ));
      return;
    }

    user.sendEmailVerification().addOnCompleteListener(task -> {
      if (task.isSuccessful()) {
        callback.onResult(new SendEmailResult(
          true,
          "Verification email sent to " + user.getEmail() + " successfully"
        ));
      } else {
        // Show failure message with more details on the main UI thread
        Exception e = task.getException();
        String errorMessage =
          e != null ? e.getMessage() : "Unknown error";

        callback.onResult(new SendEmailResult(
          false,
          "Failed to send" + " verification email: " + errorMessage
        ));

      }
    });


  }

  public static CompletableFuture<AuthResult> createUser(
    FirebaseAuth auth,
    String email,
    String password) {
    CompletableFuture<AuthResult> future = new CompletableFuture<>();

    // Run the Firebase call in a separate thread
    new Thread(() -> {
      try {
        // Create user and wait for the result
        AuthResult result =
          Tasks.await(auth.createUserWithEmailAndPassword(
            email,
            password
          ));
        future.complete(result); // Complete the future with the result
      } catch (Exception e) {
        future.completeExceptionally(e); // Complete exceptionally if there's an error
      }
    }).start();

    return future; // Return the CompletableFuture
  }

  public interface RegistrationCallback {
    void onResult(RegisterResult result);
  }

  public interface SendVerfyEmailCallback {
    void onResult(SendEmailResult result);
  }


}
