"""App plugin"""

from dataclasses import dataclass
from typing import List
from tomsze_utils.plugins.plugin_utils import factory


@dataclass
class PluginSample:
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, dict_app_data, plugin_unique_key) -> None:
        print(f"{self.type} does something")

        dict_app_data["test_result"] = "abc"
        self.aa = "a"


def register(type: str) -> None:
    """
    Registers a plugin class with the factory using the provided type.

    This function retrieves a class from the current module's global scope
    based on the provided type and registers it with the factory. If the
    class is not found, a ValueError is raised.

    Args:
        type (str): The type of the plugin to register.

    Raises:
        ValueError: If the specified type is not found in the current module.

    Examples:
    ```python
    register(type="PluginSample")  # Successfully registers the PluginSample class

    register(type="NonExistentPlugin")  # Raises ValueError: Type 'NonExistentPlugin' not found in the current module.
    ```
    """
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
