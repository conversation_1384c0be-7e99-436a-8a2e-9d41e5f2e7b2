from datetime import datetime, timedelta, timezone
import os
import random
import string
from typing import Any, Dict, List
import bcrypt
import jwt
from tomsze_utils.email_utils import send_email
from dotenv import load_dotenv
from fastapi import Depends, FastAPI, HTTPException, status
from jwt.exceptions import (
    InvalidTokenError,
    InvalidSignatureError,
    ExpiredSignatureError,
)


def send_verification_email(
    env_path: str,
    receiver_email: str,
    token: str,
    base_url: str = "http://localhost:8000",
    verify_endpoint: str = "verify_email",
    subject: str = "Comfirm Your Email",
    SMTP_server: str = "gmail",
    SMTP_server_port: int = 587,
) -> str:
    """
    Sends a verification email to the specified receiver.

    Args:
    -----
    env_path: str
        Path to the .env file containing email credentials.
    receiver_email: str
        The email address of the receiver.
    token: str
        The token to be included in the verification link.
    subject: str, optional
        The subject of the verification email (default is "Comfirm Your Email").
    SMTP_server: str, optional
        The SMTP server to use for sending the email (default is "gmail").
    SMTP_server_port: int, optional
        The port of the SMTP server (default is 587).

    Examples:
    ---------
    To send a verification email with default subject and server:
    ```python
    send_verification_email(env_path="./.env_email", receiver_email="<EMAIL>", token="abc123")
    ```

    To send a verification email with a custom subject and SMTP server:
    ```python
    send_verification_email(env_path="./.env_email", receiver_email="<EMAIL>", token="abc123", subject="Please Verify Your Email", SMTP_server="yahoo", SMTP_server_port=587)
    ```

    """
    verification_link = (
        f"{base_url}/{verify_endpoint}?token={token}&email={receiver_email}"
    )

    html_body = f"""
<html>
  <body>
    <h1>Confirm your email</h1>
    <p>Follow this link to confirm your email:</p>
    <p><a href="{verification_link}">Confirm your email address</a></p>
    <p>This link will expire in 30 minutes.</p>
  </body>
</html>
"""

    send_email(
        env_path=env_path,
        subject=subject,
        body=html_body,
        receiver_email=receiver_email,
        SMTP_server=SMTP_server,
        SMTP_server_port=SMTP_server_port,
    )

    return "Verification email sent successfully!"


def send_verification_email_with_code_only(
    env_path: str,
    receiver_email: str,
    verification_code: str,
    subject: str = "Comfirm Your Email",
    SMTP_server: str = "gmail",
    SMTP_server_port: int = 587,
) -> str:
    """
    Sends a verification email containing a verification code to the specified receiver.

    Args:
    -----
    env_path: str
        Path to the .env file containing email credentials.
    receiver_email: str
        The email address of the receiver.
    verification_code: str
        The verification code to be included in the email.
    subject: str, optional
        The subject of the verification email (default is "Comfirm Your Email").
    SMTP_server: str, optional
        The SMTP server to use for sending the email (default is "gmail").
    SMTP_server_port: int, optional
        The port of the SMTP server (default is 587).

    Returns:
    --------
    str
        A message indicating the email was sent successfully.

    Examples:
    ---------
    To send a verification email with default subject and server:
    ```python
    send_verification_email_with_code_only(env_path="./.env_email", receiver_email="<EMAIL>", verification_code="123456")
    ```

    To send a verification email with a custom subject and SMTP server:
    ```python
    send_verification_email_with_code_only(env_path="./.env_email", receiver_email="<EMAIL>", verification_code="123456", subject="Verify Your Account", SMTP_server="yahoo", SMTP_server_port=587)
    ```

    """
    html_body = f"""
<html>
  <body>
    <h1>Confirm your email</h1>
    <p>We need to verify your email address {receiver_email} before you can access your account. Enter the code below in the app.</p>
    <p>{verification_code}</p>
    <p>If you didn't sign up for WhatsAppDo, you can safely ignore this email. Someone else might have typed your email address by mistake.</p>
  </body>
</html>
"""

    send_email(
        env_path=env_path,
        subject=subject,
        body=html_body,
        receiver_email=receiver_email,
        SMTP_server=SMTP_server,
        SMTP_server_port=SMTP_server_port,
    )

    return "Verification email sent successfully!"


def create_verification_code(length: int = 6) -> str:
    """
    Generates a random verification code consisting of digits.

    Args:
    -----
    length: int
        The length of the verification code to be generated (default is 6).

    Returns:
    --------
    str
        A string representing the generated verification code.

    Examples:
    ---------
    To create a verification code of default length:
    ```python
    code = create_verification_code()
    ```

    To create a verification code of length 8:
    ```python
    code = create_verification_code(length=8)
    ```

    """
    return "".join(random.choices(string.digits, k=length))


def create_token(
    payload: Dict[str, any],
    secret_key: str = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7",
    algorithm: str = "HS256",
    expires_delta: timedelta | None = timedelta(minutes=15),
    current_datetime: datetime | None = None,
) -> str:
    """
    Creates a JSON Web Token (JWT) for the given data.

    Args:
    -----
    payload: dict[str, any]
        The data to encode in the token.
    secret_key: str, optional
        The secret key to encode the token (default is a predefined key).
    algorithm: str, optional
        The algorithm to use for encoding the token (default is "HS256").
    expires_delta: timedelta | None, optional
        The expiration time for the token (default is 15 minutes).
    current_datetime: datetime | None, optional
        The current datetime to use for token expiration (default is now in UTC).

    Returns:
    --------
    str
        The encoded JWT.

    The token includes the following claims:
    - `exp`: Expiration timestamp, after which the token is no longer valid.
    - `iat`: Issued at timestamp, representing when the token was created.

    Examples:
    ---------
    To create a token with default settings:
    ```python
    token = create_token(data={"user_id": 1})
    ```

    To create a token with a custom expiration time:
    ```python
    from datetime import timedelta
    token = create_token(data={"user_id": 2}, expires_delta=timedelta(hours=1))
    ```

    """
    if current_datetime is None:
        current_datetime = datetime.now(timezone.utc)  # Set to now if None
    to_encode = payload.copy()
    expire = current_datetime + expires_delta
    timestamp = int(current_datetime.timestamp())  # unit in seconds
    to_encode.update({"exp": int(expire.timestamp()), "iat": timestamp})
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=algorithm)
    return encoded_jwt


def create_access_token(
    payload: Dict[str, any],
    secret_key: str = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7",
    algorithm: str = "HS256",
    expires_delta: timedelta | None = timedelta(minutes=60),
    current_datetime: datetime | None = None,
) -> str:
    """
    Creates an access token (JWT) for the given data.

    This function is a wrapper around create_token, pre-configured for access tokens.
    The token includes the following claims:
    - `exp`: Expiration timestamp, after which the token is no longer valid.
    - `iat`: Issued at timestamp, representing when the token was created.

    Args:
    -----
    payload: dict[str, any]
        The data to encode in the token.
    secret_key: str, optional
        The secret key to encode the token (default is a predefined key).
    algorithm: str, optional
        The algorithm to use for encoding the token (default is "HS256").
    expires_delta: timedelta | None, optional
        The expiration time for the token (default is 60 minutes).
    current_datetime: datetime | None, optional
        The current datetime to use for token expiration (default is now in UTC).

    Returns:
    --------
    str
        The encoded JWT access token.
    """
    return create_token(payload, secret_key, algorithm, expires_delta, current_datetime)


def create_refresh_token(
    payload: Dict[str, any],
    secret_key: str = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7",
    algorithm: str = "HS256",
    expires_delta: timedelta | None = timedelta(days=30),
    current_datetime: datetime | None = None,
) -> str:
    """
    Creates a refresh token (JWT) for the given data.

    This function is a wrapper around create_token, pre-configured for refresh tokens with a default expiration of 30 days.
    The token includes the following claims:
    - `exp`: Expiration timestamp, after which the token is no longer valid.
    - `iat`: Issued at timestamp, representing when the token was created.

    Args:
    -----
    payload: dict[str, any]
        The data to encode in the token.
    secret_key: str, optional
        The secret key to encode the token (default is a predefined key).
    algorithm: str, optional
        The algorithm to use for encoding the token (default is "HS256").
    expires_delta: timedelta | None, optional
        The expiration time for the token (default is 30 days).
    current_datetime: datetime | None, optional
        The current datetime to use for token expiration (default is now in UTC).

    Returns:
    --------
    str
        The encoded JWT refresh token.
    """
    return create_token(payload, secret_key, algorithm, expires_delta, current_datetime)


def validate_token(
    token: str,
    secret_key: str = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7",
    algorithm: str = "HS256",
    required_keys: List[str] = None,
) -> tuple[Dict[str, Any] | None, str | None]:
    """
    Validates a JSON Web Token (JWT) and checks for required keys.

    Args:
    -----
    token: str
        The JWT to validate.
    secret_key: str, optional
        The secret key to decode the token (default is a predefined key).
    algorithm: str, optional
        The algorithm to use for decoding the token (default is "HS256").
    required_keys: List[str], optional
        A list of keys that must be present in the token payload.

    Returns:
    --------
    tuple[Dict[str, Any] | None, str | None]
        A tuple containing the payload and an optional error message.
        If the token is valid, the tuple will contain the payload and None.
        If the token is invalid, the tuple will contain None and an error message.

    Examples:
    ---------
    To validate a token without required keys:
    ```python
    payload, error = validate_token(token="your.jwt.token")
    ```

    To validate a token with required keys:
    ```python
    payload, error = validate_token(token="your.jwt.token", required_keys=["user_id", "email"])
    ```

    """
    if required_keys is None:
        required_keys = []

    try:
        payload = jwt.decode(
            jwt=token,
            key=secret_key,
            algorithms=[algorithm],
        )

        for key in required_keys:
            if key not in payload:
                return None, f"Missing required key: {key}"

        return payload, None
    except ExpiredSignatureError as e:  # Expired token
        return None, f"Expired signature: {str(e)}"
    except InvalidSignatureError as e:
        return None, f"Invalid signature: {str(e)}"
    except InvalidTokenError as e:
        return None, f"Invalid token: {str(e)}"
    except Exception as e:
        return None, f"An unexpected error occurred: {str(e)}"


def decode_jwt_without_secret_key(token: str) -> Dict[str, Any]:
    """
    Decode a JWT without verifying the signature.

    Args:
        token (str): The JWT token to decode.

    Returns:
        Dict[str, Any]: The decoded data if the token is valid, None otherwise.

    Examples:
    ---------
    To decode a valid JWT:
    ```python
    decoded_data = decode_jwt_without_secret_key(token="your.jwt.token")
    ```

    To decode an invalid JWT:
    ```python
    decoded_data = decode_jwt_without_secret_key(token="invalid.token.string")
    ```

    """
    try:
        decoded_data = jwt.decode(
            token,
            options={"verify_signature": False},
        )
        return decoded_data
    except jwt.InvalidTokenError:
        return None


def create_repeatable_token(
    payload: Dict[str, Any],
    secret_key: str,
    algorithm: str = "HS256",
) -> str:
    """
    Create a repeatable JWT token.

    Note:
    This function can create a repeatable if supplied the same secret key and payload.
    This function is not secure and should only be used for testing purposes.

    Args:
        payload (Dict[str, Any]): The payload data to encode in the token.
        secret_key (str): The secret key used to encode the token.
        algorithm (str): The algorithm to use for encoding the token (default is "HS256").

    Examples:
    ---------
    To create a repeatable token:
    ```python
    token = create_repeatable_token(payload={"user_id": 1}, secret_key="my_secret")
    ```

    To create a token with a different algorithm:
    ```python
    token = create_repeatable_token(payload={"user_id": 2}, secret_key="my_secret", algorithm="HS512")
    ```

    """
    token = jwt.encode(
        payload=payload,
        key=secret_key,
        algorithm=algorithm,
    )
    return token


def hash_password(password: str) -> bytes:
    """
    Generate a hashed password using bcrypt.

    Note:
    This function generates a salt and hashes the password with it.

    Args:
        password (str): The password to be hashed.

    Examples:
    ---------
    To hash a password:
    ```python
    hashed = hash_password(password="my_secure_password")
    ```

    To hash another password:
    ```python
    hashed = hash_password(password="another_password")
    ```

    """
    # Generate a salt
    salt = bcrypt.gensalt()
    # Hash the password with the generated salt
    hashed_password = bcrypt.hashpw(password.encode("utf-8"), salt)
    return hashed_password


def verify_password(hashed_password: bytes, password: str) -> bool:
    """
    Check if the provided password matches the stored hash.

    Note:
    This function uses bcrypt to compare the hashed password with the provided password.

    Args:
        hashed_password (bytes): The hashed password to compare against.
        password (str): The plain text password to verify.

    Examples:
    ---------
    To verify a password against a hash:
    ```python
    is_valid = verify_password(hashed_password=b'my_hashed_password', password='my_secure_password')
    ```

    To verify another password:
    ```python
    is_valid = verify_password(hashed_password=b'another_hashed_password', password='another_password')
    ```

    """
    try:
        return bcrypt.checkpw(password.encode("utf-8"), hashed_password)
    except Exception as e:
        print(e)
        return False


def main():
    ## Try sending an email
    # load_dotenv("./.env_email")
    # receiver_email = os.getenv("RECEIVER_EMAIL")  # no need @gmail.com

    # send_verification_email(
    #     env_path="./.env_email",
    #     receiver_email=receiver_email,
    #     token="asdflhu",
    # )

    # # Try decoding a JWT without a secret key
    # token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJleHAiOjE3MzEyMTAwMDR9.N9vc-10_HQrMDV4r34WjgrJJwBvTSmbrpFpDhetQqnE"
    # decoded = decode_jwt_without_secret_key(token)
    # print(decoded)

    # # Try creating a repeatable token
    # token = create_repeatable_token(
    #     payload={"user_id": 1},
    #     secret_key="secret",
    # )
    # assert (
    #     token
    #     == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxfQ.jYyRJbb0WImFoUUdcslQQfwnXTHJzne-6tsPd8Hrw0I"
    # )
    # print(token)

    # Try hashing a password
    hashed_password = hash_password("passwordABC0!")
    print(hashed_password)
    # b'$2b$12$KBZWpXAWNzdU7wCWzXuew.k5xpUKpS89RVOs8c.chwNNZdrj8sXkS'


if __name__ == "__main__":
    main()
