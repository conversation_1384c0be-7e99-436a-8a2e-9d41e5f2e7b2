{"general": {"init_steps": [], "steps": ["create_green_image"], "variables": {"var_online": false}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "log_to_file": true, "folder_path": "./tests/tmp_CA_logs", "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_simple_image_creator": [{"step_name": "create_green_image", "type": "PluginSimpleImageCreator", "use": true, "width": 1920, "height": 1080, "num_channels": 3, "channel_values_rgb": [16, 124, 16], "buffer_to_which": "image", "save": true, "save_path": "./tests/temp/green_image.png", "save2": false, "save_path2": ""}]}