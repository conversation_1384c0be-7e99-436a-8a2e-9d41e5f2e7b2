"""App plugin"""

import os
from dotenv import find_dotenv, dotenv_values
from dataclasses import dataclass
from tomsze_utils.env_parser_utils import parse_env
from tomsze_utils.plugins.constant.plugin_constants import (
    PLUGIN_ENV_READER_ENV_DICT_VAR_NAME,
)
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginEnvReader:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        use_env_path = parse_data_and_store(
            logger,
            "use_env_path",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        env_path = parse_data_and_store(
            logger,
            "env_path",
            data_obj,
            step_config,
            config,
            type="str",
            default=None,
        )

        if not use_env_path:
            env_path = find_dotenv()

        if not os.path.exists(env_path):
            error_text = f'.env path "{env_path}" does not exist.'
            print(error_text)

        # Read .env and store data to data_obj.
        env_dict = parse_env(env_path)
        for key in env_dict.keys():
            env_data = env_dict[key]
            data_obj.dict_var[f"{current_step}.{key}"] = env_data

        data_obj.dict_var[f"{current_step}.{PLUGIN_ENV_READER_ENV_DICT_VAR_NAME}"] = (
            env_dict
        )
        data_obj.dict_var[current_step] = "test_result"

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
