from typing import List, Optional  # Add Optional import

from typing import Union  # Add Union import


def try_decode_byte(
    byte: bytes,
    encodings_to_try: List[str] = ["utf-8", "gbk"],
) -> Union[str, None]:  # Use Union for type hinting
    """
    Attempts to decode a byte sequence using a list of encodings.

    Args:
        byte (bytes): The byte sequence to decode.
        encodings_to_try (List[str], optional): A list of encodings to try. Defaults to ["utf-8", "gbk"].

    Returns:
        Optional[str]: The decoded string if successful, otherwise None.

    Examples:
        ```python
        >>> try_decode_byte(b'Hello, world!')
        'Hello, world!'

        >>> try_decode_byte(b'\xe4\xbd\xa0\xe5\xa5\xbd', encodings_to_try=['gbk', 'utf-8'])
        '你好'
        ```
    """
    for encoding in encodings_to_try:
        try:
            return byte.decode(encoding)
        except (UnicodeDecodeError, TypeError):  # Catch specific exceptions
            continue  # Use continue instead of pass for clarity
    return None


def byte_data_to_int_list(byte_data: bytes) -> List[int]:
    """
    Converts a byte sequence to a list of integers.

    Args:
        byte_data (bytes): The byte sequence to convert.

    Returns:
        List[int]: A list of integers representing the byte values.

    Examples:
        >>> byte_data_to_int_list(b'Hello')
        [72, 101, 108, 108, 111]
    """
    return list(byte_data)
