import json
import re
import copy
from typing import Any, List, Tuple


class Data(object):
    def __init__(self):
        self.current_step = None
        self.config = {}
        self.dict_var = {}
        self.dict_step_type = {}
        self.dict_step_config = {}
        self.list_steps = []
        self.hijack_next_step_index = False
        self.next_step_index = []
        self.logger = None
        self.log_file_path = None


def parse_data_and_store(
    logger: Any,
    key: str,
    dict_data: Any,
    dict_config: dict,
    dict_main_config: dict,
    type: str = "float",
    default: Any = None,
) -> Any:
    """
    Parses data and stores it in the provided dictionary.

    This function retrieves data based on the provided key and stores it in the
    `dict_var` of `dict_data` under the step name if specified.

    Args:
        logger: The logger instance used for logging.
        key: The key to retrieve data from the configuration.
        dict_data: The data dictionary where parsed data will be stored.
        dict_config: The configuration dictionary containing the data.
        dict_main_config: The main configuration dictionary.
        type: The type of data to parse (default is "float") If type is list, just ignore it.
        default: The default value to return if the key is not found.

    Returns:
        The parsed data.

    Examples:
    ```python
    result = parse_data_and_store(logger=my_logger, key='temperature', dict_data=my_data_dict, dict_config=my_config_dict, dict_main_config=my_main_config_dict)
    ```

    ```python
    result = parse_data_and_store(logger=my_logger, key='pressure', dict_data=my_data_dict, dict_config=my_config_dict, dict_main_config=my_main_config_dict, type='int', default=0)
    ```
    """
    data = parse_data(
        logger,
        key,
        dict_data,
        dict_config,
        dict_main_config,
        type=type,
        default=default,
    )

    stepname = dict_config.get("step_name", None)
    if stepname:
        dict_data.dict_var[f"{stepname}.{key}"] = data

    return data


def parse_data(
    logger: Any,
    key: str,
    dict_data: Any,
    dict_config: dict,
    dict_main_config: dict,
    type: str = "float",
    default: Any = None,
) -> Any:
    """
    Parses data and retrieves it from the provided dictionaries.

    This function retrieves data based on the provided key and stores it in the
    `dict_var` of `dict_data` under the step name if specified.

    Args:
        logger: The logger instance used for logging.
        key: The key to retrieve data from the configuration.
        dict_data: The data dictionary where parsed data will be stored.
        dict_config: The configuration dictionary containing the data.
        dict_main_config: The main configuration dictionary.
        type: The type of data to parse (default is "float").
        default: The default value to return if the key is not found.

    Returns:
        The parsed data.

    Examples:
    ```python
    result = parse_data(logger=my_logger, key='temperature', dict_data=my_data_dict, dict_config=my_config_dict, dict_main_config=my_main_config_dict)
    ```

    ```python
    result = parse_data(logger=my_logger, key='pressure', dict_data=my_data_dict, dict_config=my_config_dict, dict_main_config=my_main_config_dict, type='int', default=0)
    ```
    """
    unit = None
    if dict_data is not None and "dict_var" in dict_data.__dict__.keys():
        if "unit" in dict_data.dict_var.keys():
            unit = dict_data.dict_var["unit"]

    pixel_size = None
    if dict_data is not None and "dict_var" in dict_data.__dict__.keys():
        if "pixel_size" in dict_data.dict_var.keys():
            pixel_size = dict_data.dict_var["pixel_size"]

    dict_var = dict_main_config["general"]["variables"]
    # use = dict_config['use']

    # must type a correct key before running the program
    # assert key in dict_config.keys(), f'key {key} does not exist, please specify in setting.json'
    if (
        key not in dict_config.keys()
        and key not in dict_var.keys()
        and key not in dict_data.dict_var.keys()
    ):
        return default

    data = copy.copy(dict_config[key])
    data_ori = copy.copy(data)

    if isinstance(data, str):
        # if contain bracket, just convert each into corresponding data
        # extract each bracket content
        pattern = "\{([^{\}]*)\}"
        pattern_eval = "eval\{([^{\}]*)\}"
        # data_ori = data
        data_tmp = data

        prev_data_tmp = data_tmp
        end_while = False
        while (
            isinstance(data_tmp, str) and "{" in data_tmp and end_while == False
        ):  # while brackets remain
            list_extracted = re.findall(pattern, data_tmp)
            list_extracted = list(set(list_extracted))
            list_extracted_eval = re.findall(pattern_eval, data_tmp)
            list_extracted_eval = list(set(list_extracted_eval))
            if len(list_extracted_eval) > 0:
                has_eval_bracket = True

            for extracted in list_extracted:
                prev_data_tmp = data_tmp
                replace_as = ""
                extracted_key = extracted
                extracted_key_with_str = extracted

                # check if (str) exists
                if "(str)" in extracted:
                    extracted_key_with_str = extracted_key
                    extracted_key = extracted_key.replace("(str)", "")

                if dict_data is not None and "dict_var" in dict_data.__dict__.keys():
                    if extracted_key in dict_data.dict_var.keys():
                        replace_as = copy.copy(dict_data.dict_var[extracted_key])

                if extracted_key in dict_var.keys():
                    replace_as = copy.copy(dict_var[extracted_key])

                if unit is not None and unit in str(replace_as):
                    replace_as = parse_data_with_unit(
                        replace_as, unit, pixel_size, type
                    )

                if extracted in list_extracted_eval:
                    if isinstance(extracted_key, str) and has_eval_bracket:
                        data_obj = dict_data  # allow access of image through eval, do not delete
                        try:
                            replace_as = eval(extracted_key)
                        except Exception as e:
                            logger.error(f"eval error on {data_ori}")
                            raise e

                if (
                    isinstance(data_tmp, str)
                    and replace_as != ""
                    and extracted_key_with_str not in list_extracted_eval
                ):
                    data_tmp = data_tmp.replace(
                        "{" + extracted + "}", str(replace_as)
                    )  # then remove them
                elif (
                    isinstance(data_tmp, str)
                    and replace_as != ""
                    and extracted_key_with_str in list_extracted_eval
                ):
                    data_tmp = data_tmp.replace(
                        "eval{" + extracted + "}", str(replace_as)
                    )  # then remove them

                if (
                    "{" not in data_tmp
                    and "{" in prev_data_tmp[0]
                    and "}" in prev_data_tmp[-1]
                ) or (
                    "{" not in data_tmp
                    and "eval{" in data_ori[:5]
                    and "}" in data_ori[-1]
                ):
                    data_tmp = replace_as

                # if no more difference,
                if prev_data_tmp == data_tmp:
                    end_while = True

        if unit is not None and unit in str(data_ori):
            data_tmp = parse_data_with_unit(data_tmp, unit, pixel_size, type)

        if type == "bool":
            data_tmp = str(data_tmp) == "True"

        if type == "int":
            data_tmp = int(data_tmp)

        if type == "dict":
            try:
                data_tmp = json_loads(data_tmp)
            except Exception as e:
                pass

        data = data_tmp

        # #TODO what if that part is not used yet
        # # final check in {} if left
        # if use:
        #     if has_bracket and not has_eval_bracket:
        #         if isinstance(data, str):
        #             assert '{' not in data, f'check the use of setting {data}'

    if isinstance(data, list):
        data_obj = dict_data
        for ind, data_in_list in enumerate(data):
            parsed_data = parse_data(
                logger,
                f"__TEMP_USE_{ind}__",
                data_obj,
                {
                    f"__TEMP_USE_{ind}__": data_in_list
                },  # Use this config to fit the rules in parse_data so not return default.
                dict_main_config,
            )
            data[ind] = parsed_data

    if isinstance(data, dict):
        data_obj = dict_data
        for key in data.keys():
            # for ind, data_in_list in enumerate(data):
            data_in_dict = data[key]
            parsed_data = parse_data(
                logger,
                f"__TEMP_USE_{key}__",
                data_obj,
                {
                    f"__TEMP_USE_{key}__": data_in_dict
                },  # Use this config to fit the rules in parse_data so not return default.
                dict_main_config,
            )
            data[key] = parsed_data

    return data


def parse_data_with_unit(
    data: Any,
    unit: str,
    pixel_size: Any,
    type: str = "float",
) -> Any:
    """
    Parses the given data by removing the specified unit and adjusting it based on the pixel size.

    This function is useful for converting data values that include units into a numerical format,
    optionally adjusting for pixel size.

    Args:
        data (Any): The data to be parsed, which can be a string or numeric value.
        unit (str): The unit to be removed from the data.
        pixel_size (Any): The pixel size to adjust the data. Can be a string or float.
        type (str): The type to convert the data to. Defaults to "float". Can be "int", "float", or "str".

    Returns:
        Any: The parsed data, adjusted for unit and pixel size.

    Examples:
    ```python
    result = parse_data_with_unit("100px", unit="px", pixel_size=2.0, type="float")
    # result will be 50.0

    result = parse_data_with_unit("200cm", unit="cm", pixel_size="1cm", type="int")
    # result will be 200
    ```

    Note: If pixel_size or unit is None, the original data is returned.
    """
    data_orig = data

    if pixel_size is None or unit is None:
        return data

    data_ori = data
    if isinstance(data, str):
        if unit in data:
            data = data.replace(unit, "")  # remove unit
            data = data.replace(" ", "")  # remove space

            if data == "":
                return data_ori

            if not isfloat(data):
                return data_ori

            if isinstance(pixel_size, str):
                data = float(data) / float(pixel_size.replace(unit, ""))
            elif isinstance(pixel_size, float):
                data = float(data) / pixel_size

            if type == "int":
                data = int(data)
            if type == "float":
                data = float(data)
            if type == "str":
                data = str(data)

    return data


def isfloat(value: Any) -> bool:
    """
    Check if the given value can be converted to a float.

    Args:
        value (Any): The value to check.

    Returns:
        bool: True if the value can be converted to a float, False otherwise.

    Examples:
    ```python
    result = isfloat("3.14")
    # result will be True

    result = isfloat("abc")
    # result will be False
    ```

    Note: This function will return False for any value that raises a ValueError when converted to float.
    """
    try:
        result = float(value)
        if "." in str(result):
            return True
        else:
            return False
    except ValueError:
        return False


def get_all_data(dict_config: dict) -> Tuple[List[Any], List[str]]:
    """
    Extracts all data from the provided configuration dictionary.

    This function traverses the given configuration dictionary recursively to collect
    all data entries into a list. It also identifies and collects any variables used
    within string data that are enclosed in curly braces ({}), excluding those that
    are prefixed with 'eval{'.

    Args:
        dict_config (dict): The configuration dictionary to extract data from.

    Returns:
        tuple: A tuple containing:
            - list_data (List[Any]): A list of all data extracted from the configuration.
            - list_used_variable (List[str]): A list of variables found within the string data.

    Examples:
        >>> config = {
        ...     "key1": "value1",
        ...     "key2": ["item1", "item2"],
        ...     "key3": {"subkey": "eval{some_function}"},
        ... }
        >>> get_all_data(dict_config=config)
        (['value1', 'item1', 'item2', 'eval{some_function}'], [])

        >>> config_with_variables = {
        ...     "keyA": "valueA with {var1}",
        ...     "keyB": ["itemB1", "itemB2"],
        ... }
        >>> get_all_data(dict_config=config_with_variables)
        (['valueA with {var1}', 'itemB1', 'itemB2'], ['{var1}'])
    """
    list_data = []
    recursive_loop_config(dict_config, list_data)
    pattern = "(\{[^}]*\})"

    list_used_variable = []
    for data in list_data:
        if isinstance(data, str):
            if "{" in data and "}" in data and "eval{" not in data:
                list_matches = re.findall(pattern, data)

                for match in list_matches:
                    list_used_variable.append(match)

    return list_data, list_used_variable


def recursive_loop_config(dict_config: dict, list_data: List[Any]) -> None:
    """
    Recursively traverses a configuration dictionary to collect all data entries.

    This function iterates through the provided dictionary and appends all non-dictionary
    and non-list items to the provided list. It also handles nested dictionaries and lists.

    Args:
        dict_config (dict): The configuration dictionary to traverse.
        list_data (List[Any]): The list to store collected data entries.

    Examples:
    ```python
    data_list = []
    config = {
        "key1": "value1",
        "key2": ["item1", "item2"],
        "key3": {"subkey": "value3"},
    }
    recursive_loop_config(dict_config=config, list_data=data_list)
    # data_list will be ['value1', 'item1', 'item2', 'value3']
    ```

    ```python
    data_list = []
    nested_config = {
        "keyA": ["valueA", {"keyB": "valueB"}],
        "keyC": "valueC",
    }
    recursive_loop_config(dict_config=nested_config, list_data=data_list)
    # data_list will be ['valueA', 'valueB', 'valueC']
    ```
    """
    for key in dict_config.keys():
        data = dict_config[key]

        if isinstance(data, list):
            for data_each in data:
                if isinstance(data_each, dict):
                    recursive_loop_config(data_each, list_data)
                else:
                    list_data.append(data_each)

        if not isinstance(data, dict) and not isinstance(data, list):
            list_data.append(data)
            # break
        elif isinstance(data, dict):
            recursive_loop_config(data, list_data)


def json_loads(json_str: str) -> dict:
    """
    Converts a JSON string to a Python dictionary.

    This function replaces single quotes with double quotes in the input JSON string
    and then parses it into a dictionary.

    Args:
        json_str (str): The JSON string to be converted.

    Returns:
        dict: The parsed JSON as a dictionary.

    Examples:
    ```python
    result = json_loads("{'key': 'value'}")
    # result will be {'key': 'value'}

    result = json_loads("{'name': 'John', 'age': 30}")
    # result will be {'name': 'John', 'age': 30}
    ```
    """
    json_str = json_str.replace("'", '"')  # replace ' to "
    json_dict = json.loads(json_str)

    return json_dict
