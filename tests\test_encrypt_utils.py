import os
import tempfile
import pytest
from tomsze_utils.api_utils import api_get_current_time_in_zone
from tomsze_utils.encrypt_utils import (
    decrypt_and_parse_json,
    encrypt_data,
    decrypt_data,
    generate_key_from_key_dict_and_api_timezone,
    generate_key_from_key_dict_and_sys_time,
    generate_key_from_keys_and_api_timezone,
    generate_key_from_keys_and_sys_time,
    generate_key_from_string,
    string_to_bytes,
)
from cryptography.fernet import Fernet
import tempfile
import json

from tomsze_utils.randome_utils import generate_date_string_dict


def test_string_to_bytes():
    # Test case 1: Normal string
    input_string = "Hello, World!"
    expected_output = b"Hello, World!"
    assert string_to_bytes(input_string) == expected_output

    # Test case 2: Empty string
    input_string = ""
    expected_output = b""
    assert string_to_bytes(input_string) == expected_output

    # Test case 3: String with special characters
    input_string = "Special characters: !@#$%^&*()"
    expected_output = b"Special characters: !@#$%^&*()"
    assert string_to_bytes(input_string) == expected_output

    # Test case 4: String with unicode characters
    input_string = "Unicode: ñ, é, ü"
    expected_output = b"Unicode: \xc3\xb1, \xc3\xa9, \xc3\xbc"
    assert string_to_bytes(input_string) == expected_output


def test_encrypt_data():
    # Generate a key for encryption
    key = Fernet.generate_key()
    data = b"Test data for encryption"

    # Encrypt the data
    encrypted_data = encrypt_data(data, key)

    # Assert that the encrypted data is not equal to the original data
    assert encrypted_data != data


def test_decrypt_data():
    # Generate a key for encryption
    key = Fernet.generate_key()
    data = b"Test data for decryption"

    # Encrypt the data
    encrypted_data = encrypt_data(data, key)

    # Decrypt the data
    decrypted_data = decrypt_data(encrypted_data, key, return_as_string=False)

    # Assert that the decrypted data is equal to the original data
    assert decrypted_data == data


def test_decrypt_data_failure():
    # Generate a key for encryption
    key = Fernet.generate_key()
    data = b"Test data for encryption"

    # Encrypt the data
    encrypted_data = encrypt_data(data, key)

    # Attempt to decrypt with a wrong key
    wrong_key = Fernet.generate_key()

    # Assert that decryption raises a ValueError
    with pytest.raises(
        ValueError,
        match="Decryption failed. The key may be invalid or the data may be corrupted.",
    ):
        decrypt_data(encrypted_data, wrong_key, return_as_string=False)


def test_key_generation_with_same_salt():
    string = "my_secret_password"
    salt = os.urandom(16)  # Generate a random salt

    # Generate key for the first call
    key1 = generate_key_from_string(string, salt)
    key1_len = len(key1)

    # Generate key for the second call using the same salt
    key2 = generate_key_from_string(string, salt)
    key2_len = len(key2)

    # Assert that the key length is the same
    assert key1_len == key2_len

    # Assert that the keys are the same
    assert key1 == key2

    # Assert that the salts are the same
    assert salt == salt


def test_decrypt_and_parse_json_success():
    key = Fernet.generate_key()
    original_data = {"key": "value"}
    encrypted_data = encrypt_data(json.dumps(original_data).encode("utf-8"), key)

    # Decrypt and parse the JSON
    result = decrypt_and_parse_json(encrypted_data, key)

    # Assert that the result matches the original data
    assert result == original_data


def test_decrypt_and_parse_json_invalid_json():
    key = Fernet.generate_key()
    invalid_encrypted_data = encrypt_data(b"Invalid JSON", key)

    # Decrypt and parse the invalid JSON
    result = decrypt_and_parse_json(invalid_encrypted_data, key)

    # Assert that the result is the decrypted string
    assert result == "Invalid JSON"


def test_generate_key_from_keys_and_api_timezone_success():
    key_1 = "@#$"
    key_2 = "#$%"
    time_zone = "Europe/London"  # Use a valid time zone

    # Generate the key
    key = generate_key_from_keys_and_api_timezone(key_1, key_2, time_zone)

    # Assert that the key is of type bytes
    assert isinstance(key, bytes)


def test_generate_key_from_keys_and_api_timezone_invalid_timezone():
    key_1 = "firstPart"
    key_2 = "secondPart"
    invalid_time_zone = "Invalid/Zone"  # Use an invalid time zone

    # Assert that generating a key with an invalid time zone raises an error
    with pytest.raises(ValueError, match="Error getting date from API."):
        generate_key_from_keys_and_api_timezone(key_1, key_2, invalid_time_zone)


def test_generate_key_from_keys_and_sys_time_success():
    key_1 = "testKey1"
    key_2 = "testKey2"

    # Generate the key
    key = generate_key_from_keys_and_sys_time(key_1, key_2)

    # Assert that the key is of type bytes
    assert isinstance(key, bytes)


def test_generate_key_from_keys_and_sys_time_invalid_key_length():
    key_1 = "short"
    key_2 = "key"

    # Generate the key
    key = generate_key_from_keys_and_sys_time(key_1, key_2)

    # Assert that the key is of type bytes
    assert isinstance(key, bytes)


def test_generate_key_from_key_dict_and_api_timezone_success():
    date_key_dict = generate_date_string_dict(num_strings=2, string_length=10)
    time_zone = "Europe/London"  # Use a valid time zone

    # Generate the key
    key = generate_key_from_key_dict_and_api_timezone(date_key_dict, time_zone)

    # Assert that the key is of type bytes
    assert isinstance(key, bytes)


def test_generate_key_from_key_dict_and_sys_time_success():
    date_key_dict = generate_date_string_dict(
        num_strings=2, string_length=10
    )  # Create date_key_dict using generate_date_string_dict
    # Generate the key
    key = generate_key_from_key_dict_and_sys_time(date_key_dict)

    # Assert that the key is of type bytes
    assert isinstance(key, bytes)
