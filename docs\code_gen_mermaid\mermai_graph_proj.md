```mermaid
flowchart TB
subgraph wdAFr["class PickleDatabase"]
    %% doc string
    VGVEQ["func"]
end
style wdAFr fill:#FFA500
subgraph hviyw["class UserDatabase"]
    %% doc string
    jjSoV["func"]
end
style hviyw fill:#FFA500
subgraph XxqeZ["class AppDatabase"]
    %% doc string
    pWggc["func"]
end
style XxqeZ fill:#FFA500
subgraph IlZcq["class SynonymDatabase"]
    %% doc string
    LoBaP["func"]
end
style IlZcq fill:#FFA500
subgraph jvaNA["classNoSynonymsDatabase"]
    %% doc string
    CIFHN["func"]
end
subgraph QBkMJ["class SynonymsNeedPowerthesaurusDatabase"]
    %% doc string
    XzhTe["func"]
end
style QBkMJ fill:#FFA500
subgraph vMtVr["class ToAddDatabase"]
    %% doc string
    MdiKC["func"]
end
style vMtVr fill:#FFA500
subgraph dNtqL["class EmailVerificationDatabase"]
    %% doc string
    byfcI["func"]
end
style dNtqL fill:#FFA500
subgraph yMkqt["class UserDataDatabase"]
    %% doc string
    DqwJa["func"]
end
style yMkqt fill:#FFA500
subgraph whVzA["class RateLimitDatabase"]
    %% doc string
    bGKzD["func"]
end
style whVzA fill:#FFA500
subgraph DJhlA["class BlockIpDatabase"]
    %% doc string
    MbDqU["func"]
end
style DJhlA fill:#FFA500
subgraph pDhtB["script FastApi"]
    %% doc string
    KuLkA["func"]
end
style pDhtB fill:#FF0000
subgraph CoxCw["script TestApiFunctions"]
    %% doc string
    SEPTG["func"]
end
style CoxCw fill:#FF0000
subgraph xFmWs["script ApiFunction"]
    %% doc string
    PsWXs["func"]
end
style xFmWs fill:#FF0000
subgraph NYtya["WrapTest"]
    %% doc string
    BjYwb["func"]
end
subgraph gfFQX["class MMCode"]
    %% 
    qyRDl["_create_coding_subgraph"]
    %% add class doc string
    JomnY["add_class"]
    %% 
    FRXlk["aggregate_class"]
    %% 
    vFHjm["composite_class"]
    %% 
    DFhPX["inherit_class"]
end
style gfFQX fill:#FFA500
subgraph KLqdo["method AddClass"]
    %% 
    TxnFN["_create_coding_subgraph"]
    %% 
    FPNjG["create_subgraph_title_from_class"]
end
style KLqdo fill:#008000
subgraph dvFTa["script Main"]
    %% 
    IsoWK["main"]
end
style dvFTa fill:#FF0000
wdAFr --> hviyw
subgraph zQkQN["WrapTest"]
    wdAFr
    hviyw
end
JomnY --> KLqdo
```
