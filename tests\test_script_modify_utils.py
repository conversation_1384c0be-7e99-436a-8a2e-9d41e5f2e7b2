import os

from tomsze_utils.script_modify_utils import (
    add_simple_function_def,
    append_simple_function_call,
    comment_last_line,
)


def test_add_simple_function_def():
    fake_script_path = r"./tests/fake_script_to_test/fake1.py"
    assert os.path.exists(fake_script_path)

    update_file, write_to_file_path = add_simple_function_def(
        script_path=fake_script_path,
        function_name="new",
        function_body="new()",
        overwrite=False,
    )

    assert update_file == True
    file = open(write_to_file_path)
    lines = file.readlines()
    assert "def new():" in lines[4]
    assert "new()" in lines[5]

    # ------
    fake_script_path = r"./tests/fake_script_to_test/fake2.py"
    assert os.path.exists(fake_script_path)

    update_file, write_to_file_path = add_simple_function_def(
        script_path=fake_script_path,
        function_name="new",
        function_body="new()",
        overwrite=False,
    )

    assert update_file == True
    file = open(write_to_file_path)
    lines = file.readlines()
    assert "def new():" in lines[4]
    assert "new()" in lines[5]

    # ----------
    fake_script3_path = r"./tests/fake_script_to_test/fake3.py"
    assert os.path.exists(fake_script3_path)

    update_file, write_to_file_path = add_simple_function_def(
        script_path=fake_script3_path,
        function_name="new",
        function_body="new()",
        overwrite=False,
    )

    assert update_file == True
    file = open(write_to_file_path)
    lines = file.readlines()
    assert "def new():" in lines[7]
    assert "new()" in lines[8]


def test_append_simple_function_call():
    fake_script_path = r"./tests/fake_script_to_test/fake4.py"
    assert os.path.exists(fake_script_path)

    update_file, write_to_file_path = append_simple_function_call(
        script_path=fake_script_path, function_name="new", overwrite=False
    )

    assert update_file == True
    file = open(write_to_file_path)
    lines = file.readlines()
    assert "new()" in lines[13]


def test_comment_last_line():
    fake_script_path = r"./tests/fake_script_to_test/fake5.py"
    assert os.path.exists(fake_script_path)

    update_file, write_to_file_path = comment_last_line(
        script_path=fake_script_path, overwrite=False
    )

    assert update_file == True
    file = open(write_to_file_path)
    lines = file.readlines()
    assert "#" in lines[-1]


if __name__ == "__main__":
    # test_add_simple_function_def()
    # test_append_simple_function_call()
    test_comment_last_line()
