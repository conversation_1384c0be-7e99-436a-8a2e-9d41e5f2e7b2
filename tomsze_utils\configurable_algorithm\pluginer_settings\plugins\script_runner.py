"""App plugin"""

from contextlib import redirect_stdout
from dataclasses import dataclass
from io import StringIO
import io
import os
import runpy
import sys
from tomsze_utils.import_utils import import_module_by_path
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginScriptRunner:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        script_path = parse_data_and_store(
            logger,
            "script_path",
            data_obj,
            step_config,
            config,
        )

        function_name = parse_data_and_store(
            logger,
            "function_name",
            data_obj,
            step_config,
            config,
        )

        args_dict = parse_data_and_store(
            logger,
            "args_dict",
            data_obj,
            step_config,
            config,
            type="dict",
            default={},
        )

        obj_stringio = io.StringIO()

        with redirect_stdout(obj_stringio):
            load_error, module = import_module_by_path(
                absolute_path=os.path.realpath(script_path)
            )
            return_val = None
            if function_name in module.__dict__.keys():
                # Run function here
                return_val = module.__dict__[function_name](**args_dict)
            else:
                error = f"{function_name}() not in script"
                print(error)
                data_obj.dict_var[f"{current_step}.exception"] = error

        captured_output = obj_stringio.getvalue()
        logger.info(captured_output)

        data_obj.dict_var[f"{current_step}.captured_output"] = captured_output
        data_obj.dict_var[f"{current_step}.return"] = return_val

        if load_error:
            logger.info(load_error)
            data_obj.dict_var[f"{current_step}.exception"] = load_error

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
