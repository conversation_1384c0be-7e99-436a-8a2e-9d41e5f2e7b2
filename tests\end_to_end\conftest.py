# import os
# import pytest
# from tomsze_utils.variable_dump_read_utils import (
#     dump_variable_to_pickle,
#     load_pickle_file,
# )


# @pytest.fixture(scope="session")
# def test_database_name():
#     return "test_server_pickle_database"


# @pytest.fixture(scope="session")
# def server_apps_ids():
#     return {
#         "com.scopely.monopolygo": 1,
#     }


# @pytest.fixture(scope="session")
# def server_apps_ids_pickle_path(
#     tmpdir_factory,
#     test_database_name,
#     server_apps_ids,
# ):

#     directory = tmpdir_factory.mktemp(test_database_name)
#     pickle_path = os.path.join(directory, "server_apps_ids.pickle")

#     dump_variable_to_pickle(
#         variable=server_apps_ids,
#         pickle_path=pickle_path,
#     )

#     return pickle_path


# @pytest.fixture(scope="session")
# def server_apps_ids():
#     load_pickle_file(
#         pickle_path=server_apps_ids_pickle_path,
#         default={},
#     )
