package com.kewtoms.whatappsdo.model;

import static org.junit.Assert.assertEquals;

import android.content.Context;
import android.content.SharedPreferences;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import org.junit.Test;
import org.junit.runner.RunWith;


@RunWith(AndroidJUnit4.class)
public class UserUsageTest {
    // ------------------------getUsageCount, setUsageCount----------------------------------------
    @Test
    public void testGetUsageCountAfterSetting() {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        UserUsage userUsage = new UserUsage(context);
        userUsage.setUsageCount(7);
        assertEquals(7, userUsage.getUsageCount());
    }

    // ------------------------resetUsageCount----------------------------------------
    @Test
    public void testResetUsageCount() {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        UserUsage userUsage = new UserUsage(context);
        userUsage.setUsageCount(5);
        userUsage.resetUsageCount();
        assertEquals(0, userUsage.getUsageCount());
    }

    @Test
    public void testResetUsageCountPersistence() {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        UserUsage userUsage = new UserUsage(context);
        userUsage.setUsageCount(10);
        userUsage.resetUsageCount();

        // Verify that the usage count is reset in SharedPreferences
        SharedPreferences prefs = context.getSharedPreferences("MyAppPrefs", Context.MODE_PRIVATE);
        int savedUsageCount = prefs.getInt("usage_count", -1);
        assertEquals(0, savedUsageCount);
    }

    // ------------------------increaseUsageCount----------------------------------------
    @Test
    public void testIncreaseUsageCount() {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        UserUsage userUsage = new UserUsage(context);
        userUsage.setUsageCount(3);
        userUsage.increaseUsageCount();
        assertEquals(4, userUsage.getUsageCount());
    }

    @Test
    public void testIncreaseUsageCountMultiple() {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        UserUsage userUsage = new UserUsage(context);
        userUsage.setUsageCount(5);
        userUsage.increaseUsageCount();
        userUsage.increaseUsageCount();
        assertEquals(7, userUsage.getUsageCount());
    }

}
