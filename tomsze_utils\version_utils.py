import semver

from typing import Literal


def bump_version_str(
    original_version: str,
    commit_type: Literal["none", "patch", "minor", "major"],
) -> str:
    """
    Bump the version string based on the specified commit type.

    Args:
        original_version (str): The original version string to be bumped.
        commit_type (Literal["none", "patch", "minor", "major"]): The type of version bump.

    Returns:
        str: The bumped version string or the original if no valid commit_type is provided.

    Examples:
        ```python
        new_version = bump_version_str(original_version="1.0.0", commit_type="patch")
        print(new_version)  # Output: "1.0.1"
        ```

        ```python
        new_version = bump_version_str(original_version="1.0.0", commit_type="minor")
        print(new_version)  # Output: "1.1.0"
        ```

    """
    if original_version[0] == '"':
        original_version = original_version.replace('"', "")
    if original_version[0] == "'":
        original_version = original_version.replace("'", "")

    if commit_type == "patch":
        return semver.bump_patch(original_version)
    if commit_type == "minor":
        return semver.bump_minor(original_version)
    if commit_type == "major":
        return semver.bump_major(original_version)

    return original_version  # Return original if no valid commit_type is provided
