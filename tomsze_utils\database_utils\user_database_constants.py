class Constants:
    MIN_PASSWORD_LENGTH = 8
    REQUIRES_NUMBER = True
    REQUIRES_SYMBOL = True
    USER_DB_NAME = "user_db"
    VERIFICATION_CODE_LENGTH = 6


class SuccessMessages:
    SIGN_UP = "Sign up successful."
    SIGN_IN = "Sign in successful."
    VERIFIED = "Verification status retrieved."
    EMAIL_VERIFIED = "Email verified."
    PROFILE_UPDATED = "Profile updated."
    ACCOUNT_DELETED = "Account deleted."
    ROLE_UPDATED = "Role updated."
    ROLE_RETRIEVED = "Role retrieved."
    PASSWORD_RESET = "Password reset successful."
    VERIFICATION_EMAIL_SENT = "Verification email sent."
    PASSWORD_RESET_EMAIL_SENT = "Password reset email sent."
    USER_UPGRADED = "User upgraded."
    USER_LOGGED_IN = "User logged in."
    USE_COUNT_UPDATED = "User use count updated."
    USE_COUNT_RETRIEVED = "User use count retrieved."
    ACCESS_TOKEN_VALIDATED = "Access token validated."
    GENERATED_VERIFICATION_CODE = "Verification code generated."
    USERS_RETRIEVED = "Users retrieved successfully."


class ErrorMessages:
    EMAIL_REGISTERED = "Email already registered."
    USER_REGISTERED = "User already registered."
    USER_NOT_REGISTERED = "User is not registered."
    USER_NOT_FOUND = "User not found."
    INCORRECT_PASSWORD = "Incorrect password."
    INVALID_TOKEN = "Invalid token."
    TOKEN_EXPIRED = "Expired Token signature."
    TOKEN_INVALID_SIGNATURE = "Invalid token signature."
    TOKEN_INVALID = "Invalid token."
    VERIFICATION_CODE_INCORRECT = "Verification code incorrect."


class ResponseKeys:
    RESULT = "db_result"
    IS_SUCCESS = "is_success"
    MESSAGE = "message"
    IS_VERIFIED = "is_verified"
    ROLE = "role"
    ACCESS_TOKEN = "access_token"
    REFRESH_TOKEN = "refresh_token"
    USERS = "users"
    USE_COUNT = "use_count"
    VERIFICATION_CODE = "verification_code"


class UserDataKeys:
    EMAIL = "email"
    HASHED_PASSWORD = "hashed_password"
    IS_VERIFIED = "is_verified"
    VERIFICATION_CODE = "verification_code"
    ROLE = "role"
    RESET_TOKEN = "reset_token"
    RESET_TOKEN_EXPIRES = "reset_token_expires"
    PLAN = "plan"
    LOG_IN_STATUS = "log_in_status"
    USE_COUNT = "use_count"


class Roles:
    USER = "user"


class Plans:
    FREE = "free"
    PAID = "paid"


class LoggedInStatus:
    NOT_LOGGED_IN = "not_logged_in"
    LOGGED_IN = "logged_in"
