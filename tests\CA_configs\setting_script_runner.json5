{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "run_script",
        ],
        "variables":{
            "var_online":false,
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "folder_path":"./tests/tmp_CA_logs",
            "log_to_file":true,
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true,
        }
    },
    "all_steps":[
        {
            "step_name": "run_script",
            "type": "PluginScriptRunner",
            "use": true,
            "script_path": './tests/fake_script_to_test/fake6.py',
            "function_name": 'fake6',
        }
    ]
   

}
