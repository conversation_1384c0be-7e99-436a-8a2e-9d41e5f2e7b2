import http
import json
from multiprocessing.pool import Thread<PERSON>ool
import sys
from urllib.request import urlopen
from bs4 import BeautifulSoup

import requests
from tomsze_utils.threads_utils import calculate_needed_qualified_workers
from typing import Dict, List, Tuple
from google_play_scraper.utils.data_processors import unescape_text
import google_play_scraper


def get_scrapingant_api_credit_status(
    conn: http.client.HTTPSConnection,
    api_key: str,
) -> dict:
    """
    Send a request to api.scrapingant.com to ask for an api key status.

    This function sends a GET request to the ScrapingAnt API to retrieve the status of the provided API key.

    Args:
        conn (http.client.HTTPSConnection): The connection object to the ScrapingAnt API.
        api_key (str): The API key to check the status for.

    Returns:
        dict: A dictionary containing the API key status information.

    Examples:
    ```python
    status = get_scrapingant_api_credit_status(conn=my_connection, api_key="your_api_key")
    ```

    ```python
    status = get_scrapingant_api_credit_status(conn=my_connection, api_key="another_api_key")
    ```
    """
    # https://docs.scrapingant.com/api-credits-usage#api-credits-usage-status
    # v2/usage?x-api-key=<YOUR_SCRAPINGANT_API_KEY>
    url = f"/v2/usage?x-api-key={api_key}"
    conn.request("GET", url)

    res = conn.getresponse()  # Might give Remote end closed connection without response
    data = res.read()
    dom = data.decode("utf-8")
    status_code = res.status

    if status_code != 200:
        print(f"status code {res.status}")
        print(f"api_key {api_key}")

    dict_api_key_status = json.loads(dom)
    dict_api_key_status["api_key"] = api_key
    print(json.dumps(dict_api_key_status, indent=4))

    return dict_api_key_status


def get_scrapingant_api_credit_status_threads(
    list_api_key: List[str],
) -> List[dict]:
    """
    Send request to api.scrapingant.com to ask
    for api key status using threads.

    Args:
        list_api_key (List[str]): A list of API keys to check the status for.

    Returns:
        List[dict]: A list of dictionaries containing the status of each API key.

    Examples:
    ```python
    status_list = get_scrapingant_api_credit_status_threads(list_api_key=["key1", "key2"])
    ```

    ```python
    status_list = get_scrapingant_api_credit_status_threads(list_api_key=["my_api_key", "another_key"])
    ```
    """
    # Create thread arguments.
    num_thread = len(list_api_key)
    list_conn = [
        http.client.HTTPSConnection("api.scrapingant.com")
        for i in range(len(list_api_key))
    ]

    list_args = list(zip(list_conn, list_api_key))

    # Send request to ask for api key status.
    list_credit_status = ThreadPool(num_thread).starmap(
        get_scrapingant_api_credit_status, list_args
    )
    for conn in list_conn:
        conn.close()

    return list_credit_status


def calculate_suitable_api_key(
    list_api_key: List[str],
    total_request_needed: int,
    safety_bound: float = 0.1,
) -> Tuple[List[str], int, bool]:
    """
    Check each API key's remaining credit.
    If more than the number of requests per thread,
    return the list of suitable API keys.

    Note:
    Used thread.

    Args:
        list_api_key (List[str]): A list of API keys to check.
        total_request_needed (int): The total number of requests needed.
        safety_bound (float): A safety margin for the required credits. Defaults to 0.1.

    Returns:
        Tuple[List[str], int, bool]: A Tuple containing:
            - A list of suitable API keys.
            - Total credits available from suitable keys.
            - A boolean indicating if the credits are enough.

    Examples:
    ```python
    suitable_keys, total_credits, is_enough = calculate_suitable_api_key(
        list_api_key=["key1", "key2", "key3"],
        total_request_needed=100
    )
    ```

    ```python
    suitable_keys, total_credits, is_enough = calculate_suitable_api_key(
        list_api_key=["keyA", "keyB"],
        total_request_needed=50,
        safety_bound=0.2
    )
    ```
    """
    list_credit_status = get_scrapingant_api_credit_status_threads(list_api_key)

    # Check if remaining credit of each api key is
    # more than number of request per thread.
    # Find the list of qualified worker (api key) by given needed works (credit needed).
    work_needed = total_request_needed * (1 + safety_bound)
    dict_worker_ability = {}
    for api_key, credit_status in zip(list_api_key, list_credit_status):
        if "remained_credits" not in credit_status:
            continue

        credit_remain = credit_status["remained_credits"]

        if credit_remain == 0:
            continue

        dict_worker_ability[api_key] = credit_remain

    list_suitable_api_key, total_credit, is_credit_enough = (
        calculate_needed_qualified_workers(
            work_needed,
            dict_worker_ability,
        )
    )

    return list_suitable_api_key, total_credit, is_credit_enough


def scrape_apkgk(
    app_id: str,
) -> Tuple[bool, dict]:
    """
    Note: The site is currently offline

    Scrapes application data from the apkgk website using the provided app ID.

    Args:
        app_id (str): The ID of the application to scrape data for.

    Returns:
        Tuple[bool, dict]: A Tuple containing:
            - A boolean indicating success or failure of the scraping.
            - A dictionary containing scraped data about the application.

    Examples:
    ```python
    success, data = scrape_apkgk(app_id="com.example.app")
    ```

    ```python
    success, data = scrape_apkgk(app_id="com.another.app")
    ```
    """
    success = True
    scrape_data = {}
    url = f"https://apkgk.com/{app_id}"

    try:
        response = requests.get(url)

        if response.status_code != 200:
            print(f"status_code: {response.status_code}")
            success = False

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, "html.parser")

            # Get app description.
            desc_element = soup.find(itemprop="description")
            description = ""
            for content in desc_element.contents:
                if content.name not in [
                    "p",
                    "div",
                    "br",
                ]:  # some texts that are not description are in p and div.
                    description += content.strip()

            scrape_data["description"] = description

            # Get app name and category using script element.
            scrip_element_list = soup.select("head script")
            for script_element in scrip_element_list:
                text = script_element.text
                if '"applicationCategory":' in text:
                    data_dict = json.loads(text)
                    scrape_data["title"] = data_dict["name"]
                    scrape_data["summary"] = data_dict["description"]
                    scrape_data["genre"] = data_dict["applicationSubCategory"]
                    scrape_data["genreId"] = (
                        f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}'
                    )
                    scrape_data["categories"] = [
                        {
                            "name": data_dict["applicationCategory"],
                            "id": f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}',
                        },
                    ]

                    break  # skip the rest of elements

            scrape_data["url"] = url
            scrape_data["source"] = "apkgk"

    except Exception as e:
        print(f"scrape_apkgk exception: {e}")
        success = False

    return success, scrape_data


def scrape_apksos(
    app_id: str,
) -> Tuple[bool, dict]:
    """
    Scrape app information from apksos.com using the provided app ID.

    This function retrieves the app's title, description, genre, and categories from the apksos website.

    Args:
        app_id (str): The unique identifier for the app to be scraped.

    Returns:
        Tuple[bool, dict]: A Tuple containing a success flag and a dictionary with scraped data.

    Examples:
    ```python
    success, data = scrape_apksos(app_id="com.example.app")
    # success: True if scraping was successful, data: contains app information.

    success, data = scrape_apksos(app_id="com.another.app")
    # success: True if scraping was successful, data: contains app information.
    ```

    Note: The function will print the status code if the response is not 200.
    """
    success = True
    scrape_data = {}
    url = f"https://apksos.com/app/{app_id}"

    try:
        response = requests.get(url)

        if response.status_code != 200:
            print(f"status_code: {response.status_code}")
            success = False

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, "html.parser")

            # Get app name and category using script element.
            scrip_element_list = soup.select("head script")
            for script_element in scrip_element_list:
                text = script_element.text
                if '"applicationCategory":' in text:
                    data_dict = json.loads(text)
                    scrape_data["title"] = data_dict["name"]
                    scrape_data["summary"] = ""  # this site only provides description
                    scrape_data["description"] = data_dict["description"]
                    scrape_data["genre"] = data_dict["applicationSubCategory"]
                    scrape_data["genreId"] = (
                        f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}'
                    )
                    scrape_data["categories"] = [
                        {
                            "name": data_dict["applicationCategory"],
                            "id": f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}',
                        },
                    ]

                    break  # skip the rest of elements

            scrape_data["url"] = url
            scrape_data["source"] = "apksos"

    except Exception as e:
        print(f"scrape_apksos exception: {e}")
        success = False

    return success, scrape_data


def scrape_apksupport(
    app_id: str,
) -> Tuple[bool, dict]:
    """
    Scrape app data from apk.support given an app ID.

    This function retrieves the app's title, description, genre, and other related data from the specified URL.

    Args:
        app_id (str): The application ID to scrape data for.

    Returns:
        Tuple[bool, dict]: A Tuple containing a success flag and a dictionary with scraped data.

    Examples:
    ```python
    success, data = scrape_apksupport(app_id="com.example.app")
    # success: True if data was scraped successfully, data: dictionary with app details.

    success, data = scrape_apksupport(app_id="com.another.app")
    # success: True if data was scraped successfully, data: dictionary with app details.
    ```

    Note: The function will print the status code if the response is not 200.
    """
    success = True
    scrape_data = {}
    url = f"https://apk.support/app/{app_id}"

    try:
        # Add headers for this site to fix 403 forbidden status code.
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            print(f"status_code: {response.status_code}")
            success = False

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, "html.parser")

            # Get app description.
            desc_element = soup.find("div", class_="description")
            description = desc_element.text
            description = unescape_text(description)
            scrape_data["description"] = description

            # Get data using script element.
            scrip_element_list = soup.select("body script")
            for script_element in scrip_element_list:
                text = script_element.text
                if '"applicationCategory":' in text:
                    data_dict = json.loads(text)
                    scrape_data["title"] = data_dict["name"]
                    scrape_data["summary"] = data_dict["description"]
                    scrape_data["genre"] = data_dict["applicationSubCategory"]
                    scrape_data["genreId"] = (
                        f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}'
                    )
                    scrape_data["categories"] = [
                        {
                            "name": data_dict["applicationCategory"],
                            "id": f'{data_dict["applicationCategory"]}_{data_dict["applicationSubCategory"]}',
                        },
                    ]

                    break  # skip the rest of elements

            scrape_data["url"] = url
            scrape_data["source"] = "apksupport"

    except Exception as e:
        print(f"scrape_apksupport exception: {e}")
        success = False

    return success, scrape_data


# NOTE: apkmonk's app description is not found in static webpage


def scrape_play_store(
    device_app_id: str,
    lang: str = "en",
) -> Tuple[bool, Dict]:
    """Scrapes application data from the Google Play Store using the provided app ID and language.

    Args:
        device_app_id (str): The ID of the application to scrape data for.
        lang (str): The language of the Play Store page to scrape (defaults to 'en').

    Returns:
        Tuple[bool, Dict]: A Tuple containing:
            - A boolean indicating success or failure of the scraping.
            - A dictionary containing scraped data about the application.

    Examples:
    ```python
    success, data = scrape_play_store(device_app_id="com.supercell.clashofclans", lang="en")
    ```

    ```python
    success, data = scrape_play_store(device_app_id="com.another.app", lang="fr")
    ```
    """
    scrap_success = False
    result = {}
    try:
        result = google_play_scraper.app(device_app_id, lang=lang)  # defaults to 'en'
        scrap_success = True
        result["source"] = "playstore"
    except google_play_scraper.exceptions.NotFoundError:
        print(f"{device_app_id} is not found")

    return scrap_success, result


def main():
    # app_id = "com.yusibo.spider"
    # success, scrape_data = scrape_apkgk(app_id=app_id)

    # app_id = "com.Devapp.GymorJailHorrorGigachad"
    # success, scrape_data = scrape_apksos(app_id=app_id)

    # app_id = "com.gamenese.animal.battle.simulator3d"
    # success, scrape_data = scrape_apksupport(app_id=app_id)

    app_id = "com.supercell.clashofclans"
    scrap_success, result = scrape_play_store(app_id, lang="en")
    print(result)


if __name__ == "__main__":
    sys.exit(main())
