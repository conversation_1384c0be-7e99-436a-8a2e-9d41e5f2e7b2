package com.kewtoms.whatappsdo.utils;


import static com.kewtoms.whatappsdo.utils.ListUtils.nestedLookupOnListObject;

import android.util.Log;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kewtoms.whatappsdo.interfaces.ElementCallback;

import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ScrapePlayStoreUtils {
 public final String TAG = "APP:ScrapePlayStoreUtils";

 public final String descriptionSectorStr = "div.bARER";
 public final String appNameSectorStr = "h1";
 public final String scriptSectorStr = "body > script";

 public final String KEY = "(ds:\\d*)";
 public final String VALUE = "data:([\\s\\S]*?), sideChannel: \\{\\}";

 private final Pattern keyPattern = Pattern.compile(KEY);
 private final Pattern valuePattern = Pattern.compile(VALUE);

 public HashMap<String, Object> getDatasetMap(Document doc) {
  HashMap<String, Object> datasetMap = new HashMap<>();


  try {
   Elements scriptElements = doc.select(scriptSectorStr);

   if (!scriptElements.isEmpty()) {
    for (Element element : scriptElements) {
     String scriptData = element.data();

     Matcher keyMatcher = keyPattern.matcher(scriptData);
     Matcher valueMatcher = valuePattern.matcher(scriptData);

     Boolean b = keyMatcher.find();
     Boolean c = valueMatcher.find();

     if (b && c) {
      String key = keyMatcher.group();
      String value = valueMatcher.group();

      datasetMap.put(
       key,
       value
      );
     }
    }
   }

  } catch (Exception e) {
   Log.e(
    TAG,
    "getDatasetMap: " + e
   );
  }

  return datasetMap;
 }

 public HashMap<String, Object> getAppInfoFromAFInitData(Document doc) {

  HashMap<String, Object> dataMap = new HashMap<>();
  try {

   HashMap<String, Object> datasetMap = getDatasetMap(doc);

   ElementSpecs elementSpecs = new ElementSpecs();
   String[] keys =
    elementSpecs.detailMap.keySet().toArray(new String[0]);

   for (String key : keys) {
    ElementSpec value = elementSpecs.detailMap.get(key);

    if (value != null) {
     Object result = value.extractContent(datasetMap);
     dataMap.put(
      key,
      result
     );
    }
   }
  } catch (Exception e) {
   Log.e(
    TAG,
    "getAppInfoFromAFInitData: " + e
   );
  }

  return dataMap;
 }

 public List<Object> getAFInitDataListFromDataset(
  HashMap<String, Object> dataset,
  String key) {

  try {
   String jsonString = (String) dataset.get(key);

   assert jsonString != null;
   jsonString =
    StringUtils.createScrapeGooglePlayStoreParsableString(jsonString);
   ObjectMapper objectMapper = new ObjectMapper();
   try {
    JsonNode rootNode = objectMapper.readTree(jsonString);
    JsonNode dataNode = rootNode.get("data");

    if (dataNode.isArray()) {
     return objectMapper.convertValue(
      dataNode,
      new TypeReference<List<Object>>() {
      }
     );
    }
   } catch (Exception e) {
    Log.e(
     TAG,
     "getAFInitDataListFromScriptElement: " + e
    );
   }

  } catch (IllegalStateException e) {
   throw new RuntimeException(e);
  }

  return null;
 }

 public List<Object> getAFInitDataListFromScriptElement(Element element) {
  String scriptData = element.data();
  if (scriptData.isEmpty()) {
   return null;
  }

  Matcher keyMatcher = keyPattern.matcher(scriptData);
  Matcher valueMatcher = valuePattern.matcher(scriptData);
  if (keyMatcher.find() && valueMatcher.find()) {
   try {
    String jsonString = valueMatcher.group();
    jsonString =
     StringUtils.createScrapeGooglePlayStoreParsableString(jsonString);
    ObjectMapper objectMapper = new ObjectMapper();
    try {
     JsonNode rootNode = objectMapper.readTree(jsonString);
     JsonNode dataNode = rootNode.get("data");

     if (dataNode.isArray()) {
      return objectMapper.convertValue(
       dataNode,
       new TypeReference<List<Object>>() {
       }
      );
     }
    } catch (Exception e) {
     Log.e(
      TAG,
      "getAFInitDataListFromScriptElement: " + e
     );
    }

   } catch (IllegalStateException e) {
    throw new RuntimeException(e);
   }
  }

  return null;
 }

 private List<HashMap<String, Object>> extractCategories(
  List<Object> s,
  List<HashMap<String, Object>> categories) {
  if (categories == null) {
   categories = new ArrayList<>();
  }

  if (s == null || s.isEmpty()) {
   return categories;
  }

  if (s.size() >= 4 && s.get(0) instanceof String) {
   HashMap<String, Object> category = new HashMap<>();
   category.put(
    "name",
    s.get(0)
   );
   category.put(
    "id",
    s.get(2)
   );
   categories.add(category);
  } else {
   for (Object si : s) {
    extractCategories(
     (List<Object>) si,
     categories
    );
   }
  }

  return categories;
 }

 private List<HashMap<String, Object>> getCategories(List<Object> s) {
  Object extracted = nestedLookupOnListObject(
   s,
   Arrays.asList(118)
  );
  List<HashMap<String, Object>> categories = extractCategories(
   (List<Object>) extracted,
   null
  );

  if (categories.isEmpty()) {
   categories.add(new HashMap<String, Object>() {{
    put(
     "name",
     nestedLookupOnListObject(
      s,
      Arrays.asList(
       79,
       0,
       0,
       2
      )
     )
    );
    put(
     "id",
     nestedLookupOnListObject(
      s,
      Arrays.asList(
       79,
       0,
       0,
       2
      )
     )
    );
   }});

  }
  return categories;
 }

 class ElementSpec {
  private final Integer dsNum;
  private final List<Integer> indexesListInt;
  private final ElementCallback elementCallback;


  ElementSpec(
   Integer dsNum,
   List<Integer> indexesListInt,
   ElementCallback elementCallback) {
   this.dsNum = dsNum;
   this.indexesListInt = indexesListInt;
   this.elementCallback = elementCallback;
  }

  /**
   * Extract content using dsNum and indexesListInt from dataset.
   */
  public Object extractContent(HashMap<String, Object> dataset) {
   Object result;

   String key = "ds:" + dsNum;
   List<Object> dataList = getAFInitDataListFromDataset(
    dataset,
    key
   );

   result = nestedLookupOnListObject(
    dataList,
    indexesListInt
   );

   if (elementCallback != null) {
    result = elementCallback.onDone((List<Object>) result);
   }

   return result;
  }
 }

 class ElementSpecs {
  HashMap<String, ElementSpec> detailMap = new HashMap<>();


  public ElementSpecs() {
   try {
    this.detailMap.put(
     "description",
     new ElementSpec(
      5,
      Arrays.asList(
       1,
       2
      ),
      new ElementCallback() {
       @Override
       public Object onDone(List<Object> result) {
        return HtmlUtils.unescape2(HtmlUtils.getFirstNonEmpty(
         (String) nestedLookupOnListObject(
          result,
          Arrays.asList(
           12,
           0,
           0,
           1
          )
         ),
         (String) nestedLookupOnListObject(
          result,
          Arrays.asList(
           72,
           0,
           1
          )
         )
        ));
       }

      }
     )
    );

    this.detailMap.put(
     "genre",
     new ElementSpec(
      5,
      Arrays.asList(
       1,
       2,
       79,
       0,
       0,
       0
      ),
      null
     )
    );

    this.detailMap.put(
     "genreId",
     new ElementSpec(
      5,
      Arrays.asList(
       1,
       2,
       79,
       0,
       0,
       2
      ),
      null
     )
    );
    //            "categories": ElementSpec(5, [1, 2], get_categories, []),
    this.detailMap.put(
     "summary",
     new ElementSpec(
      5,
      Arrays.asList(
       1,
       2,
       73,
       0,
       1
      ),
      null
     )
    );
    //            "summary": ElementSpec(5, [1, 2, 73, 0, 1], unescape_text),
    this.detailMap.put(
     "categories",
     new ElementSpec(
      5,
      Arrays.asList(
       1,
       2
      ),
      new ElementCallback() {
       @Override
       public List<HashMap<String, Object>> onDone(List<Object> result) {
        return getCategories(result);
       }
      }

     )
    );

   } catch (Exception e) {
    Log.e(
     TAG,
     "ElementSpecs: " + e
    );
   }

  }


 }

}
