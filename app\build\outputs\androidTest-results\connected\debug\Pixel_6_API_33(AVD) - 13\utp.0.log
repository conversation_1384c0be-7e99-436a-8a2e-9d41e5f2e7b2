信息: Constructing runner from config.
信息: Configuring Android Instrumentation driver: android_instrumentation_runtime {
  instrumentation_info {
    app_package: "com.kewtoms.whatappsdo"
    test_package: "com.kewtoms.whatappsdo.test"
    test_runner_class: "androidx.test.runner.AndroidJUnitRunner"
  }
  instrumentation_args {
    args_map {
      key: "additionalTestOutputDir"
      value: "/sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output"
    }
    args_map {
      key: "class"
      value: "com.kewtoms.whatappsdo.auth.SignInTest#test_sign_in_success_mock"
    }
    args_map {
      key: "debug"
      value: "true"
    }
  }
}
am_instrument_timeout: 31536000

信息: Configuring AndroidTestApkInstallerPlugin: apks_to_install {
  apk_paths: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\intermediates\\apk\\debug\\app-debug.apk"
  install_options {
  }
  uninstall_after_test: true
}
apks_to_install {
  apk_paths: "D:\\code\\my_projects\\WhatAppsDo\\app\\build\\intermediates\\apk\\androidTest\\debug\\app-debug-androidTest.apk"
  install_options {
  }
  uninstall_after_test: true
}

信息: No installables found in test fixture. Nothing to install.
信息: Installing [D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\debug\app-debug.apk] on device emulator-5554.
信息: Installing [D:\code\my_projects\WhatAppsDo\app\build\intermediates\apk\androidTest\debug\app-debug-androidTest.apk] on device emulator-5554.
信息: Start logcat streaming.
信息: Running Android Instrumentation driver.
信息: Copying files from device to host: /sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output to D:\code\my_projects\WhatAppsDo\app\build\outputs\connected_android_test_additional_output\debugAndroidTest\connected\Pixel_6_API_33(AVD) - 13
信息: Copying /sdcard/Android/media/com.kewtoms.whatappsdo/additional_test_output/view-op-error-1.png to D:\code\my_projects\WhatAppsDo\app\build\outputs\connected_android_test_additional_output\debugAndroidTest\connected\Pixel_6_API_33(AVD) - 13\view-op-error-1.png
信息: Stop logcat streaming.
信息: Uninstalling com.kewtoms.whatappsdo for device emulator-5554.
信息: Uninstalling com.kewtoms.whatappsdo.test for device emulator-5554.
严重: Execute com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock: FAILED
androidx.test.espresso.AppNotIdleException: androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 539, 1040 and precision: 16, 16' on view 'view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)
... 33 trimmed
Caused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.
at androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)

androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 539, 1040 and precision: 16, 16' on view 'view.getId() is <2131230834/com.kewtoms.whatappsdo:id/button_login_large>'.
at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
at com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)
... 33 trimmed
Caused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.
at androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)
at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at android.os.Handler.handleCallback(Handler.java:942)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:201)
at android.os.Looper.loop(Looper.java:288)
at android.app.ActivityThread.main(ActivityThread.java:7924)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
