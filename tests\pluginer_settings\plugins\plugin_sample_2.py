"""App plugin"""

from dataclasses import dataclass
from typing import List
from tomsze_utils.plugins.plugin_utils import factory


@dataclass
class PluginSample2:
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, dict_app_data, plugin_unique_key) -> None:
        print(f"{self.type} does something")

        dict_app_data["test_result"] = "abc"
        self.aa = "a"


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
