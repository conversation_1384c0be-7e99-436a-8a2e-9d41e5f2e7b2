@startuml
'skinparam linetype polyline
'skinparam linetype ortho

folder "ui" {
    folder "home" {
        file "HomeFragment.java" {
          [onCreateView]
        }
    }
}

folder "utils" {
    file "RequestUtils.java" {
      [sendPostJsonVolley]
      [sendPostEncryptedJsonVolleyFuture]
    }
}

folder "model" {
    file "AppSearcher" {
      [searchApp]
      [context]
      [callback]
    }
}

usecase "To Update RecyclerViewAdapter"

[callback] <-up- "To Update RecyclerViewAdapter"


folder "interfaces" {
    top to bottom direction

    file "PostResponseCallback" {
      [onPostSuccess]
      [onPostError]
    }
    file "AppSearcherCallback" {
          [onSearchDone]
    }
}

cloud "server api" {
    [__sua]
    [__xxx]
}

[onPostSuccess] -> [callback] : call

[searchApp] <- [context] : use
[searchApp] <-up- [callback] : use

[onCreateView] -> "AppSearcher": create instance

[callback] -> "AppSearcherCallback": instance of
[searchApp] -down-> [sendPostJsonVolley] : call
[sendPostJsonVolley] -down-> [__sua] : request
[sendPostJsonVolley] <- [PostResponseCallback] : inline create instance of

@enduml