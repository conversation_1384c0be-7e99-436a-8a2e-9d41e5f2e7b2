import os
import pytest
from tomsze_utils.variable_dump_read_utils import (
    dump_variable_to_pickle,
    load_pickle_file,
)


from server.api.utils.cache import cache_or_func


@pytest.fixture(scope="session")
def variable_to_test():
    variable = {"data1_key": "data1_value"}
    return variable


@pytest.fixture(scope="session")
def test_pickle_path(tmpdir_factory, variable_to_test):

    directory = tmpdir_factory.mktemp("temp")
    pickle_path = os.path.join(directory, "data.pickle")

    dump_variable_to_pickle(
        variable=variable_to_test,
        pickle_path=pickle_path,
    )

    return pickle_path


def test_base_cache_function(test_pickle_path):
    pickle_data = {"data1_key": "data1_value"}
    data_key = "data1_key"
    expect_use_cache = True
    expect_obtained_value = "data1_value"
    expect_cached_value = {"data1_key": "data1_value"}

    def func(key):
        if "data2_key" == key:
            return "data2_value"
        return None

    kwargs = {"key": "data2_key"}

    cache_result = cache_or_func(test_pickle_path, pickle_data, data_key, func, kwargs)
    assert cache_result.use_cache == expect_use_cache
    assert cache_result.obtained_value == expect_obtained_value
    assert cache_result.cached_value == expect_cached_value

    pickle_data = {"data1_key": "data1_value"}
    data_key = "data2_key"
    expect_use_cache = False
    expect_obtained_value = "data2_value"
    expect_cached_value = {"data1_key": "data1_value", "data2_key": "data2_value"}

    cache_result = cache_or_func(test_pickle_path, pickle_data, data_key, func, kwargs)
    assert cache_result.use_cache == expect_use_cache
    assert cache_result.obtained_value == expect_obtained_value
    assert cache_result.cached_value == expect_cached_value

    loaded_variable = load_pickle_file(test_pickle_path, {})
    assert loaded_variable == expect_cached_value
