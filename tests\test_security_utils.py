import os
import pytest
import tempfile
from tomsze_utils.security_utils import (
    IpVisitEndpointData,
    LegitIPData,
    detect_frequent_ips,
    detect_hacker_ips_by_strange_endpoints,
    detect_hacker_ips_by_strange_endpoints_in_folder,
)


def test_detect_frequent_ips():
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary file
    temp_file_path = os.path.join(temp_dir, "temp_file.txt")

    # Write content to the temporary file
    with open(temp_file_path, "w") as f:
        f.write("2022-01-01 00:00:00,***********\n")
        f.write("2022-01-01 00:00:01,***********\n")
        f.write("2022-01-01 00:00:02,***********\n")
        f.write("2022-01-01 00:00:03,***********\n")

    # Test the function
    ip_datetime_list = []
    with open(temp_file_path, "r") as f:
        for line in f.readlines():
            dt_str, ip = line.strip().split(",")
            ip_datetime_list.append((dt_str, ip))

    result = detect_frequent_ips(ip_datetime_list, 3, 3)

    # Expected result
    expected_result = ["***********"]

    # Assertions
    assert result == expected_result

    # Clean up
    os.remove(temp_file_path)
    os.rmdir(temp_dir)


def test_detect_frequent_ips_no_frequent_ips():
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()

    # Create a temporary file
    temp_file_path = os.path.join(temp_dir, "temp_file.txt")

    # Write content to the temporary file
    with open(temp_file_path, "w") as f:
        f.write("2022-01-01 00:00:00,***********\n")
        f.write("2022-01-01 00:00:01,***********\n")
        f.write("2022-01-01 00:00:02,***********\n")
        f.write("2022-01-01 00:00:03,***********\n")

    # Test the function
    ip_datetime_list = []
    with open(temp_file_path, "r") as f:
        for line in f.readlines():
            dt_str, ip = line.strip().split(",")
            ip_datetime_list.append((dt_str, ip))

    result = detect_frequent_ips(ip_datetime_list, 3, 3)

    # Expected result
    expected_result = []

    # Assertions
    assert result == expected_result

    # Clean up
    os.remove(temp_file_path)
    os.rmdir(temp_dir)


def test_detect_frequent_ips_empty_input():
    # Test the function with an empty input
    result = detect_frequent_ips([], 3, 3)

    # Expected result
    expected_result = []

    # Assertions
    assert result == expected_result


def test_detect_hacker_ips_by_strange_endpoints():
    legit_ips = LegitIPData(ip_list=["***********", "***********"])

    # Test case 1: IP is legitimate and endpoint is legitimate
    endpoint_hacker_data_list_1 = [
        IpVisitEndpointData(
            ip="***********", requested_endpoint="/favicon.ico", method="GET"
        ),
        IpVisitEndpointData(
            ip="***********", requested_endpoint="/static/favicon.ico", method="GET"
        ),
    ]
    result_dict_1 = detect_hacker_ips_by_strange_endpoints(
        base_endpoint_url="http://example.com",
        ipVisitEndpointData_list=endpoint_hacker_data_list_1,
        legitIPData=legit_ips,
    )
    assert result_dict_1 == {}

    # Test case 2: IP is not legitimate and endpoint is strange
    endpoint_hacker_data_list_2 = [
        IpVisitEndpointData(
            ip="***********", requested_endpoint="/unknown", method="GET"
        ),
    ]
    result_dict_2 = detect_hacker_ips_by_strange_endpoints(
        base_endpoint_url="http://example.com",
        ipVisitEndpointData_list=endpoint_hacker_data_list_2,
        legitIPData=legit_ips,
    )
    assert result_dict_2 == {"***********": ["/unknown"]}

    # Test case 3: using ip_ranges_dict
    legit_ips = LegitIPData(ip_ranges_dict={"range1": "***********0-***********0"})

    # Test case 3.1: IP is within a legitimate range and endpoint is strange
    endpoint_hacker_data_list_1 = [
        IpVisitEndpointData(
            ip="***********5", requested_endpoint="/unknown", method="GET"
        ),
    ]
    result_dict_1 = detect_hacker_ips_by_strange_endpoints(
        base_endpoint_url="http://example.com",
        ipVisitEndpointData_list=endpoint_hacker_data_list_1,
        legitIPData=legit_ips,
    )
    assert result_dict_1 == {}

    # Test case 3.2: IP is not legitimate and endpoint is strange
    endpoint_hacker_data_list_2 = [
        IpVisitEndpointData(
            ip="***********0", requested_endpoint="/malicious", method="GET"
        ),
    ]
    result_dict_2 = detect_hacker_ips_by_strange_endpoints(
        base_endpoint_url="http://example.com",
        ipVisitEndpointData_list=endpoint_hacker_data_list_2,
        legitIPData=legit_ips,
    )
    assert result_dict_2 == {"***********0": ["/malicious"]}

    # Test case 3.3: multiple legit ip range
    legit_ips = LegitIPData(
        ip_ranges_dict={
            "range1": "***********0-***********0",
            "range2": "***********0-***********0",
        },
    )

    endpoint_hacker_data_list_2 = [
        IpVisitEndpointData(
            ip="***********1", requested_endpoint="/malicious", method="GET"
        ),
        IpVisitEndpointData(
            ip="***********1", requested_endpoint="/malicious", method="GET"
        ),
    ]
    result_dict_2 = detect_hacker_ips_by_strange_endpoints(
        base_endpoint_url="http://example.com",
        ipVisitEndpointData_list=endpoint_hacker_data_list_2,
        legitIPData=legit_ips,
    )
    assert result_dict_2 == {
        "***********1": ["/malicious"],
        "***********1": ["/malicious"],
    }


def test_detect_hacker_ips_by_strange_endpoints_in_folder():
    legit_ips = LegitIPData(ip_list=["***********", "***********"])

    # Test case 1: No hacker IPs in the log file
    with tempfile.TemporaryDirectory() as tmpdir:
        log_content = "Request ip: ***********\nRequest endpoint: /favicon.ico\nRequest method: GET\n----,\n"
        log_file_path = os.path.join(tmpdir, "log1.log")
        with open(log_file_path, "w") as log_file:
            log_file.write(log_content)

        result = detect_hacker_ips_by_strange_endpoints_in_folder(
            folder_path=tmpdir,
            base_endpoint_url="http://example.com",
            legitIPData=legit_ips,
        )
        assert result == {}

    # Test case 2: One hacker IP in the log file
    with tempfile.TemporaryDirectory() as tmpdir:
        log_content = "Request ip: ***********\nRequest endpoint: /unknown\nRequest method: GET\n----,\n"
        log_file_path = os.path.join(tmpdir, "log2.log")
        with open(log_file_path, "w") as log_file:
            log_file.write(log_content)

        result = detect_hacker_ips_by_strange_endpoints_in_folder(
            folder_path=tmpdir,
            base_endpoint_url="http://example.com",
            legitIPData=legit_ips,
        )
        assert result == {
            "log2.log": {
                "***********": [
                    "/unknown",
                ],
            },
        }


def test_detect_hacker_ips_by_strange_endpoints_in_folder_with_hacker_ips():
    # Test case 1: Valid log file with hacker IPs
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create a sample log file
        log_content = (
            "Request ip: ***********\n"
            "Request method: GET\n"
            "Request endpoint: /unknown\n"
            "Request body: {}\n"
            "----,\n"
            "Request ip: ***********\n"
            "Request method: GET\n"
            "Request endpoint: /favicon.ico\n"
            "Request body: {}\n"
            "----,\n"
            "Request ip: ***********\n"
            "Request method: POST\n"
            "Request endpoint: /malicious\n"
            "Request body: {}\n"
            "----,\n"
        )
        with open(os.path.join(tmpdir, "test_log1.log"), "w") as f:
            f.write(log_content)

        legit_ip_data = LegitIPData(ip_list=["***********", "***********"])
        result = detect_hacker_ips_by_strange_endpoints_in_folder(
            tmpdir, "http://example.com", legit_ip_data
        )
        assert "test_log1.log" in result
        assert result["test_log1.log"] == {
            "***********": ["/unknown"],
            "***********": ["/malicious"],
        }


def test_detect_hacker_ips_by_strange_endpoints_in_folder_with_no_hacker_ips():
    # Test case 2: Log file with no hacker IPs
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create a sample log file
        log_content = (
            "Request ip: ***********\n"
            "Request method: GET\n"
            "Request endpoint: /favicon.ico\n"
            "Request body: {}\n"
            "----,\n"
        )
        with open(os.path.join(tmpdir, "test_log2.log"), "w") as f:
            f.write(log_content)

        legit_ip_data = LegitIPData(ip_list=["***********", "***********"])
        result = detect_hacker_ips_by_strange_endpoints_in_folder(
            tmpdir, "http://example.com", legit_ip_data
        )
        assert "test_log2.log" not in result
