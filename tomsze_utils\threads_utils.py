from typing import Dict

def calculate_needed_qualified_workers(
    work_needed: float,
    dict_worker_ability: Dict[str, float],
):
    '''
    There is a kind of work that the workers must do it parallelly together.
    Say worker A and worker B do A work using A ability giving total works as 2.
    If one worker cannot continue to work to satisfy the work needed, he will be kicked.
    
    Find the final qualified workers that are possible to finish the work.
    
    Parameters:
    -----------
    work_needed: float
        The work that is needed.
        
    dict_worker_ability: Dict
        The dict containing each worker's ability.
        
    Returns:
    list_worker: List
        The list of workers qualified for the work.
        
    total_abilibity: float
        The total ability of qualified workers.
        
    is_worker_enough: bool
        Whether the final list of workders are enough for the work.
    --------
    
    Examples:
    ---------
    Given:
    work_needed = 160
    dict_work_ability = {
        'tom': 100,
        'david': 80,
        'mary': 50,
        'john': 20,
    }
    Returns:
    list_worker = ['Tom', 'david']
    total_abilibity = 180
    is_worker_enough = True
    
    Given:
    work_needed = 210
    dict_work_ability = {
        'tom': 100,
        'david': 80,
        'mary': 50,
        'john': 20,
    }
    Returns:
    list_worker = []
    total_abilibity = 0
    is_worker_enough = False
    
    Given:
    work_needed = 160
    dict_work_ability = {
        'tom': 100,
        'david': 55,
        'mary': 50,
        'john': 20,
    }
    Returns:
    list_worker = []
    total_abilibity = 0
    is_worker_enough = False
    '''
    list_worker = list(dict_worker_ability.keys())
    total_abilibity = 0
    is_worker_enough = False
    
    if not dict_worker_ability:
        return list_worker, total_abilibity, is_worker_enough
    
    num_worker_remain = len(dict_worker_ability)
    while True:
        avg_work = work_needed / num_worker_remain
        list_worker_loop = list_worker.copy()
        
        # Kick the worker if he/she is not qualified.
        num_worker_remain_tmp = num_worker_remain
        for worker in list_worker_loop:
            if dict_worker_ability[worker] < avg_work:
                list_worker.remove(worker)
                
        num_worker_remain = len(list_worker)
        
        if num_worker_remain == 0:
            break
        
        # End if no more worker is kicked.
        if num_worker_remain_tmp == num_worker_remain:
            break
        
    # Check if final worker is enought.
    for worker in list_worker:
        total_abilibity += dict_worker_ability[worker]
        
    if total_abilibity > work_needed:
        is_worker_enough = True

    return list_worker, total_abilibity, is_worker_enough