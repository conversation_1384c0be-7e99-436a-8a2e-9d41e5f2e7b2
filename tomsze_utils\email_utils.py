import os
import smtplib
from dotenv import find_dotenv, load_dotenv
from email.mime.text import MIMEText


def send_email(
    env_path: str,
    subject: str,
    body: str,
    receiver_email: str,  # Added receiver_email to function arguments
    subtype: str = "html",
    SMTP_server: str = "gmail",
    SMTP_server_port: int = 587,
) -> str:
    """
    Sends an email using the specified SMTP server.

    Parameters:
    ----------
    env_path: str
        .env path that contains SENDER_USERNAME, SENDER_PASS variables.
    subject: str
        The subject of the email.
    body: str
        The email body.
    receiver_email: str
        The email address of the receiver.
    SMTP_server: str
        The SMTP server provider.
    SMTP_server_port: int
        The SMTP server port.

    Examples:
    ---------
    To send a simple email:
    ```
    send_email('.env', 'Hello', 'This is a test email.', '<EMAIL>')
    ```

    To send an email using a different SMTP server:
    ```
    send_email('.env', 'Greetings', 'This is another test email.', '<EMAIL>', SMTP_server='yahoo', SMTP_server_port=587)
    ```

    """
    load_dotenv(env_path)

    sender_username = os.getenv("SENDER_USERNAME")  # no need @gmail.com
    sender_password = os.getenv(
        "SENDER_PASS"
    )  # generated from magage google account - app passowrd

    # Connect to the SMTP server.
    s = smtplib.SMTP(f"smtp.{SMTP_server}.com", SMTP_server_port)
    s.starttls()  # TLS mode
    s.login(sender_username, sender_password)

    # Create email message
    msg = MIMEText(body, subtype)
    msg["Subject"] = subject
    msg["From"] = sender_username
    msg["To"] = receiver_email

    # Send the email
    s.send_message(msg)
    s.quit()

    return "done"  # Return string "done" at the end
