<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.sign_up.SignupFragment">

    <Button
        android:id="@+id/button_login_small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:backgroundTint="@color/white"
        android:text="@string/login_button_small_text"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="@+id/guideline"
        app:layout_constraintTop_toTopOf="@+id/guideline_top" />

    <Button
        android:id="@+id/button_sign_up"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:text="@string/sign_up_button_text"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintTop_toTopOf="@+id/guideline_top" />

    <EditText
        android:id="@+id/editText_email_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:autofillHints="emailAddress"
        android:background="@drawable/edit_text_selector"
        android:ems="10"
        android:hint="@string/email_address_editText_text"
        android:inputType="textEmailAddress"
        android:padding="16dp"
        android:textColor="@color/editTextColor"
        android:textColorHint="@color/editTextHintColor"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@+id/button_login_small" />


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayout_password"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:hint="@string/password_editText_text"
        android:textColor="@color/editTextColor"
        android:textColorHint="@color/editTextHintColor"
        app:boxStrokeWidth="0dp"
        app:boxStrokeWidthFocused="0dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@+id/editText_email_address"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/textInputEditText_password"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:autofillHints="password"
            android:background="@drawable/edit_text_selector"
            android:inputType="textPassword">

        </com.google.android.material.textfield.TextInputEditText>
    </com.google.android.material.textfield.TextInputLayout>


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayout_confirm_password"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:hint="@string/confirm_password_editText_text"
        android:textColor="@color/editTextColor"
        android:textColorHint="@color/editTextHintColor"
        app:boxStrokeWidth="0dp"
        app:boxStrokeWidthFocused="0dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@+id/textInputLayout_password"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/textInputEditText_confirm_password"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:autofillHints="password"
            android:background="@drawable/edit_text_selector"
            android:inputType="textPassword">

        </com.google.android.material.textfield.TextInputEditText>
    </com.google.android.material.textfield.TextInputLayout>


    <EditText
        android:id="@+id/editText_verification_code"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:autofillHints="Verification code"
        android:background="@drawable/edit_text_selector"
        android:ems="10"
        android:hint="@string/verification_code_editText_text"
        android:inputType="number"
        android:padding="16dp"
        android:textColor="@color/editTextColor"
        android:textColorHint="@color/editTextHintColor"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right_middle"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@+id/textInputLayout_confirm_password" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintGuide_percent="0.5"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/button_sign_up_large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/sign_up_large_button_text"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/editText_verification_code" />

    <TextView
        android:id="@+id/textView_signin_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/sign_up_hint_default_text"
        android:textColor="@color/sign_up_hint_color"
        android:textSize="20sp"
        app:layout_constraintEnd_toEndOf="@+id/button_sign_up_large"
        app:layout_constraintStart_toStartOf="@+id/button_sign_up_large"
        app:layout_constraintTop_toBottomOf="@+id/button_sign_up_large" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="@dimen/signup_login_left_guideline_percentage" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="@dimen/signup_login_right_guideline_percentage" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="@dimen/signup_login_top_guideline_percentage" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_right_middle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintGuide_percent="0.65"
        app:layout_constraintStart_toStartOf="parent" />


    <Button
        android:id="@+id/button_get_verification_code"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:paddingHorizontal="0dp"
        android:text="@string/get_verification_code_button_text"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintStart_toEndOf="@id/guideline_right_middle"
        app:layout_constraintTop_toTopOf="@+id/editText_verification_code"
        app:layout_constraintBottom_toBottomOf="@id/editText_verification_code" />

</androidx.constraintlayout.widget.ConstraintLayout>