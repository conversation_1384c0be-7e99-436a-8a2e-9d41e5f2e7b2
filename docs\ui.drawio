<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36" version="24.8.3">
  <diagram name="第 1 页" id="vjuoHTFcSSVpj8_2455O">
    <mxGraphModel dx="2382" dy="1602" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MsuhZJ9VR9ve1l35eAj--15" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="-760" y="490" width="300" height="560" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--13" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry width="300" height="560" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--14" value="res" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="10" y="10" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--16" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="50" y="40" width="230" height="320" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--17" value="layout" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="60" y="50" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--18" value="fragment_sign_up.xml" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="85" y="300" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--55" value="fragment_home.xml" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="85" y="105" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--62" value="nav_header_main.xml" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="85" y="250" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--63" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="50" y="390" width="230" height="150" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--64" value="navigation" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="60" y="400" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--65" value="mobile_navigation.xml" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="85" y="430" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--66" value="content_main.xml" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--15" vertex="1">
          <mxGeometry x="85" y="145" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="kolMTltG5-48C87zoj-w-4" value="activity_main.xml" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="MsuhZJ9VR9ve1l35eAj--15">
          <mxGeometry x="85" y="185" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--24" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="-790" y="-180" width="470" height="590" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--22" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--24" vertex="1">
          <mxGeometry width="470" height="590" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--21" value="" style="group" parent="MsuhZJ9VR9ve1l35eAj--24" vertex="1" connectable="0">
          <mxGeometry x="60" y="70" width="370" height="460" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--19" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--21" vertex="1">
          <mxGeometry width="370" height="460" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--20" value="com.package.name" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--21" vertex="1">
          <mxGeometry x="10" y="10" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--9" value="" style="group" parent="MsuhZJ9VR9ve1l35eAj--21" vertex="1" connectable="0">
          <mxGeometry x="50" y="60" width="300" height="350" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--7" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry width="300" height="350" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--8" value="ui" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="20" y="10" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--10" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="30" y="40" width="250" height="140" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--11" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="30" y="190" width="250" height="140" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--25" value="home" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="40" y="50" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--26" value="HomeFragment.java" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="80" y="80" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--27" value="HomeViewModel.java" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="80" y="130" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--28" value="sign_up" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="40" y="200" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--44" value="SignupFragment.java" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="80" y="230" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--45" value="SlideshowViewModel.java" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--9" vertex="1">
          <mxGeometry x="80" y="280" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--23" value="java" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--24" vertex="1">
          <mxGeometry x="10" y="10" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="MsuhZJ9VR9ve1l35eAj--44" target="MsuhZJ9VR9ve1l35eAj--48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-250" y="185" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="MsuhZJ9VR9ve1l35eAj--45" target="MsuhZJ9VR9ve1l35eAj--52" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-250" y="430" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--50" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="-170" y="100" width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--48" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="MsuhZJ9VR9ve1l35eAj--50" vertex="1">
          <mxGeometry width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--49" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;SignupFragment&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--50" vertex="1">
          <mxGeometry x="10" y="10" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--51" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="-160" y="370" width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--52" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="MsuhZJ9VR9ve1l35eAj--51" vertex="1">
          <mxGeometry width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--53" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;SlideshowViewModel&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--51" vertex="1">
          <mxGeometry x="10" y="10" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--54" value="inflated in&lt;div&gt;and reference through&amp;nbsp;tools:context&lt;/div&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="MsuhZJ9VR9ve1l35eAj--18" target="MsuhZJ9VR9ve1l35eAj--48" edge="1">
          <mxGeometry x="-0.3845" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-440" y="805" />
              <mxPoint x="-440" y="695" />
              <mxPoint x="-290" y="695" />
              <mxPoint x="-290" y="228" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--56" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="-170" y="-150" width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--57" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="MsuhZJ9VR9ve1l35eAj--56" vertex="1">
          <mxGeometry width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--58" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;HomeFragment&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--56" vertex="1">
          <mxGeometry x="10" y="10" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--59" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="MsuhZJ9VR9ve1l35eAj--26" target="MsuhZJ9VR9ve1l35eAj--57" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="1" source="MsuhZJ9VR9ve1l35eAj--55" target="MsuhZJ9VR9ve1l35eAj--57" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--61" value="inflated in" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MsuhZJ9VR9ve1l35eAj--60" vertex="1" connectable="0">
          <mxGeometry x="-0.4837" y="3" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--67" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="50" y="570" width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--68" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="MsuhZJ9VR9ve1l35eAj--67" vertex="1">
          <mxGeometry width="230" height="170" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--69" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;MainActivity&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--67" vertex="1">
          <mxGeometry x="10" y="10" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--70" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="MsuhZJ9VR9ve1l35eAj--66" target="MsuhZJ9VR9ve1l35eAj--68" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--71" value="inflated in" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="MsuhZJ9VR9ve1l35eAj--70" vertex="1" connectable="0">
          <mxGeometry x="-0.3108" y="2" relative="1" as="geometry">
            <mxPoint x="-51" y="2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--72" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="-230" y="780" width="270" height="260" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--73" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=3;" parent="MsuhZJ9VR9ve1l35eAj--72" vertex="1">
          <mxGeometry width="270" height="260" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--74" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;content_main.xml&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--72" vertex="1">
          <mxGeometry x="10" y="10" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--75" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--72" vertex="1">
          <mxGeometry x="30" y="40" width="210" height="180" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--76" value="ConstraintLayout" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="MsuhZJ9VR9ve1l35eAj--72" vertex="1">
          <mxGeometry x="40" y="50" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--77" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="MsuhZJ9VR9ve1l35eAj--72" vertex="1">
          <mxGeometry x="60" y="90" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--78" value="fragment" style="text;whiteSpace=wrap;" parent="MsuhZJ9VR9ve1l35eAj--72" vertex="1">
          <mxGeometry x="80" y="100" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MsuhZJ9VR9ve1l35eAj--79" value="used in" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" parent="1" source="MsuhZJ9VR9ve1l35eAj--65" target="MsuhZJ9VR9ve1l35eAj--77" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="kolMTltG5-48C87zoj-w-1" value="navigation graph" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-1030" y="780" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="kolMTltG5-48C87zoj-w-2" value="is a" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="MsuhZJ9VR9ve1l35eAj--65" target="kolMTltG5-48C87zoj-w-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="kolMTltG5-48C87zoj-w-3" value="implemented in" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="MsuhZJ9VR9ve1l35eAj--66" target="MsuhZJ9VR9ve1l35eAj--73">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
