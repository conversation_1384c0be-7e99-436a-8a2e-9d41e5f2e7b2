from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from const_path import proj_ser_api_db_user_path


def main():
    part_data_len = 2
    db_name = "db_user_data"
    n_data = 40  # num pickles 20

    db = PickleDatabaseSplit(
        db_fpath=proj_ser_api_db_user_path,
        db_name=db_name,
        part_data_count=part_data_len,
    )

    # user role
    db.update_data_with_keys(
        keys=["user_test_abc", "role"],
        data="free_user",
    )

    # user usage limit per month
    db.update_data_with_keys(
        keys=["user_test_abc", "usage_limit_per_month"],
        data=60,
    )

    # user usage count
    db.update_data_with_keys(
        keys=["user_test_abc", "usage_count"],
        data=0,
    )

    db.dump_all_parts_to_pickles()


if __name__ == "__main__":
    main()
