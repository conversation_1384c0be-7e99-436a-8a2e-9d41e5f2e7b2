import os
import tempfile
from typing import Annotated
import pytest
from tomsze_utils.ai_utils.llm_api_utils import (
    create_function_schema,
    create_function_schema_list_from_dir,
    create_tool_descriptoin_string_from_function_schema_list,
)
from tomsze_utils.regular_expression_utils import (
    ToolCallArgs,
    extract_first_tool_call,
    extract_first_tool_call_to_ToolCallArgs,
    extract_first_tool_call_to_dict,
)


class TestCreateFunctionSchema:

    def test_create_function_schema_valid(self):
        def sample_function(
            a: Annotated[str, "Parameter a"],
            b: int = 2,
        ) -> None:
            """Sample function for testing."""
            pass

        schema = create_function_schema(
            function_callable=sample_function,
            name="sample_function",
            description="This is a sample function.",
        )

        assert schema["type"] == "function"
        assert schema["function"]["name"] == "sample_function"
        assert schema["function"]["description"] == "This is a sample function."
        assert "parameters" in schema["function"]

    def test_create_function_schema_invalid(self):
        with pytest.raises(TypeError):
            create_function_schema(
                function_callable=None,  # Invalid callable
                name="invalid_function",
                description="This should raise an error.",
            )


class TestExtractFirstToolCall:

    def test_extract_first_tool_call_valid(self):
        input_text = """sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 3, "operator": "+"}}
        </tool_call>
        fgdh
        <tool_call>
        {"name": "calculator", "arguments": {"a": 3, "b": 4, "operator": "+"}}
        </tool_call>"""

        expected_output = (
            '{"name": "calculator", "arguments": {"a": 2, "b": 3, "operator": "+"}}'
        )
        result = extract_first_tool_call(input_text)
        assert result == expected_output

    def test_extract_first_tool_call_no_tool_call(self):
        input_text = """This text does not contain any tool call tags."""
        expected_output = ""
        result = extract_first_tool_call(input_text)
        assert result == expected_output

    def test_extract_first_tool_call_include_tag(self):
        input_text = """sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 3, "operator": "+"}}
        </tool_call>
        fgdh"""

        expected_output = '<tool_call>{"name": "calculator", "arguments": {"a": 2, "b": 3, "operator": "+"}}</tool_call>'
        result = extract_first_tool_call(input_text, is_include_tag=True)
        assert result == expected_output


class TestExtractFirstToolCallToDict:

    def test_extract_first_tool_call_to_dict_valid(self):
        input_text = """sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 3, "operator": "+"}}
        </tool_call>
        fgdh"""

        expected_output = {
            "name": "calculator",
            "arguments": {"a": 2, "b": 3, "operator": "+"},
        }
        result = extract_first_tool_call_to_dict(input_text)
        assert result == expected_output

    def test_extract_first_tool_call_to_dict_no_tool_call(self):
        input_text = """This text does not contain any tool call tags."""
        expected_output = {}
        result = extract_first_tool_call_to_dict(input_text)
        assert result == expected_output


class TestExtractFirstToolCallToToolCallArgs:

    def test_extract_first_tool_call_to_tool_call_args_valid(self):
        input_text = """sdfg
        <tool_call>
        {"name": "calculator", "arguments": {"a": 2, "b": 3, "operator": "+"}}
        </tool_call>
        fgdh"""

        expected_output = ToolCallArgs(
            name="calculator", arguments={"a": 2, "b": 3, "operator": "+"}
        )
        result = extract_first_tool_call_to_ToolCallArgs(input_text)
        assert result == expected_output

    def test_extract_first_tool_call_to_tool_call_args_no_tool_call(self):
        input_text = """This text does not contain any tool call tags."""
        expected_output = None
        result = extract_first_tool_call_to_ToolCallArgs(input_text)
        assert result == expected_output


class TestCreateFunctionSchemaListFromDir:

    def test_create_function_schema_list_from_dir_valid(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a temporary Python file with a function
            script_content = """
def add(a:int, b:int)->int:
    '''Adds two numbers'''
    return a + b
            """
            script_path = os.path.join(temp_dir, "script.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Call the function to test
            function_schema_list = create_function_schema_list_from_dir(temp_dir)

            # Check if the schema is created correctly
            assert len(function_schema_list) == 1

    def test_create_function_schema_list_from_dir_empty_directory(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Call the function with an empty directory
            function_schema_list = create_function_schema_list_from_dir(temp_dir)

            # Check if the result is an empty list
            assert function_schema_list == []

    def test_create_function_schema_list_from_dir_include_function_names(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a temporary Python file with two functions
            script_content = """
def add(a: int, b: int) -> int:
    '''Adds two numbers'''
    return a + b

def subtract(a: int, b: int) -> int:
    '''Subtracts two numbers'''
    return a - b
            """
            script_path = os.path.join(temp_dir, "script.py")
            with open(script_path, "w") as f:
                f.write(script_content)

            # Call the function to test with include_function_names
            function_schema_list = create_function_schema_list_from_dir(
                temp_dir, include_function_names=["add"]
            )

            # Check if the schema is created correctly for the included function
            assert len(function_schema_list) == 1
            assert function_schema_list[0]["function"]["name"] == "add"
            assert (
                function_schema_list[0]["function"]["description"] == "Adds two numbers"
            )


class TestCreateToolDescriptionStringFromDir:

    def test_create_tool_description_string_from_dir_valid(self):
        function_schema_list = [
            {
                "function": {
                    "name": "add",
                    "description": "Adds two numbers",
                }
            },
            {
                "function": {
                    "name": "subtract",
                    "description": "Subtracts two numbers",
                }
            },
        ]
        expected_output = "add: Adds two numbers\nsubtract: Subtracts two numbers"
        result = create_tool_descriptoin_string_from_function_schema_list(
            function_schema_list
        )
        assert result == expected_output

    def test_create_tool_description_string_from_dir_empty_list(self):
        function_schema_list = []
        expected_output = ""
        result = create_tool_descriptoin_string_from_function_schema_list(
            function_schema_list
        )
        assert result == expected_output
