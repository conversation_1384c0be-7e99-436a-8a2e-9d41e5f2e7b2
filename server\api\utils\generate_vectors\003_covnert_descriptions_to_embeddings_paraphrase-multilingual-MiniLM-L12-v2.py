import os
from pathlib import Path
import sys
import pandas as pd

from utils import convert_to_embedding_using_huggingface_model, load_app_data

def main():
    # Load app descriptions (here only appstore, android needs TODO).
    num_total = 1000
    num_rows_per_batch = 80
    model_id = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    list_id, list_list_description = load_app_data(
                                        num_total=num_total,
                                        num_rows_per_batch=num_rows_per_batch,
                                    )
    
    # Converting to embeddings.
    final_list_description = []
    final_list_embeddings = []
    for list_description in list_list_description:
        list_embeddings = convert_to_embedding_using_huggingface_model(
                            model_id=model_id,
                            list_text=list_description, 
                        )
        final_list_embeddings += list_embeddings
        final_list_description += list_description
    
    script_directory_path = os.path.dirname(os.path.abspath(sys.argv[0]))

    id_csv_save_path = os.path.join(script_directory_path, '000_id_all-MiniLM-L6-v2.csv')
    df_id = pd.DataFrame(list_id)
    df_id.to_csv(id_csv_save_path, index=False, encoding='utf_8_sig')
    
    embedding_csv_save_path = os.path.join(script_directory_path, '003_embeddings_paraphrase-multilingual-MiniLM-L12-v2.csv')
    df_description_embedding = pd.DataFrame(final_list_embeddings)
    df_description_embedding.to_csv(embedding_csv_save_path, index=False, encoding='utf_8_sig')
    
    decription_csv_save_path = os.path.join(script_directory_path, '003_descriptions_paraphrase-multilingual-MiniLM-L12-v2.csv')
    df_description = pd.DataFrame({'description': final_list_description})
    df_description.to_csv(decription_csv_save_path, index=False, encoding='utf_8_sig')
    
if __name__ == "__main__":
    sys.exit(main())