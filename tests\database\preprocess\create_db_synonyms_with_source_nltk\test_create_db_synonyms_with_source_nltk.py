import shutil
import sys
import os
import tempfile
import pytest
import spacy
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit

from pickle_db.android.early_preprocess.create_db_synonyms_with_source.create_db_synonyms_with_source_utils import (
    create_db_synonyms_with_nltk_source,
)


@pytest.fixture(scope="function")
def tmp_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


def test_create_db_synonyms_with_nltk_source(tmp_path):

    # create a db synonyms (in current dir)
    db_synonyms = PickleDatabaseSplit(
        db_fpath=tmp_path,
        db_name="db_syns",
        load_by_thread=False,
    )
    keywords = ["talks"]
    nlp_model = spacy.load("en_core_web_sm", disable=["parser", "ner"])
    create_db_synonyms_with_nltk_source(
        db_synonyms=db_synonyms,
        nlp_model=nlp_model,
        keywords=keywords,
    )

    assert (
        len(
            db_synonyms.query_key_col(
                key="talk",  # lemmatized
                col="nltk",
                default={},
            )
        )
        == 19
    )


def main():
    test_create_db_synonyms_with_nltk_source()


if __name__ == "__main__":
    sys.exit(main())
