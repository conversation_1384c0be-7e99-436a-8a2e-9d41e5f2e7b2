{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "XXclean_doc_test_result_csv",
            "assign_variable_1",
            "assign_variable_2",
            "assign_variable_3",
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tests/tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_variable_assigner":[
        {
            "step_name": "assign_variable_1",
            "type": "PluginVariableAssigner",
            "use": true,
            "var1":1
        },
        {
            "step_name": "assign_variable_2",
            "type": "PluginVariableAssigner",
            "use": true,
            "var2":2
        },
        {
            "step_name": "assign_variable_3",
            "type": "PluginVariableAssigner",
            "use": true,
            "var3":"{assign_variable_2.var2}"
        }
    ]
   
    

}
