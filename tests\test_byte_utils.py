from tomsze_utils.byte_utils import byte_data_to_int_list, try_decode_byte


def test_try_decode_output():
    byte_sequence = b"This is a test"
    encodings_to_try = ["utf-8", "gbk"]

    # Test with utf-8 encoding
    result = try_decode_byte(byte_sequence, encodings_to_try)
    assert result == "This is a test", "Failed to decode with utf-8 encoding"

    # Test with invalid encoding
    byte_sequence = b"\x80abc"
    result = try_decode_byte(byte_sequence, encodings_to_try)
    assert result is None, "Should return None for invalid encoding"


def test_byte_data_to_int_list():
    # Test case 1: Normal byte sequence
    byte_data = b"Hello"
    expected_output = [72, 101, 108, 108, 111]
    assert byte_data_to_int_list(byte_data) == expected_output

    # Test case 2: Empty byte sequence
    byte_data = b""
    expected_output = []
    assert byte_data_to_int_list(byte_data) == expected_output

    # Test case 3: Byte sequence with non-ASCII values
    byte_data = b"\x00\x01\x02\x03\x04"
    expected_output = [0, 1, 2, 3, 4]
    assert byte_data_to_int_list(byte_data) == expected_output
