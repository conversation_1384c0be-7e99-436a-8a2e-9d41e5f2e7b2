<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="com.kewtoms.whatappsdo.ui.home.HomeFragment"
        android:label="@string/menu_home_name"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_nav_home_to_nav_login"
            app:destination="@id/nav_login" />
    </fragment>

    <fragment
        android:id="@+id/nav_gallery"
        android:name="com.kewtoms.whatappsdo.ui.gallery.GalleryFragment"
        android:label="@string/menu_gallery_name"
        tools:layout="@layout/fragment_gallery" />

    <fragment
        android:id="@+id/nav_sign_up"
        android:name="com.kewtoms.whatappsdo.ui.sign_up.SignupFragment"
        android:label="@string/menu_sign_up_name"
        tools:layout="@layout/fragment_sign_up">
        <action
            android:id="@+id/action_nav_sign_up_to_nav_login"
            app:destination="@id/nav_login" />
    </fragment>

    <fragment
        android:id="@+id/nav_login"
        android:name="com.kewtoms.whatappsdo.ui.login.LoginFragment"
        android:label="@string/menu_login_name"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_nav_login_to_nav_home"
            app:destination="@id/nav_home"
            app:popUpTo="@id/nav_login"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_nav_login_to_nav_sign_up"
            app:destination="@id/nav_sign_up" />
    </fragment>

</navigation>