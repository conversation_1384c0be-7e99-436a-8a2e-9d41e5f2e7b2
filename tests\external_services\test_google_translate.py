import pytest
from googletrans import Translator
from server.api.utils.translation.translate import check_is_eng, translate_to_eng


@pytest.fixture(scope="module")
def translator():
    translator = Translator()
    yield translator


def test_googletrans_still_service(translator):
    # translator = Translator()

    translation = translator.translate("안녕하세요").text
    assert translation == "hello"


def test_check_is_eng(translator):
    # translator = Translator()
    success, is_eng = check_is_eng(translator, "hi there")
    assert is_eng == True

    success, is_eng = check_is_eng(translator, "你好")
    assert is_eng == False


def test_translate_to_eng(translator):
    success, translated = translate_to_eng(translator, "你好")
    assert translated == "Hello"

    success, translated = translate_to_eng(translator, "外卖")
    assert translated == "takeout"
