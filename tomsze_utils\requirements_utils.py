import os
import shutil
from typing import <PERSON>ple
import semver
from tomsze_utils.pip_list_parser_utils import parse_pip_list_output_b
from tomsze_utils.subprocess_utils import subprocess_run
from tomsze_utils.text_file_modify_utils import write_string_list_to_file
from tomsze_utils.whls_folder_file_parser_utils import parse_whls_in_folder_to_dict


def create_update_requirements(
    whl_copy_to_folder_path: str = "./local_wheels",
    output_folder_path: str = "./",
    no_torch: bool = True,
) -> str:
    """
    Create and update requirements.txt file by building wheels for editable packages
    and replacing the requirements with the latest versions from pip list.

    Args:
        whl_copy_to_folder_path (str): The folder path to copy built wheels. Defaults to "./local_wheels".
        output_folder_path (str): The folder path to save the requirements.txt file. Defaults to "./".
        no_torch (bool): If True, excludes 'torch' from the requirements. Defaults to True.

    Returns:
        str: The path to the updated requirements.txt file.

    Examples:
        ```python
        req_file_path = create_update_requirements(whl_copy_to_folder_path="./my_wheels", output_folder_path="./output")
        ```

        ```python
        req_file_path = create_update_requirements(no_torch=False)
        ```
    """
    if not os.path.exists(whl_copy_to_folder_path):
        os.makedirs(whl_copy_to_folder_path, exist_ok=True)

    if not os.path.exists(output_folder_path):
        os.makedirs(output_folder_path, exist_ok=True)

    # Run pip list the first time.
    pip_list_output_b = subprocess_run(
        "pip list",
        capture_output=True,
        shell=False,  # Must use False
    ).stdout
    pip_list_dict = parse_pip_list_output_b(pip_list_output_b)
    # Re pip install the package (to update package version in pip list)
    for key in pip_list_dict.keys():
        editable_project_path = pip_list_dict[key]["editable_project_path"]
        if not editable_project_path:
            continue
        subprocess_run(
            f"pip install --editable {editable_project_path}",
            shell=False,
        )

    # Run pip list after re-install local package.
    pip_list_output_b = subprocess_run(
        "pip list",
        capture_output=True,
        shell=False,
    ).stdout  # Must use False
    pip_list_dict = parse_pip_list_output_b(pip_list_output_b)

    local_package_dict = {}
    for key in pip_list_dict.keys():
        editable_project_path = pip_list_dict[key]["editable_project_path"]
        if not editable_project_path:
            continue
        local_package_dict[key] = editable_project_path

    # Build the wheel for each editable package (known from pip list)
    for key in pip_list_dict.keys():
        package_name = key
        editable_project_path = pip_list_dict[package_name]["editable_project_path"]
        if not editable_project_path:
            continue

        package_dist_path = os.path.join(editable_project_path, "dist")

        # Build the wheel.
        subprocess_run(
            f"python -m build {editable_project_path}",
            shell=False,
        )

        # Copy the wheel
        wheels_dict = parse_whls_in_folder_to_dict(folder_path=package_dist_path)
        package_name = package_name.replace("-", "_")
        file_path = wheels_dict[package_name]["file_path"]
        file_name = wheels_dict[package_name]["file_name"]
        destination = os.path.join(whl_copy_to_folder_path, file_name)

        # Remove existing wheel if it exists
        existing_wheels = parse_whls_in_folder_to_dict(whl_copy_to_folder_path)
        for existing_package_name, existing_wheel_data in existing_wheels.items():
            if existing_package_name == package_name:
                existing_file_path = existing_wheel_data["file_path"]
                os.remove(existing_file_path)
                break

        shutil.copyfile(file_path, destination)

    local_wheels_dict = parse_whls_in_folder_to_dict(
        folder_path=whl_copy_to_folder_path
    )

    # Use pipreqs to generate requirements.txt
    req_txt_save_path = os.path.join(output_folder_path, "requirements.txt")
    subprocess_run(
        f"pipreqs ./ --force --savepath {req_txt_save_path}",
        shell=False,
    )

    # Remove duplicated package and keep the one in pip list.
    update_file, req_txt_save_path = remove_req_txt_duplication(
        req_txt_path=req_txt_save_path,
        overwrite=True,
    )
    assert update_file == True

    # Replace the reqs txt with pip list versions.
    update_file, write_to_file_path = replace_req_txt_with_pip_list_version(
        req_txt_path=req_txt_save_path,
        pip_list_dict=pip_list_dict,
        overwrite=True,
    )
    assert update_file

    # Update requirements.txt on local packages
    file = open(req_txt_save_path)
    lines = file.readlines()

    with open(req_txt_save_path, "w") as file:
        for i, line in enumerate(lines):
            modify = False
            write = True
            # To locate the line contain local package name.
            for key in local_package_dict.keys():
                if key in line:
                    modify = True
                    local_package_name = key
                    break

            # To locate the line it contains the package torch.
            if no_torch:
                if "torch" in line:
                    modify = False
                    write = False

            if not write:
                continue

            if modify:
                # if "egg==info" in line:
                #     version = pip_list_dict[local_package_name]["version"]
                #     line = line.replace(".egg==info", f"=={version}")
                # file.write(f"-f {whl_copy_to_folder_path} {line}")
                whl_file_path = local_wheels_dict[local_package_name]["file_path"]
                file.write(f"{whl_file_path}\n")
            else:
                file.write(f"{line}")

    return req_txt_save_path


def remove_req_txt_duplication(
    req_txt_path: str,
    overwrite: bool = True,
) -> Tuple[bool, str]:
    """
    Remove duplicate package entries from a requirements text file and keep the latest version.

    This function reads a requirements.txt file, identifies duplicate package names, and retains the latest version
    based on semantic versioning. The updated list of packages is then written back to the file.

    Args:
        req_txt_path (str): The path to the requirements.txt file.
        overwrite (bool): Whether to overwrite the existing file. Defaults to True.

    Returns:
        Tuple[bool, str]: A tuple containing a boolean indicating if the file was updated and the path to the written file.

    Examples:
    ```python
    updated, path = remove_req_txt_duplication(req_txt_path='requirements.txt', overwrite=True)
    ```

    ```python
    updated, path = remove_req_txt_duplication(req_txt_path='requirements.txt', overwrite=False)
    ```
    """
    assert os.path.exists(req_txt_path)
    data = open(req_txt_path).read()
    line_list = data.splitlines()

    # Get the latest package version for each package in req txt.
    dict_package_version = {}
    for line in line_list:
        line_split = line.split("=")
        if len(line_split) == 3:
            package_name = line_split[0]
            version = line_split[2]

            if not package_name in dict_package_version:
                dict_package_version[package_name] = version
            else:
                if (
                    semver.compare(version, dict_package_version[package_name]) == 1
                ):  # is version 1 larger
                    dict_package_version[package_name] = version

    new_string_list = []
    for key in dict_package_version:
        package_name = key
        version = dict_package_version[key]
        new_string_list.append(f"{package_name}=={version}")

    update_file, write_to_file_path = write_string_list_to_file(
        file_path=req_txt_path,
        string_list=new_string_list,
        add_newline=True,
        overwrite=overwrite,
    )

    return update_file, write_to_file_path


def replace_req_txt_with_pip_list_version(
    req_txt_path: str,
    pip_list_dict: dict,
    overwrite: bool = True,
) -> Tuple[bool, str]:
    """
    Replace the versions in the requirements.txt file with the latest versions from pip list.

    This function reads the requirements.txt file, checks the versions of the packages listed,
    and updates them with the versions found in the provided pip list dictionary.

    Args:
        req_txt_path (str): The path to the requirements.txt file.
        pip_list_dict (dict): A dictionary containing package names and their latest versions.
        overwrite (bool): Whether to overwrite the existing file. Defaults to True.

    Returns:
        Tuple[bool, str]: A tuple containing a boolean indicating if the file was updated and the path to the written file.

    Examples:
    ```python
    updated, path = replace_req_txt_with_pip_list_version(req_txt_path='requirements.txt', pip_list_dict={'package1': {'version': '1.0.0'}}, overwrite=True)
    ```

    ```python
    updated, path = replace_req_txt_with_pip_list_version(req_txt_path='requirements.txt', pip_list_dict={'package2': {'version': '2.0.0'}}, overwrite=False)
    ```
    """
    assert os.path.exists(req_txt_path)
    data = open(req_txt_path).read()
    line_list = data.splitlines()

    new_string_list = []
    for line in line_list:
        line_split = line.split("=")
        if not len(line_split) == 3:
            continue

        package_name = line_split[0]
        version = line_split[2]

        if not package_name in pip_list_dict:
            new_string_list.append(f"{package_name}=={version}")
            continue

        new_string_list.append(
            f"{package_name}=={pip_list_dict[package_name]['version']}"
        )

    update_file, write_to_file_path = write_string_list_to_file(
        file_path=req_txt_path,
        string_list=new_string_list,
        add_newline=True,
        overwrite=overwrite,
    )

    return update_file, write_to_file_path


def main():
    create_update_requirements()


if __name__ == "__main__":
    main()
