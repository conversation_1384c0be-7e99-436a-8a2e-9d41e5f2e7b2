import sys
import os
import time
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit

# Create db object using PickleDatabase (will also load pickle file)
script_directory_path = os.path.dirname(
    os.path.abspath(sys.argv[0]),
)

db_fpath = os.path.join(
    script_directory_path,
    "splits_of_pickles",
)

time_start = time.time()
db = PickleDatabaseSplit(
    db_fpath=db_fpath,
    db_name="apps",
)
time_end = time.time()
print("time used:" + "{:.2f}".format((time_end - time_start) * 1000) + " ms")
