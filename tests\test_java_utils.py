import subprocess
import pytest
import os
import tempfile
from tomsze_utils.java_utils import create_java_file, run_java_file, run_java_test_file


class TestRunJavaFile:

    def test_run_java_file_success(self):
        # Create a temporary directory to hold the Java file
        with tempfile.TemporaryDirectory() as temp_dir:
            java_file_path = os.path.join(temp_dir, "TestProgram.java")
            with open(java_file_path, "w") as java_file:
                java_file.write(
                    """
public class TestProgram {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
                """
                )
            output = run_java_file(java_file_path)
            assert output.strip() == "Hello, World!"

    def test_run_java_file_file_not_found(self):
        with pytest.raises(FileNotFoundError):
            run_java_file("non_existent_file.java")


class TestRunJavaTestFile:

    def test_run_java_test_file_success(self):
        # Create a temporary directory to hold the Java test file and JUnit jar
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a simple JUnit test file
            test_file_path = os.path.join(temp_dir, "TestProgramTest.java")
            with open(test_file_path, "w") as test_file:
                test_file.write(
                    """
import org.junit.Test;
import static org.junit.Assert.assertEquals;

public class TestProgramTest {
    @Test
    public void testMain() {
        assertEquals(1, 1); // Simple test
    }
}
                """
                )

            output = run_java_test_file(
                test_file_path,
            )
            assert "OK" in output  # Check for successful test output

    def test_run_java_test_file_file_not_found(self):
        with pytest.raises(FileNotFoundError):
            run_java_test_file(
                "non_existent_test_file.java",
                "non_existent_junit.jar",
                "non_existent_hamcrest.jar",
            )
