import os
import semver


def parse_whls_in_folder_to_dict(folder_path: str) -> dict:
    """
    Parse the whl files in a folder into a dictionary.
    If multiple versions of whls exist for a package,
    the latest version will be used.

    Examples:
        >>> parse_whls_in_folder_to_dict("path/to/folder")
        {'package_name': {'front_filename': 'package_name-1.0', 'version': '1.0', 'file_name': 'package_name-1.0.whl', 'file_path': 'path/to/folder/package_name-1.0.whl'}}

        >>> parse_whls_in_folder_to_dict("path/to/another/folder")
        {'package_name': {'front_filename': 'package_name-2.0', 'version': '2.0', 'file_name': 'package_name-2.0.whl', 'file_path': 'path/to/another/folder/package_name-2.0.whl'}}
    """
    package_dict = {}

    max_version = "0.0.0"
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filetype = filename.split(".")[-1]
            front_filename = filename.replace("." + filetype, "")
            if filetype in ["whl"]:
                file_path = os.path.join(dirpath, filename)
                splits_list = front_filename.split("-")
                package_name = splits_list[0]
                version = splits_list[1]

                if semver.compare(version, max_version) == 1:  # is version 1 larger
                    max_version = version

                    package_dict[package_name] = {
                        "front_filename": front_filename,
                        "version": version,
                        "file_name": filename,
                        "file_path": file_path.replace("\\", "/"),
                    }

    return package_dict
