import tempfile
import pytest
from tomsze_utils.operating_system_utils import (
    get_active_window_title,
    get_opened_windows,
    get_system_usage,
)


class TestGetActiveWindowTitle:

    def test_get_active_window_title(self):
        # This test checks if the function returns a string
        title = get_active_window_title()
        assert isinstance(title, str)  # Expecting the title to be a string

    def test_get_active_window_title_not_empty(self):
        # This test checks if the function returns a non-empty string
        title = get_active_window_title()
        assert title != ""  # Expecting the title to not be empty


class TestGetOpenedWindows:

    def test_get_opened_windows_return_type(self):
        # This test checks if the function returns a list
        windows = get_opened_windows()
        assert isinstance(windows, list)  # Expecting the return type to be a list

    def test_get_opened_windows_non_empty(self):
        # This test checks if the function returns a non-empty list when there are opened windows
        # Create a dummy window (this is just a placeholder, as we can't create actual windows in tests)
        # The test will pass if there are any opened windows, otherwise it will check for an empty list.
        windows = get_opened_windows()
        assert len(windows) >= 0  # Expecting the list to be non-negative in length


class TestGetSystemUsage:
    def test_get_system_usage(self):
        # This test checks if the function returns a tuple with the correct types
        usage = get_system_usage()
        assert isinstance(usage, tuple)
        assert len(usage) == 4  # Expecting a tuple of 4 elements
        assert isinstance(usage[0], float)  # cpu_percent
        assert isinstance(usage[1], float)  # ram_usage_gb
        assert isinstance(usage[2], float)  # total_ram_gb
        assert isinstance(usage[3], float)  # ram_percent
