"""App plugin"""

from dataclasses import dataclass
from typing import List
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data,
    parse_data_and_store,
)


@dataclass
class PluginVariableAssigner:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        var_pool = data_obj.dict_var
        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            "bool",
            default=False,
        )
        if not use:
            return True

        for variable_name in step_config.keys():
            if variable_name != "step_name":
                # get data
                data = parse_data(
                    logger,
                    variable_name,
                    data_obj,
                    step_config,
                    config,
                )

                # if isinstance(data, str) and has_eval_bracket:
                #     data_tmp = data.replace('eval{','').replace('}','')
                #     eval_tmp = eval(data_tmp)
                #     image.dict_var[f'{variable_name}'] = eval(data_tmp)
                # else:
                #     image.dict_var[f'{variable_name}'] = data

                if use:
                    if variable_name != "use" and variable_name != "type":
                        var_pool[f"{variable_name}"] = data
                    var_pool[f"{current_step}.{variable_name}"] = data

        data_obj.dict_var[current_step] = "test_result_BasicAoiVariableAssigner"

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
