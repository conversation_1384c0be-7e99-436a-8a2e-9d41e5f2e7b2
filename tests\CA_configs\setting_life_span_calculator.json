{"general": {"init_steps": [], "steps": ["calculate_life_span"], "variables": {"var_online": false}, "logger": {"format": "%(asctime)s %(message)s", "datefmt": "%m/%d/%Y %I:%M:%S %p", "log_to_file": true, "folder_path": "./tests/tmp_CA_logs", "filename": "log.log", "loglevel": "logging.DEBUG"}, "debug": {"debug_visualize": true, "save_result_txt": true}}, "all_life_span_calculator": [{"step_name": "calculate_life_span", "type": "PluginLifeSpanCalculator", "use": true, "birth_year": 1980, "birth_month": 1, "birth_day": 1, "live_to_age": 70, "life_span_days_buffer_to": "days"}]}