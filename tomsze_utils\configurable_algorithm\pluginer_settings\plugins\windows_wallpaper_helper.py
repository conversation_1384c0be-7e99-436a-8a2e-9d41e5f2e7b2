"""App plugin"""

from dataclasses import dataclass
import os
from typing import List

import numpy as np
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data,
    parse_data_and_store,
)
from tomsze_utils.windows_wallpaper_utils import Wallpaper


@dataclass
class PluginWindowsWallpaperHelper:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        var_pool = data_obj.dict_var
        step_config = data_obj.dict_step_config[current_step]

        use = parse_data(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            "bool",
            default=False,
        )

        if use:
            is_get_wallpaper = parse_data_and_store(
                logger,
                "is_get_wallpaper",
                data_obj,
                step_config,
                config,
                type="bool",
                default=False,
            )

            buffer_to_which = parse_data_and_store(
                logger,
                "buffer_to_which",
                data_obj,
                step_config,
                config,
                type="str",
                default=None,
            )

            save = parse_data_and_store(
                logger,
                "save",
                data_obj,
                step_config,
                config,
                type="bool",
                default=False,
            )

            save_path = parse_data_and_store(
                logger,
                "save_path",
                data_obj,
                step_config,
                config,
                type="str",
                default=None,
            )

            is_set_wallpaper = parse_data_and_store(
                logger,
                "is_set_wallpaper",
                data_obj,
                step_config,
                config,
                type="bool",
                default=False,
            )

            wallpaper_path = parse_data_and_store(
                logger,
                "wallpaper_path",
                data_obj,
                step_config,
                config,
                type="str",
                default=None,
            )

            if is_get_wallpaper:
                pil_image = Wallpaper.get(returnImgObj=True)

                np_im = np.array(pil_image)
                data_obj.dict_var[buffer_to_which] = np_im
                data_obj.dict_var[f"{current_step}.{buffer_to_which}"] = np_im
                data_obj.dict_var[f"{current_step}.height"] = np_im.shape[0]
                data_obj.dict_var[f"{current_step}.width"] = np_im.shape[1]

                if save:
                    save_folder_path = save_path.replace(save_path.split("/")[-1], "")
                    if not os.path.exists(save_folder_path):
                        os.makedirs(save_folder_path, exist_ok=True)

                    pil_image.save(save_path)

            if is_set_wallpaper:
                Wallpaper.set(wallpaperToBeSet=wallpaper_path)

        data_obj.dict_var[current_step] = "test_result"

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
