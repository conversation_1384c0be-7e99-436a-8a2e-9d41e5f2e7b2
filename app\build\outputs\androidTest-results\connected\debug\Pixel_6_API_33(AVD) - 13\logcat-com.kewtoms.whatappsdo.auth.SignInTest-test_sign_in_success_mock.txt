07-05 13:06:26.771  6586  6623 I TestRunner: started: test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest)
07-05 13:06:26.787  1543  1736 I AiAiEcho: Predicting[0]:
07-05 13:06:26.787  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 13:06:26.791  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 13:06:26.795  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 13:06:26.796  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 13:06:26.802  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 13:06:26.803   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 13:06:26.803  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 13:06:26.805  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 13:06:26.809  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 13:06:26.811  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 13:06:26.813  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 13:06:26.826   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 13:06:26.827   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 13:06:27.016   336  6648 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10178 0}
07-05 13:06:27.017  6586  6623 D TrafficStats: tagSocket(90) with statsTag=0xffffffff, statsUid=-1
07-05 13:06:27.027   336  6650 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10178 0}
07-05 13:06:27.046  6586  6623 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 13:06:27.080   796   930 D SplashScreenView: Build android.window.SplashScreenView{5436b50 V.E...... ......ID 0,0-0,0}
07-05 13:06:27.080   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 13:06:27.080   796   930 D SplashScreenView: Branding: view: android.view.View{8474149 G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 13:06:27.082   796   966 W Parcel  : Expecting binder but got null!
07-05 13:06:27.089  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 13:06:27.097  6586  6653 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 13:06:27.098  6586  6653 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 13:06:27.101  6586  6653 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 13:06:27.132  1166  1782 D EGL_emulation: app_time_stats: avg=1798.77ms min=294.49ms max=3303.06ms count=2
07-05 13:06:27.139   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 13:06:27.155  2019  6520 W A       : [Download] response code: 400
07-05 13:06:27.163   564  2198 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 13:06:27.163   564  2198 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 13:06:27.163   564  2198 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 13:06:27.163   564  2198 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 13:06:27.163   564  2198 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 13:06:27.163   564  2198 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 13:06:27.163   564  2198 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 13:06:27.163   564  2198 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 13:06:27.219   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3810
07-05 13:06:27.220   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d35d0
07-05 13:06:27.220   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d35d0
07-05 13:06:27.220   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3810
07-05 13:06:27.254  6586  6586 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 13:06:27.263  6586  6586 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: PRE_ON_CREATE
07-05 13:06:27.264  6586  6586 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 13:06:27.325   796   966 D EGL_emulation: app_time_stats: avg=123327.02ms min=123327.02ms max=123327.02ms count=1
07-05 13:06:27.348  6586  6586 D APP:MainActivity: onCreate: run
07-05 13:06:27.348  6586  6586 D APP:Constants: initializeData: run
07-05 13:06:27.349  6586  6586 D APP:Constants: initializeData: done
07-05 13:06:27.370   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4590
07-05 13:06:27.652   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3630
07-05 13:06:27.818  6586  6586 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10178; state: ENABLED
07-05 13:06:27.849  6586  6586 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 13:06:27.850  6586  6586 I APP:MainActivity: onCreate: done setting root view
07-05 13:06:27.868  6586  6586 I APP:MainActivity: onCreate: Done initializing drawer
07-05 13:06:27.917  6586  6586 I APP:MainActivity: onCreate:  mode:TEST Overriding startDestination to login fragment
07-05 13:06:28.049  6586  6586 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.login.LoginFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 111.087ms (1773.38 bytecodes/s) (7224B approximate peak alloc)
07-05 13:06:28.081  6586  6586 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 13:06:28.081  6586  6586 D APP:MainActivity: onCreate: Done
07-05 13:06:28.082  6586  6586 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: CREATED
07-05 13:06:28.083  6586  6586 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@fe2de0d
07-05 13:06:28.088  6586  6586 D APP:LoginFragment: onCreateView: run
07-05 13:06:28.568  6586  6586 D APP:LoginFragment: onCreateView: : Done
07-05 13:06:28.575  6586  6586 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: STARTED
07-05 13:06:28.575  6586  6586 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@fe2de0d
07-05 13:06:28.578  6586  6586 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: RESUMED
07-05 13:06:28.578  6586  6586 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@fe2de0d
07-05 13:06:28.584  6586  6586 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10178; state: DISABLED
07-05 13:06:28.590  6586  6651 W Parcel  : Expecting binder but got null!
07-05 13:06:28.642   398  1303 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10178 pid=0
07-05 13:06:28.642   398  1303 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10178 => denied (604 us)
07-05 13:06:28.642   398   545 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10178 pid=6586
07-05 13:06:28.642   398   545 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10178 => denied (411 us)
07-05 13:06:28.643   398  1303 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10178 pid=0
07-05 13:06:28.643   398   545 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10178 pid=6586
07-05 13:06:28.643   398  1303 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10178 => denied (498 us)
07-05 13:06:28.643   398   545 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10178 => denied (455 us)
07-05 13:06:28.643   398  1303 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10178 pid=0
07-05 13:06:28.643   398  1303 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10178 => denied (306 us)
07-05 13:06:28.643   398   545 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10178 pid=6586
07-05 13:06:28.643   398   545 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10178 => denied (472 us)
07-05 13:06:28.653  6586  6651 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:06:28.653  6586  6586 D AutofillManager: Trigger fill request at view entered
07-05 13:06:28.654  6586  6651 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 13:06:28.655  6586  6651 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 13:06:28.667  6586  6651 D EGL_emulation: eglCreateContext: 0x720c5f5e4110: maj 3 min 1 rcv 4
07-05 13:06:28.670   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d5370
07-05 13:06:28.670   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3990
07-05 13:06:28.698  6586  6651 D EGL_emulation: eglMakeCurrent: 0x720c5f5e4110: ver 3 1 (tinfo 0x720e772a0080) (first time)
07-05 13:06:28.704   796   966 D EGL_emulation: app_time_stats: avg=24165.98ms min=24165.98ms max=24165.98ms count=1
07-05 13:06:28.715   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 13:06:28.715  6586  6651 I Gralloc4: mapper 4.x is not supported
07-05 13:06:28.718   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 13:06:28.719   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 13:06:28.719  6586  6651 W Gralloc4: allocator 4.x is not supported
07-05 13:06:28.742  6586  6651 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:06:28.744  6586  6651 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 13:06:28.837   564   602 W ziparchive: Unable to open '/data/app/~~Axul7AwONBeYppcwKi_dlQ==/com.kewtoms.whatappsdo-wavKR-a5xFiM7IyPJu-INw==/base.dm': No such file or directory
07-05 13:06:28.839   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +1s782ms
07-05 13:06:28.861   564  2198 W InputManager-JNI: Input channel object 'fe0699 Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 13:06:28.866  6586  6586 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10178; state: ENABLED
07-05 13:06:28.870  6586  6586 I AssistStructure: Flattened final assist data: 3596 bytes, containing 1 windows, 23 views
07-05 13:06:28.875  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 13:06:28.877  1166  5750 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef61cdf0 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 13:06:28.894   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4bf0
07-05 13:06:28.894   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4590
07-05 13:06:28.895   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4530
07-05 13:06:28.896   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4530
07-05 13:06:28.896   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4530
07-05 13:06:28.898   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4bf0
07-05 13:06:28.898   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0ed0
07-05 13:06:28.899   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d4cb0
07-05 13:06:28.905  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 13:06:28.906  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 13:06:28.910  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.kewtoms.whatappsdo fieldId=-1 fieldName=null extras=null}, false)
07-05 13:06:28.911  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 13:06:28.912  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 13:06:28.920   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d0c90
07-05 13:06:28.926  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 13:06:28.931  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 13:06:28.931  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 13:06:28.947   796   966 D EGL_emulation: app_time_stats: avg=1620.72ms min=1620.72ms max=1620.72ms count=1
07-05 13:06:28.958  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:55066391
07-05 13:06:28.977  1274  4200 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef61b350 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 13:06:29.018  6586  6623 W FileTestStorage: Output properties is not supported.
07-05 13:06:29.021  6586  6623 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 13:06:29.054  6586  6623 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 13:06:29.054  6586  6623 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 13:06:29.054  6586  6623 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 13:06:29.054  6586  6623 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 13:06:29.107  6586  6623 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 13:06:29.108  1274  2311 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 13:06:29.109  1274  2311 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 13:06:29.110  1274  2311 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 13:06:29.125  1274  2311 I FontLog : Pulling font file for id = 23, cache size = 4 [CONTEXT service_id=132 ]
07-05 13:06:29.133  1274  2311 I FontLog : Pulling font file for id = 23, cache size = 4 [CONTEXT service_id=132 ]
07-05 13:06:29.138  6586  6586 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 13:06:29.139  6586  6586 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 13:06:29.140  6586  6586 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 13:06:29.157  6586  6586 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 13:06:29.157  6586  6586 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 13:06:29.158  6586  6586 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 13:06:29.173  6586  6586 I ViewInteraction: Performing 'single click' action on view view.getContentDescription() is "Open navigation drawer"
07-05 13:06:29.181  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=73.0, y[0]=201.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=1180178, downTime=1180178, deviceId=-1, source=0x1002, displayId=0, eventId=-883940727 }
07-05 13:06:29.194   398   427 I BpBinder: onLastStrongRef automatically unlinking death recipients: <uncached descriptor>
07-05 13:06:29.208   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 13:06:29.224  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=73.0, y[0]=201.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=1180222, downTime=1180178, deviceId=-1, source=0x1002, displayId=0, eventId=-********* }
07-05 13:06:29.251   398   778 I BpBinder: onLastStrongRef automatically unlinking death recipients: <uncached descriptor>
07-05 13:06:29.261  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:29.262  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:29.269   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3390
07-05 13:06:29.283  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getAccountEmail(SecurePrefsManager.java:518)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.MainActivity$1.onDrawerSlide(MainActivity.java:107)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.dispatchOnDrawerSlide(DrawerLayout.java:962)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.setDrawerViewOffset(DrawerLayout.java:974)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout$ViewDragCallback.onViewPositionChanged(DrawerLayout.java:2264)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.customview.widget.ViewDragHelper.continueSettling(ViewDragHelper.java:779)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.computeScroll(DrawerLayout.java:1367)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22116)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateViewTreeDisplayList(ThreadedRenderer.java:689)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateRootDisplayList(ThreadedRenderer.java:695)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.draw(ThreadedRenderer.java:793)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.draw(ViewRootImpl.java:4789)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:4500)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:3687)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2371)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:9297)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1231)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1239)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer.doCallbacks(Choreographer.java:899)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer.doFrame(Choreographer.java:832)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1214)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.injectMotionEvent(UiControllerImpl.java:5)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:7)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:1)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.sendSingleTap(Tap.java:5)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.-$$Nest$smsendSingleTap(Unknown Source:0)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:3)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.os.Looper.loop(Looper.java:288)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 13:06:29.478  6586  6586 W AndroidKeysetManager: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getAccountEmail(SecurePrefsManager.java:518)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.MainActivity$1.onDrawerSlide(MainActivity.java:107)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.dispatchOnDrawerSlide(DrawerLayout.java:962)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.setDrawerViewOffset(DrawerLayout.java:974)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout$ViewDragCallback.onViewPositionChanged(DrawerLayout.java:2264)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.customview.widget.ViewDragHelper.continueSettling(ViewDragHelper.java:779)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.computeScroll(DrawerLayout.java:1367)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22116)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateViewTreeDisplayList(ThreadedRenderer.java:689)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateRootDisplayList(ThreadedRenderer.java:695)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.draw(ThreadedRenderer.java:793)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.draw(ViewRootImpl.java:4789)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:4500)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:3687)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2371)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:9297)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1231)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1239)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer.doCallbacks(Choreographer.java:899)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer.doFrame(Choreographer.java:832)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1214)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.injectMotionEvent(UiControllerImpl.java:5)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:7)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:1)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.sendSingleTap(Tap.java:5)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.-$$Nest$smsendSingleTap(Unknown Source:0)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:3)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.os.Looper.loop(Looper.java:288)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 13:06:29.551  6586  6586 W AndroidKeysetManager: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 13:06:29.579  6586  6586 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 13:06:29.587  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:29.588  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:29.589  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:29.592  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:29.621  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:29.641  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:29.641  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:29.643  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:29.677  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:29.677  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:29.678  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:29.681  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:29.712  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:29.718  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:29.719  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:29.721  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:29.753  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:29.753  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:29.754  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:29.756  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:29.786  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:29.797  6586  6586 W OnBackInvokedCallback: OnBackInvokedCallback is not enabled for the application.
07-05 13:06:29.797  6586  6586 W OnBackInvokedCallback: Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
07-05 13:06:30.019  6586  6586 I ViewInteraction: Performing 'single click' action on view view.getId() is <**********/com.kewtoms.whatappsdo:id/nav_login>
07-05 13:06:30.022  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=367.0, y[0]=1091.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=1181019, downTime=1181019, deviceId=-1, source=0x1002, displayId=0, eventId=-******** }
07-05 13:06:30.061  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=367.0, y[0]=1091.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=1181060, downTime=1181019, deviceId=-1, source=0x1002, displayId=0, eventId=-********* }
07-05 13:06:30.074  6586  6651 D EGL_emulation: app_time_stats: avg=110.39ms min=2.20ms max=375.51ms count=11
07-05 13:06:30.099  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.099  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.102  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.109   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(32)
07-05 13:06:30.134  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.135  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.135  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.137  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.166  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.173  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.174  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.176  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.208  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.208  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.208  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.209  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.240  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.246  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.246  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.249  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.279  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.280  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.280  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.282  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.313  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.320  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.320  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.322  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.355  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.356  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.356  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.359  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.390  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.397  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.397  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.400  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.433  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.433  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.433  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.436  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.469  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.475  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.476  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.478  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.513  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.513  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:30.513  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:30.516  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:30.548  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:30.794  6586  6586 I ViewInteraction: Performing 'single click' action on view view.getId() is <**********/com.kewtoms.whatappsdo:id/button_login_large>
07-05 13:06:30.797  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=1040.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=1181795, downTime=1181795, deviceId=-1, source=0x1002, displayId=0, eventId=-********* }
07-05 13:06:30.839  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=1040.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=1181838, downTime=1181795, deviceId=-1, source=0x1002, displayId=0, eventId=-874997832 }
07-05 13:06:30.858  6586  6586 I APP:LoginFragment: onCreateView: : Clicked login button large
07-05 13:06:30.858  6586  6586 I APP:Authenticator: signIn: run
07-05 13:06:31.032  6586  6586 I APP:Authenticator: signIn: Sending login request: http://localhost:44215/__mock_sign_in
07-05 13:06:31.032  6586  6586 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 13:06:31.034  6586  6586 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 13:06:31.035  6586  6669 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 13:06:31.035  6586  6669 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 13:06:31.072  6586  6669 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 13:06:31.073  6586  6586 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 13:06:31.073  6586  6586 D APP:Authenticator: signIn: end
07-05 13:06:31.073  6586  6673 D APP:SecurePrefsManager: getAccessToken: run
07-05 13:06:31.073  6586  6673 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:31.075  6586  6673 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:31.079  6586  6651 D EGL_emulation: app_time_stats: avg=97.25ms min=1.70ms max=286.21ms count=10
07-05 13:06:31.110  6586  6673 D APP:SecurePrefsManager: getAccessToken: done
07-05 13:06:31.113   336  6675 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10178 0}
07-05 13:06:31.113  6586  6673 D TrafficStats: tagSocket(120) with statsTag=0xb8332793, statsUid=-1
07-05 13:06:31.118  6586  6649 D TrafficStats: tagSocket(137) with statsTag=0xffffffff, statsUid=-1
07-05 13:06:31.158   336  6679 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10178 0}
07-05 13:06:31.167  6586  6586 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 13:06:31.167  6586  6586 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 13:06:31.167  6586  6586 I APP:Authenticator: signIn: Post request success
07-05 13:06:31.169  6586  6586 I APP:Authenticator: signIn: responseJsonObj: {"ApiResponse":{"ApiFunResult":{"db_result":{"is_success":true,"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_access_token","refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_refresh_token","message":"Sign in successful"}}}}
07-05 13:06:31.169  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: run
07-05 13:06:31.169  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: done
07-05 13:06:31.170  6586  6586 D APP:SecurePrefsManager: saveSignInDataFromResponseJSONObject: run
07-05 13:06:31.171  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: run
07-05 13:06:31.171  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: done
07-05 13:06:31.173  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: run
07-05 13:06:31.173  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: done
07-05 13:06:31.173  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: run
07-05 13:06:31.174  6586  6586 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: done
07-05 13:06:31.174  6586  6586 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:06:31.175  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:31.178  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:31.211  6586  6586 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:06:31.211  6586  6586 D APP:SecurePrefsManager: saveSignInData: run
07-05 13:06:31.212  6586  6586 D APP:SecurePrefsManager: saveAccessToken: run
07-05 13:06:31.212  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:31.214  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:31.247  6586  6586 D APP:SecurePrefsManager: saveAccessToken: done
07-05 13:06:31.247  6586  6586 I APP:SecurePrefsManager: saveSignInData: saved access token
07-05 13:06:31.247  6586  6586 D APP:SecurePrefsManager: saveRefreshToken: run
07-05 13:06:31.248  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:31.251  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:31.281  6586  6586 D APP:SecurePrefsManager: saveRefreshToken: done
07-05 13:06:31.281  6586  6586 I APP:SecurePrefsManager: saveSignInData: saved refresh token
07-05 13:06:31.281  6586  6586 D APP:SecurePrefsManager: saveAccountEmail: run
07-05 13:06:31.281  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:31.284  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:31.315  6586  6586 D APP:SecurePrefsManager: saveAccountEmail: done
07-05 13:06:31.316  6586  6586 I APP:SecurePrefsManager: saveSignInData: saved email
07-05 13:06:31.316  6586  6586 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: run
07-05 13:06:31.316  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:31.318  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:31.349  6586  6586 D APP:SecurePrefsManager: saveHasSuccessLoggedIn: done
07-05 13:06:31.350  6586  6586 I APP:SecurePrefsManager: saveSignInData: saved hasSuccessLoggedIn
07-05 13:06:31.350  6586  6586 D APP:SecurePrefsManager: saveLoginTime: run
07-05 13:06:31.350  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:06:31.353  6586  6586 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:06:31.383  6586  6586 D APP:SecurePrefsManager: saveLoginTime: done
07-05 13:06:31.383  6586  6586 I APP:SecurePrefsManager: saveSignInData: saved login time
07-05 13:06:31.383  6586  6586 D APP:SecurePrefsManager: saveSignInData: done
07-05 13:06:31.383  6586  6586 D APP:SecurePrefsManager: saveSignInDataFromResponseJSONObject: done
07-05 13:06:35.636  1274  1274 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.contextmanager.service.ContextManagerService.START pkg=com.google.android.gms }
07-05 13:06:38.896  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.feedback.internal.IFeedbackService dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 13:06:42.416  1274  1274 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.gms.phenotype.service.START pkg=com.google.android.gms }
07-05 13:06:43.966  1562  1562 D BoundBrokerSvc: onUnbind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 13:06:44.545   796   966 D EGL_emulation: app_time_stats: avg=7920.71ms min=189.81ms max=15651.62ms count=2
07-05 13:06:45.605   796   807 I ndroid.systemui: Background young concurrent copying GC freed 164743(8805KB) AllocSpace objects, 0(0B) LOS objects, 23% free, 24MB/32MB, paused 9.585ms,63us total 22.604ms
07-05 13:07:00.008  1543  1736 I AiAiEcho: Predicting[0]:
07-05 13:07:00.009  1543  1736 I AiAiEcho: Ranked targets strategy: SORT, count: 0, ranking metadata:
07-05 13:07:00.009  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface lockscreen with targets# 0
07-05 13:07:00.011  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface home with targets# 0
07-05 13:07:00.012  1543  1736 I AiAiEcho: #postPredictionTargets: Sending updates to UISurface media_data_manager with targets# 0
07-05 13:07:00.024   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 13:07:00.027   796   966 D EGL_emulation: app_time_stats: avg=15480.93ms min=15480.93ms max=15480.93ms count=1
07-05 13:07:05.561   564   728 D ConnectivityService: releasing NetworkRequest [ REQUEST id=179, [ Transports: CELLULAR Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&NOT_VCN_MANAGED Uid: 10120 RequestorUid: 10120 RequestorPkg: com.google.android.googlequicksearchbox UnderlyingNetworks: Null] ] (release request)
07-05 13:07:05.562   564   728 I ConnectivityService: Update capabilities for net 101 : -FOREGROUND
07-05 13:07:05.699   336   414 I netd    : networkSetPermissionForNetwork(101, 1) <116.93ms>
07-05 13:07:16.101   366   366 E android.hardware.power.stats@1.0-service-mock: Failed to getEnergyData
07-05 13:07:24.539   796   966 D EGL_emulation: app_time_stats: avg=24512.49ms min=24512.49ms max=24512.49ms count=1
07-05 13:07:25.423   796  1030 D ControlsListingControllerImpl: Subscribing callback, service count: 0
07-05 13:07:25.440   796  1030 D ControlsListingControllerImpl: Unsubscribing callback
07-05 13:07:32.386  6586  6586 I APP:LoginFragment: onCreateView: : Navigating to home fragment
07-05 13:07:32.389  6586  6586 W OnBackInvokedCallback: OnBackInvokedCallback is not enabled for the application.
07-05 13:07:32.389  6586  6586 W OnBackInvokedCallback: Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
07-05 13:07:32.408  6586  6597 W System  : A resource failed to call close.
07-05 13:07:32.409  6586  6597 W System  : A resource failed to call close.
07-05 13:07:32.409  6586  6597 W System  : A resource failed to call close.
07-05 13:07:32.971  6586  6586 W toms.whatappsdo: Verification of android.view.View com.kewtoms.whatappsdo.ui.home.HomeFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle) took 304.996ms (911.49 bytecodes/s) (8240B approximate peak alloc)
07-05 13:07:32.993  6586  6684 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 13:07:32.993  6586  6586 D APP:HomeFragment: onCreateView: run
07-05 13:07:33.046  6586  6586 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: run
07-05 13:07:33.047  6586  6586 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: disabled
07-05 13:07:33.047  6586  6586 I APP:HomeFragment: runThreadCheckAndCachePackagesRelated: done
07-05 13:07:33.049  6586  6586 D APP:HomeFragment: onCreateView: done
07-05 13:07:33.070  6586  6586 E RecyclerView: No adapter attached; skipping layout
07-05 13:07:33.081  6586  6586 W UiControllerImpl: ignoring signal of: DELAY_HAS_PAST from previous generation: 53 current generation: 54
07-05 13:07:33.086  6586  6586 D takeScreenshot: Found 1 global views to redraw
07-05 13:07:33.098  6586  6651 D EGL_emulation: app_time_stats: avg=20672.88ms min=13.14ms max=61991.22ms count=3
07-05 13:07:33.122   398  1303 D PermissionCache: checking android.permission.READ_FRAME_BUFFER for uid=2000 => granted (675 us)
07-05 13:07:33.144   398  1303 W ServiceManager: Permission failure: android.permission.CAPTURE_BLACKOUT_CONTENT from uid=2000 pid=6572
07-05 13:07:33.144   398  1303 D PermissionCache: checking android.permission.CAPTURE_BLACKOUT_CONTENT for uid=2000 => denied (579 us)
07-05 13:07:33.147   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 13:07:33.148  6572  6584 I Gralloc4: mapper 4.x is not supported
07-05 13:07:33.188  6572  6584 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:07:33.194  6572  6688 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 13:07:33.195  6572  6688 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 13:07:33.197  6572  6688 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 13:07:33.217  6572  6688 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:07:33.218  6572  6688 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 13:07:33.219  6572  6688 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 13:07:33.231  6572  6688 D EGL_emulation: eglCreateContext: 0x787ebbc1e810: maj 3 min 1 rcv 4
07-05 13:07:33.253  6572  6688 D EGL_emulation: eglMakeCurrent: 0x787ebbc1e810: ver 3 1 (tinfo 0x7880de97c080) (first time)
07-05 13:07:33.310  1525  5177 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 13:07:33.311  1525  5177 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 13:07:33.313  1525  5177 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 13:07:33.366  1274  5419 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService started execution. cause:9 exec_start_elapsed_seconds: 1244 [CONTEXT service_id=218 ]
07-05 13:07:33.374  1274  6430 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService finished executing. cause:9 result: 1 elapsed_millis: 17 uptime_millis: 17 exec_start_elapsed_seconds: 1244 [CONTEXT service_id=218 ]
07-05 13:07:33.374  1274  1274 D BoundBrokerSvc: onBind: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 13:07:33.374  1274  1274 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.gms.scheduler.ACTION_PROXY_SCHEDULE dat=chimera-action:/... cmp=com.google.android.gms/.chimera.PersistentApiService }
07-05 13:07:33.410  6586  6623 E TestRunner: failed: test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest)
07-05 13:07:33.410  6586  6623 E TestRunner: ----- begin exception -----
07-05 13:07:33.415  6586  6623 E TestRunner: androidx.test.espresso.PerformException: Error performing 'single click - At Coordinates: 539, 1040 and precision: 16, 16' on view 'view.getId() is <**********/com.kewtoms.whatappsdo:id/button_login_large>'.
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.PerformException$Builder.build(PerformException.java:1)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:8)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.base.PerformExceptionHandler.handleSafely(PerformExceptionHandler.java:9)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction.desugaredPerform(ViewInteraction.java:11)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction.perform(ViewInteraction.java:8)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:149)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 13:07:33.415  6586  6623 E TestRunner: Caused by: androidx.test.espresso.AppNotIdleException: Looped for 2 iterations over 60 SECONDS. The following Idle Conditions failed DELAY_HAS_PAST.
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.IdlingPolicy.handleTimeout(IdlingPolicy.java:5)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:50)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.base.UiControllerImpl.loopMainThreadForAtLeast(UiControllerImpl.java:7)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:6)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at android.os.Looper.loop(Looper.java:288)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 13:07:33.415  6586  6623 E TestRunner: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 13:07:33.415  6586  6623 E TestRunner: ----- end exception -----
07-05 13:07:33.427  6586  6623 I TestRunner: finished: test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest)
