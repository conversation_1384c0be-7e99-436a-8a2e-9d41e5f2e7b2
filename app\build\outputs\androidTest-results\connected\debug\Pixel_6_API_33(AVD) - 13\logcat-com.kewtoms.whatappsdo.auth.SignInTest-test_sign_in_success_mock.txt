07-05 13:59:35.671  8114  8137 I TestRunner: started: test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest)
07-05 13:59:35.677   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 13:59:35.677   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 13:59:35.678   796   796 D SsMediaDataProvider: Forwarding Smartspace updates []
07-05 13:59:35.886   336  8162 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10184 0}
07-05 13:59:35.886  8114  8137 D TrafficStats: tagSocket(90) with statsTag=0xffffffff, statsUid=-1
07-05 13:59:35.896   336  8164 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10184 0}
07-05 13:59:35.914  8114  8137 W Settings: Setting always_finish_activities has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
07-05 13:59:35.946   796   930 D SplashScreenView: Build android.window.SplashScreenView{9fa8b1a V.E...... ......ID 0,0-0,0}
07-05 13:59:35.946   796   930 D SplashScreenView: Icon: view: null drawable: null size: 0
07-05 13:59:35.946   796   930 D SplashScreenView: Branding: view: android.view.View{62b564b G.ED..... ......I. 0,0-0,0 #10204dc android:id/splashscreen_branding_view} drawable: null size w: 0 h: 0
07-05 13:59:35.947   796   966 W Parcel  : Expecting binder but got null!
07-05 13:59:35.961  1166  1166 D MainContentCaptureSession: Flushing 1 event(s) for act:com.google.android.apps.nexuslauncher/.NexusLauncherActivity [state=2 (ACTIVE), disabled=false], reason=FULL
07-05 13:59:35.964  8114  8166 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 13:59:35.966  8114  8166 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 13:59:35.969  8114  8166 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 13:59:35.997   564  1949 W Binder  : Caught a RuntimeException from the binder stub implementation.
07-05 13:59:35.997   564  1949 W Binder  : java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
07-05 13:59:35.997   564  1949 W Binder  : 	at android.util.ArraySet.valueAt(ArraySet.java:422)
07-05 13:59:35.997   564  1949 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.updateContentCaptureOptions(ContentCapturePerUserService.java:733)
07-05 13:59:35.997   564  1949 W Binder  : 	at com.android.server.contentcapture.ContentCapturePerUserService$ContentCaptureServiceRemoteCallback.setContentCaptureWhitelist(ContentCapturePerUserService.java:646)
07-05 13:59:35.997   564  1949 W Binder  : 	at android.service.contentcapture.IContentCaptureServiceCallback$Stub.onTransact(IContentCaptureServiceCallback.java:115)
07-05 13:59:35.997   564  1949 W Binder  : 	at android.os.Binder.execTransactInternal(Binder.java:1285)
07-05 13:59:35.997   564  1949 W Binder  : 	at android.os.Binder.execTransact(Binder.java:1244)
07-05 13:59:36.003  1166  1782 D EGL_emulation: app_time_stats: avg=1913.66ms min=354.67ms max=3472.65ms count=2
07-05 13:59:36.005   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 13:59:36.145   796   966 D EGL_emulation: app_time_stats: avg=52814.18ms min=52814.18ms max=52814.18ms count=1
07-05 13:59:36.187  8114  8114 D AppCompatDelegate: Checking for metadata for AppLocalesMetadataHolderService : Service not found
07-05 13:59:36.197  8114  8114 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: PRE_ON_CREATE
07-05 13:59:36.198  8114  8114 V ActivityScenario: Activity lifecycle changed event received but ignored because the reported transition was not ON_CREATE while the last known transition was PRE_ON_CREATE
07-05 13:59:36.236   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d48f0
07-05 13:59:36.294  8114  8114 D APP:MainActivity: onCreate: run
07-05 13:59:36.295  8114  8114 D APP:Constants: initializeData: run
07-05 13:59:36.296  8114  8114 D APP:Constants: initializeData: done
07-05 13:59:36.519   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d3cf0
07-05 13:59:36.828  8114  8114 D CompatibilityChangeReporter: Compat change id reported: 210923482; UID 10184; state: ENABLED
07-05 13:59:36.859  8114  8114 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
07-05 13:59:36.859  8114  8114 I APP:MainActivity: onCreate: done setting root view
07-05 13:59:36.877  8114  8114 I APP:MainActivity: onCreate: Done initializing drawer
07-05 13:59:36.928  8114  8114 I APP:MainActivity: onCreate:  mode:TEST Overriding startDestination to login fragment
07-05 13:59:36.987  8114  8114 I APP:MainActivity: onCreate: . Done initializing NavigationUI and navController
07-05 13:59:36.987  8114  8114 D APP:MainActivity: onCreate: Done
07-05 13:59:36.987  8114  8114 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: CREATED
07-05 13:59:36.988  8114  8114 V ActivityScenario: Update currentActivityStage to CREATED, currentActivity=com.kewtoms.whatappsdo.MainActivity@fe2de0d
07-05 13:59:36.992  8114  8114 D APP:LoginFragment: onCreateView: run
07-05 13:59:37.495  8114  8114 D APP:LoginFragment: onCreateView: : Done
07-05 13:59:37.504  8114  8114 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: STARTED
07-05 13:59:37.504  8114  8114 V ActivityScenario: Update currentActivityStage to STARTED, currentActivity=com.kewtoms.whatappsdo.MainActivity@fe2de0d
07-05 13:59:37.507  8114  8114 D LifecycleMonitor: Lifecycle status change: com.kewtoms.whatappsdo.MainActivity@fe2de0d in: RESUMED
07-05 13:59:37.507  8114  8114 V ActivityScenario: Update currentActivityStage to RESUMED, currentActivity=com.kewtoms.whatappsdo.MainActivity@fe2de0d
07-05 13:59:37.513  8114  8114 D CompatibilityChangeReporter: Compat change id reported: 237531167; UID 10184; state: DISABLED
07-05 13:59:37.518  8114  8165 W Parcel  : Expecting binder but got null!
07-05 13:59:37.573   398   427 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10184 pid=0
07-05 13:59:37.574   398   778 W ServiceManager: Permission failure: android.permission.ACCESS_SURFACE_FLINGER from uid=10184 pid=8114
07-05 13:59:37.574   398   427 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10184 => denied (701 us)
07-05 13:59:37.574   398   778 D PermissionCache: checking android.permission.ACCESS_SURFACE_FLINGER for uid=10184 => denied (566 us)
07-05 13:59:37.574   398   778 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10184 pid=8114
07-05 13:59:37.574   398   427 W ServiceManager: Permission failure: android.permission.ROTATE_SURFACE_FLINGER from uid=10184 pid=0
07-05 13:59:37.574   398   427 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10184 => denied (666 us)
07-05 13:59:37.574   398   778 D PermissionCache: checking android.permission.ROTATE_SURFACE_FLINGER for uid=10184 => denied (628 us)
07-05 13:59:37.575   398   427 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10184 pid=0
07-05 13:59:37.575   398   427 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10184 => denied (268 us)
07-05 13:59:37.576   398   778 W ServiceManager: Permission failure: android.permission.INTERNAL_SYSTEM_WINDOW from uid=10184 pid=8114
07-05 13:59:37.576   398   778 D PermissionCache: checking android.permission.INTERNAL_SYSTEM_WINDOW for uid=10184 => denied (1614 us)
07-05 13:59:37.584  8114  8165 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:59:37.587  8114  8165 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 13:59:37.587  8114  8165 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 13:59:37.590  8114  8114 D AutofillManager: Trigger fill request at view entered
07-05 13:59:37.603  8114  8165 D EGL_emulation: eglCreateContext: 0x720c5f5e5c10: maj 3 min 1 rcv 4
07-05 13:59:37.636   796   966 D EGL_emulation: app_time_stats: avg=37606.54ms min=37606.54ms max=37606.54ms count=1
07-05 13:59:37.637  8114  8165 D EGL_emulation: eglMakeCurrent: 0x720c5f5e5c10: ver 3 1 (tinfo 0x720e772a0080) (first time)
07-05 13:59:37.653   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 13:59:37.653  8114  8165 I Gralloc4: mapper 4.x is not supported
07-05 13:59:37.657   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.allocator@4.0::IAllocator/default in either framework or device VINTF manifest.
07-05 13:59:37.658   171   171 I servicemanager: Could not find android.hardware.graphics.allocator.IAllocator/default in the VINTF manifest.
07-05 13:59:37.658  8114  8165 W Gralloc4: allocator 4.x is not supported
07-05 13:59:37.680  8114  8165 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:59:37.681  8114  8165 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 13:59:37.779   564   602 W ziparchive: Unable to open '/data/app/~~O4wNzL-kAFK5a0N9rghnFg==/com.kewtoms.whatappsdo-cXOqnLTf6h7Cf7SyKlAd-A==/base.dm': No such file or directory
07-05 13:59:37.781   564   602 I ActivityTaskManager: Displayed com.kewtoms.whatappsdo/.MainActivity: +1s857ms
07-05 13:59:37.797   564  1950 W InputManager-JNI: Input channel object '71d7c9d Splash Screen com.kewtoms.whatappsdo (client)' was disposed without first being removed with the input manager!
07-05 13:59:37.808  1166  1910 D OneSearchSuggestProvider: Shut down the binder channel
07-05 13:59:37.809  1166  1181 I s.nexuslauncher: oneway function results for code 2 on binder at 0x720bef62e2e0 will be dropped but finished with status UNKNOWN_TRANSACTION
07-05 13:59:37.815  8114  8114 D CompatibilityChangeReporter: Compat change id reported: 163400105; UID 10184; state: ENABLED
07-05 13:59:37.818  8114  8114 I AssistStructure: Flattened final assist data: 3596 bytes, containing 1 windows, 23 views
07-05 13:59:37.828  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onFinishInput():3227
07-05 13:59:37.831  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 0, unlocked = true
07-05 13:59:37.835   398   429 W TransactionTracing: Could not find layer handle 0x71102f7d32d0
07-05 13:59:37.836  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1877 onStartInput(EditorInfo{inputType=0x0(NULL) imeOptions=0x0 privateImeOptions=null actionName=UNSPECIFIED actionLabel=null actionId=0 initialSelStart=-1 initialSelEnd=-1 initialCapsMode=0x0 hintText=null label=null packageName=com.kewtoms.whatappsdo fieldId=-1 fieldName=null extras=null}, false)
07-05 13:59:37.837  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.shouldHideHeaderOnInitialState():4008 ShouldHideHeaderOnInitialState = false
07-05 13:59:37.842  1371  1371 I GoogleInputMethodService: GoogleInputMethodService.updateDeviceLockedStatus():2087 repeatCheckTimes = 2, unlocked = true
07-05 13:59:37.862  1274  1274 W AutofillChimeraService: Pending fill request while another request in the same session was triggered. [CONTEXT service_id=177 ]
07-05 13:59:37.878  1562  1562 D BoundBrokerSvc: onBind: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 13:59:37.878  1562  1562 D BoundBrokerSvc: Loading bound service for intent: Intent { act=com.google.android.mdd.service.START dat=chimera-action:/... cmp=com.google.android.gms/.chimera.GmsBoundBrokerService }
07-05 13:59:37.880   796   966 D EGL_emulation: app_time_stats: avg=1734.70ms min=1734.70ms max=1734.70ms count=1
07-05 13:59:37.898  1003  1003 V InlineSuggestionRenderService: handleDestroySuggestionViews called for 0:425536128
07-05 13:59:37.901  1274  2311 I .gms.persistent: oneway function results for code 1 on binder at 0x720bef678290 will be dropped but finished with status UNKNOWN_TRANSACTION and reply parcel size 80
07-05 13:59:37.950  8114  8137 W FileTestStorage: Output properties is not supported.
07-05 13:59:37.954  8114  8137 I Tracing : Tracer added: class androidx.test.platform.tracing.AndroidXTracer
07-05 13:59:37.987  8114  8137 D EventInjectionStrategy: Creating injection strategy with input manager.
07-05 13:59:37.987  8114  8137 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->getInstance()Landroid/hardware/input/InputManager; (unsupported, reflection, allowed)
07-05 13:59:37.987  8114  8137 W toms.whatappsdo: Accessing hidden method Landroid/hardware/input/InputManager;->injectInputEvent(Landroid/view/InputEvent;I)Z (unsupported, reflection, allowed)
07-05 13:59:37.988  8114  8137 W toms.whatappsdo: Accessing hidden field Landroid/hardware/input/InputManager;->INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH:I (unsupported, reflection, allowed)
07-05 13:59:38.049  8114  8137 W toms.whatappsdo: Accessing hidden method Landroid/view/ViewConfiguration;->getDoubleTapMinTime()I (unsupported, reflection, allowed)
07-05 13:59:38.053  1274  2311 I FontLog : Received query Noto Color Emoji Compat, URI content://com.google.android.gms.fonts [CONTEXT service_id=132 ]
07-05 13:59:38.053  1274  2311 I FontLog : Query [emojicompat-emoji-font] resolved to {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} [CONTEXT service_id=132 ]
07-05 13:59:38.054  1274  2311 I FontLog : Fetch {Noto Color Emoji Compat, wdth 100.0, wght 400, ital 0.0, bestEffort false} end status Status{statusCode=SUCCESS, resolution=null} [CONTEXT service_id=132 ]
07-05 13:59:38.070  1274  2311 I FontLog : Pulling font file for id = 30, cache size = 7 [CONTEXT service_id=132 ]
07-05 13:59:38.077  1274  2311 I FontLog : Pulling font file for id = 30, cache size = 7 [CONTEXT service_id=132 ]
07-05 13:59:38.088  8114  8114 W toms.whatappsdo: Accessing hidden method Landroid/os/MessageQueue;->next()Landroid/os/Message; (unsupported, reflection, allowed)
07-05 13:59:38.088  8114  8114 W toms.whatappsdo: Accessing hidden field Landroid/os/MessageQueue;->mMessages:Landroid/os/Message; (unsupported, reflection, allowed)
07-05 13:59:38.090  8114  8114 W toms.whatappsdo: Accessing hidden method Landroid/os/Message;->recycleUnchecked()V (unsupported, reflection, allowed)
07-05 13:59:38.108  8114  8114 W toms.whatappsdo: Accessing hidden method Landroid/view/WindowManagerGlobal;->getInstance()Landroid/view/WindowManagerGlobal; (unsupported, reflection, allowed)
07-05 13:59:38.109  8114  8114 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mViews:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 13:59:38.111  8114  8114 W toms.whatappsdo: Accessing hidden field Landroid/view/WindowManagerGlobal;->mParams:Ljava/util/ArrayList; (unsupported, reflection, allowed)
07-05 13:59:38.130  8114  8114 I ViewInteraction: Performing 'single click' action on view view.getContentDescription() is "Open navigation drawer"
07-05 13:59:38.141  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=73.0, y[0]=201.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4369137, downTime=4369137, deviceId=-1, source=0x1002, displayId=0, eventId=-507126267 }
07-05 13:59:38.161   398   427 I BpBinder: onLastStrongRef automatically unlinking death recipients: <uncached descriptor>
07-05 13:59:38.179   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 13:59:38.181  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=73.0, y[0]=201.0, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4369177, downTime=4369137, deviceId=-1, source=0x1002, displayId=0, eventId=-********* }
07-05 13:59:38.206  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:38.206  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:38.229   398   778 I BpBinder: onLastStrongRef automatically unlinking death recipients: <uncached descriptor>
07-05 13:59:38.229  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_key_keyset__ does not exist
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:155)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getAccountEmail(SecurePrefsManager.java:518)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.MainActivity$1.onDrawerSlide(MainActivity.java:107)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.dispatchOnDrawerSlide(DrawerLayout.java:962)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.setDrawerViewOffset(DrawerLayout.java:974)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout$ViewDragCallback.onViewPositionChanged(DrawerLayout.java:2264)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.customview.widget.ViewDragHelper.continueSettling(ViewDragHelper.java:779)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.computeScroll(DrawerLayout.java:1367)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22116)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateViewTreeDisplayList(ThreadedRenderer.java:689)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateRootDisplayList(ThreadedRenderer.java:695)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.draw(ThreadedRenderer.java:793)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.draw(ViewRootImpl.java:4789)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:4500)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:3687)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2371)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:9297)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1231)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1239)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer.doCallbacks(Choreographer.java:899)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer.doFrame(Choreographer.java:832)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1214)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:59:38.453  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.injectMotionEvent(UiControllerImpl.java:5)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:7)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:1)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.sendSingleTap(Tap.java:5)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.-$$Nest$smsendSingleTap(Unknown Source:0)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:3)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at android.os.Looper.loop(Looper.java:288)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 13:59:38.454  8114  8114 W AndroidKeysetManager: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: keyset not found, will generate a new one
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: java.io.FileNotFoundException: can't read keyset; the pref value __androidx_security_crypto_encrypted_prefs_value_keyset__ does not exist
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readPref(SharedPrefKeysetReader.java:71)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.SharedPrefKeysetReader.readEncrypted(SharedPrefKeysetReader.java:89)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.KeysetHandle.read(KeysetHandle.java:105)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.read(AndroidKeysetManager.java:311)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.readOrGenerateNewKeyset(AndroidKeysetManager.java:287)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.google.crypto.tink.integration.android.AndroidKeysetManager$Builder.build(AndroidKeysetManager.java:238)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:160)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.security.crypto.EncryptedSharedPreferences.create(EncryptedSharedPreferences.java:120)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getSharedPreferences(SecurePrefsManager.java:110)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.utils.SecurePrefsManager.getAccountEmail(SecurePrefsManager.java:518)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.kewtoms.whatappsdo.MainActivity$1.onDrawerSlide(MainActivity.java:107)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.dispatchOnDrawerSlide(DrawerLayout.java:962)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.setDrawerViewOffset(DrawerLayout.java:974)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout$ViewDragCallback.onViewPositionChanged(DrawerLayout.java:2264)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.customview.widget.ViewDragHelper.continueSettling(ViewDragHelper.java:779)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.drawerlayout.widget.DrawerLayout.computeScroll(DrawerLayout.java:1367)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22116)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4513)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4486)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.View.updateDisplayListIfDirty(View.java:22089)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateViewTreeDisplayList(ThreadedRenderer.java:689)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.updateRootDisplayList(ThreadedRenderer.java:695)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ThreadedRenderer.draw(ThreadedRenderer.java:793)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.draw(ViewRootImpl.java:4789)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:4500)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:3687)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2371)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:9297)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1231)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1239)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer.doCallbacks(Choreographer.java:899)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer.doFrame(Choreographer.java:832)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1214)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.Interrogator.loopAndInterrogate(Interrogator.java:14)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:8)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.loopUntil(UiControllerImpl.java:1)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.base.UiControllerImpl.injectMotionEvent(UiControllerImpl.java:5)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:7)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.MotionEvents.sendUp(MotionEvents.java:1)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.sendSingleTap(Tap.java:5)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap.-$$Nest$smsendSingleTap(Unknown Source:0)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.Tap$1.sendTap(Tap.java:3)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.action.GeneralClickAction.perform(GeneralClickAction.java:6)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$SingleExecutionViewAction.perform(ViewInteraction.java:2)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.doPerform(ViewInteraction.java:25)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction.-$$Nest$mdoPerform(Unknown Source:0)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:7)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at androidx.test.espresso.ViewInteraction$1.call(ViewInteraction.java:1)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.handleCallback(Handler.java:942)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.os.Handler.dispatchMessage(Handler.java:99)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.os.Looper.loopOnce(Looper.java:201)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.os.Looper.loop(Looper.java:288)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at android.app.ActivityThread.main(ActivityThread.java:7924)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
07-05 13:59:38.525  8114  8114 W AndroidKeysetManager: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
07-05 13:59:38.552  8114  8114 I EngineFactory: Provider GmsCore_OpenSSL not available
07-05 13:59:38.561  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:38.561  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:38.562  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:38.564  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:38.593  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:38.612  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:38.612  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:38.615  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:38.649  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:38.649  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:38.649  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:38.653  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:38.682  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:38.702  8114  8114 W OnBackInvokedCallback: OnBackInvokedCallback is not enabled for the application.
07-05 13:59:38.702  8114  8114 W OnBackInvokedCallback: Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
07-05 13:59:38.929  8114  8114 I ViewInteraction: Performing 'single click' action on view view.getId() is <**********/com.kewtoms.whatappsdo:id/nav_login>
07-05 13:59:38.932  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=367.0, y[0]=1091.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4369930, downTime=4369930, deviceId=-1, source=0x1002, displayId=0, eventId=-********* }
07-05 13:59:38.974  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=367.0, y[0]=1091.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4369972, downTime=4369930, deviceId=-1, source=0x1002, displayId=0, eventId=-********* }
07-05 13:59:38.991  8114  8165 D EGL_emulation: app_time_stats: avg=132.74ms min=2.23ms max=411.65ms count=9
07-05 13:59:39.012   385   525 D AudioFlinger: mixer(0x7133073959a0) throttle end: throttle time(36)
07-05 13:59:39.015  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.015  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.017  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.050  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.050  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.050  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.053  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.055   385   525 D AF::Track: interceptBuffer: took 735us to intercept 0 tracks
07-05 13:59:39.082  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.091  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.091  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.094  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.128  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.129  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.129  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.131  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.163  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.170  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.170  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.172  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.205  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.205  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.205  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.208  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.242  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.249  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.249  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.251  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.282  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.282  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.282  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.285  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.316  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.323  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.323  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.325  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.355  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.355  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.355  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.358  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.387  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.395  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.395  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.397  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.428  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.428  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.429  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.431  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.461  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.467  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.467  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.469  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.504  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.504  8114  8114 D APP:SecurePrefsManager: getAccountEmail: run
07-05 13:59:39.504  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:39.507  8114  8114 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:39.539  8114  8114 D APP:SecurePrefsManager: getAccountEmail: done
07-05 13:59:39.766  8114  8114 I ViewInteraction: Performing 'single click' action on view view.getId() is <**********/com.kewtoms.whatappsdo:id/button_login_large>
07-05 13:59:39.768  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_DOWN, actionButton=0, id[0]=0, x[0]=539.5, y[0]=1040.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4370766, downTime=4370766, deviceId=-1, source=0x1002, displayId=0, eventId=-******** }
07-05 13:59:39.810  1166  1166 D TaplEvents: TIS / TouchInteractionService.onInputEvent: MotionEvent { action=ACTION_UP, actionButton=0, id[0]=0, x[0]=539.5, y[0]=1040.5, toolType[0]=TOOL_TYPE_UNKNOWN, buttonState=BUTTON_PRIMARY, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=4370808, downTime=4370766, deviceId=-1, source=0x1002, displayId=0, eventId=-426857127 }
07-05 13:59:39.824  8114  8114 I APP:LoginFragment: onCreateView: : Clicked login button large
07-05 13:59:39.825  8114  8114 I APP:Authenticator: signIn: run
07-05 13:59:39.972  8114  8114 W toms.whatappsdo: Verification of com.android.volley.toolbox.JsonObjectRequest com.kewtoms.whatappsdo.utils.RequestUtils.getJsonObjectRequestForVolley(org.json.JSONObject, byte[], java.lang.String, android.content.Context, boolean, com.kewtoms.whatappsdo.interfaces.PostResponseCallback, int) took 125.729ms (254.51 bytecodes/s) (2528B approximate peak alloc)
07-05 13:59:40.061  8114  8114 I APP:Authenticator: signIn: Sending login request: http://localhost:58469/__mock_sign_in
07-05 13:59:40.062  8114  8114 D APP:RequestUtils: sendPostEncryptedJsonVolley: run
07-05 13:59:40.063  8114  8114 I APP:RequestUtils: sendPostEncryptedJsonVolley: done starting thread
07-05 13:59:40.063  8114  8183 D APP:RequestUtils: sendPostEncryptedJsonVolley: Run run() method of Thread
07-05 13:59:40.064  8114  8183 I APP:RequestUtils: sendPostEncryptedJsonVolley: done encrypting data
07-05 13:59:40.102  8114  8183 I APP:RequestUtils: sendPostEncryptedJsonVolley: End of Thread run.
07-05 13:59:40.103  8114  8114 I APP:RequestUtils: sendPostEncryptedJsonVolley: done
07-05 13:59:40.103  8114  8114 D APP:Authenticator: signIn: end
07-05 13:59:40.107  8114  8185 D APP:SecurePrefsManager: getAccessToken: run
07-05 13:59:40.107  8114  8185 D APP:SecurePrefsManager: getSharedPreferences: run
07-05 13:59:40.110  8114  8165 D EGL_emulation: app_time_stats: avg=99.51ms min=3.72ms max=271.80ms count=11
07-05 13:59:40.110  8114  8185 D APP:SecurePrefsManager: getSharedPreferences: done
07-05 13:59:40.144  8114  8185 D APP:SecurePrefsManager: getAccessToken: done
07-05 13:59:40.147   336  8189 I resolv  : GetAddrInfoHandler::run: {100 100 100 983140 10184 0}
07-05 13:59:40.148  8114  8185 D TrafficStats: tagSocket(118) with statsTag=0xb8332793, statsUid=-1
07-05 13:59:40.156  8114  8163 D TrafficStats: tagSocket(120) with statsTag=0xffffffff, statsUid=-1
07-05 13:59:40.190   336  8193 I resolv  : GetHostByAddrHandler::run: {100 100 100 983140 10184 0}
07-05 13:59:40.198  8114  8114 D APP:RequestUtils: getJsonObjectRequest.onResponse: run
07-05 13:59:40.198  8114  8114 D APP:RequestUtils: getJsonObjectRequest.onResponse: done
07-05 13:59:40.198  8114  8114 I APP:Authenticator: signIn: Post request success
07-05 13:59:40.198  8114  8114 I APP:Authenticator: signIn: responseJsonObj: {"code":200,"is_success":true,"message":"Sign in successful","data":{"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_access_token","refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_refresh_token"}}
07-05 13:59:40.198  8114  8114 D APP:RequestUtils: getDbResultJsonObjectFromResponseJSONObject: DEPRECATED - Use getApiResponseDataFromResponseJSONObject instead
07-05 13:59:40.198  8114  8114 D APP:RequestUtils: getApiResponseDataFromResponseJSONObject: run
07-05 13:59:40.198  8114  8114 D APP:RequestUtils: getApiResponseDataFromResponseJSONObject: done
07-05 13:59:40.199  8114  8114 E APP:Authenticator: signIn: JSONException while parsing response: org.json.JSONException: No value for is_success
07-05 13:59:40.200  8114  8114 D CompatibilityChangeReporter: Compat change id reported: 147798919; UID 10184; state: ENABLED
07-05 13:59:40.211   796   966 W Parcel  : Expecting binder but got null!
07-05 13:59:40.249   796   966 E OpenGLRenderer: Unable to match the desired swap behavior.
07-05 13:59:40.401  8114  8114 I ViewInteraction: Checking 'MatchesViewAssertion{viewMatcher=(view has effective visibility <VISIBLE> and view.getGlobalVisibleRect() to return non-empty rectangle)}' assertion on view view.getId() is <**********/com.kewtoms.whatappsdo:id/search_field>
07-05 13:59:40.422  8114  8114 D takeScreenshot: Found 1 global views to redraw
07-05 13:59:40.480   172   172 I hwservicemanager: getTransport: Cannot find entry android.hardware.graphics.mapper@4.0::IMapper/default in either framework or device VINTF manifest.
07-05 13:59:40.480  8100  8141 I Gralloc4: mapper 4.x is not supported
07-05 13:59:40.491  8100  8141 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:59:40.498  8100  8196 D libEGL  : loaded /vendor/lib64/egl/libEGL_emulation.so
07-05 13:59:40.500  8100  8196 D libEGL  : loaded /vendor/lib64/egl/libGLESv1_CM_emulation.so
07-05 13:59:40.502  8100  8196 D libEGL  : loaded /vendor/lib64/egl/libGLESv2_emulation.so
07-05 13:59:40.522  8100  8196 D HostConnection: HostComposition ext ANDROID_EMU_CHECKSUM_HELPER_v1 ANDROID_EMU_native_sync_v2 ANDROID_EMU_native_sync_v3 ANDROID_EMU_native_sync_v4 ANDROID_EMU_dma_v1 ANDROID_EMU_direct_mem ANDROID_EMU_host_composition_v1 ANDROID_EMU_host_composition_v2 ANDROID_EMU_vulkan ANDROID_EMU_deferred_vulkan_commands ANDROID_EMU_vulkan_null_optional_strings ANDROID_EMU_vulkan_create_resources_with_requirements ANDROID_EMU_YUV_Cache ANDROID_EMU_vulkan_ignored_handles ANDROID_EMU_has_shared_slots_host_memory_allocator ANDROID_EMU_vulkan_free_memory_sync ANDROID_EMU_vulkan_shader_float16_int8 ANDROID_EMU_vulkan_async_queue_submit ANDROID_EMU_vulkan_queue_submit_with_commands ANDROID_EMU_vulkan_batched_descriptor_set_update ANDROID_EMU_sync_buffer_data ANDROID_EMU_vulkan_async_qsri ANDROID_EMU_read_color_buffer_dma ANDROID_EMU_hwc_multi_configs GL_OES_EGL_image_external_essl3 GL_OES_vertex_array_object GL_KHR_texture_compression_astc_ldr ANDROID_EMU_host_side_tracing ANDROID_EMU_gles_max_version_3_1
07-05 13:59:40.523  8100  8196 W OpenGLRenderer: Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
07-05 13:59:40.525  8100  8196 W OpenGLRenderer: Failed to initialize 101010-2 format, error = EGL_SUCCESS
07-05 13:59:40.539  8100  8196 D EGL_emulation: eglCreateContext: 0x7b2cea819a50: maj 3 min 1 rcv 4
07-05 13:59:40.567  8100  8196 D EGL_emulation: eglMakeCurrent: 0x7b2cea819a50: ver 3 1 (tinfo 0x7b2f11b4a080) (first time)
07-05 13:59:40.613  8114  8198 D ProfileInstaller: Installing profile for com.kewtoms.whatappsdo
07-05 13:59:40.626  1525  1711 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 13:59:40.665  1274  7844 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService started execution. cause:9 exec_start_elapsed_seconds: 4371 [CONTEXT service_id=218 ]
07-05 13:59:40.672  1274  7843 I NetworkScheduler.Stats: Task com.google.android.gms/com.google.android.gms.ipa.base.IpaGcmTaskService finished executing. cause:9 result: 1 elapsed_millis: 13 uptime_millis: 13 exec_start_elapsed_seconds: 4371 [CONTEXT service_id=218 ]
07-05 13:59:40.736  1525  1712 W MediaProvider: isAppCloneUserPair for user 0: false
07-05 13:59:40.748  8114  8137 W ViewHierarchyExceptionHandler: The complete view hierarchy is available in artifact file 'view-hierarchy-1.txt'.
07-05 13:59:40.773  8114  8137 E TestRunner: failed: test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest)
07-05 13:59:40.773  8114  8137 E TestRunner: ----- begin exception -----
07-05 13:59:40.779  8114  8137 E TestRunner: androidx.test.espresso.NoMatchingViewException: No views in hierarchy found matching: view.getId() is <**********/com.kewtoms.whatappsdo:id/search_field>
07-05 13:59:40.779  8114  8137 E TestRunner:
07-05 13:59:40.779  8114  8137 E TestRunner: View Hierarchy:
07-05 13:59:40.779  8114  8137 E TestRunner: +>DecorView{id=-1, visibility=VISIBLE, width=1080, height=2400, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params={(0,0)(fillxfill) ty=BASE_APPLICATION wanim=0x10302fd
07-05 13:59:40.779  8114  8137 E TestRunner:   fl=LAYOUT_IN_SCREEN LAYOUT_INSET_DECOR SPLIT_TOUCH HARDWARE_ACCELERATED DRAWS_SYSTEM_BAR_BACKGROUNDS
07-05 13:59:40.779  8114  8137 E TestRunner:   pfl=NO_MOVE_ANIMATION FORCE_DRAW_STATUS_BAR_BACKGROUND FIT_INSETS_CONTROLLED
07-05 13:59:40.779  8114  8137 E TestRunner:   bhv=DEFAULT
07-05 13:59:40.779  8114  8137 E TestRunner:   fitSides=}, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=3}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +->LinearLayout{id=-1, visibility=VISIBLE, width=1080, height=2337, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=2}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +-->ViewStub{id=16908741, res-name=action_mode_bar_stub, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +-->FrameLayout{id=-1, visibility=VISIBLE, width=1080, height=2337, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +--->FitWindowsLinearLayout{id=2131230774, res-name=action_bar_root, visibility=VISIBLE, width=1080, height=2337, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=2}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---->ViewStubCompat{id=2131230785, res-name=action_mode_bar_stub, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---->ContentFrameLayout{id=16908290, res-name=content, visibility=VISIBLE, width=1080, height=2337, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +----->DrawerLayout{id=2131230907, res-name=drawer_layout, visibility=VISIBLE, width=1080, height=2337, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=2}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------>CoordinatorLayout{id=2131230808, res-name=app_bar_main, visibility=VISIBLE, width=1080, height=2209, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.drawerlayout.widget.DrawerLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=128.0, child-count=2}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------->AppBarLayout{id=-1, visibility=VISIBLE, width=1080, height=147, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.coordinatorlayout.widget.CoordinatorLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +-------->Toolbar{id=2131231256, res-name=toolbar, visibility=VISIBLE, width=1080, height=147, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=com.google.android.material.appbar.AppBarLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=3}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +--------->AppCompatTextView{id=-1, visibility=VISIBLE, width=137, height=71, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.Toolbar$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=189.0, y=38.0, text=Login, input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +--------->AppCompatImageButton{id=-1, desc=Open navigation drawer, visibility=VISIBLE, width=147, height=147, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.Toolbar$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +--------->ActionMenuView{id=-1, visibility=VISIBLE, width=105, height=147, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.Toolbar$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=975.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->OverflowMenuButton{id=-1, desc=More options, visibility=VISIBLE, width=105, height=126, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.ActionMenuView$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=10.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------->ConstraintLayout{id=-1, visibility=VISIBLE, width=1080, height=2062, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.coordinatorlayout.widget.CoordinatorLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=147.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +-------->FragmentContainerView{id=2131231067, res-name=nav_host_fragment_content_main, visibility=VISIBLE, width=1080, height=2062, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +--------->ConstraintLayout{id=-1, visibility=VISIBLE, width=1080, height=2062, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=10}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->MaterialButton{id=2131230837, res-name=button_sign_up, visibility=VISIBLE, width=248, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=266.0, y=206.0, text=SIGN UP, input-type=0, ime-target=false, has-links=false, is-checked=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->MaterialButton{id=2131230835, res-name=button_login_small, visibility=VISIBLE, width=231, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=566.0, y=206.0, text=LOGIN, input-type=0, ime-target=false, has-links=false, is-checked=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->Guideline{id=2131230954, res-name=guideline2, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=540.0, y=0.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->AppCompatEditText{id=2131230914, res-name=editText_email_address, visibility=VISIBLE, width=648, height=147, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=true, editor-info=[inputType=0x21 imeOptions=0x8000005 privateImeOptions=null actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=Email Address label=null packageName=null autofillId=null fieldId=0 fieldName=null extras=Bundle[{android.support.text.emoji.emojiCompat_metadataVersion=9, android.support.text.emoji.emojiCompat_replaceAll=false}] hintLocales=null contentMimeTypes=null ], x=216.0, y=358.0, text=<EMAIL>, hint=Email Address, input-type=33, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->TextInputLayout{id=2131231229, res-name=textInputLayout_password, visibility=VISIBLE, width=648, height=146, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=216.0, y=531.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +----------->FrameLayout{id=-1, visibility=VISIBLE, width=648, height=146, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=3}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------>TextInputEditText{id=2131231227, res-name=textInputEditText_password, visibility=VISIBLE, width=648, height=146, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=true, editor-info=[inputType=0x81 imeOptions=0x4000006 privateImeOptions=null actionLabel=null actionId=0 initialSelStart=0 initialSelEnd=0 initialCapsMode=0x0 hintText=Password label=null packageName=null autofillId=null fieldId=0 fieldName=null extras=Bundle[{android.support.text.emoji.emojiCompat_metadataVersion=9, android.support.text.emoji.emojiCompat_replaceAll=false}] hintLocales=null contentMimeTypes=null ], x=0.0, y=0.0, text=passwordABC0!, hint=Password, input-type=129, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------>StartCompoundLayout{id=-1, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=2}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------->CheckableImageButton{id=2131231244, res-name=text_input_start_icon, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, is-checked=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------->AppCompatTextView{id=2131231249, res-name=textinput_prefix_text, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, text=, input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------>EndCompoundLayout{id=-1, visibility=VISIBLE, width=137, height=146, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=511.0, y=0.0, child-count=3}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------->AppCompatTextView{id=2131231250, res-name=textinput_suffix_text, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, text=, input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------->FrameLayout{id=-1, visibility=VISIBLE, width=137, height=146, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +-------------->CheckableImageButton{id=2131231242, res-name=text_input_end_icon, desc=Show password, visibility=VISIBLE, width=126, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=11.0, y=10.0, is-checked=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------------->CheckableImageButton{id=2131231243, res-name=text_input_error_icon, desc=Error, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, is-checked=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->MaterialButton{id=**********, res-name=button_login_large, visibility=VISIBLE, width=648, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=216.0, y=703.0, text=Login, input-type=0, ime-target=false, has-links=false, is-checked=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->MaterialTextView{id=2131231235, res-name=textView_login_status, visibility=VISIBLE, width=648, height=71, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=216.0, y=855.0, text=, input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->Guideline{id=2131230956, res-name=guideline_left, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=216.0, y=0.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->Guideline{id=2131230957, res-name=guideline_right, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=864.0, y=0.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->Guideline{id=2131230955, res-name=guideline3, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.constraintlayout.widget.ConstraintLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=206.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------>NavigationView{id=2131231070, res-name=nav_view, visibility=INVISIBLE, width=735, height=2337, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.drawerlayout.widget.DrawerLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=-735.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +------->NavigationMenuView{id=2131230890, res-name=design_navigation_view, visibility=VISIBLE, width=735, height=2337, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=5}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +-------->LinearLayout{id=2131231077, res-name=navigation_header_container, visibility=VISIBLE, width=735, height=651, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.recyclerview.widget.RecyclerView$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=1}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +--------->LinearLayout{id=-1, visibility=VISIBLE, width=735, height=630, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0, child-count=5}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->AppCompatImageView{id=2131230978, res-name=imageView_user_icon, desc=Navigation header, visibility=VISIBLE, width=189, height=273, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=42.0, y=48.0}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->MaterialTextView{id=2131231239, res-name=textView_user_name, visibility=VISIBLE, width=651, height=93, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=42.0, y=321.0, text=, input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->MaterialTextView{id=2131231238, res-name=textView_user_is_registered, visibility=VISIBLE, width=331, height=72, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=42.0, y=414.0, text=User Not Registered, input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.779  8114  8137 E TestRunner: +---------->MaterialTextView{id=2131231237, res-name=textView_used_search_times, visibility=VISIBLE, width=433, height=51, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=42.0, y=486.0, text=Used search 0 times (100), input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.779  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +---------->MaterialTextView{id=2131231234, res-name=textView_app_version, visibility=VISIBLE, width=368, height=51, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.LinearLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=42.0, y=537.0, text=1.107.2 (LOCAL TEST), input-type=0, ime-target=false, has-links=false}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +-------->NavigationMenuItemView{id=2131231065, res-name=nav_home, visibility=VISIBLE, width=735, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.recyclerview.widget.RecyclerView$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=651.0, child-count=2}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->AppCompatCheckedTextView{id=2131230889, res-name=design_menu_item_text, visibility=VISIBLE, width=619, height=126, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=58.0, y=0.0, text=Home, input-type=0, ime-target=false, has-links=false, is-checked=false}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->ViewStub{id=2131230888, res-name=design_menu_item_action_area_stub, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +-------->NavigationMenuItemView{id=2131231064, res-name=nav_gallery, visibility=VISIBLE, width=735, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.recyclerview.widget.RecyclerView$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=777.0, child-count=2}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->AppCompatCheckedTextView{id=2131230889, res-name=design_menu_item_text, visibility=VISIBLE, width=619, height=126, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=58.0, y=0.0, text=Gallery, input-type=0, ime-target=false, has-links=false, is-checked=false}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->ViewStub{id=2131230888, res-name=design_menu_item_action_area_stub, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +-------->NavigationMenuItemView{id=2131231069, res-name=nav_sign_up, visibility=VISIBLE, width=735, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.recyclerview.widget.RecyclerView$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=903.0, child-count=2}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->AppCompatCheckedTextView{id=2131230889, res-name=design_menu_item_text, visibility=VISIBLE, width=619, height=126, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=58.0, y=0.0, text=Sign-up, input-type=0, ime-target=false, has-links=false, is-checked=false}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->ViewStub{id=2131230888, res-name=design_menu_item_action_area_stub, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +-------->NavigationMenuItemView{id=**********, res-name=nav_login, visibility=VISIBLE, width=735, height=126, has-focus=false, has-focusable=true, has-window-focus=true, is-clickable=true, is-enabled=true, is-focused=false, is-focusable=true, is-layout-requested=false, is-selected=false, layout-params=androidx.recyclerview.widget.RecyclerView$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=1029.0, child-count=2}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->AppCompatCheckedTextView{id=2131230889, res-name=design_menu_item_text, visibility=VISIBLE, width=619, height=126, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=58.0, y=0.0, text=Login, input-type=0, ime-target=false, has-links=false, is-checked=true}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +--------->ViewStub{id=2131230888, res-name=design_menu_item_action_area_stub, visibility=GONE, width=0, height=0, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=true, is-selected=false, layout-params=androidx.appcompat.widget.LinearLayoutCompat$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +->View{id=16908336, res-name=navigationBarBackground, visibility=VISIBLE, width=1080, height=63, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=2337.0}
07-05 13:59:40.780  8114  8137 E TestRunner: |
07-05 13:59:40.780  8114  8137 E TestRunner: +->View{id=16908335, res-name=statusBarBackground, visibility=VISIBLE, width=1080, height=128, has-focus=false, has-focusable=false, has-window-focus=true, is-clickable=false, is-enabled=true, is-focused=false, is-focusable=false, is-layout-requested=false, is-selected=false, layout-params=android.widget.FrameLayout$LayoutParams@YYYYYY, tag=null, root-is-layout-requested=false, has-input-connection=false, x=0.0, y=0.0}
07-05 13:59:40.780  8114  8137 E TestRunner: The complete view hierarchy is available in artifact file 'view-hierarchy-1.txt'.
07-05 13:59:40.780  8114  8137 E TestRunner: 	at androidx.test.espresso.NoMatchingViewException$Builder.build(NoMatchingViewException.java:5)
07-05 13:59:40.780  8114  8137 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler.lambda$getNoMatchingViewExceptionTruncater$0(DefaultFailureHandler.java:5)
07-05 13:59:40.780  8114  8137 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler$$ExternalSyntheticLambda1.truncateExceptionMessage(Unknown Source:2)
07-05 13:59:40.780  8114  8137 E TestRunner: 	at androidx.test.espresso.base.ViewHierarchyExceptionHandler.handleSafely(ViewHierarchyExceptionHandler.java:5)
07-05 13:59:40.780  8114  8137 E TestRunner: 	at androidx.test.espresso.base.ViewHierarchyExceptionHandler.handleSafely(ViewHierarchyExceptionHandler.java:1)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler$TypedFailureHandler.handle(DefaultFailureHandler.java:4)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.espresso.base.DefaultFailureHandler.handle(DefaultFailureHandler.java:5)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.espresso.ViewInteraction.waitForAndHandleInteractionResults(ViewInteraction.java:8)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.espresso.ViewInteraction.check(ViewInteraction.java:12)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at com.kewtoms.whatappsdo.auth.SignInTest.test_sign_in_success_mock(SignInTest.java:152)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at java.lang.reflect.Method.invoke(Native Method)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:67)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:58)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:446)
07-05 13:59:40.781  8114  8137 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2361)
07-05 13:59:40.781  8114  8137 E TestRunner: ----- end exception -----
07-05 13:59:40.794  8114  8137 I TestRunner: finished: test_sign_in_success_mock(com.kewtoms.whatappsdo.auth.SignInTest)
