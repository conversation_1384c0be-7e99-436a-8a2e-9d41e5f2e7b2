# https://gist.github.com/ismailkarsli/bc1008c32417c02e9b9e52ab8d8e6e62

# Wallpaper.get() Get current wallpapers path. For getting as Pillow image object, use True as parameter.
# Wallpaper.set() Set wallpaper. Can be path of image or Pillow object. File type doesn't matter and path can be absolute or relative.
# Wallpaper.copy() - Copy current wallpaper. First parameter is directory and the second is file name. File extension should be JPG. Default directory is current directory and file name is 'wallpaper.jpg'


from os import path, getenv, getcwd
from ctypes import windll
from shutil import copyfile
from typing import Union
from PIL import Image
from tempfile import NamedTemporaryFile


class Wallpaper:
    """
    A utility class for managing Windows wallpapers.
    """

    @staticmethod
    def get(returnImgObj: bool = False) -> Union[str, Image.Image]:
        """
        Get the current wallpaper path or as a Pillow image object.

        Args:
            returnImgObj (bool, optional): If True, returns the wallpaper as a Pillow image object. Defaults to False.

        Returns:
            Union[str, Image.Image]: The path to the current wallpaper or the wallpaper as a Pillow image object.

        Examples:
            >>> Wallpaper.get()
            'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\Themes\\TranscodedWallpaper'
            >>> Wallpaper.get(True)
            <PIL.Image.Image image mode=RGB size=1920x1080 at 0x...>
        """
        currentWallpaper = (
            getenv("APPDATA") + "\\Microsoft\\Windows\\Themes\\TranscodedWallpaper"
        )
        if returnImgObj:
            return Image.open(currentWallpaper)
        else:
            tempFile = NamedTemporaryFile(mode="wb", suffix=".jpg").name
            copyfile(currentWallpaper, tempFile)
            return tempFile

    @staticmethod
    def set(wallpaperToBeSet: Union[str, Image.Image]) -> bool:
        """
        Set the wallpaper to the specified image.

        Args:
            wallpaperToBeSet (Union[str, Image.Image]): The path to the image file or a Pillow image object.

        Returns:
            bool: True if the wallpaper was set successfully, False otherwise.

        Examples:
            >>> Wallpaper.set('path/to/wallpaper.jpg')
            True
            >>> Wallpaper.set(Image.open('path/to/wallpaper.png'))
            True
        """
        # Check it is a file
        if path.isfile(wallpaperToBeSet):
            wallpaperToBeSet = path.abspath(wallpaperToBeSet)
            # If a JPG, set
            if wallpaperToBeSet.lower().endswith(
                ".jpg"
            ) or wallpaperToBeSet.lower().endswith(".jpeg"):
                windll.user32.SystemParametersInfoW(
                    20, 0, path.abspath(wallpaperToBeSet), 3
                )
                return True
            # If not a JPG, convert and set
            else:
                image = Image.open(wallpaperToBeSet)
                with NamedTemporaryFile(mode="wb", suffix=".jpg") as tempFile:
                    image.save(tempFile, quality=100)
                    windll.user32.SystemParametersInfoW(
                        20, 0, path.abspath(wallpaperToBeSet), 3
                    )
                return True

        # Check it is a Pillow object
        elif str(wallpaperToBeSet).find("PIL"):
            with NamedTemporaryFile(mode="wb", suffix=".jpg") as tempFile:
                wallpaperToBeSet.save(tempFile, quality=100)
                windll.user32.SystemParametersInfoW(20, 0, path.abspath(tempFile), 3)
            return True
        else:
            return False

    @staticmethod
    def copy(copyTo: str = getcwd(), fileName: str = "wallpaper.jpg") -> bool:
        """
        Copy the current wallpaper to a specified directory with a specified file name.

        Args:
            copyTo (str, optional): The directory to copy the wallpaper to. Defaults to the current working directory.
            fileName (str, optional): The file name to use for the copied wallpaper. Defaults to 'wallpaper.jpg'.

        Returns:
            bool: True if the wallpaper was copied successfully, False otherwise.

        Example:
            >>> Wallpaper.copy()
            True
        """
        return copyfile(Wallpaper.get(), path.join(path.abspath(copyTo), fileName))
