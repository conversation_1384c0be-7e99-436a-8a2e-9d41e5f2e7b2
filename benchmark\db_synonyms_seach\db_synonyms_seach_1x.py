import os
import time
from const_path import proj_ser_api_db_path
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit

"""
Set db dir
Load db
Search word (time this)
"""
# Set db dir
db_dir_path = os.path.join(
    proj_ser_api_db_path,
    "early_preprocess",
    "create_db_synonyms_with_source",
    "run_create_db_synonyms_with_source_nltk_using_nltk_words",
)

exist0 = os.path.exists(proj_ser_api_db_path)
exist1 = os.path.exists(db_dir_path)

# Load db
db_synonyms = PickleDatabaseSplit(
    db_fpath=db_dir_path,
    db_name="db_syn",
    load_by_thread=False,
)

# Search word
time_start = time.time()

synonyms = db_synonyms.query_key_col(
    key="talk",
    col="nltk",
    default=[],
)

assert len(synonyms) > 0

time_end = time.time()


print(f"db loaded parts len: {db_synonyms.get_db_num_keys()}")
print("time used:" + "{:.2f}".format((time_end - time_start) * 1000) + " ms")

# 0 - 45ms (release / debug mode)
