# https://www.sbert.net/docs/pretrained-models/msmarco-v3.html

from sentence_transformers import SentenceTransformer, util
# model = SentenceTransformer('msmarco-distilroberta-base-v3')  # cannot download this one
model = SentenceTransformer('msmarco-distilbert-base-v4')

query_embedding = model.encode('How big is London')
passage_embedding = model.encode('London has 9,787,426 inhabitants at the 2011 census')

print("Similarity:", util.cos_sim(query_embedding, passage_embedding))


# Repository Not Found for url: https://huggingface.co/api/models/sentence-transformers/msmarco-distilroberta-base-v3.
# Please make sure you specified the correct `repo_id` and `repo_type`.
# If you are trying to access a private or gated repo, make sure you are authenticated.
# Invalid username or password.