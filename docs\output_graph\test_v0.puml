@startuml test_verify_client_apps_trace
skinparam ranksep 0
allowmixing



together{
  card "script\nfastapi_app" as asdf{
    usecase "signup" as api_signup_endpoint
    usecase "signin" as api_signin_endpoint
    usecase "search_user_app" as api_search_user_app_endpoint
  }

  note as nasf
  Each endpoint has response format
  {"response": data_dict}
  end note
}


together{
  card "class\nUserDatabase" as sd5fe{
    usecase "signup" as user_db_signup
    note left of user_db_signup
      in: xx, dd
      out: asdf
    end note

    usecase "signin" as user_db_signin
    note left of user_db_signin
      Sign in user
      in: email, password
    end note

    usecase "sign_out" as user_db_sign_out
    note left of user_db_sign_out
      Sign out
    end note

    usecase "delete" as user_db_delete
    note left of user_db_delete
      Delete user
    end note


  }

  note as dnasff
  Each method has response format
  {"result": data_dict}
  end note
}

api_signup_endpoint --> user_db_signup
api_signin_endpoint --> user_db_signin


together{
  card "class\nAppDatabase" as asfddf{
    usecase "search_app" as app_db_search_app
  }

  note as nsasf
  Each method has response format
  {"result": data_dict}
  end note
}

api_search_user_app_endpoint --> app_db_search_app

class UserDatabase {
+ sign_in
+ sign_out
+ sign_up
+ delete
}

@enduml


