{
    "general":{
        "init_steps":[
        ],
        "steps":[
            "read_env",
            "ssh_docker_compose_down",
            "wait_for_5s",
            "ssh_log_docker_compose",
        ],
        "variables":{
            "var_online":false
        },
        "logger":{
            "format":"%(asctime)s %(message)s",
            "datefmt":"%m/%d/%Y %I:%M:%S %p",
            "log_to_file":true,
            "folder_path":"./tmp_CA_logs",
            "filename":"log.log",
            "loglevel":"logging.DEBUG"
        },
        "debug":{
            "debug_visualize":true,
            "save_result_txt":true
        }
    },
    "all_steps":[
        {
            "step_name": "read_env",
            "type": "PluginEnvReader",
            "use": true,
            "use_env_path": true,
            "env_path": "./.env"
        },
        {
            "step_name": "ssh_docker_compose_down",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
cd /root/code/{read_env.REPO}/; \
docker-compose down; \
exit;",
            "output_str_buffer_to": "output_str",
        },
        {
            "step_name": "wait_for_5s",
            "type": "PluginWaiter",
            "use": true,
            "wait_time": 5
        },
        {
            "step_name": "ssh_log_docker_compose",
            "type": "PluginCommandRunner",
            "use": true,
            "command": "\
ssh \
-p {read_env.SSH_PORT} \
{read_env.SSH_USER}@{read_env.SSH_HOST} \
cd /root/code/{read_env.REPO}/; \
docker-compose logs --tail 1000 --timestamps; \
exit;",
            "output_str_buffer_to": "output_str",
        },
    ]
   

    

}
