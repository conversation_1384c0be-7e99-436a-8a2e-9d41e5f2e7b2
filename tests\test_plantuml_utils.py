import os
import shutil
import tempfile
import pytest
from tomsze_utils.plantuml_utils import PlantUmlGenerator


def a():
    print("a")


def test_a():
    a()


@pytest.fixture(scope="function")
def temp_dir_path():
    # Create a temporary directory for log files
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Clean up the temporary directory and its contents after each test
    shutil.rmtree(temp_dir, ignore_errors=True)


class TestPlantUmlGenerator:

    def test_generate_valid_case(self, temp_dir_path):
        generator = PlantUmlGenerator()
        test_script_path = "./tests/test_plantuml_utils.py"
        test_function_name = "test_a"
        output_dir = temp_dir_path

        generator.generate(test_script_path, test_function_name, output_dir)

        # Check if the output file is created
        output_file = os.path.join(output_dir, "test_a_trace.puml")
        assert os.path.exists(output_file)

        # Optionally, check the content of the generated file
        with open(output_file, "r") as f:
            content = f.read()
            assert "@startuml test_a_trace" in content
            assert "@enduml" in content

    def test_generate_invalid_case(self, temp_dir_path):
        generator = PlantUmlGenerator()
        test_script_path = "./tests/non_existent_script.py"
        test_function_name = "test_non_existent"
        output_dir = temp_dir_path

        # Expecting an error or specific behavior when the script does not exist
        with pytest.raises(FileNotFoundError):
            generator.generate(test_script_path, test_function_name, output_dir)
