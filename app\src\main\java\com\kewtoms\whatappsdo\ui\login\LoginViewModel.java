package com.kewtoms.whatappsdo.ui.login;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.kewtoms.whatappsdo.data.Configuration;
import com.kewtoms.whatappsdo.data.Constants;
import com.kewtoms.whatappsdo.data.Environment;

public class LoginViewModel
  extends ViewModel {

  private final MutableLiveData<String> mEmailAddress;
  private final MutableLiveData<String> mPassword;
  private final MutableLiveData<String> mStatus;
  private Configuration config = Configuration.getInstance();

  public LoginViewModel() {
    mEmailAddress = new MutableLiveData<>();
    mPassword = new MutableLiveData<>();
    mStatus = new MutableLiveData<>();

    if (config.isTest() || config.isDev()) {
      mEmailAddress.setValue(Constants.test_email_address);
      mPassword.setValue(Constants.test_password);
      mStatus.setValue("");
    }
  }


  public LiveData<String> getEmailAddress() {
    return mEmailAddress;
  }

  public void setEmailAddress(String emailAddress) {
    mEmailAddress.postValue(emailAddress);
  }

  public LiveData<String> getPassword() {
    return mPassword;
  }

  public void setPassword(String password) {
    mPassword.postValue(password);
  }


  public LiveData<String> getStatus() {
    return mStatus;
  }

  public void setStatus(String status) {
    mStatus.postValue(status);
  }

}