import copy
import datetime
import shutil
import sys
import os
from const_path import (
    proj_ser_tools_path,
    proj_ser_api_db_apps_path,
    proj_ser_api_db_to_add_path,
)
from tomsze_utils.database_utils.pickle_database_split import (
    PickleDatabaseSplit,
    create_db_info,
    remove_pickles,
)
from server.api.end_to_end.api_constant_keys import DBAppsColEnum
from server.api.end_to_end.api_constant_keys import DBToAddColEnum
from server.api.end_to_end.utils import ppd_emb_kw_kwpp_update_using_keys

from server.api.utils.descriptions.get_description import scrape_play_store
from sentence_transformers import SentenceTransformer

"""
To fill in new apps (from a db_to_add) to db_apps
"""
max_ngram_size = 2
deduplication_threshold = 0.9
max_num_keywords = 20

not_found_playstore_str = DBToAddColEnum.NOT_FOUND_PLAYSTORE
check_datetime_str = DBToAddColEnum.CHECK_DATE_TIME


def fill_new_apps_with_db_to_add(
    db_apps_folder_path,
    db_to_add,
    db_apps_output_folder_path,
    db_apps_backup_zip_path,
    model_id: str = "multi-qa-MiniLM-L6-cos-v1",
):
    embedder = SentenceTransformer(model_id)

    # Backup the db_apps folder first.
    shutil.make_archive(db_apps_backup_zip_path, "zip", db_apps_folder_path)

    # Load the db_apps from folder
    db_apps = PickleDatabaseSplit(
        db_fpath=db_apps_folder_path,
        db_name="db_apps",
        dump_db_fpath=db_apps_output_folder_path,
        load_by_thread=False,
    )

    # Read new apps from db_to_add
    loaded_parts_tmp = copy.deepcopy(db_to_add.loaded_parts)

    for loaded_part_dict in loaded_parts_tmp:
        for package_name in loaded_part_dict.keys():
            not_found_playstore = loaded_part_dict[package_name][
                not_found_playstore_str
            ]
            check_datetime = loaded_part_dict[package_name][check_datetime_str]

            if not_found_playstore == "":
                # Scrape from playstore.
                scrap_success, result = scrape_play_store(device_app_id=package_name)

                if scrap_success:
                    ppd_emb_kw_kwpp_update_using_keys(
                        app_id=package_name,
                        db_apps=db_apps,
                        scrape_result=result,
                        embedder=embedder,
                        deduplication_threshold=deduplication_threshold,
                        max_ngram_size=max_ngram_size,
                        max_num_keywords=max_num_keywords,
                    )

                    db_to_add.remove_key(package_name)
                else:
                    db_to_add.update_data_with_keys(
                        keys=[package_name, not_found_playstore_str],
                        data=True,
                    )

                    date_now = datetime.datetime.now()
                    str_datetime = (
                        "y"
                        + str(date_now.year).zfill(2)
                        + "_m"
                        + str(date_now.month).zfill(2)
                        + "_d"
                        + str(date_now.day).zfill(2)
                        + "_h"
                        + str(date_now.hour).zfill(2)
                        + "_m"
                        + str(date_now.minute).zfill(2)
                        + "_s"
                        + str(date_now.second).zfill(2)
                    )
                    db_to_add.update_data_with_keys(
                        keys=[package_name, check_datetime_str],
                        data=str_datetime,
                    )

            g = 1

    # Dump db_apps (all parts) to folder.
    db_apps.dump_all_parts_to_pickles()


def test_fill_new_apps_with_db_to_add():
    # Set paths.
    db_apps_original_folder_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_db_with_db_to_add_original_folder",
    )
    if not os.path.exists(db_apps_original_folder_path):
        os.makedirs(db_apps_original_folder_path)

    db_to_add_folder_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_db_with_db_to_add_db_folder",
    )
    if not os.path.exists(db_to_add_folder_path):
        os.makedirs(db_to_add_folder_path)

    db_apps_output_folder_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_db_with_db_to_add_output_folder",
    )
    if not os.path.exists(db_apps_output_folder_path):
        os.makedirs(db_apps_output_folder_path)

    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    db_apps_backup_zip_path = os.path.join(
        proj_ser_tools_path,
        "test_fill_new_apps_db_with_db_to_add_backup_zip_folder",
        f"db_apps_{time_str}",
    )
    if not os.path.exists(db_apps_backup_zip_path):
        os.makedirs(db_apps_backup_zip_path)

    # Create a test db_apps_old.
    db_apps_old = PickleDatabaseSplit(
        db_fpath=db_apps_original_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )
    assert db_apps_old.query_key("com.opera.gx") == None
    assert db_apps_old.query_key("io.loudplay.android.app") == None

    # Create a test db_apps_old.
    db_to_add = PickleDatabaseSplit(
        db_fpath=db_to_add_folder_path,
        db_name="db_to_add",
        load_by_thread=False,
    )

    db_to_add.update_data_with_keys(
        keys=["com.opera.gx", not_found_playstore_str], data=""
    )
    db_to_add.update_data_with_keys(keys=["com.opera.gx", check_datetime_str], data="")

    db_to_add.update_data_with_keys(
        keys=["io.loudplay.android.app", not_found_playstore_str], data=""
    )
    db_to_add.update_data_with_keys(
        keys=["io.loudplay.android.app", check_datetime_str], data=""
    )
    db_to_add.update_data_with_keys(keys=["xx.xx", not_found_playstore_str], data="")
    db_to_add.update_data_with_keys(keys=["xx.xx", check_datetime_str], data="")

    assert db_to_add.query_key("com.opera.gx") != None
    assert db_to_add.query_key("io.loudplay.android.app") != None

    db_to_add.dump_all_parts_to_pickles()

    remove_pickles(db_apps_output_folder_path)
    fill_new_apps_with_db_to_add(
        db_apps_folder_path=db_apps_original_folder_path,
        db_to_add=db_to_add,
        db_apps_backup_zip_path=db_apps_backup_zip_path,
        db_apps_output_folder_path=db_apps_output_folder_path,
    )

    db_apps_new = PickleDatabaseSplit(
        db_fpath=db_apps_output_folder_path,
        db_name="db_apps",
        load_by_thread=False,
    )

    assert db_to_add.query_key("com.opera.gx") == None
    assert db_to_add.query_key("io.loudplay.android.app") == None

    assert db_to_add.query_key("xx.xx") != None
    assert db_to_add.query_key("xx.xx")[not_found_playstore_str] == True
    assert db_to_add.query_key("xx.xx")[check_datetime_str] != ""

    assert db_apps_new.get_db_num_keys() == 4
    assert db_apps_new.query_key("com.opera.gx") != None
    assert db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.DESC] != ""
    assert (
        db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.EMBD] != None
        or db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.EMBD] != []
    )
    assert len(db_apps_new.query_key("com.opera.gx")[DBAppsColEnum.KEYW]) > 0
    assert db_apps_new.query_key("io.loudplay.android.app") != None

    g = 1


def run_fill_new_apps_with_db_to_add():
    db_apps_folder_path = proj_ser_api_db_apps_path
    db_to_add_folder_path = proj_ser_api_db_to_add_path
    db_apps_output_folder_path = proj_ser_api_db_apps_path

    time_str = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H-%M-%S")
    db_apps_backup_zip_path = os.path.join(
        proj_ser_tools_path,
        "run_fill_new_apps_with_db_to_add_backup_zip_folder",
        f"db_apps_{time_str}",
    )

    # Create a test db_apps_old.
    db_to_add = PickleDatabaseSplit(
        db_fpath=db_to_add_folder_path,
        db_name="db_to_add",
        load_by_thread=False,
    )

    if db_to_add.get_db_num_keys() == 0:
        print("There is no need to update database")
        return

    fill_new_apps_with_db_to_add(
        db_apps_folder_path=db_apps_folder_path,
        db_to_add=db_to_add,
        db_apps_backup_zip_path=db_apps_backup_zip_path,
        db_apps_output_folder_path=db_apps_output_folder_path,
    )


def main():
    # Test function and check two more new app
    test_fill_new_apps_with_db_to_add()

    # Run function
    # run_fill_new_apps_with_db_to_add()


if __name__ == "__main__":
    sys.exit(main())
