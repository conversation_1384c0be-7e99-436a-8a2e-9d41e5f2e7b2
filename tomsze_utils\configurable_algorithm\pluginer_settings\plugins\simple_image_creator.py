"""App plugin"""

import os
import cv2
import numpy as np
from dataclasses import dataclass
from tomsze_utils.plugins.plugin_utils import factory
from tomsze_utils.configurable_algorithm.utils import (
    Data,
    parse_data_and_store,
)


@dataclass
class PluginSimpleImageCreator:  # Must start with Plugin
    type: str  # must exist
    plugin_can_change_files: bool  # must exist

    def do_something(self, data_obj: Data, plugin_unqiue_key: str) -> None:
        current_step = data_obj.__dict__["current_step"]
        logger = data_obj.__dict__["logger"]
        logger.info(f"{self.type} step {current_step} runs")
        config = data_obj.__dict__["config"]

        var_pool = data_obj.dict_var
        step_config = data_obj.dict_step_config[current_step]

        use = parse_data_and_store(
            logger,
            "use",
            data_obj,
            step_config,
            config,
            type="bool",
            default=False,
        )

        if not use:
            return True

        width = parse_data_and_store(
            logger,
            "width",
            data_obj,
            step_config,
            config,
            type="int",
            default=0,
        )

        height = parse_data_and_store(
            logger,
            "height",
            data_obj,
            step_config,
            config,
            type="int",
            default=0,
        )

        num_channels = parse_data_and_store(
            logger,
            "num_channels",
            data_obj,
            step_config,
            config,
            type="int",
            default=1,
        )

        channel_values_rgb = parse_data_and_store(
            logger,
            "channel_values_rgb",
            data_obj,
            step_config,
            config,
        )
        data_obj.dict_var[f"{current_step}.channel_values_rgb"] = channel_values_rgb
        channel_values_rgb = channel_values_rgb[::-1]

        # Create simple image
        im = np.ones((height, width, num_channels))
        im = im * channel_values_rgb

        # which image to buffer to
        buffer_to_which = step_config["buffer_to_which"]
        data_obj.dict_var[buffer_to_which] = im

        save = parse_data_and_store(logger, "save", data_obj, step_config, config)

        # save image to path
        if save:
            save_path = parse_data_and_store(
                logger, "save_path", data_obj, step_config, config
            )
            save2 = parse_data_and_store(logger, "save2", data_obj, step_config, config)
            save_path2 = parse_data_and_store(
                logger,
                "save_path2",
                data_obj,
                step_config,
                config,
            )

            save_folder_path = save_path.replace(save_path.split("/")[-1], "")
            if not os.path.exists(save_folder_path):
                os.makedirs(save_folder_path, exist_ok=True)

            cv2.imwrite(save_path, im)
            data_obj.dict_var[f"{current_step}.save_path"] = save_path

            if save2 and save_path2 != "":
                save_folder_path = save_path2.replace(save_path2.split("/")[-1], "")
                if not os.path.exists(save_folder_path):
                    os.makedirs(save_folder_path, exist_ok=True)

                cv2.imwrite(save_path2, im)
                data_obj.dict_var[f"{current_step}.save_path2"] = save_path2

        data_obj.dict_var[current_step] = "test_result"

        return True


def register(type: str) -> None:
    cls = globals().get(type)
    if cls:
        factory.register(type, cls)
    else:
        raise ValueError(f"Type '{type}' not found in the current module.")
