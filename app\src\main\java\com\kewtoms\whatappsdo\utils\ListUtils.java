package com.kewtoms.whatappsdo.utils;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {

 public static Object nestedLookupOnListObject(
  List<Object> dataList,
  List<Integer> indexes) {
  if (dataList == null) {
   return null;
  }

  if (indexes.size() == 1) {
   int index = indexes.get(0);
   return dataList.get(index);
  }

  int index = indexes.get(0);
  List<Object> newDataList = (List<Object>) dataList.get(index);
  List<Integer> newIndexes = new ArrayList<>(indexes);
  newIndexes.remove(0);
  return nestedLookupOnListObject(
   newDataList,
   newIndexes
  );
 }
}
